# Use OpenJDK 21 as base image
FROM openjdk:21-jdk-slim AS build

# Install Maven
RUN apt-get update && apt-get install -y maven

# Set working directory
WORKDIR /app

# Copy pom.xml and download dependencies
COPY pom.xml .
RUN mvn dependency:go-offline -B

# Copy source code
COPY src ./src
COPY json-files ./json-files
COPY images ./images
COPY pdf-files ./pdf-files

# Build the application
RUN mvn clean package -DskipTests

# Use OpenJDK 21 runtime for final image
FROM openjdk:21-slim

WORKDIR /app

# Copy the jar from build stage
COPY --from=build /app/target/*.jar app.jar
COPY --from=build /app/json-files ./json-files
COPY --from=build /app/images ./images
COPY --from=build /app/pdf-files ./pdf-files

# Expose port 8080
EXPOSE 8080

# Run the jar file
ENTRYPOINT ["java", "--enable-preview", "-jar", "app.jar"] 