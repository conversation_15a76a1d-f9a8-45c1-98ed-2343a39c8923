# StageServer - Spring Boot Backend Application
StageServer is a Java Spring Boot application using Neo4J as the backend database. The project is build with maven.
The script file build_with_version.sh shall be used to build the project and the resultant Jar file will be located in the target folder. The script uses the latest git tag number for naming.  For example if the latest git tag is v1.10.0, the output jar file will be named as
StageServer-1.10.0.jar. 
Running this script checkout the pom.xml file and modify the version number to build it.  However, it will undo the changes after the build is completed.

### Folder Organization
As per normal Spring Boot conventions, the source code is organized in the following way.
1. StageServer/json-files  - This directory contains the json files that are used to populate the database with pre-defined data.
2. StageServer/scripts/stage_server_startup.sh   - This is a script that will start the stage server.
3. StageServer/scripts/db_backup/copy_db.sh  - This script can be used to copy the database from the deployment server to a local server
4. src/main - All the Java source code are here; organized into packages/folders as per standard practice
5. src/main/resources - All the static resources(translation files, email templates, application.properties file are here)


## Overview

This is a Spring Boot-based backend application that serves as the core for the StageMinder platform. It provides APIs for managing virtual profiles, bookings, payments, file storage, authentication, and notifications. The application uses a Neo4j graph database for data storage, supports JWT and OAuth2 authentication, integrates with Stripe for payments, AWS S3 for file storage (including dynamically uploaded images), and Gmail for email notifications. Static icons used by the backend are stored locally in an `images/icons` folder. This README outlines the configuration, setup instructions, build and deployment scripts, and key considerations for running and deploying the application.

## Table of Contents

- [Purpose](#purpose)
- [Features](#features)
- [Prerequisites](#prerequisites)
- [Configuration](#configuration)
- [Setup Instructions](#setup-instructions)
- [Building the Application](#building-the-application)
- [Deploying and Running the Application](#deploying-and-running-the-application)
- [Environment Variables and Placeholders](#environment-variables-and-placeholders)
- [Important Notes](#important-notes)
- [Contact](#contact)

## Purpose

The Spring Boot backend application is designed to:
- Connect to a Neo4j graph database for data storage.
- Provide API endpoints with Swagger documentation.
- Support file uploads (JSON, PDF, and images) and logging.
- Enforce password policies and two-factor authentication (2FA).
- Send email and SMS notifications for authentication and other purposes.
- Secure the application with JWT and OAuth2 (Google, Facebook).
- Process payments via Stripe.
- Store dynamically uploaded images and other files in AWS S3.
- Support virtual profile and booking features with language-specific configurations.

## Features

- **Neo4j Database**: Stores and manages data using a graph database.
- **API Documentation**: Exposed via Swagger at `/apidocs`.
- **File Uploads**: Supports JSON, PDF, and image uploads with a maximum file size of 10MB; dynamically uploaded images are stored in AWS S3.
- **Authentication**: Uses JWT for token-based authentication and OAuth2 for Google and Facebook login.
- **Payment Processing**: Integrates with Stripe for secure payments.
- **Cloud Storage**: Stores dynamically uploaded files (e.g., images) in AWS S3; static icons are stored locally in `images/icons`.
- **Email Notifications**: Sends emails via Gmail SMTP.
- **Two-Factor Authentication**: Supports 2FA with 6-digit SMS codes and 15-minute token expiration.
- **Virtual Profiles**: Configures booking cancellation windows and supported languages (English, French, French(CA), Italian, German, Don't Care).
- **Monitoring**: Exposes management endpoints for health checks and metrics.

## Prerequisites

- **Java**: JDK 21 or higher.
- **Maven**: For dependency management and building the project.
- **Neo4j Database**: A running Neo4j instance (local or hosted).
- **Gmail Account**: For SMTP email configuration (with app-specific password).
- **Google Cloud Console**: For Google API and OAuth2 credentials.
- **Facebook Developer Portal**: For OAuth2 credentials.
- **Stripe Account**: For payment processing keys.
- **AWS Account**: For S3 storage and IAM credentials (for dynamically uploaded images and files).
- **Environment Variables**: A secrets management system or `.env` file for sensitive configurations.
- **Bash**: For running shell scripts (`start_stage_server.sh` and `build_with_version.sh`).

## Configuration

The application is configured via the `application.properties` file (or `application.yml`) in the `src/main/resources` directory. Below are the key configuration sections and their properties:

### 1. Neo4j Database
- **Purpose**: Configures the connection to a Neo4j graph database.
- **Properties**:
  - `spring.neo4j.uri`: URI for Neo4j (default: `bolt://localhost:7687`).
  - `spring.neo4j.authentication.username`: Neo4j username.
  - `spring.neo4j.authentication.password`: Neo4j password.
  - `spring.neo4j.ssl.trust.strategy`: SSL trust strategy (e.g., `TRUST_ALL_CERTIFICATES` for less secure setups).
  - `org.springframework.data.neo4j`: Logging level for Neo4j operations (e.g., `DEBUG`).

### 2. API Settings
- **Purpose**: Configures API documentation and Spring profiles.
- **Properties**:
  - `springdoc.api-docs.path`: Path for API documentation (e.g., `/apidocs`).
  - `spring.profiles.active`: Active profile (e.g., `prod` for production).
  - `spring.messages.basename`: Base name for internationalization messages (e.g., `lang/messages`).

### 3. Management Endpoints
- **Purpose**: Exposes endpoints for monitoring and metrics.
- **Properties**:
  - `management.endpoints.web.exposure.include`: Exposes all endpoints (e.g., `*`).
  - `management.endpoint.health.show-details`: Shows detailed health info (e.g., `always`).
  - `management.metrics.distribution.*`: Configures HTTP request metrics percentiles.

### 4. File Storage and Logging
- **Purpose**: Defines locations for JSON, PDF, and static icon files, as well as logging levels. Dynamically uploaded images are stored in AWS S3.
- **Properties**:
  - `json.files.location`: Directory for JSON files (e.g., `/config/json`).
  - `pdf.files.location`: Directory for PDF files (e.g., `/pdf-files`).
  - `images/icons`: Local directory for static icons used by the backend (e.g., `/images/icons`). This folder contains all the icons needed for backend functionality.
  - `logging.level.root`: Root logging level (e.g., `INFO`).
  - `logging.level.kakapo`: Application-specific logging level (e.g., `INFO`).
- **Note**: Dynamically uploaded images (e.g., user-uploaded profile pictures) are stored in AWS S3, configured via the AWS S3 properties below.

### 5. Password Policy
- **Purpose**: Enforces password requirements.
- **Properties**:
  - `stage-server.password.max-length`: Maximum password length (e.g., `20`).
  - `stage-server.password.min-length`: Minimum password length (e.g., `6`).
  - `stage-server.password.min-special-char`: Minimum special characters (e.g., `0`).
  - `stage-server.password.min-uppercase`: Minimum uppercase letters (e.g., `0`).

### 6. File Uploads
- **Purpose**: Configures multipart file uploads (including images, JSON, and PDF).
- **Properties**:
  - `spring.http.multipart.max-file-size`: Maximum file size (e.g., `10MB`).
  - `spring.http.multipart.max-request-size`: Maximum request size (e.g., `10MB`).
  - `spring.http.multipart.enabled`: Enables multipart uploads (e.g., `true`).
  - `spring.http.multipart.location`: Local upload directory for temporary storage before S3 upload (e.g., `../Uploads`).

### 7. Email/SMS Tokens
- **Purpose**: Manages token expiration for email/SMS authentication.
- **Properties**:
  - `stage-server.token.expiration`: Token expiration time (e.g., `15` minutes).
  - `stage-server.two-factor-authentication-prompt`: Enables 2FA prompt (e.g., `true`).
  - `stage-server.sms-code-length`: SMS code length (e.g., `6` digits).

### 8. Virtual Profile
- **Purpose**: Configures virtual profile booking settings.
- **Properties**:
  - `virtual-profile.booking-cancel-time`: Cancellation window (e.g., `30` minutes).

### 9. Server URLs
- **Purpose**: Defines frontend and backend server URLs.
- **Properties**:
  - `stage-server.front-end-url`: Frontend server URL.
  - `stage-server.back-end-url`: Backend server URL.

### 10. ACT Profile
- **Purpose**: Specifies supported languages for ACT profiles.
- **Properties**:
  - `stage-server.act.performing-languages`: Supported languages (e.g., `English, French, French(CA), Italian, German, Don't Care`).

### 11. Email Server
- **Purpose**: Configures SMTP for sending emails via Gmail.
- **Properties**:
  - `spring.mail.host`: SMTP host (e.g., `smtp.gmail.com`).
  - `spring.mail.port`: SMTP port (e.g., `587`).
  - `spring.mail.username`: Gmail username.
  - `spring.mail.password`: Gmail app-specific password.
  - `spring.mail.properties.mail.smtp.auth`: Enables SMTP authentication (e.g., `true`).
  - `spring.mail.properties.mail.smtp.starttls.enable`: Enables STARTTLS (e.g., `true`).

### 12. JWT Settings
- **Purpose**: Configures JSON Web Tokens for authentication.
- **Properties**:
  - `app.jwt-secret`: Secret key for signing JWTs.
  - `app.jwt-expiration-milliseconds`: Token expiration time (e.g., `2592000000` for 30 days).

### 13. Google API
- **Purpose**: Configures Google API services (e.g., Maps).
- **Properties**:
  - `google.api.key`: API key for Google services.

### 14. OAuth2 (Google and Facebook)
- **Purpose**: Configures OAuth2 for Google and Facebook authentication.
- **Properties**:
  - `spring.security.oauth2.client.registration.google.client-id`: Google OAuth2 client ID.
  - `spring.security.oauth2.client.registration.google.client-secret`: Google OAuth2 client secret.
  - `spring.security.oauth2.client.registration.google.redirect-uri`: Google redirect URI (e.g., `[baseUrl]/login/oauth2/code/google`).
  - `spring.security.oauth2.client.registration.facebook.client-id`: Facebook OAuth2 client ID.
  - `spring.security.oauth2.client.registration.facebook.client-secret`: Facebook OAuth2 client secret.
  - `spring.security.oauth2.client.registration.facebook.redirect-uri`: Facebook redirect URI (e.g., `[baseUrl]/login/oauth2/code/facebook`).
  - `spring.security.oauth2.client.registration.facebook.scopes`: Facebook scopes (e.g., `email, public_profile`).

### 15. Stripe Payments
- **Purpose**: Configures Stripe for payment processing.
- **Properties**:
  - `stripe.payment.key`: Stripe API key.
  - `stripe.webhook.key`: Stripe webhook key.