#!/bin/bash

set -euo pipefail
trap 'echo "❌ Error occurred at line $LINENO"; exit 1' ERR

# Default values
REMOTE_USER="ubuntu"
BACKUP_DIR="/var/lib/neo4j/backups"
BACKUP_FILE="neo4j.dump"
NEO4J_DATA_DIR="/var/lib/neo4j"
NEO4J_DB_DIR="${NEO4J_DATA_DIR}/data/databases/neo4j"
NEO4J_SERVICE="neo4j"
LOCAL_BACKUP_DIR="/tmp"

# -------------------------------
# Command line argument parsing
# -------------------------------
usage() {
  echo "Usage: $0 --source <SOURCE_IP_ADDRESS>"
  exit 1
}

SOURCE_SERVER=""

while [[ $# -gt 0 ]]; do
  case "$1" in
    -s|--source)
      SOURCE_SERVER="$2"
      shift 2
      ;;
    -h|--help)
      usage
      ;;
    *)
      echo "❌ Unknown option: $1"
      usage
      ;;
  esac
done

if [[ -z "${SOURCE_SERVER}" ]]; then
  echo "❌ Source IP address not provided."
  usage
fi

echo "🔧 Using source server IP: $SOURCE_SERVER"

DUMP_PATH="${BACKUP_DIR}/${BACKUP_FILE}"

# 0. Check Neo4j version on both source and destination
echo "🔍 Checking Neo4j versions..."

SOURCE_VERSION=$(ssh ${REMOTE_USER}@${SOURCE_SERVER} "neo4j --version | awk '{print \$NF}'")
LOCAL_VERSION=$(neo4j --version | awk '{print $NF}')

echo "Source server Neo4j version: $SOURCE_VERSION"
echo "Local machine Neo4j version: $LOCAL_VERSION"

if [[ "$SOURCE_VERSION" != "$LOCAL_VERSION" ]]; then
    echo "❌ Version mismatch: source ($SOURCE_VERSION) vs local ($LOCAL_VERSION). Aborting!"
    exit 1
fi

echo "✅ Neo4j versions match. Proceeding..."

# 1. Ensure backup directory exists on source server
echo "Ensuring backup directory exists on the source server ($SOURCE_SERVER)..."
ssh ${REMOTE_USER}@${SOURCE_SERVER} "sudo mkdir -p ${BACKUP_DIR} && sudo chown neo4j:neo4j ${BACKUP_DIR}"

# 2. Rename existing backup if it exists on the Remote server
echo "Checking for an existing backup file on the source server..."
ssh ${REMOTE_USER}@${SOURCE_SERVER} "if [ -f ${DUMP_PATH} ]; then
    TIMESTAMP=\$(date +'%Y-%m-%d_%H-%M-%S');
    sudo mv ${DUMP_PATH} ${BACKUP_DIR}/neo4j.dump.\$TIMESTAMP.back;
    echo \"Existing backup renamed to neo4j.dump.\$TIMESTAMP.back\";
fi"

# 3. Rename existing backup if it exists on the local machine
if [ -f "${LOCAL_BACKUP_DIR}/${BACKUP_FILE}" ]; then
    TIMESTAMP=$(date +'%Y-%m-%d_%H-%M-%S')
    mv ${LOCAL_BACKUP_DIR}/${BACKUP_FILE} ${LOCAL_BACKUP_DIR}/neo4j.dump.$TIMESTAMP.back
    echo "Existing backup renamed to neo4j.dump.$TIMESTAMP.back"
fi

# 4. Stop Neo4j on the source server and create a backup
echo "Stopping Neo4j on the source server ($SOURCE_SERVER)..."
ssh ${REMOTE_USER}@${SOURCE_SERVER} "sudo ${NEO4J_SERVICE} stop"

echo "Creating a backup of the Neo4j database on the source server..."
ssh ${REMOTE_USER}@${SOURCE_SERVER} "sudo -u neo4j neo4j-admin database dump neo4j --to-path=${BACKUP_DIR}"

# 5. Transfer the backup file to the destination server
echo "Copying the backup file from $SOURCE_SERVER to local machine..."
scp ${REMOTE_USER}@${SOURCE_SERVER}:${DUMP_PATH} ${LOCAL_BACKUP_DIR}

# 6. Stop Neo4j on the destination server
echo "Stopping Neo4j on the local machine..."
sudo ${NEO4J_SERVICE} stop

# 7. Remove the existing Neo4j database on the destination server
echo "Removing existing Neo4j database on the destination server..."
if [ -d "${NEO4J_DB_DIR}" ]; then
    sudo rm -rf ${NEO4J_DB_DIR}
    echo "Deleted existing database directory: ${NEO4J_DB_DIR}"
fi

# 8. Remove transaction logs
if [ -d "${NEO4J_DATA_DIR}/data/transactions/neo4j" ]; then
    sudo rm -rf ${NEO4J_DATA_DIR}/data/transactions/neo4j
    echo "Deleted existing transaction logs directory: ${NEO4J_DATA_DIR}/data/transactions/neo4j"
fi

echo "Ensuring complete cleanup of any prior Neo4j database remnants..."
sudo rm -rf ${NEO4J_DATA_DIR}/data/databases/neo4j
sudo rm -rf ${NEO4J_DATA_DIR}/data/transactions/neo4j

# 9. Load the new database
echo "Restoring the Neo4j database on local machine..."
sudo -u neo4j neo4j-admin database load neo4j --from-path=${LOCAL_BACKUP_DIR} --overwrite-destination=true

# 10. Start Neo4j on both servers
echo "Starting Neo4j on source server..."
ssh ${REMOTE_USER}@${SOURCE_SERVER} "sudo ${NEO4J_SERVICE} start"

echo "Starting Neo4j on local machine..."
sudo ${NEO4J_SERVICE} start

echo "🎉 Neo4j database migration completed and verified successfully!"
echo "Backup file is located at: ${LOCAL_BACKUP_DIR}/${BACKUP_FILE}"