package com.stageserver;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stageserver.service.DbInitializerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.neo4j.repository.config.EnableNeo4jRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;

@Slf4j
@EnableNeo4jRepositories
@EnableScheduling
@SpringBootApplication
public class StageServerApplication implements CommandLineRunner {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private DbInitializerService dbInitializerService;

    public static void main(String[] args) {
        log.info("StageServer Application is starting...");
        SpringApplication.run(StageServerApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("Populating the database with initial system data and preparing it");
        dbInitializerService.loadSupportedMusicGenre();
        dbInitializerService.loadSupportedLanguages();
        dbInitializerService.loadSupportedActRoles();
        dbInitializerService.loadCountries();
        dbInitializerService.loadSupportedOptions();
        dbInitializerService.loadEntertainmentTypes();
        dbInitializerService.addSystemUser();
        dbInitializerService.loadFinePrintData();
        log.info("STAGE SERVER IS UP AND RUNNING");
    }
}
