package com.stageserver.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.lang.Nullable;

@Slf4j
@Configuration
@PropertySource("classpath:application.properties")
public class Constants implements EnvironmentAware {
    @Autowired
    Environment environment;

    public static final int ERROR_RESULT = -1;
    public static final int DEFAULT_EXPIRY_TIME = 15;
    public static final int MAX_RECENTLY_VIEWED = 20;

    @Override
    public void setEnvironment(Environment environment) {
        this.environment= environment;
    }

    public int getPasswordMaxLength(){
        String value = environment.getProperty("stage-server.password.max-length");
        if(value != null) {
            return Integer.parseInt(value);
        }
        return ERROR_RESULT;
    }

    public int getPasswordMinLength(){
        String value = environment.getProperty("stage-server.password.min-length");
        if(value != null) {
            return Integer.parseInt(value);
        }
        return ERROR_RESULT;
    }

    public int getPasswordMinSpecialChars(){
        String value = environment.getProperty("stage-server.password.min-special-chars");
        if(value != null) {
            return Integer.parseInt(value);
        }
        return ERROR_RESULT;
    }

    public int getPasswordMinUpperCaseChars(){
        String value = environment.getProperty("stage-server.password.min-uppercase-chars");
        if(value != null) {
            return Integer.parseInt(value);
        }
        return ERROR_RESULT;
    }

    public int getTokenExpirationTime(){
        String value = environment.getProperty("stage-server.token.expiration-time");
        if(value != null) {
            return Integer.parseInt(value);
        }
        return ERROR_RESULT;
    }

    public boolean getTwoFactorAuthenticationPrompt() {
        String value =  environment.getProperty("stage-server.two-factor-authentication-prompt");
        if(value != null) {
            return Boolean.parseBoolean(value);
        }
        return false;
    }

    public String getFrontEndUrl() {
        return environment.getProperty("stage-server-front-end-url");
    }

    public String getBackEndUrl() {
        return environment.getProperty("stage-server-back-end-url");
    }

    public int getMaxSMSCodeLength() {
        String value = environment.getProperty("stage-server-sms-code-length");
        if(value != null) {
            return Integer.parseInt(value);
        }
        return ERROR_RESULT;
    }
}
