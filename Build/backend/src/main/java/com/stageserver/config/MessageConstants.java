package com.stageserver.config;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;

import java.util.Locale;
import java.util.Map;

@Configuration
public class MessageConstants {


    public static final String SUCCESS = "success";
    public static final String FAILURE = "failure";

    public static final String MSG_KEY_PHONE_ADDED = "PhoneNumberAdded";
    public static final String MSG_KEY_EMAIL_SENT = "EmailSent";
    public static final String MSG_KEY_PHONE_NUMBER_CONFIRMED = "PhoneNumberConfirmed";
    public static final String MSG_KEY_USER_REGISTERED = "UserRegistered";
    public static final String MSG_KEY_TWO_FA_ENABLED = "TwoFaEnabledUser";
    public static final String MSG_KEY_LOGIN_SUCCESS = "UserLoginSuccess";
    public static final String MSG_KEY_SMS_SENT = "TwoFaTokenSent";
    public static final String MSG_KEY_RESET_PASSWORD_SUCCESS = "ResetPasswordSuccess";
    public static final String MSG_KEY_RETRIEVAL_SUCCESS = "SuccessfulRetrieval";
    public static final String MSG_KEY_PROFILE_CREATED = "ProfileCreateSuccess";
    public static final String MSG_KEY_LOCATION_CREATE_SUCCESS = "ActLocationCreateSuccess";
    public static final String MSG_KEY_PROFILE_DATA_UPDATED = "ActProfileDataUpdated";
    public static final String MSG_KEY_ACT_INFO_CREATE_SUCCESS = "ActInfoCreateSuccess";
    public static final String MSG_KEY_DATA_DELETE_SUCCESS = "DataDeleteSuccess";
    public static final String MSG_KEY_DATA_ADD_SUCCESS = "DataAddSuccess";
    public static final String MSG_KEY_FILE_UPLOADED = "ImageFileUploadSuccess";
    public static final String MSG_KEY_FILE_DELETED = "FileDeleteSuccess";
    public static final String MSG_KEY_SCHEDULE_RETRIEVE_SUCCESS = "ScheduleRetrieveSuccess";
    public static final String MSG_KEY_SCHEDULE_CREATE_SUCCESS = "ScheduleCreateSuccess";
    public static final String MSG_KEY_LOCATION_NOT_EXISTS = "NoLocationExistsYet";
    public static final String MSG_KEY_NO_RIDER_DOCS_FOUND = "NoRiderDocumentExists";
    public static final String MSG_KEY_PASSWORD_CONFIRMED = "PasswordConfirmed";
    public static final String MSG_KEY_PASSWORD_NOT_CONFIRMED = "PasswordNotConfirmed";
    public static final String MSG_VIRTUAL_PROFILE_CLAIMED_SUCCESSFULLY = "VirtualProfileClaimedSuccessfully";
    public static final String MSG_VIRTUAL_PROFILE_CLAIMED_FAILED = "VirtualProfileClaimFailed";
    public static final String MSG_KEY_DATA_UPDATE_SUCCESS= "DataUpdateSuccess";
    public static final String MSG_KEY_CONTRACT_ACTION_SUCCESS = "ContractActionSuccess";


    public static final String ERROR_WORKING_HOURS_NOT_EXPECTED = "WorkingHoursNotExpected";
    public static final String ERROR_CONTACT_NOT_PROVIDED = "ContactNotProvided";
    public static final String ERROR_INVALID_PROFILE_TYPE = "InvalidProfileTypeError";
    public static final String ERR_REGION_NOT_SUPPORTED = "RegionNotSupported";
    public static final String ERROR_DATA_ADD_FAIL = "DataAddFailed";
    public static final String ERROR_PROFILE_NAME_EXISTS = "ProfileNameAlreadyExists";
    public static final String ERROR_SCHEDULE_READ_FAILED = "ScheduleReadFailed";
    public static final String ERROR_SCHEDULE_CREATE_FAILED = "ScheduleCreateFailed";
    public static final String ERROR_INVALID_PHONE_NUMBER = "InvalidPhoneNumber";
    public static final String ERROR_INVALID_SMS_CODE = "InvalidSMSCode";
    public static final String ERROR_INVALID_EMAIL_FORMAT = "InvalidEmailFormat";
    public static final String ERROR_PASSWORD_POLICY_VIOLATION = "PasswordPolicyViolation";
    public static final String ERROR_USER_ALREADY_EXISTS = "UserAlreadyExists";
    public static final String ERROR_UNABLE_TO_SEND_EMAIL = "UnableToSendEMail";
    public static final String ERROR_USER_IS_NOT_TWO_FA_ENABLED = "UserNotTwoFaEnabled";
    public static final String ERROR_TWO_FA_FAILED = "TwoFaVerifyFailed";
    public static final String ERROR_INVALID_TOKEN = "InvalidOrExpiredToken";
    public static final String ERROR_FORGOT_PASSWORD_TOKEN_EXISTS = "ForgotPasswordTokenExists";
    public static final String ERROR_VERIFY_EMAIL_TOKEN_EXISTS = "VerifyEmailTokenExists";
    public static final String ERROR_TWO_FA_TOKEN_EXISTS = "TwoFaTokenExists";
    public static final String ERROR_LOCATION_ALREADY_EXISTS = "LocationForActAlreadyExists";
    public static final String ERROR_ACT_INFO_ALREADY_EXISTS = "ActInfoAlreadyExists";
    public static final String ERROR_INVALID_CURRENCY = "InvalidCurrencyError";
    public static final String ERROR_INVALID_PAYMENT_METHOD = "InvalidPaymentMethodError";
    public static final String ERROR_INVALID_ACT_TYPE = "InvalidActTypeError";
    public static final String ERROR_INVALID_MUSIC_GENRE = "InvalidMusicGenreError";
    public static final String ERROR_INVALID_REGION = "InvalidRegionError";
    public static final String ERROR_INVALID_ACT_ROLE = "InvalidActRoleError";
    public static final String ERROR_INVALID_LANGUAGE = "InvalidLanguageError";
    public static final String ERROR_DATA_READ_FAILED = "DataReadFailed";
    public static final String ERROR_DATA_UPDATE_FAILED = "DataUpdateFailed";
    public static final String ERROR_DATA_DELETE_FAILED = "DataDeleteFailed";
    public static final String ERROR_UNABLE_TO_COMPUTE_GEO_COORDINATES = "UnableToComputeGeoCoordinates"; ;
    public static final String ERROR_USER_NOT_AUTHORIZED_TO_UPDATE = "UserNotAuthorizedToUpdate";

    private static MessageSource messageSource;

    @Autowired
    private MessageSource messageSourceInstance;

    @PostConstruct
    public void init() {
        // To initialize the static variable
        messageSource = messageSourceInstance;
    }

    public static Map<String, String> getMessageMap() {
        Locale locale = LocaleContextHolder.getLocale();
        return Map.ofEntries(
                Map.entry(MSG_KEY_CONTRACT_ACTION_SUCCESS, messageSource.getMessage("message-contract-sent-success", null, locale)),
                Map.entry(MSG_KEY_DATA_UPDATE_SUCCESS, messageSource.getMessage("message-data-update-success", null, locale)),
                Map.entry(MSG_VIRTUAL_PROFILE_CLAIMED_FAILED, messageSource.getMessage("message-virtual-profile-claim-failed", null, locale)),
                Map.entry(MSG_VIRTUAL_PROFILE_CLAIMED_SUCCESSFULLY, messageSource.getMessage("message-virtual-profile-claimed-success", null, locale)),
                Map.entry(MSG_KEY_PASSWORD_NOT_CONFIRMED, messageSource.getMessage("message-password-not-confirmed", null, locale)),
                Map.entry(MSG_KEY_PASSWORD_CONFIRMED, messageSource.getMessage("message-password-confirmed", null, locale)),
                Map.entry(MSG_KEY_NO_RIDER_DOCS_FOUND, messageSource.getMessage("message-no-rider-docs-found", null, locale)),
                Map.entry(MSG_KEY_LOCATION_NOT_EXISTS, messageSource.getMessage("message-location-not-exists", null, locale)),
                Map.entry(MSG_KEY_SCHEDULE_CREATE_SUCCESS, messageSource.getMessage("message-schedule-create-success", null, locale)),
                Map.entry(MSG_KEY_SCHEDULE_RETRIEVE_SUCCESS, messageSource.getMessage("message-schedule-read-success", null, locale)),
                Map.entry(MSG_KEY_FILE_DELETED, messageSource.getMessage("message-file-delete-success", null, locale)),
                Map.entry(MSG_KEY_FILE_UPLOADED, messageSource.getMessage("message-image-upload-success", null, locale)),
                Map.entry(MSG_KEY_DATA_ADD_SUCCESS, messageSource.getMessage("message-data-add-success", null, locale)),
                Map.entry(MSG_KEY_DATA_DELETE_SUCCESS, messageSource.getMessage("message-delete-data-success", null, locale)),
                Map.entry(MSG_KEY_ACT_INFO_CREATE_SUCCESS, messageSource.getMessage("message-profile-info-create-success", null, locale)),
                Map.entry(MSG_KEY_PROFILE_DATA_UPDATED, messageSource.getMessage("message-profile-data-updated", null, locale)),
                Map.entry(MSG_KEY_LOCATION_CREATE_SUCCESS, messageSource.getMessage("message-data-location-create-success", null, locale)),
                Map.entry(MSG_KEY_RETRIEVAL_SUCCESS, messageSource.getMessage("message-data-retrieval-success", null, locale)),
                Map.entry(MSG_KEY_PROFILE_CREATED, messageSource.getMessage("message-profile-create-success", null, locale)),
                Map.entry(MSG_KEY_RESET_PASSWORD_SUCCESS, messageSource.getMessage("message-reset-password-success", null, locale)),
                Map.entry(MSG_KEY_PHONE_ADDED, messageSource.getMessage("message-phone-added-success", null, locale)),
                Map.entry(MSG_KEY_PHONE_NUMBER_CONFIRMED, messageSource.getMessage("message-phone-confirmed-success", null, locale)),
                Map.entry(MSG_KEY_USER_REGISTERED, messageSource.getMessage("message-user-registered-success", null, locale)),
                Map.entry(MSG_KEY_TWO_FA_ENABLED, messageSource.getMessage("message-user-twofa-enabled", null, locale)),
                Map.entry(MSG_KEY_LOGIN_SUCCESS, messageSource.getMessage("message-user-login-success", null, locale)),
                Map.entry(MSG_KEY_EMAIL_SENT, messageSource.getMessage("message-email-sent", null, locale)),
                Map.entry(MSG_KEY_SMS_SENT, messageSource.getMessage("message-sms-token-sent", null, locale))
        );
    }

    public static Map<String, String> getErrorMap() {
        Locale locale = LocaleContextHolder.getLocale();
        return Map.ofEntries(
                Map.entry(ERROR_USER_NOT_AUTHORIZED_TO_UPDATE, messageSource.getMessage("error-user-not-authorized-to-update", null, locale)),
                Map.entry(ERROR_UNABLE_TO_COMPUTE_GEO_COORDINATES, messageSource.getMessage("error-unable-to-compute-geo-coordinates", null, locale)),
                Map.entry(ERROR_DATA_UPDATE_FAILED, messageSource.getMessage("error-data-update-failed", null, locale)),
                Map.entry(ERROR_DATA_DELETE_FAILED, messageSource.getMessage("error-data-delete-failed", null, locale)),
                Map.entry(ERROR_DATA_READ_FAILED, messageSource.getMessage("error-data-read-failed", null, locale)),
                Map.entry(ERROR_WORKING_HOURS_NOT_EXPECTED, messageSource.getMessage("error-working-hours-not-expected", null, locale)),
                Map.entry(ERROR_CONTACT_NOT_PROVIDED, messageSource.getMessage("error-contact-not-provided", null, locale)),
                Map.entry(ERROR_INVALID_PROFILE_TYPE, messageSource.getMessage("error-invalid-profile-type", null, locale)),
                Map.entry(ERR_REGION_NOT_SUPPORTED, messageSource.getMessage("error-region-not-supported", null, locale)),
                Map.entry(ERROR_DATA_ADD_FAIL, messageSource.getMessage("error-data-add-fail", null, locale)),
                Map.entry(ERROR_PROFILE_NAME_EXISTS, messageSource.getMessage("error-profile-name-exists", null, locale)),
                Map.entry(ERROR_SCHEDULE_READ_FAILED, messageSource.getMessage("error-schedule-read-failed", null, locale)),
                Map.entry(ERROR_SCHEDULE_CREATE_FAILED, messageSource.getMessage("error-schedule-create-failed", null, locale)),
                Map.entry(ERROR_INVALID_LANGUAGE, messageSource.getMessage("error-invalid-language", null, locale)),
                Map.entry(ERROR_INVALID_ACT_ROLE, messageSource.getMessage("error-invalid-act-role", null, locale)),
                Map.entry(ERROR_INVALID_REGION, messageSource.getMessage("error-invalid-region", null, locale)),
                Map.entry(ERROR_INVALID_ACT_TYPE, messageSource.getMessage("error-invalid-act-type", null, locale)),
                Map.entry(ERROR_INVALID_MUSIC_GENRE, messageSource.getMessage("error-invalid-music-genre", null, locale)),
                Map.entry(ERROR_INVALID_CURRENCY, messageSource.getMessage("error-invalid-currency", null, locale)),
                Map.entry(ERROR_INVALID_PAYMENT_METHOD, messageSource.getMessage("error-invalid-payment-method", null, locale)),
                Map.entry(HttpStatus.SERVICE_UNAVAILABLE.name(), messageSource.getMessage("error-service-unavailable", null, locale)),
                Map.entry(ERROR_TWO_FA_TOKEN_EXISTS, messageSource.getMessage("error-verify-twofa-token-exists", null, locale)),
                Map.entry(ERROR_VERIFY_EMAIL_TOKEN_EXISTS,  messageSource.getMessage("error-verify-email-token-exists", null, locale)),
                Map.entry(ERROR_FORGOT_PASSWORD_TOKEN_EXISTS , messageSource.getMessage("error-forgot-password-token-exists", null, locale)),
                Map.entry(ERROR_INVALID_TOKEN, messageSource.getMessage("error-invalid-or-expired-token", null, locale)),
                Map.entry(ERROR_TWO_FA_FAILED, messageSource.getMessage("error-user-twofa-verify-failed", null, locale)),
                Map.entry(ERROR_USER_IS_NOT_TWO_FA_ENABLED, messageSource.getMessage("error-user-not-twofa-enabled", null, locale)),
                Map.entry(ERROR_UNABLE_TO_SEND_EMAIL, messageSource.getMessage("error-unable-to-send-email", null, locale)),
                Map.entry(ERROR_INVALID_PHONE_NUMBER, messageSource.getMessage("error-invalid-phone-number", null, locale)),
                Map.entry(ERROR_INVALID_SMS_CODE, messageSource.getMessage("error-invalid-sms-code", null, locale)),
                Map.entry(ERROR_INVALID_EMAIL_FORMAT, messageSource.getMessage("error-invalid-email-format", null, locale)),
                Map.entry(ERROR_PASSWORD_POLICY_VIOLATION, messageSource.getMessage("error-password-policy-violation", null, locale)),
                Map.entry(ERROR_USER_ALREADY_EXISTS, messageSource.getMessage("error-user-already-exists", null, locale)),
                Map.entry(ERROR_LOCATION_ALREADY_EXISTS, messageSource.getMessage("error-act-location-already-exists", null, locale)),
                Map.entry(ERROR_ACT_INFO_ALREADY_EXISTS, messageSource.getMessage("error-act-info-already-exists", null, locale)),

                Map.entry(HttpStatus.NOT_IMPLEMENTED.name(), messageSource.getMessage("error-not-implemented", null, locale)),
                Map.entry(HttpStatus.BANDWIDTH_LIMIT_EXCEEDED.name(), messageSource.getMessage("error-bandwidth-exceeded", null, locale)),
                Map.entry(HttpStatus.INTERNAL_SERVER_ERROR.name(), messageSource.getMessage("error-internal-server-error", null, locale)),
                Map.entry(HttpStatus.EXPECTATION_FAILED.name(), messageSource.getMessage("error-expectation-failed", null, locale)),
                Map.entry(HttpStatus.UNAUTHORIZED.name(), messageSource.getMessage("error-unauthorized", null, locale)),
                Map.entry(HttpStatus.BAD_REQUEST.name(), messageSource.getMessage("error-bad-request", null, locale)),
                Map.entry(HttpStatus.NOT_FOUND.name(), messageSource.getMessage("error-not-found", null, locale))
        );
    }

    public static Map<String, String> getExceptionMap() {
        Locale locale = LocaleContextHolder.getLocale();
        return Map.of(
                DisabledException.class.getSimpleName(), messageSource.getMessage("exception-disabled", null, locale),
                BadCredentialsException.class.getSimpleName(), messageSource.getMessage("exception-bad-credentials", null, locale),
                HttpStatus.INTERNAL_SERVER_ERROR.name(), messageSource.getMessage("error-internal-server-error", null, locale)
        );
    }
}