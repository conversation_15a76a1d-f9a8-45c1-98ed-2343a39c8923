package com.stageserver.controller.utils;

import com.stageserver.dto.contracts.ContractDetailsDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Slf4j
@Component
public class ContractListSorter {

    public Page<ContractDetailsDto> sortContractListByTime(Page<ContractDetailsDto> contractPage, Pageable pageable) {
        // Create a new modifiable list from the content
        List<ContractDetailsDto> sortedList = new ArrayList<>(contractPage.getContent());

        // Sort the list by timestamp in descending order
        sortedList.sort(Comparator.comparing(ContractDetailsDto::getTimeStamp).reversed());

        // Create and return a new PageImpl with the sorted list
        return new PageImpl<>(sortedList, pageable, contractPage.getTotalElements());
    }
}
