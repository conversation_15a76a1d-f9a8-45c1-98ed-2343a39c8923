package com.stageserver.controller.utils;

import com.stageserver.dto.profile.EntertainmentTypeDto;
import com.stageserver.dto.supported.MusicGenreDto;
import com.stageserver.dto.supported.SupportedEntertainmentTypesDto;
import com.stageserver.dto.supported.SupportedMusicGenreDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class DataSorter {

    public SupportedEntertainmentTypesDto sortEntertainmentTypes(SupportedEntertainmentTypesDto entertainmentTypes) {
        SupportedEntertainmentTypesDto result = new SupportedEntertainmentTypesDto();
        List<EntertainmentTypeDto> entertainmentTypeList = entertainmentTypes.getEntertainmentTypeList();

        // Define custom priority map
        Map<String, Integer> priorityMap = new HashMap<>();
        priorityMap.put("Live Music", 0);
        priorityMap.put("Kids and Family Entertainment", 1);
        priorityMap.put("DJ's and <PERSON><PERSON>'s", 2);
        priorityMap.put("Comedy and Improv", 3);

        // Separate "Other" to append later
        EntertainmentTypeDto otherDto = null;
        List<EntertainmentTypeDto> filteredList = new ArrayList<>();

        for (EntertainmentTypeDto type : entertainmentTypeList) {
            if ("Other Speciality Acts".equals(type.getName())) {
                otherDto = type;
            } else {
                filteredList.add(type);
            }
        }

        // Sort based on priority, defaulting to Integer.MAX_VALUE for everything else
        filteredList.sort(Comparator.comparingInt(
                t -> priorityMap.getOrDefault(t.getName(), Integer.MAX_VALUE)
        ));

        // Add sorted items back
        if (otherDto != null) {
            filteredList.add(otherDto); // Always push "Other" to end
        }

        result.setEntertainmentTypeList(filteredList);
        return result;
    }

    public SupportedMusicGenreDto sortMusicGenres(SupportedMusicGenreDto musicGenres) {
        SupportedMusicGenreDto result = new SupportedMusicGenreDto();
        List<MusicGenreDto> musicGenreList = musicGenres.getGenreList();

        int otherIndex = -1;
        MusicGenreDto other = null;
        for (int i = 0; i < musicGenreList.size(); i++) {
            MusicGenreDto musicGenre = musicGenreList.get(i);
            if (musicGenre.getName().equals("Other")) {
                other = musicGenreList.get(i);
                otherIndex = i;
                break;
            }
        }

        if (other != null) {
            musicGenreList.remove(otherIndex);
            musicGenreList.add(other);
        }
        result.setGenreList(musicGenreList);
        return result;
    }
}
