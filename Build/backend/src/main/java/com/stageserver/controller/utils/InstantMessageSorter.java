package com.stageserver.controller.utils;

import com.stageserver.dto.IM.InstantMessageDto;
import com.stageserver.dto.contracts.ContractDetailsDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;

@Slf4j
@Component
public class InstantMessageSorter {
    public List<InstantMessageDto> sortInstantMessagesByTime(List<InstantMessageDto> messageList) {
        messageList.sort(Comparator.comparing(InstantMessageDto::getTimestamp).reversed());
        return messageList;
    }
}
