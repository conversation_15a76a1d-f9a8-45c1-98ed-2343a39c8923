package com.stageserver.dto.IM;

import com.stageserver.dto.contracts.ContractDetailsDto;
import com.stageserver.dto.contracts.NegotiateDataDto;
import com.stageserver.model.contract.ContractState;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class ContractContentDto {


    private String contractId;

    private ContractDetailsDto.ContractParty bookingParty;

    private ContractDetailsDto.ContractParty otherParty;

    private List<String> actImgList;

    private List<String> venueImgList;

    private List<String> userImgList;

    private String actProfileName;

    private String venueProfileName;

    private String userName;

    private ContractState contractState;

    private NegotiateDataDto negotiateData;

    private String actionString;
}
