package com.stageserver.dto.IM;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class InstantMessageDto {

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String messageId;

    private String sender;

    private String receiver;

    private MessageContentDto content;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private LocalDateTime timestamp;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private boolean seen;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private boolean dismissed;
}
