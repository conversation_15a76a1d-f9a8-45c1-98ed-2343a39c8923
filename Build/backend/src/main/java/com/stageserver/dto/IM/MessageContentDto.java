package com.stageserver.dto.IM;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stageserver.dto.feedback.FeedbackMsgDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class MessageContentDto {

    public enum MessageType {
        GENERIC_MESSAGE,
        BOOKING_REQUEST,
        FEEDBACK_MESSAGE
    }

    private MessageType messageType;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private ContractContentDto contractContentDto;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private GenericMsgContentDto genericMsgContentDto;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private FeedbackMsgDto feedbackMsgDto;

}
