package com.stageserver.dto.calendar;

import com.stageserver.dto.schedule.ScheduleTimeDto;
import com.stageserver.model.contract.ContractState;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class ContractCalendarViewDto {

    private String contractId;

    private ContractState contractState;

    private ScheduleTimeDto scheduleTime;

    private String actProfileId;

    private String venueProfileId;

}
