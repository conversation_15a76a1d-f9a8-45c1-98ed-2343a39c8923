package com.stageserver.dto.calendar;

import com.stageserver.dto.schedule.ScheduleTimeDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class EventCalendarViewDto {

    private String eventId;

    private String eventName;

    private boolean isOwnEvent;

    private ScheduleTimeDto scheduleTime;

    private List<String> eventImageUrls;
}
