package com.stageserver.dto.calendar;

import com.stageserver.dto.schedule.ScheduleTimeDto;
import com.stageserver.model.common.ProfileType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class ProfileCalendarViewDto {

    private String profileId;

    private String profileName;

    private ProfileType profileType;

    private List<String> profileImageUrls;

//    private List<ScheduleTimeDto> scheduleTime;

    private List<EventCalendarViewDto> eventList;

    private boolean isOwnProfile;
}
