package com.stageserver.dto.calendar;

import com.stageserver.dto.event.SpecialEventDto;
import com.stageserver.dto.profile.ProfileMinimizedViewDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class UserCalendarViewDto {

    private List<ProfileCalendarViewDto> profileList;

    private List<ProfileCalendarViewDto> favoriteProfileList;

    private List<ContractCalendarViewDto> contractList;

    private List<SpecialEventDto> specialEventList;

}