package com.stageserver.dto.common;

import com.stageserver.config.MessageConstants;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Component
public class APIResponseDto<T> {

    private String status;

    private String message;

    private T data;


    public static <T> ResponseEntity<APIResponseDto<T>> ok(T data) {
        APIResponseDto<T> response = APIResponseDto.<T>builder()
                .status(MessageConstants.SUCCESS)
                .data(data)
                .build();
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    public static <T> ResponseEntity<APIResponseDto<T>> ok(T data, String key) {
         APIResponseDto<T> response = APIResponseDto.<T>builder()
                .status(MessageConstants.SUCCESS)
                .message(MessageConstants.getMessageMap().get(key))
                .data(data)
                .build();
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }


    public static <T> ResponseEntity<APIResponseDto<T>> ok_pageable(T data, int page, int size, String key) {
        APIResponseDto<T> response = APIResponseDto.<T>builder()
                .status(MessageConstants.SUCCESS)
                .message(MessageConstants.getMessageMap().get(key))
                .data(data)
                .build();
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    public static <T> ResponseEntity<APIResponseDto<T>> ok(T data, String key, HttpStatus status) {
        APIResponseDto<T> response = APIResponseDto.<T>builder()
                .status(MessageConstants.SUCCESS)
                .message(MessageConstants.getMessageMap().get(key))
                .data(data)
                .build();
        return ResponseEntity.status(status).body(response);
    }

    public static <T> ResponseEntity<APIResponseDto<T>> error(HttpStatus status) {
        APIResponseDto<T> response = APIResponseDto.<T>builder()
                .status(MessageConstants.FAILURE)
                .message(MessageConstants.getErrorMap().get(status.name()))
                .build();
        return ResponseEntity.status(status).body(response);
    }

    public static <T> ResponseEntity<APIResponseDto<T>> error(HttpStatus status, String customizedMessage) {

        APIResponseDto<T> response = APIResponseDto.<T>builder()
                .status(MessageConstants.FAILURE)
                .message(MessageConstants.getErrorMap().get(customizedMessage))
                .build();
        return ResponseEntity.status(status).body(response);
    }

    public static <T> ResponseEntity<APIResponseDto<T>> exception(HttpStatus status, String exceptionName) {
        APIResponseDto<T> response = APIResponseDto.<T>builder()
                .status(MessageConstants.FAILURE)
                .message(MessageConstants.getExceptionMap().get(exceptionName))
                .build();
        return ResponseEntity.status(status).body(response);
    }
}



