package com.stageserver.dto.contracts;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class ActRiderNotesDto {

    private boolean riderAccepted;

    private boolean riderRejected;

    private boolean acceptedWithConditions;

    private String riderRejectionReason;

    private String acceptanceConditions;

    private String riderUrl;

}
