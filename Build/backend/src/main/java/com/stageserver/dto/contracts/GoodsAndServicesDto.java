package com.stageserver.dto.contracts;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.ZonedDateTime;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class GoodsAndServicesDto {

    public enum ActOrVenue {
        ACT,
        VENUE
    }

    public enum EquipmentProvider {
        PURCHASER,
        PERFORMER,
        NOT_APPLICABLE
    }

    public enum PaymentType {
        FLAT_RATE,
        DOOR_GIG,
        EXPOSURE_GIG
    }

    private String performersRole;

    private ZonedDateTime startDate;

    private double durationInHours;

    private ZonedDateTime loadingTime;

    private PaymentType paymentType;

    private String flatRateCurrency;

    private double flatRateAmount;

    private double flatRatePercentage;

    private double doorGigEntryFee;

    private int venueCapacity;

    private ActOrVenue doorManagedBy;

    private ActOrVenue doorGigPaidBy;

    private ActOrVenue payableTo;

    private double exposureGigFee;

    private String exposureGigCurrency;

    private double maximumPercentage;

    private int guaranteedMaximum;

    private EquipmentProvider equipmentProvider;

    private boolean mealsProvidedByPurchaser;

    private  boolean accommodationProvided;

    private boolean merchandiseSalesAllowed;

    private boolean performerMemberOfUnion;

    private String message;


}
