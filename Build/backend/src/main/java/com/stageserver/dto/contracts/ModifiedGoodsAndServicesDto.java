package com.stageserver.dto.contracts;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class ModifiedGoodsAndServicesDto {

    private boolean performersRoleModified;

    private boolean startDateModified;

    private boolean durationInHoursModified;

    private boolean loadingTimeModified;

    private boolean paymentTypeModified;

    private boolean flatRateCurrencyModified;

    private boolean flatRateAmountModified;

    private boolean flatRatePercentageModified;

    private boolean doorGigEntryFeeModified;

    private boolean venueCapacityModified;

    private boolean doorManagedByModified;

    private boolean doorGigPaidByModified;

    private boolean payableToModified;

    private boolean exposureGigFeeModified;

    private boolean exposureGigCurrencyModified;

    private boolean maximumPercentageModified;

    private boolean guaranteedMaximumModified;

    private boolean equipmentProviderModified;

    private boolean mealsProvidedByPurchaserModified;

    private  boolean accommodationProvidedModified;

    private boolean merchandiseSalesAllowedModified;

    private boolean performerMemberOfUnionModified;

    private boolean messageModified;
}
