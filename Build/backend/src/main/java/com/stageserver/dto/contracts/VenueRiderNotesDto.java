package com.stageserver.dto.contracts;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class VenueRiderNotesDto {

    private boolean declined;

    private String declineWithReason;

    private boolean accepted;

    private boolean acceptedWithConditions;

    private String acceptanceConditions;

    private String riderUrl;
}
