package com.stageserver.dto.event;

import com.stageserver.dto.contracts.ContractDetailsDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ContractInfoDetailsDto {

    private ContractDetailsDto primeContractDetails;

    private List<ContractDetailsDto> actContractDetailsList;
}
