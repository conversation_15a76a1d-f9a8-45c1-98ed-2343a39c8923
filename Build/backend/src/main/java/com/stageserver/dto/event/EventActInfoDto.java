package com.stageserver.dto.event;

import com.stageserver.dto.profile.ProfileDetailedViewDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EventActInfoDto {

    private ProfileDetailedViewDto primaryActProfileDetails;

    private List<ProfileDetailedViewDto> actProfileDetailsList;

    private String primaryContractId;

    private List<String> actContractIds;

}
