package com.stageserver.dto.event;

import com.stageserver.dto.location.LocationDto;
import com.stageserver.dto.schedule.ScheduleTimeDto;
import com.stageserver.model.common.EventStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EventDto {

        private String eventName;

        private EventStatus eventStatus;

        private String eventId;

        private EventStatus status;

        private ScheduleTimeDto scheduleTime;

        private EventMainInfoDto eventMainInfo;

        private EventMediaInfoDto eventMediaInfo;

        private String venueProfileId;

        private String primeContractId;

        private String venueContractId;

        private List<String> actContractIdList;

        private List<ActProfileInfoDto> actProfileInfoList;

        private String venueName;

        private String eventOwner;

        private boolean editable;

        private LocationDto venueLocation;

        private List<String> venueImageUrls;

        private List<EventDto> similarEvents;

        private String actName;

        private List<String> actImageUrls;
}
