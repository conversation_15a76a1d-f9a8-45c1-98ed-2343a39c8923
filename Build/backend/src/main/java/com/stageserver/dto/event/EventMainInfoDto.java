package com.stageserver.dto.event;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stageserver.dto.schedule.ScheduleTimeDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EventMainInfoDto {

    private List<String> tags;

    private String eventName;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private ScheduleTimeDto scheduleTime;

    private String aboutEvent;

    private List<String> socialMediaUrls;
}
