package com.stageserver.dto.event;

import com.stageserver.dto.profile.ProfileDetailedViewDto;
import com.stageserver.model.contract.ContractState;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EventVenueInfoDto {

    private ProfileDetailedViewDto venueProfileDetails;

    private String contractId;

    private ContractState contractState;
}
