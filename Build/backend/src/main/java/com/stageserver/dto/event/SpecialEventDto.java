package com.stageserver.dto.event;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stageserver.dto.schedule.ScheduleTimeDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SpecialEventDto {

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String specialEventId;

    private String aboutTheEvent;

    private String eventName;

    private ScheduleTimeDto scheduleTime;

    private List<String> eventImageUrls;

    private String ownerProfileId;

    private List<String> ownerImageUrls;

    private String ownerName;
}
