package com.stageserver.dto.feedback;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class FeedbackMsgDto {

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private boolean isUserFeedback;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String feedbackId;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String providerProfileId;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String receiverProfileId;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String otherPartyName;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String providerName;

    private double professionalismValue;

    private double entertainmentValue;

    private double drawAsExpectedValue;

    private String publicMessage;

    private String privateMessage;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String actionString;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private List<String> providerImageUrls;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private List<String> receiverImageUrls;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String contractId;
}
