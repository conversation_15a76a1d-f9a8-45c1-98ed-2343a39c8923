package com.stageserver.dto.feedback;

import com.stageserver.dto.profile.ProfileRatingDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class FeedbackResultsDto {

    private ProfileRatingDto actRating;

    private List<FeedbackMsgDto> receivedFeedbacks;

    private List<FeedbackMsgDto> providedFeedbacks;
}
