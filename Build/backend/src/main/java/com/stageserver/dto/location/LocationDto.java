package com.stageserver.dto.location;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class LocationDto {

    @NotBlank
    private String country;

    @NotBlank
    private String state;

    @NotBlank
    private String city;

    @NotBlank
    private String streetAddress;

    @NotBlank
    private String zipCode;

    private boolean canTravelLongDistance;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private double latitude;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private double longitude;

}
