package com.stageserver.dto.login;

import com.stageserver.dto.location.LocationDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;


@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class UserDto {

    private String email;

    private String firstName;

    private String lastName;

    private boolean isEnabled;

    private List<String> distributionList;

    private List<String> favouriteActProfiles;

    private List<BlockedProfileInfoDto> blockedProfilesInfoList;

    private LocationDto location;

    private String phoneNumber;

    private String firstAlternativePhoneNumber;

    private String secondAlternativePhoneNumber;

    private boolean socialLoginUser;

}
