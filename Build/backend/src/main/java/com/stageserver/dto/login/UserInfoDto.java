package com.stageserver.dto.login;

import com.stageserver.dto.location.LocationDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class UserInfoDto {

    private String firstName;

    private String lastName;

    private LocationDto location;

    private String phoneNumber;

    private String firstAlternativePhoneNumber;

    private String secondAlternativePhoneNumber;

}
