package com.stageserver.dto.mapper;

import com.stageserver.dto.contracts.ActRiderChangesDto;
import com.stageserver.model.contract.ActRiderChanges;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Setter
@Component
public class ActRiderChangesDtoMapper {
    private final ModelMapper modelMapper = new ModelMapper();

    public ActRiderChangesDto toActRiderChangesDto(ActRiderChanges contact) {
        return modelMapper.map(contact, ActRiderChangesDto.class);
    }

    public ActRiderChanges toActRiderChanges(ActRiderChangesDto dto) {
        return modelMapper.map(dto, ActRiderChanges.class);
    }
}

