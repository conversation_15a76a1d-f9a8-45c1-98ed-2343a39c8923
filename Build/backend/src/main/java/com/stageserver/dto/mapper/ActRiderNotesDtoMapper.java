package com.stageserver.dto.mapper;

import com.stageserver.dto.contracts.ActRiderNotesDto;
import com.stageserver.model.contract.ActRiderNotes;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Setter
@Component
public class ActRiderNotesDtoMapper {
    private final ModelMapper modelMapper = new ModelMapper();

    public ActRiderNotesDto toActRiderNotesDto(ActRiderNotes contact) {
        return modelMapper.map(contact, ActRiderNotesDto.class);
    }

    public ActRiderNotes toActRiderNotes(ActRiderNotesDto dto) {
        return modelMapper.map(dto, ActRiderNotes.class);
    }
}
