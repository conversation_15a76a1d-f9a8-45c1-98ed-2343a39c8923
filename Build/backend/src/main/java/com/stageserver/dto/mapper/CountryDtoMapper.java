package com.stageserver.dto.mapper;

import com.stageserver.dto.location.CountryDto;
import com.stageserver.model.location.Country;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Getter
@Setter
@Component
public class CountryDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public Country toCountry(CountryDto dto) {
        return modelMapper.map(dto, Country.class);
    }

    public CountryDto toCountryDto(Country country) {
        return modelMapper.map(country, CountryDto.class);
    }

    public List<CountryDto> toCountryDtoList(List<Country> list) {
        List<CountryDto> countryDtoList = new ArrayList<>();
        for(Country country : list) {
            countryDtoList.add(toCountryDto(country));
        }
        return countryDtoList;
    }
}
