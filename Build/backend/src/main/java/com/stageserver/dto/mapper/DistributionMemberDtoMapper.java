package com.stageserver.dto.mapper;

import com.stageserver.dto.distribution.DistributionMemberDto;
import com.stageserver.model.distribution.DistributionMember;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Getter
@Setter
@Component
public class DistributionMemberDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public DistributionMember toDistributionMember(DistributionMemberDto dto) {
        return modelMapper.map(dto, DistributionMember.class);
    }

    public DistributionMemberDto toDistributionMemberDto(DistributionMember skills) {
        return modelMapper.map(skills, DistributionMemberDto.class);
    }

    public List<DistributionMemberDto> toDistributionMemberDtoList(List<DistributionMember> scheduleList) {
        List<DistributionMemberDto> dtoList = new ArrayList<>();
        if (!scheduleList.isEmpty()) {
            scheduleList.forEach(schedule -> {
                dtoList.add(modelMapper.map(schedule, DistributionMemberDto.class));
            });
        }
        return dtoList;
    }
}
