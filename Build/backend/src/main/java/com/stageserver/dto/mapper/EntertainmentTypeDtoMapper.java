package com.stageserver.dto.mapper;

import com.stageserver.dto.profile.EntertainmentTypeDto;
import com.stageserver.dto.profile.EntertainmentTypeMemberDto;
import com.stageserver.model.profile.EntertainmentType;
import com.stageserver.model.profile.EntertainmentTypeMember;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Getter
@Setter
@Component
public class EntertainmentTypeDtoMapper {
    private final ModelMapper modelMapper = new ModelMapper();

    public EntertainmentTypeDto toEntertainmentTypeDto(EntertainmentType type) {
        EntertainmentTypeDto dto = new EntertainmentTypeDto();
        dto.setName(type.getLocalizedName());
        dto.setIconUrl(type.getIconUrl());
        dto.setMembers(new ArrayList<>());
        for( EntertainmentTypeMember member : type.getMembers() ){
            EntertainmentTypeMemberDto memberDto = new EntertainmentTypeMemberDto();
            memberDto.setName(member.getLocalizedName());
            dto.getMembers().add(memberDto);
        }
        return dto;
    }

    public List<EntertainmentTypeDto> toEntertainmentTypeDtoList(List<EntertainmentType> types) {
        List<EntertainmentTypeDto> dtoList = new ArrayList<>();

        for( EntertainmentType type : types){
            EntertainmentTypeDto dto = new EntertainmentTypeDto();
            dto.setName(type.getLocalizedName());
            dto.setIconUrl(type.getIconUrl());
            dto.setMembers(new ArrayList<>());
            for( EntertainmentTypeMember member : type.getMembers() ){
                EntertainmentTypeMemberDto memberDto = new EntertainmentTypeMemberDto();
                memberDto.setName(member.getLocalizedName());
                dto.getMembers().add(memberDto);
            }
            dtoList.add(dto);
        }
        return dtoList;
    }
}
