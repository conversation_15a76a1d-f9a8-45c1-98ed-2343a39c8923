package com.stageserver.dto.mapper;

import com.stageserver.dto.event.EventDto;
import com.stageserver.model.event.Event;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Getter
@Setter
@Component
public class EventDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public Event toEvent(EventDto dto) {
        return modelMapper.map(dto, Event.class);
    }

    public EventDto toEventDto(Event request) {
        return modelMapper.map(request, EventDto.class);

    }

    public List<EventDto> toEventDtoList(List<Event> list) {
        List<EventDto> dtoList = new ArrayList<>();
        for(Event event : list) {
            dtoList.add(toEventDto(event));
        }
        return dtoList;
    }
}
