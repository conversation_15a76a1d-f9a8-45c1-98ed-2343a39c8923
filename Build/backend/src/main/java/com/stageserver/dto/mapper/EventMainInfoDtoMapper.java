package com.stageserver.dto.mapper;

import com.stageserver.dto.event.EventMainInfoDto;
import com.stageserver.model.event.EventMainInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Setter
@Component
public class EventMainInfoDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public EventMainInfo toEventMainInfo(EventMainInfoDto dto) {
        return modelMapper.map(dto, EventMainInfo.class);
    }

    public EventMainInfoDto toEventMainInfoDto(EventMainInfo request) {
        return modelMapper.map(request, EventMainInfoDto.class);

    }
}
