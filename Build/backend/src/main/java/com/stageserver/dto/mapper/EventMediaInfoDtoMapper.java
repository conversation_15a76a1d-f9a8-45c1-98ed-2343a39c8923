package com.stageserver.dto.mapper;

import com.stageserver.dto.event.EventMediaInfoDto;
import com.stageserver.model.event.EventMediaInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Setter
@Component
public class EventMediaInfoDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();
    public EventMediaInfo toEventMediaInfo(EventMediaInfoDto dto) {
        return modelMapper.map(dto, EventMediaInfo.class);
    }

    public EventMediaInfoDto toEventMediaInfoDto(EventMediaInfo request) {
        return modelMapper.map(request, EventMediaInfoDto.class);

    }
}
