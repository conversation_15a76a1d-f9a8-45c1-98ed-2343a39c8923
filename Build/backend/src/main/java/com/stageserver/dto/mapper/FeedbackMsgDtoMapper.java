package com.stageserver.dto.mapper;

import com.stageserver.dto.feedback.FeedbackMsgDto;
import com.stageserver.dto.schedule.ScheduleTimeDto;
import com.stageserver.model.feedback.FeedbackMsg;
import com.stageserver.model.schedule.ScheduleTime;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Getter
@Setter
@Component
public class FeedbackMsgDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public FeedbackMsg toFeedbackMsg(FeedbackMsgDto dto) {
        return modelMapper.map(dto, FeedbackMsg.class);
    }

    public FeedbackMsgDto toFeedbackMsgDto(FeedbackMsg request) {
        return modelMapper.map(request, FeedbackMsgDto.class);

    }

    public List<FeedbackMsgDto> toFeedbackMsgDtoList(List<FeedbackMsg> feedbackList) {
        List<FeedbackMsgDto> dtoList = new ArrayList<>();
        if (!feedbackList.isEmpty()) {
            feedbackList.forEach(feedbackMsg -> {
                dtoList.add(modelMapper.map(feedbackMsg, FeedbackMsgDto.class));
            });
        }
        return dtoList;
    }

}
