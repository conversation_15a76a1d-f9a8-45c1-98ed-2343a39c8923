package com.stageserver.dto.mapper;

import com.stageserver.dto.common.FinePrintDataDto;
import com.stageserver.dto.event.EventMainInfoDto;
import com.stageserver.model.common.FinePrintData;
import com.stageserver.model.event.EventMainInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Setter
@Component
public class FinePrintDataDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public FinePrintData toFinePrintData(FinePrintDataDto dto) {
        return modelMapper.map(dto, FinePrintData.class);
    }

    public FinePrintDataDto toFinePrintDataDto(FinePrintData request) {
        return modelMapper.map(request, FinePrintDataDto.class);

    }
}
