package com.stageserver.dto.mapper;

import com.stageserver.dto.contracts.GoodsAndServicesDto;
import com.stageserver.model.contract.GoodsAndServices;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Setter
@Component
public class GoodsAndServicesDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public GoodsAndServices toGoodsAndServices(GoodsAndServicesDto dto) {
        return modelMapper.map(dto, GoodsAndServices.class);
    }

    public GoodsAndServicesDto toGoodsAndServicesDto(GoodsAndServices request) {
        return modelMapper.map(request, GoodsAndServicesDto.class);
    }

}
