package com.stageserver.dto.mapper;

import com.stageserver.dto.contracts.GoodsAndServicesMessageDto;
import com.stageserver.model.contract.GoodsAndServicesMessage;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Getter
@Setter
@Component
public class GoodsAndServicesMessageDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public GoodsAndServicesMessageDto toGoodsAndServicesMessageDto(GoodsAndServicesMessage request) {
        return modelMapper.map(request, GoodsAndServicesMessageDto.class);
    }

    public GoodsAndServicesMessage toGoodsAndServicesMessage(GoodsAndServicesMessageDto dto) {
        return modelMapper.map(dto, GoodsAndServicesMessage.class);
    }

    public List<GoodsAndServicesMessageDto> toGoodsAndServicesMessageDtoList(List<GoodsAndServicesMessage> goodsAndServicesMessages) {
        return goodsAndServicesMessages.stream().map(this::toGoodsAndServicesMessageDto).toList();
    }
}
