package com.stageserver.dto.mapper;

import com.stageserver.dto.IM.InstantMessageDto;
import com.stageserver.model.IM.InstantMessage;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Getter
@Setter
@Component
public class InstantMessageDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();
    private MessageContentDtoMapper contentMapper = new MessageContentDtoMapper();

    public InstantMessage toInstantMessage(InstantMessageDto dto) {
        InstantMessage message = modelMapper.map(dto, InstantMessage.class);
        if (dto.getContent() != null) {
            message.setContent(contentMapper.toMessageContent(dto.getContent()));
        }
        return message;
    }

    public InstantMessageDto toInstantMessageDto(InstantMessage message) {
        InstantMessageDto dto = modelMapper.map(message, InstantMessageDto.class);
        if (message.getContent() != null) {
            dto.setContent(contentMapper.toMessageContentDto(message.getContent()));
        }
        return dto;
    }

    public List<InstantMessageDto> toInstantMessageDtoList(List<InstantMessage> messages) {
        return messages.stream()
                .map(this::toInstantMessageDto)
                .collect(Collectors.toList());
    }
}
