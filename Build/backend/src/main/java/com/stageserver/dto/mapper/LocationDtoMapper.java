package com.stageserver.dto.mapper;

import com.stageserver.dto.location.LocationDto;
import com.stageserver.model.location.Location;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Setter
@Component
public class LocationDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public Location toLocation(LocationDto dto) {
        return modelMapper.map(dto, Location.class);
    }

    public LocationDto toLocationDto(Location request) {
        return modelMapper.map(request, LocationDto.class);

    }
}
