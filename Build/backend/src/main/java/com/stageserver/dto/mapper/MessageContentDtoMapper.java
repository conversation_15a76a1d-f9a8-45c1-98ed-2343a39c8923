package com.stageserver.dto.mapper;


import com.stageserver.dto.IM.MessageContentDto;
import com.stageserver.model.IM.MessageContent;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Setter
@Component
public class MessageContentDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public MessageContent toMessageContent(MessageContentDto dto) {
        return modelMapper.map(dto, MessageContent.class);
    }

    public MessageContentDto toMessageContentDto(MessageContent content) {
        return modelMapper.map(content, MessageContentDto.class);
    }
}

