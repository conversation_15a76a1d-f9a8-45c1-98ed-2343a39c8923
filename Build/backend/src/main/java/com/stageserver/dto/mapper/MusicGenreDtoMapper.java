package com.stageserver.dto.mapper;

import com.stageserver.dto.supported.MusicGenreDto;
import com.stageserver.model.supported.MusicGenre;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Getter
@Setter
@Component
public class MusicGenreDtoMapper {
    private final ModelMapper modelMapper = new ModelMapper();

    public MusicGenreDto toMusicGenreDto(MusicGenre genre) {
        return modelMapper.map(genre, MusicGenreDto.class);
    }

    public List<MusicGenreDto> toMusicGenreDtoList(List<MusicGenre> genres) {
        List<MusicGenreDto> dtoList = new ArrayList<>();
        if (!genres.isEmpty()) {
            genres.forEach(genre -> {
                dtoList.add(modelMapper.map(genre, MusicGenreDto.class));
            });
        }
        return dtoList;
    }
}
