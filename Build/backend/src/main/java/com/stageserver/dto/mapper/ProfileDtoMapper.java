package com.stageserver.dto.mapper;

import com.stageserver.dto.profile.ProfileDto;
import com.stageserver.model.profile.Profile;
import lombok.Getter;
import lombok.Setter;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Component
public class ProfileDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public Profile toProfile(ProfileDto dto) {
        return modelMapper.map(dto, Profile.class);
    }

    public ProfileDto toActProfileDto(Profile profile) {
       return modelMapper.map(profile, ProfileDto.class);
    }

    public Page<ProfileDto> toProfileDtoList(Page<Profile> profiles, int page, int size) {
        List<ProfileDto> dtoList = new ArrayList<>();
        if (!profiles.isEmpty()) {
            profiles.forEach(profile -> {
                dtoList.add(modelMapper.map(profile, ProfileDto.class));
            });
        }
        Pageable pageable = PageRequest.ofSize(size).withPage(page);
        return new PageImpl<ProfileDto>(dtoList, pageable, dtoList.size());
    }
}
