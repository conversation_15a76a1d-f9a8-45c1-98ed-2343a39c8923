package com.stageserver.dto.mapper;

import com.stageserver.dto.profile.ProfileInfoDto;
import com.stageserver.model.profile.ProfileInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Setter
@Component
public class ProfileInfoDtoMapper {

    private ModelMapper modelMapper = new ModelMapper();

    public ProfileInfo toActInfo(ProfileInfoDto dto) {
        return modelMapper.map(dto, ProfileInfo.class);
    }

    public ProfileInfoDto toActInfoDto(ProfileInfo request) {
        return modelMapper.map(request, ProfileInfoDto.class);

    }
}

