package com.stageserver.dto.mapper;

import com.stageserver.dto.profile.ProfileMediaDto;
import com.stageserver.model.profile.ProfileMedia;
import lombok.Getter;
import lombok.Setter;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
public class ProfileMediaDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public ProfileMedia toProfileMedia(ProfileMediaDto dto) {
        return modelMapper.map(dto, ProfileMedia.class);
    }

    public ProfileMediaDto toProfileMediaDto(ProfileMedia skills) {
        return modelMapper.map(skills, ProfileMediaDto.class);
    }
}
