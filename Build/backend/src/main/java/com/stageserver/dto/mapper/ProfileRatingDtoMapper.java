package com.stageserver.dto.mapper;

import com.stageserver.dto.profile.ProfileRatingDto;
import com.stageserver.model.profile.ProfileRating;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Setter
@Component
public class ProfileRatingDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public ProfileRating toActFeedback(ProfileRatingDto dto) {
        return modelMapper.map(dto, ProfileRating.class);
    }

    public ProfileRatingDto toProfileRatingDto(ProfileRating request) {
        return modelMapper.map(request, ProfileRatingDto.class);

    }
}
