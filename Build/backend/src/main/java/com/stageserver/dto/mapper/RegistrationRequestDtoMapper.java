package com.stageserver.dto.mapper;

import com.stageserver.dto.login.RegistrationRequestDto;
import com.stageserver.model.login.RegistrationRequest;
import lombok.Getter;
import lombok.Setter;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Component
@Setter
@Getter
public class RegistrationRequestDtoMapper {

    // TODO: Auto wiring does not work
    private ModelMapper modelMapper = new ModelMapper();

    public RegistrationRequest toRegistrationRequest(RegistrationRequestDto dto) {
        RegistrationRequest registrationRequest =  modelMapper.map(dto, RegistrationRequest.class);
        registrationRequest.setRole("USER");
        return  registrationRequest;
    }

    public RegistrationRequestDto toRegistrationRequestDto(RegistrationRequest request) {
        return modelMapper.map(request, RegistrationRequestDto.class);
    }
}
