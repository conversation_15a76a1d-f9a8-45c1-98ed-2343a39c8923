package com.stageserver.dto.mapper;

import com.stageserver.dto.media.RiderDetailsDto;
import com.stageserver.model.profile.RiderDetails;
import lombok.Getter;
import lombok.Setter;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Component
public class RiderDetailsDtoMapper {
    private final ModelMapper modelMapper = new ModelMapper();

    public RiderDetailsDto toRiderDetailsDto(RiderDetails rider) {
        return modelMapper.map(rider, RiderDetailsDto.class);
    }

    public List<RiderDetailsDto> toRiderDetailsDtoList(List<RiderDetails> riders) {
        List<RiderDetailsDto> dtoList = new ArrayList<>();
        if (!riders.isEmpty()) {
            riders.forEach(rider -> {
                dtoList.add(modelMapper.map(rider, RiderDetailsDto.class));
            });
        }
        return dtoList;
    }
}
