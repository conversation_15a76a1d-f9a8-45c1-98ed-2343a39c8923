package com.stageserver.dto.mapper;

import com.stageserver.dto.schedule.ScheduleTimeDto;
import com.stageserver.model.schedule.ScheduleTime;
import lombok.Getter;
import lombok.Setter;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Component
public class ScheduleTimeDtoMapper {
    private final ModelMapper modelMapper = new ModelMapper();

    public ScheduleTime toScheduleTime(ScheduleTimeDto dto) {
        return modelMapper.map(dto, ScheduleTime.class);
    }

    public ScheduleTimeDto toScheduleTimeDto(ScheduleTime schedule) {
        return modelMapper.map(schedule, ScheduleTimeDto.class);
    }

    public List<ScheduleTimeDto> toScheduleTimeDtoList(List<ScheduleTime> scheduleList) {
        List<ScheduleTimeDto> dtoList = new ArrayList<>();
        if (!scheduleList.isEmpty()) {
            scheduleList.forEach(schedule -> {
                dtoList.add(modelMapper.map(schedule, ScheduleTimeDto.class));
            });
        }
        return dtoList;
    }

}
