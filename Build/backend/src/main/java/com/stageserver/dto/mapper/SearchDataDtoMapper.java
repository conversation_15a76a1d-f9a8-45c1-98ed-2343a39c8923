package com.stageserver.dto.mapper;

import com.stageserver.dto.search.SearchDataDto;
import com.stageserver.model.search.SearchData;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@Component
public class SearchDataDtoMapper {
    private final ModelMapper modelMapper = new ModelMapper();

    public SearchDataDto toSearchDataDto(SearchData request) {
        return modelMapper.map(request, SearchDataDto.class);
    }

    public SearchData toSearchData(SearchDataDto dto) {
        return modelMapper.map(dto, SearchData.class);
    }

    public List<SearchDataDto> toSearchDataDtoList(List<SearchData> dataList) {
        List<SearchDataDto> dtoList = new ArrayList<>();
        if (!dataList.isEmpty()) {
            dataList.forEach(data -> {
                dtoList.add(modelMapper.map(data, SearchDataDto.class));
            });
        }
        return dtoList;
    }
}
