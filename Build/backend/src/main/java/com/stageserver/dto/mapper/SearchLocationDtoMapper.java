package com.stageserver.dto.mapper;

import com.stageserver.dto.search.SearchLocationDto;
import com.stageserver.model.location.SearchLocation;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Setter
@Getter
@NoArgsConstructor
@Component
public class SearchLocationDtoMapper {

    private ModelMapper modelMapper = new ModelMapper();

    public SearchLocation toSearchLocation(SearchLocationDto dto) {
        return modelMapper.map(dto, SearchLocation.class);
    }

    public SearchLocationDto toSearchLocationDto(SearchLocation request) {
        return modelMapper.map(request, SearchLocationDto.class);

    }
}
