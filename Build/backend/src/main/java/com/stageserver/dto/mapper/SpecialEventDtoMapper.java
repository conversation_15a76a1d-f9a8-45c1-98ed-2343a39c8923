package com.stageserver.dto.mapper;

import com.stageserver.dto.event.SpecialEventDto;
import com.stageserver.model.event.SpecialEvent;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Getter
@Setter
@Component
public class SpecialEventDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public SpecialEvent toSpecialEvent(SpecialEventDto dto) {
        return modelMapper.map(dto, SpecialEvent.class);
    }

    public SpecialEventDto toSpecialEventDto(SpecialEvent request) {
        return modelMapper.map(request, SpecialEventDto.class);
    }

    public List<SpecialEventDto> toSpecialEventDtoList(List<SpecialEvent> list) {
        List<SpecialEventDto> dtoList = new ArrayList<>();
        for(SpecialEvent event : list) {
            dtoList.add(toSpecialEventDto(event));
        }
        return dtoList;
    }
}
