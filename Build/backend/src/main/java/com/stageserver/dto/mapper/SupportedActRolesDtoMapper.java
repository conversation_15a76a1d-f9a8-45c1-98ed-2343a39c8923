package com.stageserver.dto.mapper;

import com.stageserver.dto.supported.SupportedActRolesDto;
import com.stageserver.model.supported.SupportedActRoles;
import lombok.Getter;
import lombok.Setter;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
public class SupportedActRolesDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public SupportedActRoles toSupportedActRoles(SupportedActRolesDto dto) {
        return modelMapper.map(dto, SupportedActRoles.class);
    }

    public SupportedActRolesDto toSupportedActRolesDto(SupportedActRoles request) {
        SupportedActRolesDto destinationDto = new SupportedActRolesDto();
        destinationDto.setRoles(request.getRoles());
        return destinationDto;
    }
}
