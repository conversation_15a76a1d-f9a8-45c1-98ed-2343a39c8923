package com.stageserver.dto.mapper;

import com.stageserver.dto.supported.SupportedEntertainmentTypesDto;
import com.stageserver.model.supported.SupportedEntertainmentTypes;
import lombok.Getter;
import lombok.Setter;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
public class SupportedEntertainmentTypesDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public SupportedEntertainmentTypes toSupportedEntertainmentTypes(SupportedEntertainmentTypesDto dto) {
        return modelMapper.map(dto, SupportedEntertainmentTypes.class);
    }

    public SupportedEntertainmentTypesDto toSupportedEntertainmentTypesDto(SupportedEntertainmentTypes request) {
        SupportedEntertainmentTypesDto dto = new SupportedEntertainmentTypesDto();
        EntertainmentTypeDtoMapper mapper = new EntertainmentTypeDtoMapper();
        dto.setEntertainmentTypeList(mapper.toEntertainmentTypeDtoList(request.getEntertainmentTypeList()));
        return dto;
    }
}
