package com.stageserver.dto.mapper;

import com.stageserver.dto.supported.SupportedLanguagesDto;
import com.stageserver.model.supported.SupportedLanguages;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Setter
@Component
public class SupportedLanguagesDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public SupportedLanguages toSupportedLanguages(SupportedLanguagesDto dto) {
        return modelMapper.map(dto, SupportedLanguages.class);
    }

    public SupportedLanguagesDto toSupportedLanguageDto(SupportedLanguages request) {
        SupportedLanguagesDto destinationDto = new SupportedLanguagesDto();
        destinationDto.setLanguages(request.getLanguages());
        return destinationDto;
    }
}
