package com.stageserver.dto.mapper;

import com.stageserver.dto.supported.MusicGenreDto;
import com.stageserver.dto.supported.SupportedMusicGenreDto;
import com.stageserver.model.supported.SupportedMusicGenre;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Setter
@Getter
public class SupportedMusicGenreDtoMapper {

    private ModelMapper modelMapper = new ModelMapper();

    public SupportedMusicGenre toSupportedMusicGenre(MusicGenreDto dto) {
        return modelMapper.map(dto, SupportedMusicGenre.class);
    }

    public SupportedMusicGenreDto toSupportedMusicGenreDto(SupportedMusicGenre request) {
        return modelMapper.map(request, SupportedMusicGenreDto.class);
    }
}
