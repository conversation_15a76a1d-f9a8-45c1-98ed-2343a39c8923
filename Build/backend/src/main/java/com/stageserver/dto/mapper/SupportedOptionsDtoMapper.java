package com.stageserver.dto.mapper;

import com.stageserver.dto.supported.SupportedOptionsDto;
import com.stageserver.model.supported.SupportedOptions;
import lombok.Getter;
import lombok.Setter;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Component
public class SupportedOptionsDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public SupportedOptions toSupportedOptions(SupportedOptionsDto dto) {
        return modelMapper.map(dto, SupportedOptions.class);
    }

    public SupportedOptionsDto toSupportedOptionsDto(SupportedOptions request) {
        SupportedOptionsDto dto = new SupportedOptionsDto();
        dto.setCurrencies(request.getCurrencies());
        dto.setProfiles(request.getProfiles());
        dto.setPaymentMethods(request.getPaymentMethods());

        if(request.getPaymentOptions() != null) {
            List<SupportedOptionsDto.ChargingType> paymentOptions = new ArrayList<>();
            paymentOptions.add(SupportedOptionsDto.ChargingType.HOURLY);
            paymentOptions.add(SupportedOptionsDto.ChargingType.EVENT);
            dto.setPaymentOptions(paymentOptions);
        }
        return  dto;
    }
}
