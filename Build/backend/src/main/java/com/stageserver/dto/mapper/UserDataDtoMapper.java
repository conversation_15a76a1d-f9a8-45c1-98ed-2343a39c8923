package com.stageserver.dto.mapper;

import com.stageserver.dto.common.UserDataDto;
import com.stageserver.dto.login.UserDto;
import com.stageserver.model.login.User;
import com.stageserver.model.supported.SupportedOptions;
import lombok.Getter;
import lombok.Setter;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
@Component
public class UserDataDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public UserDataDto toUserDataDto(User user) {
        return modelMapper.map(user, UserDataDto.class);
    }

    public User toUser(UserDataDto userDataDto) {
        return modelMapper.map(userDataDto, User.class);
    }

    public List<UserDataDto> toUserDataDtoList(List<User> users) {
        return users.stream().map(this::toUserDataDto).collect(Collectors.toList());
    }
}
