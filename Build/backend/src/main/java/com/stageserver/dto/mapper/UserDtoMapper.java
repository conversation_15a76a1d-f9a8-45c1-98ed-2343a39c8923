package com.stageserver.dto.mapper;

import com.stageserver.dto.login.UserDto;
import com.stageserver.model.login.User;
import lombok.Getter;
import lombok.Setter;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Component
public class UserDtoMapper {
    private final ModelMapper modelMapper = new ModelMapper();

    public UserDto toUserDto(User user) {
        return modelMapper.map(user, UserDto.class);
    }

    public User toUser(UserDto userDto) {
        return modelMapper.map(userDto, User.class);
    }

    public List<UserDto> toUserDtoList(List<User> users) {
        List<UserDto> dtoList = new ArrayList<>();
        if (!users.isEmpty()) {
            users.forEach(user -> {
                dtoList.add(modelMapper.map(user, UserDto.class));
            });
        }
        return dtoList;
    }
}
