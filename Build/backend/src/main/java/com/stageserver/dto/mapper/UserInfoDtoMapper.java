package com.stageserver.dto.mapper;

import com.stageserver.dto.common.UserDataDto;
import com.stageserver.dto.login.UserInfoDto;
import com.stageserver.model.login.User;
import lombok.Getter;
import lombok.Setter;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
@Component
public class UserInfoDtoMapper {
    private ModelMapper modelMapper = new ModelMapper();

    public UserInfoDto toUserInfoDto(User user) {
        return modelMapper.map(user, UserInfoDto.class);
    }

    public User toUser(UserInfoDto userInfoDto) {
        return modelMapper.map(userInfoDto, User.class);
    }

    public List<UserInfoDto> toUserDataDtoList(List<User> users) {
        return users.stream().map(this::toUserInfoDto).collect(Collectors.toList());
    }
}
