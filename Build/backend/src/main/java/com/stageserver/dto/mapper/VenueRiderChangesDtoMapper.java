package com.stageserver.dto.mapper;

import com.stageserver.dto.contracts.VenueRiderChangesDto;
import com.stageserver.model.contract.VenueRiderChanges;
import lombok.Getter;
import lombok.Setter;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
public class VenueRiderChangesDtoMapper {

    private final ModelMapper modelMapper = new ModelMapper();

    public VenueRiderChangesDto toVenueRiderChangesDto(VenueRiderChanges changes) {
        return modelMapper.map(changes, VenueRiderChangesDto.class);
    }

    public VenueRiderChanges toVenueRiderChanges(VenueRiderChangesDto dto) {
        return modelMapper.map(dto, VenueRiderChanges.class);
    }
}
