package com.stageserver.dto.mapper;

import com.stageserver.dto.contracts.VenueRiderNotesDto;
import com.stageserver.model.contract.VenueRiderNotes;
import lombok.Getter;
import lombok.Setter;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
public class VenueRiderNotesDtoMapper {

    private final ModelMapper modelMapper = new ModelMapper();

    public VenueRiderNotesDto toVenueRiderNotesDto(VenueRiderNotes notes) {
        return modelMapper.map(notes, VenueRiderNotesDto.class);
    }

    public VenueRiderNotes toVenueRiderNotes(VenueRiderNotesDto dto) {
        return modelMapper.map(dto, VenueRiderNotes.class);
    }
}
