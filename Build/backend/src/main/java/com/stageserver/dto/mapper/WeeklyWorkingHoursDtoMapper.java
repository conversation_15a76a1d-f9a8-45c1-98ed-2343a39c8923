package com.stageserver.dto.mapper;

import com.stageserver.dto.profile.WeeklyWorkingHoursDto;
import com.stageserver.dto.profile.WorkingHoursDto;
import com.stageserver.model.profile.WeeklyWorkingHours;
import com.stageserver.model.profile.WorkingHours;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Getter
@Setter
@Component
public class WeeklyWorkingHoursDtoMapper {
    private final ModelMapper modelMapper = new ModelMapper();

    public WorkingHoursDto toWorkingHoursDto(WorkingHours workingHours) {
        return modelMapper.map(workingHours, WorkingHoursDto.class);
    }

    public WorkingHours toWorkingHours(WorkingHoursDto workingHoursDto) {
        return modelMapper.map(workingHoursDto, WorkingHours.class);
    }

    public WeeklyWorkingHoursDto toWeeklyWorkingHoursDto(WeeklyWorkingHours weeklyWorkingHours) {
        WeeklyWorkingHoursDto dto = new WeeklyWorkingHoursDto();
        List<WorkingHoursDto> dtoList = new ArrayList<>();
        for(WorkingHours workingHours : weeklyWorkingHours.getWorkingHoursList()) {
            dtoList.add(toWorkingHoursDto(workingHours));
        }
        dto.setWorkingHoursList(dtoList);
        return dto;
    }

    public WeeklyWorkingHours toWeeklyWorkingHours(WeeklyWorkingHoursDto dto) {
        WeeklyWorkingHours weeklyWorkingHours = new WeeklyWorkingHours();
        List<WorkingHours> workingHoursList = new ArrayList<>();
        for(WorkingHoursDto workingHoursDto : dto.getWorkingHoursList()) {
            workingHoursList.add(toWorkingHours(workingHoursDto));
        }
        weeklyWorkingHours.setWorkingHoursList(workingHoursList);
        return weeklyWorkingHours;
    }
}
