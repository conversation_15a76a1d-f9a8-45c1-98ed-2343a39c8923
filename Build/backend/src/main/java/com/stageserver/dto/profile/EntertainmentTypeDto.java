package com.stageserver.dto.profile;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EntertainmentTypeDto {

    private String name;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String iconUrl;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private List<EntertainmentTypeMemberDto> members;
}
