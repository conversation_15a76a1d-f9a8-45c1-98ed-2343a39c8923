package com.stageserver.dto.profile;

import com.stageserver.dto.location.LocationDto;
import com.stageserver.model.common.ProfileStatus;
import com.stageserver.model.common.ProfileType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ProfileDetailedViewDto {

    private String profileId;

    private boolean isOwnProfile;

    private ProfileStatus profileStatus;

    private ProfileType profileType;

    private ProfileDto profileDto;

    private ProfileInfoDto infoDto;

    private ActSkillsDto skillsDto;

    private ProfileMediaDto mediaDto;

    private ProfilePaymentsDto paymentsDto;

    private LocationDto locationDto;

    private VirtualContactDto virtualContactDto;

    private ProfileRatingDto profileRatingDto;

    private boolean isCurrentUsersFavorite;

    private boolean isBlockedByCurrentUser;
}
