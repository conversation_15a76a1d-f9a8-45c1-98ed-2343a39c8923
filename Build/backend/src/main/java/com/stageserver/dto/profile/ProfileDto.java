package com.stageserver.dto.profile;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stageserver.model.common.ProfileType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ProfileDto {

    private String profileRole;

    private String profileName;

    private String profileEmail;

    private int numMembers;

    private boolean owner;

    private boolean useMyEmail;

    private List<String> performanceLanguages;

    private List<String> communicationLanguages;

    private String preferredLanguage;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String profileId;

    private VirtualContactDto virtualContactDto;

    private ProfileType profileType;

    private double gigsPerMonth;

    private double averageGigsPrice;

    private int numberOfGigs;

    private int numberOfBookings;

    private double numberOfBookingsPerMonth;

    private double averageBookingPrice;

    private int numberOfFollowers;
}
