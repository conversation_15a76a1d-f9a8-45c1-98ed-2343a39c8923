package com.stageserver.dto.profile;

import com.stageserver.dto.location.LocationDto;
import com.stageserver.model.common.ProfileStatus;
import com.stageserver.model.common.ProfileType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.ZonedDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ProfileMinimizedViewDto {

    private String profileId;

    private String profileName;

    private List<String> socialMediaLinks;

    private double popularityStars;

    private int numberOfReviews;

    private LocationDto locationDto;

    private List<String> profileImageUrls;

    private String currency;

    private int typicalPrice;

    private int minimumPrice;

    private ProfilePaymentsDto.ChargingType minPriceChargingType;

    private ProfilePaymentsDto.ChargingType typicalPriceChargingType;

    private int numberOfFollowers;

    private double averagePayForGig;

    private double averagePricePerBooking;

    private ZonedDateTime nextEventDate;

    private boolean favouriteSelected;

    private ProfileStatus profileStatus;

    private ProfileType profileType;

    private VirtualContactDto virtualContactDto;

    private WeeklyWorkingHoursDto weeklyWorkingHoursDto;
}
