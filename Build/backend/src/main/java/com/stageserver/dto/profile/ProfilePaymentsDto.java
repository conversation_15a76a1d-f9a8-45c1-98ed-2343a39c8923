package com.stageserver.dto.profile;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ProfilePaymentsDto {

    public enum ChargingType {
        HOURLY,
        EVENT
    }

    private String currency;

    private int typicalPrice;

    private int minimumPrice;

    private ChargingType minPriceChargingType;

    private ChargingType typicalPriceChargingType;

    private List<String> acceptablePaymentMethods;

    private boolean forRent;
}
