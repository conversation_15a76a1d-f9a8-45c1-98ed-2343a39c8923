package com.stageserver.dto.schedule;

import lombok.Getter;
import lombok.Setter;

import java.time.DayOfWeek;
import java.time.Month;
import java.util.List;

@Setter
@Getter
public class RecurrenceDto {

    private RecurrenceType recurrenceType;

    //If for example, in Weekly recurrence, if interval is 1, the schedule will repeat every week.
    //If interval is 2, the schedule will repeat every other week.
    private int interval;  // repeat of the recurrence

    private int count;

    private List<DayOfWeek> daysOfWeek;

    private Integer daysOfMonth;

    private List<Month> monthsOfYear;

    private RecurrenceEndTypeDto recurrenceEndType;

}
