package com.stageserver.dto.schedule;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stageserver.model.common.ProfileType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;


@Setter
@Getter
@NoArgsConstructor
public class ScheduleTimeDto {

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String scheduleId;

    private ProfileType profileType;

    private String scheduleName;

    private String scheduleDescription;

    private ScheduleType scheduleType;

    String timeZone;

    ZonedDateTime startDate;

    ZonedDateTime startTime;

    ZonedDateTime endDate;

    ZonedDateTime endTime;

    boolean recurring;

    RecurrenceDto recurrence;

}
