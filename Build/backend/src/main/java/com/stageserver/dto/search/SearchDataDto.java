package com.stageserver.dto.search;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.stereotype.Component;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@Component
public class SearchDataDto {

    private String searchName;

    private List<String> searchStrings;

    private SearchFilterDto searchFilter;

    public enum SearchType {
        SEARCH_TYPE_ACT,
        SEARCH_TYPE_VENUE,
        SEARCH_TYPE_EVENT
    }

    private SearchType searchType;

    @Override
    public String toString() {

        if((searchFilter != null) && (searchFilter.searchLocation != null)) {
            return "SearchData{" +
                    "countryName='" + searchFilter.searchLocation.getCountryName() + '\'' +
                    ", stateName='" + searchFilter.searchLocation.getStateName() + '\'' +
                    ", cityName='" + searchFilter.searchLocation.getCityName() + '\'' +
                    ", distance=" + searchFilter.searchLocation.getDistance() +
                    '}';
        } else {
            return "SearchData{" +
                    "searchName='" + searchName + '\'' +
                    ", searchStrings=" + searchStrings +
                    '}';
        }
    }

}
