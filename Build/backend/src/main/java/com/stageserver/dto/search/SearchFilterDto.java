package com.stageserver.dto.search;

import com.stageserver.dto.profile.ProfileRatingDto;
import com.stageserver.dto.profile.EntertainmentTypeDto;
import com.stageserver.dto.supported.MusicGenreDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.stereotype.Component;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@Component
public class SearchFilterDto {
    SearchLocationDto searchLocation;
    SearchDateDto searchDate;
    List<EntertainmentTypeDto> entertainmentTypesList;
    List<MusicGenreDto> musicGenreList;
    ProfileRatingDto actRating;
}
