package com.stageserver.dto.serailizer;

import com.stageserver.config.Constants;
import com.stageserver.dto.profile.ProfileRatingDto;
import com.stageserver.dto.profile.EntertainmentTypeDto;
import com.stageserver.dto.search.SearchDataDto;
import com.stageserver.dto.search.SearchDateDto;
import com.stageserver.dto.search.SearchFilterDto;
import com.stageserver.dto.search.SearchLocationDto;
import com.stageserver.dto.supported.MusicGenreDto;
import com.stageserver.model.search.SearchData;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Getter
@Setter
@Component
public class SearchDataSerializer {

    @Autowired
    private Constants constants;

    public SearchData serialize(SearchDataDto searchDataDto) {
        SearchData searchData = new SearchData();
        searchData.setSearchName(searchDataDto.getSearchName());
        searchData.setSearchStrings(searchDataDto.getSearchStrings());
        searchData.setSearchType(searchDataDto.getSearchType());
        // Extracting search location data from search filter
        SearchFilterDto searchFilterDto = searchDataDto.getSearchFilter();

        if(searchFilterDto != null) {
            if (searchFilterDto.getSearchLocation() != null) {
                log.info("Search Location is provided: Country {}, State {}, City {}",
                        searchFilterDto.getSearchLocation().getCountryName(),
                        searchFilterDto.getSearchLocation().getStateName(),
                        searchFilterDto.getSearchLocation().getCityName());

                searchData.setCountryName(searchFilterDto.getSearchLocation().getCountryName());
                searchData.setStateName(searchFilterDto.getSearchLocation().getStateName());
                searchData.setCityName(searchFilterDto.getSearchLocation().getCityName());
                if ((searchData.getDistance() >= 0) && (searchData.getDistance() <= 1000)) {
                    searchData.setDistance(searchFilterDto.getSearchLocation().getDistance());
                }
            }
            // Extracting search date data from search filter
            if (searchFilterDto.getSearchDate() != null) {
                log.info("Search Date is provided: Start Date {}, End Date {}",
                        searchFilterDto.getSearchDate().getStartDate(),
                        searchFilterDto.getSearchDate().getEndDate());
                searchData.setSearchDateType(searchFilterDto.getSearchDate().getSearchDateType());
                searchData.setStartDate(searchFilterDto.getSearchDate().getStartDate());
                searchData.setEndDate(searchFilterDto.getSearchDate().getEndDate());
            }

            // Extracting entertainment type data from search filter
            if (searchFilterDto.getEntertainmentTypesList() != null) {
                List<String> entertainmentTypeList = new ArrayList<>();
                for (EntertainmentTypeDto eType : searchFilterDto.getEntertainmentTypesList()) {
                    log.info("Search Entertainment Type is provided: {}", eType.getName());
                    entertainmentTypeList.add(eType.getName());
                }
                searchData.setEntertainmentTypeList(entertainmentTypeList);
            }

            if(searchFilterDto.getMusicGenreList() != null) {
                List<String> musicGenreList = new ArrayList<>();
                for(MusicGenreDto mGenre : searchFilterDto.getMusicGenreList()) {
                    log.info("Search Music Genre is provided: {}", mGenre.getName());
                    musicGenreList.add(mGenre.getName());
                }
                searchData.setMusicGenreList(musicGenreList);
            }

            // Extracting act rating data from search filter
            if (searchFilterDto.getActRating() != null) {
                log.info("Search Act Rating is provided: Overall Rating {}", searchFilterDto.getActRating().getOverallRating());
                searchData.setOverallRating(searchFilterDto.getActRating().getOverallRating());
            }
        }
        return searchData;
    }

    public SearchDataDto deserialize(SearchData searchData) {
        SearchDataDto searchDataDto = new SearchDataDto();
        searchDataDto.setSearchName(searchData.getSearchName());
        searchDataDto.setSearchStrings(searchData.getSearchStrings());
        searchDataDto.setSearchType(searchData.getSearchType());
        SearchFilterDto searchFilterDto = new SearchFilterDto();
        SearchLocationDto searchLocationDto = new SearchLocationDto();
        searchLocationDto.setCountryName(searchData.getCountryName());
        searchLocationDto.setStateName(searchData.getStateName());
        searchLocationDto.setCityName(searchData.getCityName());
        searchLocationDto.setDistance(searchData.getDistance());
        searchFilterDto.setSearchLocation(searchLocationDto);

        // Extracting info from searchDate and setting it in searchFilterDto
        SearchDateDto searchDateDto = new SearchDateDto();
        searchDateDto.setSearchDateType(searchData.getSearchDateType());
        searchDateDto.setStartDate(searchData.getStartDate());
        searchDateDto.setEndDate(searchData.getEndDate());
        searchFilterDto.setSearchDate(searchDateDto);

        // Extracting EntertainmentType info from searchData and setting it in searchFilterDto
        if(searchData.getEntertainmentTypeList() != null) {
            List<EntertainmentTypeDto> entertainmentTypeDtoList = new ArrayList<>();
            for (String eType : searchData.getEntertainmentTypeList()) {
                EntertainmentTypeDto entertainmentTypeDto = new EntertainmentTypeDto();
                entertainmentTypeDto.setName(eType);
                entertainmentTypeDtoList.add(entertainmentTypeDto);
            }
            searchFilterDto.setEntertainmentTypesList(entertainmentTypeDtoList);
        }


        // Extracting MusicGenre info from searchData and setting it in searchFilterDto
        if(searchData.getMusicGenreList() != null) {
        List<MusicGenreDto> musicGenreDtoList = new ArrayList<>();
        for(String mGenre : searchData.getMusicGenreList()) {
            MusicGenreDto musicGenreDto = new MusicGenreDto();
            musicGenreDto.setName(mGenre);
            musicGenreDtoList.add(musicGenreDto);
        }
        searchFilterDto.setMusicGenreList(musicGenreDtoList);
        }

        // Extracting Rating info from searchData and setting it in searchFilterDto
        ProfileRatingDto profileRatingDto = new ProfileRatingDto();
        profileRatingDto.setOverallRating(searchData.getOverallRating());
        searchFilterDto.setActRating(profileRatingDto);

        searchDataDto.setSearchFilter(searchFilterDto);

        return searchDataDto;
    }

    public List<SearchDataDto> deserializeList(List<SearchData> searchDataList) {
        List<SearchDataDto> searchDataDtoList = new ArrayList<>();

        for (SearchData searchData : searchDataList) {
            searchDataDtoList.add(deserialize(searchData));
        }
        return searchDataDtoList;
    }
}
