package com.stageserver.dto.supported;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.stereotype.Component;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@Component
public class MusicGenreDto {
    private String name;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String iconUrl;
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private List<String> members;
}
