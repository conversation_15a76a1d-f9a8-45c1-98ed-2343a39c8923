package com.stageserver.events;

import com.stageserver.config.MessageConstants;
import com.stageserver.exceptions.TokenAlreadyExistException;
import com.stageserver.model.login.ConfirmPhoneNumberToken;
import com.stageserver.model.login.User;
import com.stageserver.repository.ConfirmPhoneNumberTokenRepository;
import com.stageserver.repository.UserRepository;
import com.stageserver.service.UserService;
import com.stageserver.service.interfaces.I_UtilityService;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.http.HttpStatus;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class ConfirmPhoneNumberEventListener  implements ApplicationListener<ConfirmPhoneNumberEvent> {

    private final UserService userService;
    private final I_UtilityService utilityService;
    private final JavaMailSender mailSender;
    private final UserRepository userRepository;
    private final ConfirmPhoneNumberTokenRepository confirmPhoneNumberTokenRepository;
    private final SpringTemplateEngine templateEngine;

    private User newUser;

    @Override
    public void onApplicationEvent(ConfirmPhoneNumberEvent event) {
        String email = event.getUserEmail();
        String confirmPhoneNumberToken = utilityService.generateNumericToken();

        Optional<User> user = userRepository.findByEmail(email);
        if(user.isPresent()) {
            List<ConfirmPhoneNumberToken> tokenList = confirmPhoneNumberTokenRepository.findAllTokensForUserByEmail(user.get().getEmail());
            if(!tokenList.isEmpty()) {
                throw new TokenAlreadyExistException(HttpStatus.ALREADY_REPORTED, MessageConstants.getErrorMap().get(MessageConstants.ERROR_TWO_FA_TOKEN_EXISTS));
            }

            userService.saveConfirmPhoneNumberToken(user.get(), confirmPhoneNumberToken);
            log.info("numeric SMS token for confirming phoneNumber: {} for user {}", confirmPhoneNumberToken, email);
        }
        try {
            sendSMSToken(email, confirmPhoneNumberToken);
        }
        catch(MessagingException | UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public void sendSMSToken(String email, String confirmPhoneNumberToken) throws MessagingException, UnsupportedEncodingException {


        MimeMessage message = mailSender.createMimeMessage();
        Context context = new Context();

        context.setVariable("token", confirmPhoneNumberToken);

        String process = templateEngine.process("twofa_email.html", context);
        MimeMessageHelper helper = new MimeMessageHelper(message, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED, StandardCharsets.UTF_8.name());
        helper.setTo(email);
        helper.setFrom("<EMAIL>", "HBP Service");
        helper.setSubject("Your SMS Code");
        helper.setText(process, true);
        mailSender.send(message);

    }
}
