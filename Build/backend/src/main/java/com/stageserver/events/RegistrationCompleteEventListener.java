package com.stageserver.events;

import com.stageserver.config.MessageConstants;
import com.stageserver.exceptions.TokenAlreadyExistException;
import com.stageserver.model.login.User;
import com.stageserver.model.login.VerificationToken;
import com.stageserver.repository.VerificationTokenRepository;
import com.stageserver.service.LoginService;
import com.stageserver.service.interfaces.I_UtilityService;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.http.HttpStatus;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class RegistrationCompleteEventListener implements ApplicationListener<RegistrationCompleteEvent> {

    /* finals are autowired */
    private final LoginService loginService;
    private final JavaMailSender mailSender;
    private final I_UtilityService utilityService;
    private final VerificationTokenRepository verificationTokenRepository;
    private final SpringTemplateEngine templateEngine;
    private User newUser;

    @Value("${stage-server-back-end-url}")
    private String backendUrl;


    @Override
    public void onApplicationEvent(RegistrationCompleteEvent event) {
        newUser = event.getUser();
        List<VerificationToken> tokenList = verificationTokenRepository.findAllTokensForUserByEmail(newUser.getEmail());
        if (!tokenList.isEmpty()) {
            throw new TokenAlreadyExistException(HttpStatus.ALREADY_REPORTED, MessageConstants.getErrorMap().get(MessageConstants.ERROR_VERIFY_EMAIL_TOKEN_EXISTS));
        }

        String verificationToken = utilityService.generateUniqueToken();
        loginService.saveUserVerificationToken(newUser, verificationToken);

        String verificationURL = backendUrl + "/api/v1/public/register/verify-email?token=" + verificationToken;
        log.info("Click the link to verify your registration: {}", verificationURL);

        try {
            sendVerificationEmail(backendUrl + "/", verificationToken);
        } catch (MessagingException | UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public void sendVerificationEmail(String url, String token) throws MessagingException, UnsupportedEncodingException {

        MimeMessage message = mailSender.createMimeMessage();
        Context context = new Context();

        context.setVariable("token", token);
        context.setVariable("url", url);
        String process = templateEngine.process("verify_email.html", context);
        MimeMessageHelper helper = new MimeMessageHelper(message, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED, StandardCharsets.UTF_8.name());
        helper.setTo(newUser.getEmail());
        helper.setFrom("<EMAIL>", "HBP Service");
        helper.setSubject("Verify Your Email");
        helper.setText(process, true);
        mailSender.send(message);
    }

}
