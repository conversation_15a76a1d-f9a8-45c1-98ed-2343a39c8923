package com.stageserver.events;

import com.stageserver.config.MessageConstants;
import com.stageserver.exceptions.TokenAlreadyExistException;
import com.stageserver.model.login.TwoFactorAuthToken;
import com.stageserver.model.login.User;
import com.stageserver.repository.TwoFactorAuthTokenRepository;
import com.stageserver.repository.UserRepository;
import com.stageserver.service.LoginService;
import com.stageserver.service.UtilityService;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.http.HttpStatus;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;


@Slf4j
@Component
@RequiredArgsConstructor
public class TwoFactorAuthenticationEventListener implements ApplicationListener<TwoFactorAuthenticationEvent> {

    private final LoginService loginService;
    private final UtilityService utilityService;
    private final UserRepository userRepository;
    private final TwoFactorAuthTokenRepository twoFactorAuthTokenRepository;
    private final SpringTemplateEngine templateEngine;
    private final JavaMailSender mailSender;

    @Override
    public void onApplicationEvent(TwoFactorAuthenticationEvent event) {
        String email = event.getUserEmail();
        String twoFactorAuthToken = utilityService.generateNumericToken();

        Optional<User> user = userRepository.findByEmail(email);
        if(user.isPresent()) {
            List<TwoFactorAuthToken> tokenList = twoFactorAuthTokenRepository.findAllTokensForUserByEmail(user.get().getEmail());
            if(!tokenList.isEmpty()) {
                throw new TokenAlreadyExistException(HttpStatus.ALREADY_REPORTED, MessageConstants.getErrorMap().get(MessageConstants.ERROR_TWO_FA_TOKEN_EXISTS));
            }

            loginService.saveUserTwoFactorAuthToken(user.get(), twoFactorAuthToken);
            log.info("numeric SMS token: {}", twoFactorAuthToken);
        }
        try {
            sendSMSRequest(email, twoFactorAuthToken);
        }
        catch(MessagingException | UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public void sendSMSRequest(String email, String twoFactorAuthToken) throws MessagingException, UnsupportedEncodingException {

        MimeMessage message = mailSender.createMimeMessage();
        Context context = new Context();

        context.setVariable("token", twoFactorAuthToken);

        String process = templateEngine.process("twofa_email.html", context);
        MimeMessageHelper helper = new MimeMessageHelper(message, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED, StandardCharsets.UTF_8.name());
        helper.setTo(email);
        helper.setFrom("<EMAIL>", "HBP Service");
        helper.setSubject("Your SMS Code");
        helper.setText(process, true);
        mailSender.send(message);

    }
}
