package com.stageserver.events;

import com.stageserver.config.Constants;
import com.stageserver.model.profile.Profile;
import com.stageserver.model.profile.VirtualContact;
import com.stageserver.repository.ProfileRepository;
import com.stageserver.service.ProfileService;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class VirtualProfileBookedEventListener implements ApplicationListener<VirtualProfileBookedEvent> {

    private final JavaMailSender mailSender;
    private final SpringTemplateEngine templateEngine;
    private final Constants constants;

    @Autowired
    private ProfileRepository profileRepository;

    @Override
    public void onApplicationEvent(VirtualProfileBookedEvent event) {

        try {
            log.info("Processing VirtualProfileBookedEvent for profileId: {}", event.getProfileId());
            String profileId = event.getProfileId();
            Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
            if (optProfile.isPresent()) {
                Profile profile = optProfile.get();
                VirtualContact virtualContact = profile.getVirtualContact();
                String signupUrl = constants.getFrontEndUrl() + "/en/signup";
                sendBookingNotificationEmail(virtualContact.getContactEmail(), signupUrl, profile.getProfileName());
            }
        } catch (MessagingException | UnsupportedEncodingException e) {
            log.warn("Error sending booking notification email: {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public void sendBookingNotificationEmail(String email, String url, String profileName) throws MessagingException, UnsupportedEncodingException {
        MimeMessage message = mailSender.createMimeMessage();
        Context context = new Context();
        context.setVariable("url", url);
        context.setVariable("profileName", profileName);

        String process = templateEngine.process("virtual_profile_booked.html", context);
        MimeMessageHelper helper = new MimeMessageHelper(message, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED, StandardCharsets.UTF_8.name());
        helper.setTo(email);
        helper.setFrom("<EMAIL>", "HBP Service");
        helper.setSubject("Someone has booked your profile "+ profileName);
        helper.setText(process, true);
        mailSender.send(message);
        log.info("Booking notification email sent to: {}", email);

    }
}
