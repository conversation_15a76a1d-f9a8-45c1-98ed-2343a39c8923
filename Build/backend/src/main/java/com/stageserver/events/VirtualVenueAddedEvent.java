package com.stageserver.events;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

@Getter
@Setter
public class VirtualVenueAddedEvent extends ApplicationEvent {

    private String contactEmail;

    private String profileId;

    public VirtualVenueAddedEvent(String email, String profileId) {
        super(email);
        this.contactEmail = email;
        this.profileId = profileId;
    }
}
