package com.stageserver.events;

import com.stageserver.config.Constants;
import com.stageserver.model.profile.Profile;
import com.stageserver.service.ProfileService;
import com.stageserver.service.SystemUserService;
import com.stageserver.service.interfaces.I_UtilityService;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class VirtualVenueAddedEventListener implements ApplicationListener<VirtualVenueAddedEvent> {

    private final SystemUserService systemUserService;
    private final I_UtilityService utilityService;
    private final JavaMailSender mailSender;
    private final SpringTemplateEngine templateEngine;
    private final Constants constants;
    private final ProfileService profileService;

    @Override
    public void onApplicationEvent(VirtualVenueAddedEvent event) {
        String email = event.getContactEmail();

        String claimVirtualVenueToken = utilityService.generateUniqueToken();
        systemUserService.saveVirtualVenueClaimToken(event.getProfileId(), email, claimVirtualVenueToken);
        String claimVirtualVenueUrl = constants.getFrontEndUrl() + "/verify-virtual-venue?token=" + claimVirtualVenueToken;
        try {
            Optional<Profile> optProfile = profileService.getProfile(event.getProfileId());
            String venueName = optProfile.map(Profile::getProfileName).orElse("Venue");
            if(profileService.userEmailAlreadyExists(email)) {
                log.info("Claim Virtual Venue URL: {}", claimVirtualVenueUrl);
                sendClaimEmailNotification(email, claimVirtualVenueToken, constants.getFrontEndUrl(), venueName);
            } else {
                String signupUrl = constants.getFrontEndUrl() + "/en/signup";
                log.info("Sign-up URL: {}", signupUrl);
                sendJoinSystemEmail(email, signupUrl, venueName);
            }

        } catch (MessagingException | UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public void sendClaimEmailNotification(String email, String token, String url, String venueName) throws MessagingException, UnsupportedEncodingException {

        MimeMessage message = mailSender.createMimeMessage();
        Context context = new Context();
        context.setVariable("token", token);
        context.setVariable("url", url);
        context.setVariable("venueName", venueName);

        String process = templateEngine.process("virtual_venue_added.html", context);
        MimeMessageHelper helper = new MimeMessageHelper(message, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED, StandardCharsets.UTF_8.name());
        helper.setTo(email);
        helper.setFrom("<EMAIL>", "HBP Service");
        helper.setSubject("Claim Your Account");
        helper.setText(process, true);
        mailSender.send(message);
    }

    public void sendJoinSystemEmail(String email, String url, String venueName) throws MessagingException, UnsupportedEncodingException {
        MimeMessage message = mailSender.createMimeMessage();
        Context context = new Context();
        context.setVariable("url", url);
        context.setVariable("actName", venueName);

        String process = templateEngine.process("join_the_system.html", context);
        MimeMessageHelper helper = new MimeMessageHelper(message, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED, StandardCharsets.UTF_8.name());
        helper.setTo(email);
        helper.setFrom("<EMAIL>", "HBP Service");
        helper.setSubject("Please join StageMinder");
        helper.setText(process, true);
        mailSender.send(message);
    }
}
