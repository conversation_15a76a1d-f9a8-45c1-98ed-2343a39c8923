package com.stageserver.model.IM;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.LocalDateTime;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Node
public class InstantMessage {

    @Id
    @GeneratedValue
    private String elementId;

    private String messageId;

    private String sender;

    private String receiver;

    @Relationship(type = "HAS_CONTENT", direction = Relationship.Direction.OUTGOING)
    private MessageContent content;

    private LocalDateTime timestamp;

    private boolean seen;

    private boolean dismissed = false;
}
