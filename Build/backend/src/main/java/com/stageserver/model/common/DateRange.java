package com.stageserver.model.common;

import lombok.Getter;
import lombok.Setter;

import java.time.ZonedDateTime;

@Getter
@Setter
public class DateRange {
    private final ZonedDateTime startDate;
    private final ZonedDateTime endDate;

    public DateRange(ZonedDateTime startDate, ZonedDateTime endDate) {
        if (startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("Start date must be before or equal to end date");
        }
        this.startDate = startDate;
        this.endDate = endDate;
    }

}
