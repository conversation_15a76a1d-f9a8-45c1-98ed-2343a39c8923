package com.stageserver.model.common;

import lombok.Getter;

@Getter
public enum EventStatus {
    STATUS_PUBLISHED("STATUS_PUBLISHED"),
    STATUS_DELETED("STATUS_DELETED"),
    STATUS_UNPUBLISHED("STATUS_UNPUBLISHED");

    private final String status;

    EventStatus(String status) {
        this.status = status;
    }

    public static EventStatus valueOfStatus(String status) {
        for (EventStatus e : values()) {
            if (e.status.equals(status)) {
                return e;
            }
        }
        return null;
    }
}
