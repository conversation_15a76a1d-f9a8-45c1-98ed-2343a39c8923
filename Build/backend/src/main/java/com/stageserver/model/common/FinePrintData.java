package com.stageserver.model.common;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

@Setter
@Getter
@Node
public class FinePrintData {
    @Id
    @GeneratedValue
    private String elementId;

    private String version;

    private String locale;

    private String title;

    private String subTitle;

    private String content;

    private String footer;
}
