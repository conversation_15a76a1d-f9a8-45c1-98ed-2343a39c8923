package com.stageserver.model.common;

import lombok.Getter;

@Getter
public enum ProfileStatus {
    STATUS_PUBLISHED("STATUS_PUBLISHED"),
    STATUS_DELETED("STATUS_DELETED"),
    STATUS_CREATED("STATUS_CREATED");

    private final String status;

     ProfileStatus(String status) {
        this.status = status;
    }

    public static ProfileStatus valueOfStatus(String status) {
        for (ProfileStatus e : values()) {
            if (e.status.equals(status)) {
                return e;
            }
        }
        return null;
    }
}
