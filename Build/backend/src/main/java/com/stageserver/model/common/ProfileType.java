package com.stageserver.model.common;

public enum ProfileType {
    ALL("ALL"),
    ACT_PROFILE("ACT_PROFILE"),
    VIRTUAL_ACT_PROFILE("VIRTUAL_ACT_PROFILE"),
    VENUE_PROFILE("VENUE_PROFILE"),
    VIRTUAL_VENUE_PROFILE("VIRTUAL_VENUE_PROFILE");

    private final String type;

    ProfileType(String type) {
        this.type = type;
    }

    public static ProfileType valueOfType(String type) {
        for (ProfileType e : values()) {
            if (e.type.equals(type)) {
                return e;
            }
        }
        return null;
    }
}
