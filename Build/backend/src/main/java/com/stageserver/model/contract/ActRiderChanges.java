package com.stageserver.model.contract;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

@Setter
@Getter
@Node
public class ActRiderChanges {
    @Id
    @GeneratedValue
    private String elementId;

    private String riderUrl;

    private String riderConditions;
}
