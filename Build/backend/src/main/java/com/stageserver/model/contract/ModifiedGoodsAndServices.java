package com.stageserver.model.contract;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
@Node
public class ModifiedGoodsAndServices {

    @Id
    @GeneratedValue
    private String elementId;

    private String contractId;

    private boolean performersRoleModified;

    private boolean startDateModified;

    private boolean durationInHoursModified;

    private boolean loadingTimeModified;

    private boolean paymentTypeModified;

    private boolean flatRateCurrencyModified;

    private boolean flatRateAmountModified;

    private boolean flatRatePercentageModified;

    private boolean doorGigEntryFeeModified;

    private boolean venueCapacityModified;

    private boolean doorManagedByModified;

    private boolean doorGigPaidByModified;

    private boolean payableToModified;

    private boolean exposureGigFeeModified;

    private boolean exposureGigCurrencyModified;

    private boolean maximumPercentageModified;

    private boolean guaranteedMaximumModified;

    private boolean equipmentProviderModified;

    private boolean mealsProvidedByPurchaserModified;

    private  boolean accommodationProvidedModified;

    private boolean merchandiseSalesAllowedModified;

    private boolean performerMemberOfUnionModified;

    private boolean messageModified;
}

