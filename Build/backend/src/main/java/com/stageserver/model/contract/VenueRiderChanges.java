package com.stageserver.model.contract;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.util.List;

@Slf4j
@Setter
@Getter
@Node
public class VenueRiderChanges {

    @Id
    @GeneratedValue
    private String elementId;

    private String parkingConditions;

    private List<String> allowedVisualEffectsList;

    private String riderUrl;
}
