package com.stageserver.model.contract;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

@Slf4j
@Setter
@Getter
@Node
public class VenueRiderNotes {
    @Id
    @GeneratedValue
    private String elementId;

    private boolean declined;

    private String declineWithReason;

    private boolean accepted;

    private boolean acceptedWithConditions;

    private String acceptanceConditions;

    private String riderUrl;
}

