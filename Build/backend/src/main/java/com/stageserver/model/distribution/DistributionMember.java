package com.stageserver.model.distribution;

import com.stageserver.dto.distribution.DistributionType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Property;

@NoArgsConstructor
@Getter
@Setter
@Node
public class DistributionMember {

    @Id
    @GeneratedValue
    private String elementId;

    private boolean useActProfile;

    private String firstName;

    private String lastName;

    private String receiverEmail;

    private String role;

    @Property
    private DistributionType distributionType;

}
