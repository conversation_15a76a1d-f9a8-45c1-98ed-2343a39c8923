package com.stageserver.model.event;

import com.stageserver.model.common.EventStatus;
import com.stageserver.model.schedule.ScheduleTime;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
@Node
public class Event {

    @Id
    @GeneratedValue
    private String elementId;

    private String eventName;

    private String eventId;

    private EventStatus status;

    @Relationship(type = "HAS_MAIN_INFO", direction = Relationship.Direction.OUTGOING)
    private EventMainInfo eventMainInfo;

    @Relationship(type = "HAS_MEDIA_INFO", direction = Relationship.Direction.OUTGOING)
    private EventMediaInfo eventMediaInfo;

    private String venueProfileId;

    private String venueContractId;

    private String primeContractId;

    private List<String> actContractIdList = new ArrayList<>();

    private List<String> actProfileIdList = new ArrayList<>();

    private int numberOfFollowers;

    private String eventOwner;
}
