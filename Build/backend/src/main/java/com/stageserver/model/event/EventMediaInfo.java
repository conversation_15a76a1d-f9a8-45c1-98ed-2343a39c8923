package com.stageserver.model.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.util.List;

@Setter
@Getter
@Node
public class EventMediaInfo {
    @Id
    @GeneratedValue
    private String elementId;

    private List<String> imageUrls;

    private List<String> videoUrls;

    private List<String> audioUrls;

    private Long totalImageSize;
}
