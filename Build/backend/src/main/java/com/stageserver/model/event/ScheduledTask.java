package com.stageserver.model.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.time.Instant;

@Getter
@Setter
@Node
public class ScheduledTask {

    @Id
    @GeneratedValue
    private String elementId;

    private String contractId;

    private Instant triggerTime;

    private String status;


    public ScheduledTask(String contractId, Instant triggerTime, String status) {
        this.contractId = contractId;
        this.triggerTime = triggerTime;
        this.status = status;
    }
}
