package com.stageserver.model.event;

import com.stageserver.model.schedule.ScheduleTime;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
@Node
public class SpecialEvent {

    @Id
    @GeneratedValue
    private String elementId;

    private String specialEventId;

    private String aboutTheEvent;

    private String eventName;

    @Relationship(type = "HAS_SCHEDULE", direction = Relationship.Direction.OUTGOING)
    private ScheduleTime scheduleTime;

    private List<String> eventImageUrls = new ArrayList<>();

    private String ownerProfileId;

    private List<String> ownerImageUrls = new ArrayList<>();

    private String ownerName;

    private Long totalImageSize;

    private boolean isBaseForRecurringEvent = false;

    private String baseSpecialEventId;

}
