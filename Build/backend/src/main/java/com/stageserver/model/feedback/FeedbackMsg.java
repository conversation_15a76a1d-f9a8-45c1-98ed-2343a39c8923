package com.stageserver.model.feedback;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.w3c.dom.stylesheets.LinkStyle;

import java.util.List;

@NoArgsConstructor
@Getter
@Setter
@Node
public class FeedbackMsg {
    @Id
    @GeneratedValue
    private String elementId;

    private String feedbackId;

    private boolean isUserFeedback;

    private String providerProfileId;

    private String receiverProfileId;

    private String otherPartyName;

    private String providerName;

    private double professionalismValue;

    private double entertainmentValue;

    private double drawAsExpectedValue;

    private String publicMessage;

    private String privateMessage;

    private boolean isDeleted = false;

    private boolean isUpdated = false;

    private List<String> providerImageUrls;

    private List<String> receiverImageUrls;

    private String contractId;
}
