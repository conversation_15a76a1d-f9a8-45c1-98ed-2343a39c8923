package com.stageserver.model.location;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Transient;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@Node
public class Country {

    @Id
    @GeneratedValue
    private String elementId;

    private String name;

    @Transient
    private Map<String, String> translations;

    @Relationship(type = "HAS_STATE", direction = Relationship.Direction.OUTGOING)
    private List<State> states;
}
