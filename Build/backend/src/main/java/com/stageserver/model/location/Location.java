package com.stageserver.model.location;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

@NoArgsConstructor
@Getter
@Setter
@Node
public class Location {

    @Id
    @GeneratedValue
    private String elementId;

    private String country;

    private String city;

    private String state;

    private String streetAddress;

    private String zipCode;

    private boolean canTravelLongDistance;

    private double latitude;

    private double longitude;

    @Override
    public String toString() {
        String address = "";
        if(streetAddress != null && !streetAddress.isEmpty()) {
            address += streetAddress + ", ";
        }
        if(city != null && !city.isEmpty()) {
            address += city + ", ";
        }
        if(state != null && !state.isEmpty()) {
            address += state + ", ";
        }
        if(country != null && !country.isEmpty()) {
            address += country;
        }
        if(zipCode != null && !zipCode.isEmpty()) {
            address += "," + zipCode;
        }
        return address;
    }
}
