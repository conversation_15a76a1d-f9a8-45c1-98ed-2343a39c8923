package com.stageserver.model.location;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

@Getter
@Setter
@NoArgsConstructor
@Node
public class SearchLocation {

    @Id
    @GeneratedValue
    private String elementId;

    private String countryName;

    private String stateName;

    private String cityName;

    private int distance;

    @Override
    public String toString() {
        return "SearchData{" +
                "countryName='" + countryName + '\'' +
                ", stateName='" + stateName + '\'' +
                ", cityName='" + cityName + '\'' +
                ", distance=" + distance +
                '}';
    }
}
