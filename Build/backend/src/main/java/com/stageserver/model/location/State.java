package com.stageserver.model.location;

import com.stageserver.model.location.City;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Node
public class State {
    @Id
    @GeneratedValue
    private String elementId;

    private String name;

    @Relationship(type = "HAS_CITY", direction = Relationship.Direction.OUTGOING)
    private List<City> cities;

}
