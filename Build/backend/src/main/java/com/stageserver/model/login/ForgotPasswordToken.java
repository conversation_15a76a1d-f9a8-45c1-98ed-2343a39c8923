package com.stageserver.model.login;

import com.stageserver.model.login.User;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.Calendar;
import java.util.Date;

@Slf4j
@Node
@NoArgsConstructor
@Getter
@Setter
public class ForgotPasswordToken {

    @Id
    @GeneratedValue
    private String elementId;

    private String token;
    private Date expirationTime;

    @Relationship(type = "HAS_PASSWORD_TOKEN",direction= Relationship.Direction.INCOMING)
    private User user;
    private int maxExpiryTime;

    public ForgotPasswordToken(String token, User user, int expiryTime) {
        super();
        this.token = token;
        this.maxExpiryTime = expiryTime;
        this.user = user;
        this.expirationTime = this.getTokenExpirationTime();
    }

    private Date getTokenExpirationTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(new Date().getTime());
        calendar.add(Calendar.MINUTE, maxExpiryTime);
        return new Date(calendar.getTime().getTime());
    }
}
