package com.stageserver.model.login;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.Calendar;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Node
public class TwoFactorAuthToken {
    @Id
    @GeneratedValue
    private String elementId;

    private String token;

    private Date expirationTime;

    private int maxExpiryTime;

    @Relationship(type = "HAS_TFA_TOKEN",direction= Relationship.Direction.INCOMING)
    private User user;

    public TwoFactorAuthToken(String token, User user, int expiryTime) {
        super();
        this.token = token;
        this.maxExpiryTime = expiryTime;
        this.user = user;
        this.expirationTime = this.getTokenExpirationTime();
    }

    private Date getTokenExpirationTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(new Date().getTime());
        calendar.add(Calendar.MINUTE, maxExpiryTime);
        return new Date(calendar.getTime().getTime());
    }
}
