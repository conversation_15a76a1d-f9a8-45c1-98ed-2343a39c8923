package com.stageserver.model.login;

import com.stageserver.model.contract.Contract;
import com.stageserver.model.contract.GoodsAndServicesMessage;
import com.stageserver.model.event.Event;
import com.stageserver.model.location.Location;
import com.stageserver.model.location.SearchLocation;
import com.stageserver.model.profile.MessageBox;
import com.stageserver.model.search.SearchData;
import com.stageserver.model.profile.Profile;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;
import org.springframework.transaction.ConfigurableTransactionManager;

import java.util.ArrayList;
import java.util.List;


@Setter
@Getter
@Node
public class User {
    @Id
    @GeneratedValue
    private String elementId;
    @Setter
    private String firstName;
    @Getter
    private String lastName;

    private String email;

    private String password;

    private String role;

    private boolean isEnabled = false;

    private boolean twoFaEnabled;

    private String phoneNumber;

    private boolean socialLoginUser;

    @Relationship(value = "HAS", direction = Relationship.Direction.OUTGOING)
    private List<Profile> profileList = new ArrayList<>();

    @Relationship(value = "HOLDS", direction = Relationship.Direction.OUTGOING)
    private List<JwtToken> jwtTokenList = new ArrayList<>();

    @Relationship(value = "HAS_SEARCH_DATA", direction = Relationship.Direction.OUTGOING)
    private List<SearchData> searchDataList = new ArrayList<>();

    private List<String> favouriteActProfiles = new ArrayList<>();

    private List<String> favouriteEventList = new ArrayList<>();

    private List<String> recentlyVisitedProfiles = new ArrayList<>();

    private List<String> blockedProfiles = new ArrayList<>();

    @Relationship(value = "HAS_SEARCH_LOCATION", direction = Relationship.Direction.OUTGOING)
    private SearchLocation searchLocation;

    @Relationship(value = "HAS_MESSAGE_BOX", direction = Relationship.Direction.OUTGOING)
    private MessageBox messageBox = new MessageBox();
//
//    @Relationship(value = "HAS_EVENTS", direction = Relationship.Direction.OUTGOING)
//    private List<Event> eventList = new ArrayList<>();

    @Relationship(value = "IS_LOCATED_AT", direction = Relationship.Direction.OUTGOING)
    private Location location;

    private String firstAlternativePhoneNumber;

    private String secondAlternativePhoneNumber;

    @Relationship(value = "INITIATED", direction = Relationship.Direction.OUTGOING)
    private List<Contract> initiatedContractList = new ArrayList<>();

    @Relationship(value = "RECEIVED", direction = Relationship.Direction.OUTGOING)
    private List<Contract> receivedContractList = new ArrayList<>();

    @Relationship(value = "HAS_GS_MESSAGE", direction = Relationship.Direction.OUTGOING)
    private List<GoodsAndServicesMessage> goodsAndServicesMessages = new ArrayList<>();
}
