package com.stageserver.model.login;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.Calendar;
import java.util.Date;

@Node
@NoArgsConstructor
@Getter
@Setter
public class VerificationToken {
    @Id
    @GeneratedValue
    private String elementId;

    private String token;
    private Date expirationTime;

    @Relationship(type = "VERIFIES",direction= Relationship.Direction.INCOMING)
    private User user;
    private int maxExpiryTime;

    public VerificationToken(String token, User user, int expTime) {
        super();
        this.token = token;
        this.user = user;
        this.maxExpiryTime = expTime;
        this.expirationTime = this.getTokenExpirationTime();
    }

    public VerificationToken(String token) {
        super();
        this.token = token;
        this.expirationTime = this.getTokenExpirationTime();
    }

    private Date getTokenExpirationTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(new Date().getTime());
        calendar.add(Calendar.MINUTE, maxExpiryTime);
        return new Date(calendar.getTime().getTime());
    }
}
