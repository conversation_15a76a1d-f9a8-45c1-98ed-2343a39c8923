package com.stageserver.model.payment;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

@Setter
@Getter
@Node
public class StripePaymentStatus {

    @Id
    @GeneratedValue
    private String elementId;

    private String sessionId;

    private boolean paymentSuccess;

    private String contractId;

    private String orderId;

    private double amountPaid;

    private String userEmail;

    private String currency;
}
