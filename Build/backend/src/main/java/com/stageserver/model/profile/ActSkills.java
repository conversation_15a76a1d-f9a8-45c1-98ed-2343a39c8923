package com.stageserver.model.profile;

import com.stageserver.model.supported.MusicGenre;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.List;

@NoArgsConstructor
@Getter
@Setter
@Node
public class ActSkills {

    @Id
    @GeneratedValue
    private String elementId;

    @Relationship(type = "HAS_ENTERTAINMENT_TYPE", direction = Relationship.Direction.OUTGOING)
    private EntertainmentType entertainmentType = new EntertainmentType();

    @Relationship(type = "HAS_MUSIC_GENRE", direction = Relationship.Direction.OUTGOING)
    private List<MusicGenre> musicGenreList;

}
