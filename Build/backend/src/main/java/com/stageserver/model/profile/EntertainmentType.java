package com.stageserver.model.profile;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.ArrayList;
import java.util.List;

@NoArgsConstructor
@Getter
@Setter
@Node
public class EntertainmentType {

    @Id
    @GeneratedValue
    private String elementId;

    private String name;

    private String iconUrl;

    @Relationship(type = "HAS_MEMBER", direction = Relationship.Direction.OUTGOING)
    List<EntertainmentTypeMember> members = new ArrayList<>();

    private Translations translations;

    public String getLocalizedName() {
        return translations.getNameForLocaleLanguage(LocaleContextHolder.getLocale().getLanguage());
    }
}
