package com.stageserver.model.profile;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

@NoArgsConstructor
@Getter
@Setter
@Node
public class EntertainmentTypeMember {
    @Id
    @GeneratedValue
    private String elementId;

    private String name;

    @Relationship(type = "HAS_TRANSLATIONS", direction = Relationship.Direction.OUTGOING)
    private Translations translations;

    public String getLocalizedName() {
        return translations.getNameForLocaleLanguage(LocaleContextHolder.getLocale().getLanguage());
    }
}
