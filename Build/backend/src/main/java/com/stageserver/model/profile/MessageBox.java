package com.stageserver.model.profile;

import com.stageserver.dto.IM.InstantMessageDto;
import com.stageserver.model.IM.InstantMessage;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.util.ArrayList;
import java.util.List;


@NoArgsConstructor
@Getter
@Setter
@Node
public class MessageBox {

    @Id
    @GeneratedValue
    private String elementId;

    private List<InstantMessage> receivedMessages = new ArrayList<>();

    private List<InstantMessage> sentMessages = new ArrayList<>();
}
