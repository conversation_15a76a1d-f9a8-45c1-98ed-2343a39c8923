package com.stageserver.model.profile;


import com.stageserver.model.common.ProfileType;
import com.stageserver.model.distribution.Distribution;
import com.stageserver.model.event.SpecialEvent;
import com.stageserver.model.feedback.FeedbackMsg;
import com.stageserver.model.location.Location;
import com.stageserver.model.common.ProfileStatus;
import com.stageserver.model.schedule.ScheduleTime;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.*;

import java.util.ArrayList;
import java.util.List;

@NoArgsConstructor
@Getter
@Setter
@Node
public class Profile {
    @Id
    @GeneratedValue
    private String elementId;

    private String profileRole;

    private String profileId;

    private String profileName;

    private String profileEmail;

    private int numMembers;

    private boolean owner;

    private boolean useMyEmail;

    private boolean email;

    private List<String> performanceLanguages;

    private List<String> communicationLanguages;

    private String preferredLanguage;

    @Property
    private ProfileStatus status;

    private ProfileType profileType;

    private int numberOfFollowers;

    private double gigsPerMonth;

    private double averageGigsPrice;

    private int numberOfGigs;

    private int numberOfBookings;

    private double numberOfBookingsPerMonth;

    private double averageBookingPrice;

    @Relationship(type = "HAS_VIRTUAL_CONTACT", direction = Relationship.Direction.OUTGOING)
    private VirtualContact virtualContact;

    @Relationship(type = "IS_LOCATED_AT", direction = Relationship.Direction.OUTGOING)
    private Location location;

    @Relationship(type = "HAS_INFO", direction = Relationship.Direction.OUTGOING)
    private ProfileInfo profileInfo;

    @Relationship(type = "HAS_SKILLS", direction = Relationship.Direction.OUTGOING)
    private ActSkills actSkills;

    @Relationship(type = "HAS_MEDIA", direction = Relationship.Direction.OUTGOING)
    private ProfileMedia profileMedia;

    @Relationship(type = "HAS_PAYMENTS", direction = Relationship.Direction.OUTGOING)
    private ProfilePayments profilePayments;

    @Relationship(type = "HAS_SCHEDULE", direction = Relationship.Direction.OUTGOING)
    private List<ScheduleTime> scheduleList;

    @Relationship(type = "HAS_DISTRIBUTION", direction = Relationship.Direction.OUTGOING)
    private Distribution distribution = new Distribution();

    @Relationship(type = "PROVIDED", direction = Relationship.Direction.OUTGOING)
    private List<FeedbackMsg> providedFeedbacks = new ArrayList<>();

    @Relationship(type = "RECEIVED", direction = Relationship.Direction.INCOMING)
    private List<FeedbackMsg> receivedFeedbacks = new ArrayList<>();

    @Relationship(type = "HAS_ACT_RATING", direction = Relationship.Direction.OUTGOING)
    private ProfileRating profileRating = new ProfileRating();

    @Relationship(type = "HAS_MESSAGE_BOX", direction = Relationship.Direction.OUTGOING)
    private MessageBox messageBox = new MessageBox();

    @Relationship(type = "HAS_SPECIAL_EVENTS", direction = Relationship.Direction.OUTGOING)
    private List<SpecialEvent> specialEvents = new ArrayList<>();
}
