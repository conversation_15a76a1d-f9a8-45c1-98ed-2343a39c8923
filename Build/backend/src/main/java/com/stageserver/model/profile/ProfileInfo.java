package com.stageserver.model.profile;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.util.List;

@NoArgsConstructor
@Getter
@Setter
@Node
public class ProfileInfo {

    @Id
    @GeneratedValue
    private String elementId;

    private String bio;

    private boolean suitableForChildren;

    private boolean suitableForAdultsOnly;

    private List<String> socialMediaLinks;

    private WeeklyWorkingHours weeklyWorkingHours;
}

