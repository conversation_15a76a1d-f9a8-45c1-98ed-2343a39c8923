package com.stageserver.model.profile;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.ArrayList;
import java.util.List;

@NoArgsConstructor
@Getter
@Setter
@Node
public class ProfileMedia {
    @Id
    @GeneratedValue
    private String elementId;

    private List<String> imageUrls;

    private Long totalImageSize;

    private List<String> videoUrls;

    private List<String> audioUrls;

    @Relationship(type = "HAS_RIDER_DETAILS", direction = Relationship.Direction.OUTGOING)
    private List<RiderDetails> rideDetailsList = new ArrayList<>();

}
