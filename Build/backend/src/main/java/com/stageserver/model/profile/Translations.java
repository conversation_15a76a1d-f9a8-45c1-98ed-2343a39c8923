package com.stageserver.model.profile;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

@NoArgsConstructor
@Getter
@Setter
@Node
public class Translations {
    @Id
    @GeneratedValue
    private String elementId;

    private String es;

    private String en;

    private String fr;


    public String getNameForLocaleLanguage(String lang) {
        return switch (lang) {
            case "es" -> es;
            case "en" -> en;
            case "fr" -> fr;
            default -> en;
        };
    }
}
