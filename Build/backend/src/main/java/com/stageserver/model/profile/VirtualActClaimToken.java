package com.stageserver.model.profile;

import com.stageserver.model.common.SystemUser;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.Calendar;
import java.util.Date;

@Slf4j
@Node
@NoArgsConstructor
@Getter
@Setter
public class VirtualActClaimToken {
    @Id
    @GeneratedValue
    private String elementId;

    private String token;

    private Date expirationTime;

    private int expiryTime;

    private String contactEmail;

    private String profileId;

    public VirtualActClaimToken(String profileId, String email, String token, SystemUser user, int expiryTime) {
        super();
        this.token = token;
        this.expiryTime = expiryTime;
        this.expirationTime = this.getTokenExpirationTime();
        this.contactEmail = email;
        this.profileId = profileId;
    }

    private Date getTokenExpirationTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(new Date().getTime());
        calendar.add(Calendar.MINUTE, expiryTime);
        return new Date(calendar.getTime().getTime());
    }
}

