package com.stageserver.model.profile;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

@NoArgsConstructor
@Getter
@Setter
@Node
public class VirtualContact {
    @Id
    @GeneratedValue
    private String elementId;

    private String contactName;

    private String contactEmail;

    private String contactPhone;
}
