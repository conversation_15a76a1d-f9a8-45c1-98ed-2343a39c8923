package com.stageserver.model.profile;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.List;

@NoArgsConstructor
@Getter
@Setter
@Node
public class WeeklyWorkingHours {

    @Id
    @GeneratedValue
    private String elementId;

    @Relationship(type = "HAS_WORKING_HOURS", direction = Relationship.Direction.OUTGOING)
    private List<WorkingHours> workingHoursList;

}
