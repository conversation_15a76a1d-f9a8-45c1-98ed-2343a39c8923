package com.stageserver.model.profile;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.time.ZonedDateTime;

@NoArgsConstructor
@Getter
@Setter
@Node
public class WorkingHours {

    @Id
    @GeneratedValue
    private String elementId;

    public enum Day {
        MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY
    }
    private Day day;

    private ZonedDateTime startTime;

    private ZonedDateTime endTime;
}
