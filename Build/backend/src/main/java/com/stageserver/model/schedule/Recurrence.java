package com.stageserver.model.schedule;

import com.stageserver.dto.schedule.RecurrenceType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.*;

import java.time.DayOfWeek;
import java.time.Month;
import java.util.List;

@NoArgsConstructor
@Getter
@Setter
@Node
public class Recurrence {

    @Id
    @GeneratedValue
    private String elementId;

    private RecurrenceType recurrenceType;

    private int interval;

    private int count;

    private String endDate;

    private List<DayOfWeek> daysOfWeek;

    private Integer daysOfMonth;

    private List<Month> monthsOfYear;

    @Relationship(type="RECURRENCE_END_TYPE", direction = Relationship.Direction.OUTGOING)
    private RecurrenceEndType recurrenceEndType;
}
