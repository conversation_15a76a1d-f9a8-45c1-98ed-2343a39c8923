package com.stageserver.model.schedule;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.time.ZonedDateTime;

@NoArgsConstructor
@Getter
@Setter
@Node
public class RecurrenceEndType {

    @Id
    @GeneratedValue
    private String elementId;

    private boolean never;

    private ZonedDateTime endDate;

    private int occurrences;
}
