package com.stageserver.model.schedule;

import com.stageserver.dto.schedule.ScheduleTimeDto;
import com.stageserver.dto.schedule.ScheduleType;
import com.stageserver.model.common.ProfileType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.*;

import java.time.Instant;
import java.time.ZonedDateTime;

@NoArgsConstructor
@Getter
@Setter
@Node
public class ScheduleTime {

    @Id
    @GeneratedValue
    private String elementId;

    private String scheduleId;

    private ProfileType profileType;

    private String scheduleName;

    private String scheduleDescription;

    private ScheduleType scheduleType;

    String timeZone;

    ZonedDateTime startDate;

    ZonedDateTime startTime;

    ZonedDateTime endDate;

    ZonedDateTime endTime;

    boolean recurring;

    @Relationship(type="RECURRENCE", direction = Relationship.Direction.OUTGOING)
    Recurrence recurrence;

    public boolean isOverlapping(ScheduleTimeDto scheduleTime) {
        if (this.startDate == null || this.endDate == null ||
                scheduleTime.getStartDate() == null || scheduleTime.getEndDate() == null) {
            return false;
        }

        Instant thisStart = this.startDate.toInstant();
        Instant thisEnd = this.endDate.toInstant();
        Instant otherStart = scheduleTime.getStartDate().toInstant();
        Instant otherEnd = scheduleTime.getEndDate().toInstant();

        return thisStart.isBefore(otherEnd) && otherStart.isBefore(thisEnd);
    }

    public ScheduleTime cloneWithoutRecurrence() {
        ScheduleTime copy = new ScheduleTime();
        copy.setScheduleName(this.getScheduleName());
        copy.setScheduleDescription(this.getScheduleDescription());
        copy.setProfileType(this.getProfileType());
        copy.setScheduleType(this.getScheduleType());
        copy.setTimeZone(this.getTimeZone());
        return copy;
    }

}
