package com.stageserver.model.search;

import com.stageserver.dto.search.SearchDataDto;
import com.stageserver.dto.search.SearchDateDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.time.LocalDate;
import java.util.List;

@NoArgsConstructor
@Getter
@Setter
@Node
public class SearchData {

    @Id
    @GeneratedValue
    private String elementId;

    private String searchName;

    private List<String> searchStrings;

    private String countryName;

    private String stateName;

    private String cityName;

    private SearchDateDto.SearchDateType searchDateType;

    private LocalDate startDate;

    private LocalDate endDate;

    private List<String> entertainmentTypeList;

    private List<String> musicGenreList;

    private double overallRating;

    private int numberOfRatings;

    private double professionalismRating;

    private double entertainmentValueRating;

    private double drawRating;

    private boolean isGoldBannerMember;

    private double longitude;

    private double latitude;

    private int distance;

    private SearchDataDto.SearchType searchType;

    @Override
    public String toString() {
        return "SearchData{" +
                "countryName='" + countryName + '\'' +
                ", stateName='" + stateName + '\'' +
                ", cityName='" + cityName + '\'' +
                ", distance=" + distance +
                '}';
    }
}
