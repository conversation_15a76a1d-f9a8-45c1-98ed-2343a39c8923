package com.stageserver.model.supported;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.util.List;

@NoArgsConstructor
@Getter
@Setter
@Node
public class MusicGenre {
    @Id
    @GeneratedValue
    private String elementId;

    private String name;

    private String iconUrl;

    private List<String> members;
}
