package com.stageserver.model.supported;

import com.stageserver.model.profile.EntertainmentType;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.List;

@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
@Node
public class SupportedEntertainmentTypes {

        @Id
        @GeneratedValue
        private String elementId;

        private String version;

        @Relationship(type = "HAS_ENTERTAINMENT_TYPE", direction = Relationship.Direction.OUTGOING)
        private List<EntertainmentType> entertainmentTypeList;
}
