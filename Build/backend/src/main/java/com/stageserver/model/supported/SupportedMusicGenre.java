package com.stageserver.model.supported;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.lang.reflect.Field;
import java.util.List;

@Slf4j
@NoArgsConstructor
@Getter
@Setter
@Node
public class SupportedMusicGenre {

    @Id
    @GeneratedValue
    private String elementId;

    private String version;

    private String locale;

    @Relationship(type = "HAS_GENRE", direction = Relationship.Direction.OUTGOING)
    private List<MusicGenre> genreList;


    public void test() throws IllegalAccessException {
        for (Field f : getClass().getDeclaredFields()) {
            String name = f.getName();
            Object value = f.get(this);
            if(f.getType() == List.class) {
                log.warn("List type field: " + name + " Name: " + f.getName());

            }

        }
    }
}
