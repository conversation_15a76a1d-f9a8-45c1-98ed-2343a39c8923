package com.stageserver.model.supported;

import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.util.List;

@Slf4j
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Node
public class SupportedOptions {
    @Id
    @GeneratedValue
    private String elementId;

    private String version;

    private String locale;

    private List<String> currencies;

    private List<String> profiles;

    private List<String> paymentMethods;

    private List<String> paymentOptions;

}
