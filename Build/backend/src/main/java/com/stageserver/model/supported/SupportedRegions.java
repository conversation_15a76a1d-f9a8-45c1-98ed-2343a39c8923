package com.stageserver.model.supported;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stageserver.model.location.Country;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Node
public class SupportedRegions {
    @Id
    @GeneratedValue
    private String elementId;

    private String version;

    private String locale;

    @JsonProperty("countries")
    @Relationship(type = "HAS_COUNTRY", direction = Relationship.Direction.OUTGOING)
    private List<Country> countries;
}
