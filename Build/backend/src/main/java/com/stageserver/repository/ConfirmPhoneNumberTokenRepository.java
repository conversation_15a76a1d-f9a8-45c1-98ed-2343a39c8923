package com.stageserver.repository;

import com.stageserver.model.login.ConfirmPhoneNumberToken;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ConfirmPhoneNumberTokenRepository extends Neo4jRepository<ConfirmPhoneNumberToken, String> {

    ConfirmPhoneNumberToken findByToken(String token);

    @Query("MATCH(u:User{email:$email})-[r:HAS_CONFIRM_PHONE_TOKEN]-(f:ConfirmPhoneNumberToken) RETURN f")
    List<ConfirmPhoneNumberToken> findAllTokensForUserByEmail(String email);

    @Query("MATCH(f:ConfirmPhoneNumberToken) WHERE f.token=$token DETACH DELETE f")
    void deleteExpired(String token);
}
