package com.stageserver.repository;

import com.stageserver.model.profile.Profile;
import com.stageserver.model.common.ProfileStatus;
import com.stageserver.model.common.ProfileType;
import com.stageserver.model.search.SearchData;
import lombok.extern.slf4j.Slf4j;
import org.neo4j.driver.Value;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.neo4j.core.Neo4jClient;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class CustomProfileRepositoryImpl implements CustomProfileRepository {

    @Autowired
    private Neo4jClient neo4jClient;

    public Page<Profile> findAllProfilesByTypeForUserX(String email, ProfileType profileType, List<String> searchStrings, Pageable pageable) {
        StringBuilder query = buildInitialQueryForUser(profileType, email);
        if (query == null) return Page.empty();  // Invalid profile type
        log.info("findAllProfilesByTypeForUserX");
        Map<String, Object> params = new HashMap<>();
        params.put("email", email);

        long totalElements = executeCountQuery(query, params);
        log.info("Total elements for User Profiles with primary filters: {}", totalElements);
        if (searchStrings != null && !searchStrings.isEmpty()) {
            appendSearchStringsSecondaryFilter(query, params, searchStrings);
        }
        appendPagination(query, params, pageable);

        List<Profile> profiles = executeProfileQuery(query, params);
        return new PageImpl<>(profiles, pageable, totalElements);
    }

    public Page<Profile> findProfilesBySearchData(SearchData searchData, ProfileType profileType, Pageable pageable) {
        StringBuilder query = buildInitialQuery(profileType);
        if (query == null) return Page.empty();  // Invalid profile type

        log.info("findProfilesBySearchData: searchData");
        Map<String, Object> params = new HashMap<>();
        if(searchData.getDistance() > 0) {
            appendDistanceFilter(query, params, searchData);
        }
        else {
            appendLocationFilters(query, params, searchData);
        }
        appendOverallRatingFilter(query, params, searchData);

        if(profileType == ProfileType.ACT_PROFILE) {
            appendConditionalEntertainmentAndMusicFilter(query, params, searchData);
        }

        long totalElements = executeCountQuery(query, params);
        log.info("Total elements after primary filters: {}", totalElements);

        if (searchData.getSearchStrings() != null && !searchData.getSearchStrings().stream().allMatch(String::isEmpty)) {
            appendSearchStringsSecondaryFilter(query, params, searchData.getSearchStrings());
        }
        appendPagination(query, params, pageable);

        List<Profile> profiles = executeProfileQuery(query, params);
        return new PageImpl<>(profiles, pageable, totalElements);
    }

    public Page<Profile> findOthersProfilesBySearchData(String email, SearchData searchData, ProfileType profileType, Pageable pageable) {
        StringBuilder query = buildInitialQueryOtherProfiles(profileType, email);
        if (query == null) return Page.empty();  // Invalid profile type
        log.info("findOthersProfilesBySearchData: searchData");
        Map<String, Object> params = new HashMap<>();
        params.put("email", email); // Add email to params

        if((searchData.getCountryName() != null) || (searchData.getStateName() != null) || (searchData.getCityName() != null)) {
            if (searchData.getDistance() > 0) {
                appendDistanceFilter(query, params, searchData);
            } else {
                appendLocationFilters(query, params, searchData);
            }
        }

        appendOverallRatingFilter(query, params, searchData);
        if(profileType == ProfileType.ACT_PROFILE) {
            appendConditionalEntertainmentAndMusicFilter(query, params, searchData);
        }

        long totalElements = executeCountQuery(query, params);
        log.info("findOthersProfilesBySearchData: Total elements after primary filters: {}", totalElements);

        // Apply secondary filters on the intermediate results
        if (searchData.getSearchStrings() != null && !searchData.getSearchStrings().isEmpty()) {
            appendSearchStringsSecondaryFilter(query, params, searchData.getSearchStrings());
        }
        appendPagination(query, params, pageable);

        List<Profile> profiles = executeProfileQuery(query, params);
        return new PageImpl<>(profiles, pageable, totalElements);
    }

    private StringBuilder buildInitialQueryOtherProfiles(ProfileType profileType, String email) {
        StringBuilder query = new StringBuilder();

        // Use parameterized email instead of injecting value directly
        query.append("MATCH (requestingUser:User {email: $email}) ");
        query.append("MATCH (u:Profile)-[:IS_LOCATED_AT]->(l:Location) ");

        switch (profileType) {
            case ACT_PROFILE:
                query.append("WHERE (");
                query.append(" (u.profileType IN ['ACT_PROFILE', 'VIRTUAL_ACT_PROFILE'] AND u.status = 'STATUS_PUBLISHED') ");
                query.append(" OR (u.profileType = 'VIRTUAL_ACT_PROFILE' AND (u)<-[:HAS_VIRTUAL_PROFILES]-(:SystemUser)) AND u.status = 'STATUS_PUBLISHED' ");
                query.append(") ");
                break;
            case VENUE_PROFILE:
                query.append("WHERE (");
                query.append(" (u.profileType IN ['VENUE_PROFILE', 'VIRTUAL_VENUE_PROFILE'] ");
                query.append(" AND u.status = 'STATUS_PUBLISHED' ");
                query.append(" AND EXISTS { MATCH (u)-[:HAS_PAYMENTS]->(:ProfilePayments) }) ");
                query.append(" OR (u.profileType = 'VIRTUAL_VENUE_PROFILE' ");
                query.append(" AND (u)<-[:HAS_VIRTUAL_PROFILES]-(:SystemUser)) AND u.status = 'STATUS_PUBLISHED' ");
                query.append(") ");
                break;
            default:
                log.info("buildInitialQueryOtherProfiles: Invalid profile type: {} returning empty list", profileType);
                return null;
        }

        // Exclude blocked profiles from result
        query.append("AND NOT u.profileId IN requestingUser.blockedProfiles ");

        return query;
    }

    private StringBuilder buildInitialQueryForUser(ProfileType profileType, String email) {
        StringBuilder query = new StringBuilder("MATCH (user:User {email: $email}) ");
        query.append("MATCH (u:Profile)<-[:HAS]-(user) ");

        switch (profileType) {
            case ACT_PROFILE:
                query.append("WHERE u.profileType IN ['ACT_PROFILE', 'VIRTUAL_ACT_PROFILE'] ");
                break;
            case VENUE_PROFILE:
                query.append("WHERE u.profileType IN ['VENUE_PROFILE', 'VIRTUAL_VENUE_PROFILE'] ");
                break;
            default:
                log.info("buildInitialQueryForUser: Invalid profile type: {} returning empty list", profileType);
                return null;
        }

        // Add block filter
        query.append("AND NOT u.profileId IN user.blockedProfiles ");

        return query;
    }

    private StringBuilder buildInitialQuery(ProfileType profileType) {
        StringBuilder query;
        switch (profileType) {
            case ACT_PROFILE:
                query = new StringBuilder("MATCH (u:Profile)-[:IS_LOCATED_AT]->(l:Location) WHERE u.profileType IN ['ACT_PROFILE', 'VIRTUAL_ACT_PROFILE'] AND u.status = 'STATUS_PUBLISHED'");
                break;
            case VENUE_PROFILE:
                query = new StringBuilder("MATCH (u:Profile)-[:IS_LOCATED_AT]->(l:Location) " +
                        "WHERE u.profileType IN ['VENUE_PROFILE', 'VIRTUAL_VENUE_PROFILE'] " +
                        "AND u.status = 'STATUS_PUBLISHED' " +
                        "AND EXISTS { " +
                        "  MATCH (u)-[:HAS_PAYMENTS]->(p:ProfilePayments) }");
                break;
            default:
                log.info("Invalid profile type: {} returning empty list", profileType);
                return null;
        }
        return query;
    }

    private void appendDistanceFilter(StringBuilder query, Map<String, Object> params, SearchData distanceFilter) {

        if (!query.toString().contains("WHERE")) {
            query.append(" WHERE (");
        } else {
            query.append(" AND (");
        }

        // Add distance condition
        query.append(" point.distance(point({longitude: l.longitude, latitude: l.latitude}), ")
                .append(" point({longitude: $longitude, latitude: $latitude})) <= $distance )");

        // Bind parameters for distance and coordinates
        params.put("longitude", distanceFilter.getLongitude());
        params.put("latitude", distanceFilter.getLatitude());
        params.put("distance", distanceFilter.getDistance() * 1000); // Convert KM to meters
    }

    private void appendLocationFilters(StringBuilder query, Map<String, Object> params, SearchData searchData) {
        boolean hasWhere = query.toString().contains("WHERE");

        if (isNotEmpty(searchData.getCountryName())) {
            query.append(hasWhere ? " AND " : " WHERE ");
            query.append("l.country = $countryName");
            params.put("countryName", searchData.getCountryName());
            hasWhere = true;
        }
        if (isNotEmpty(searchData.getStateName())) {
            query.append(hasWhere ? " AND " : " WHERE ");
            query.append("l.state = $stateName");
            params.put("stateName", searchData.getStateName());
            hasWhere = true;
        }
        if (isNotEmpty(searchData.getCityName())) {
            query.append(hasWhere ? " AND " : " WHERE ");
            query.append("l.city = $cityName");
            params.put("cityName", searchData.getCityName());
        }
    }

    private void appendConditionalEntertainmentAndMusicFilter(StringBuilder query, Map<String, Object> params, SearchData searchData) {
        var entertainmentList = searchData.getEntertainmentTypeList();
        var genreList = searchData.getMusicGenreList();

        if (entertainmentList == null || entertainmentList.isEmpty()) {
            return;
        }

        // Always start with EXISTS block for filtering relevant skills
        query.append(" AND EXISTS { ");
        query.append(" MATCH (u)-[:HAS_SKILLS]->(a:ActSkills)-[:HAS_ENTERTAINMENT_TYPE]->(e:EntertainmentType) ");
        query.append(" WHERE (");

        List<String> conditionList = new ArrayList<>();

        for (int i = 0; i < entertainmentList.size(); i++) {
            String entName = entertainmentList.get(i);
            String entParam = "entertainmentType" + i;
            params.put(entParam, entName);

            if ("Live Music".equalsIgnoreCase(entName) && genreList != null && !genreList.isEmpty()) {
                // Build EXISTS block for genre
                List<String> genreConditions = new ArrayList<>();
                for (int j = 0; j < genreList.size(); j++) {
                    String genreParam = "musicGenre" + j;
                    params.put(genreParam, genreList.get(j));
                    genreConditions.add("m.name = $" + genreParam);
                }
                conditionList.add("(e.name = $" + entParam + " AND EXISTS { MATCH (a)-[:HAS_MUSIC_GENRE]->(m:MusicGenre) WHERE " + String.join(" OR ", genreConditions) + " })");
            } else {
                conditionList.add("(e.name = $" + entParam + ")");
            }
        }

        query.append(String.join(" OR ", conditionList));
        query.append(") } "); // End EXISTS
    }

    private void appendOverallRatingFilter(StringBuilder query, Map<String, Object> params, SearchData searchData) {
        if (searchData.getOverallRating() > 0) {
            query.append(" MATCH (u)-[:HAS_ACT_RATING]->(r:ProfileRating) WHERE r.overallRating >= $overallRating");
            params.put("overallRating", searchData.getOverallRating());
        }
    }
    
    private void appendSearchStringsSecondaryFilter(StringBuilder query, Map<String, Object> params, List<String> searchStrings) {
        if (searchStrings != null && !searchStrings.isEmpty()) {
            query.append(" WITH u ")
                    .append(" OPTIONAL MATCH (u)-[:HAS_INFO]->(info:ProfileInfo) ")
                    .append(" OPTIONAL MATCH (u)-[:HAS_SKILLS]->(skills:ActSkills) ")
                    .append(" OPTIONAL MATCH (skills)-[:HAS_ENTERTAINMENT_TYPE]->(entertainmentType:EntertainmentType) ")
                    .append(" OPTIONAL MATCH (skills)-[:HAS_MUSIC_GENRE]->(musicGenre:MusicGenre) ")
                    .append(" WITH u, info, entertainmentType, musicGenre ");

            // Ensure WHERE clause is chained correctly
            query.append(" WHERE (");

            for (int i = 0; i < searchStrings.size(); i++) {
                if (i > 0) query.append(" OR ");
                query.append(" (")
                        .append(" toLower(info.bio) CONTAINS $searchFieldSearchString").append(i)
                        .append(" OR toLower(entertainmentType.name) CONTAINS $searchFieldSearchString").append(i)
                        .append(" OR toLower(musicGenre.name) CONTAINS $searchFieldSearchString").append(i)
                        .append(" OR toLower(u.profileName) CONTAINS $searchFieldSearchString").append(i)
                        .append(")");
                params.put("searchFieldSearchString" + i, searchStrings.get(i).toLowerCase());
            }

            query.append(") WITH DISTINCT u ");
        }
    }

    private long executeCountQuery(StringBuilder query, Map<String, Object> params) {
        String countQuery = "RETURN count(u) AS total";
        return neo4jClient.query(query.toString() + " " + countQuery)
                .bindAll(params)
                .fetchAs(Long.class)
                .one()
                .orElse(0L);
    }

    private void appendPagination(StringBuilder query, Map<String, Object> params, Pageable pageable) {
        query.append(" RETURN u SKIP $skip LIMIT $limit");
        params.put("skip", (int) pageable.getOffset());
        params.put("limit", pageable.getPageSize());
    }

    private List<Profile> executeProfileQuery(StringBuilder query, Map<String, Object> params) {
        logQueryWithParams(query, params);
        return neo4jClient.query(query.toString())
                .bindAll(params)
                .fetchAs(Profile.class)
                .mappedBy((typeSystem, record) -> mapProfileRecord(record))
                .all().stream().toList();
    }

    private Profile mapProfileRecord(org.neo4j.driver.Record record) {
        Profile profile = new Profile();
        profile.setProfileName(record.get("u").get("profileName").asString());
        profile.setProfileRole(record.get("u").get("profileRole").asString());
        profile.setStatus(ProfileStatus.valueOf(record.get("u").get("status").asString()));
        profile.setProfileType(ProfileType.valueOf(record.get("u").get("profileType").asString()));
        profile.setElementId(record.get("u").get("elementId").asString());
        profile.setProfileId(record.get("u").get("profileId").asString());
        profile.setProfileEmail(record.get("u").get("profileEmail").asString());
        profile.setCommunicationLanguages(record.get("u").get("communicationLanguages").asList(Value::asString));
        profile.setPerformanceLanguages(record.get("u").get("performanceLanguages").asList(Value::asString));
        profile.setPreferredLanguage(record.get("u").get("preferredLanguage").asString());
        profile.setNumMembers(record.get("u").get("numMembers").asInt());
        profile.setOwner(record.get("u").get("owner").asBoolean());
        profile.setUseMyEmail(record.get("u").get("useMyEmail").asBoolean());
        profile.setNumberOfFollowers(record.get("u").get("numberOfFollowers").asInt());
        return profile;
    }

    private boolean isNotEmpty(String value) {
        return value != null && !value.isEmpty();
    }

    private void logQueryWithParams(StringBuilder query, Map<String, Object> params) {
        String interpolatedQuery = query.toString();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            // Replace placeholders in the query with actual values (for logging purposes only)
            String value = entry.getValue() == null ? "null" : entry.getValue().toString();
            interpolatedQuery = interpolatedQuery.replace("$" + entry.getKey(), "'" + value + "'");
        }
        // Log the interpolated query
        log.info("Executing query: {}", interpolatedQuery);
    }

}
