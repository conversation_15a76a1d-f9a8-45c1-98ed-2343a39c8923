package com.stageserver.repository;

import com.stageserver.model.distribution.DistributionMember;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DistributionMemberRepository extends Neo4jRepository<DistributionMember, String>{
    @Query("MATCH (a:Profile{profileId:$profileId})-[r:HAS_DISTRIBUTION]-(d:Distribution)-[:HAS_MEMBER]-(m:DistributionMember) RETURN m")
    List<DistributionMember> findMembersByProfileId(String profileId);
}
