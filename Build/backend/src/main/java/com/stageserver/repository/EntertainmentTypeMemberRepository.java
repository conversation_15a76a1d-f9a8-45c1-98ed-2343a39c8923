package com.stageserver.repository;

import com.stageserver.model.profile.EntertainmentTypeMember;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EntertainmentTypeMemberRepository extends Neo4jRepository<EntertainmentTypeMember, String>{
    @Query("MATCH(a:Profile{profileId:$profileId})-[r:HAS_SKILLS]-(l:ActSkills)-[r2:HAS_ENTERTAINMENT_TYPE]-(l2:EntertainmentType)-[r3:HAS_MEMBER]-(l3:EntertainmentTypeMember) RETURN l3")
    Optional<List<EntertainmentTypeMember>> findMembersByProfileId(String profileId);
}
