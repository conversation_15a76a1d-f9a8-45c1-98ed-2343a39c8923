package com.stageserver.repository;

import com.stageserver.model.login.ForgotPasswordToken;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ForgotPasswordTokenRepository extends Neo4jRepository<ForgotPasswordToken, String> {
    ForgotPasswordToken findByToken(String token);

    @Query("MATCH(u:User{email:$email})-[:HAS_PASSWORD_TOKEN]-(f:ForgotPasswordToken) RETURN f")
    Optional<ForgotPasswordToken> findTokenForUserByEmail(@Param("email")String email);

    @Query("MATCH(f:ForgotPasswordToken) WHERE f.token=$token DETACH DELETE f")
    void deleteExpired(String token);

    @Query("MATCH(u:User{email:$email})-[r:HAS_PASSWORD_TOKEN]-(f:ForgotPasswordToken) RETURN f")
    List<ForgotPasswordToken> findAllTokensForUserByEmail(@Param("email")String email);
}
