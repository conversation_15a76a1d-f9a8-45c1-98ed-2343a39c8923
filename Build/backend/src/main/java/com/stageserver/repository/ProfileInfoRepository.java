package com.stageserver.repository;

import com.stageserver.model.profile.ProfileInfo;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ProfileInfoRepository extends Neo4jRepository<ProfileInfo, String> {
    @Query("MATCH(a:Profile{profileId:$profileId})-[r:HAS_INFO]-(l:ProfileInfo) RETURN l")
    Optional<ProfileInfo> findByProfileId(String profileId);
}
