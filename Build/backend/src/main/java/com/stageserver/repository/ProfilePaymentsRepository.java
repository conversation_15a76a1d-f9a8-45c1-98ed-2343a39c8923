package com.stageserver.repository;

import com.stageserver.model.profile.ProfilePayments;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ProfilePaymentsRepository extends Neo4jRepository <ProfilePayments, String>{

    @Query("MATCH(a:Profile{profileId:$profileId})-[r:HAS_PAYMENTS]-(m:ProfilePayments) RETURN m")
    Optional<ProfilePayments> findByProfileId(String profileId);
}
