package com.stageserver.repository;

import com.stageserver.model.location.SearchLocation;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SearchLocationRepository extends Neo4jRepository<SearchLocation, String>{
    @Query("MATCH(u:User{email:$email})-[:HAS_SEARCH_LOCATION]->(sl:SearchLocation) RETURN sl")
    Optional<SearchLocation> findSearchLocationByEmail(String email);

    @Query("MATCH(u:User{email:$email})-[:HAS_SEARCH_LOCATION]->(sl:SearchLocation) DETACH DELETE sl")
    void deleteDefaultLocationByEmail(String email);
}
