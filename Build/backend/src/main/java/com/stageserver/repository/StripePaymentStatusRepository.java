package com.stageserver.repository;

import com.stageserver.model.payment.StripePaymentStatus;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface StripePaymentStatusRepository extends Neo4jRepository<StripePaymentStatus, String> {

    StripePaymentStatus findBySessionId(String sessionId);

    StripePaymentStatus findByContractId(String contractId);
}
