package com.stageserver.repository;

import com.stageserver.model.supported.SupportedActRoles;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SupportedActRolesRepository extends Neo4jRepository<SupportedActRoles, String> {

    @Query("MATCH (n:SupportedActRoles) WHERE n.locale = $locale RETURN n")
    Optional<SupportedActRoles> findByLocale(String locale);
}
