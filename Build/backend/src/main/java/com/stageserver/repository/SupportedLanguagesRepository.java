package com.stageserver.repository;

import com.stageserver.model.supported.SupportedLanguages;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SupportedLanguagesRepository extends Neo4jRepository<SupportedLanguages, String> {

    @Query("MATCH (n:SupportedLanguages) WHERE n.locale = $locale RETURN n")
    Optional<SupportedLanguages> findByLocale(String locale);
}
