package com.stageserver.repository;

import com.stageserver.model.supported.SupportedMusicGenre;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;


@Repository
public interface SupportedMusicGenreRepository extends Neo4jRepository<SupportedMusicGenre, String> {

    @Query("MATCH (sg:SupportedMusicGenre {locale: $locale})-[:HAS_GENRE]->(g:MusicGenre) RETURN sg, COLLECT(g) AS genreList")
    Optional<SupportedMusicGenre> findByLocaleWithGenreList(String locale);
}
