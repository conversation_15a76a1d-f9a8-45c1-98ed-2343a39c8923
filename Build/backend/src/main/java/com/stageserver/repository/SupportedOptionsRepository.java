package com.stageserver.repository;

import com.stageserver.model.supported.SupportedOptions;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;

import java.util.Optional;

public interface SupportedOptionsRepository extends Neo4jRepository<SupportedOptions, String> {

    @Query("MATCH (n:SupportedOptions) WHERE n.locale = $locale RETURN n")
    Optional<SupportedOptions> findByLocale(String locale);
}
