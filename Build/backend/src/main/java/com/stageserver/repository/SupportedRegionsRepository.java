package com.stageserver.repository;

import com.stageserver.model.supported.SupportedRegions;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;

import java.util.Optional;

public interface SupportedRegionsRepository extends Neo4jRepository<SupportedRegions, String> {

    @Query("MATCH (n:SupportedRegions) WHERE n.locale = $locale RETURN n")
    Optional<SupportedRegions> findByLocale(String locale);
}
