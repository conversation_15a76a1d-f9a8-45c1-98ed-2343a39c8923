package com.stageserver.repository;

import com.stageserver.model.login.TwoFactorAuthToken;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TwoFactorAuthTokenRepository extends Neo4jRepository<TwoFactorAuthToken, String> {

    TwoFactorAuthToken findByToken(String token);

    @Query("MATCH(u:User{email:$email})-[r:HAS_TFA_TOKEN]-(f:TwoFactorAuthToken) RETURN f")
    List<TwoFactorAuthToken> findAllTokensForUserByEmail(String email);

    @Query("MATCH(u:User{email:$email})-[r:HAS_TFA_TOKEN]-(t:TwoFactorAuthToken) RETURN t")
    Optional<TwoFactorAuthToken> findTokenForUserByEmail(String email);

    @Query("MATCH(f:TwoFactorAuthToken) WHERE f.token=$token DETACH DELETE f")
    void deleteExpired(String token);
}
