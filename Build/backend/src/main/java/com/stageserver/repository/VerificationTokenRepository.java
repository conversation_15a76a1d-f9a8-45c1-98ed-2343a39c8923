package com.stageserver.repository;

import com.stageserver.model.login.VerificationToken;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface VerificationTokenRepository extends Neo4jRepository<VerificationToken, String> {
    VerificationToken findByToken(String token);

    @Query("MATCH(v:VerificationToken) WHERE v.token=$token DETACH DELETE v")
    void deleteExpired(String token);

    @Query("MATCH(u:User{email:$email})-[r:VERIFIES]-(v:VerificationToken) RETURN v")
    List<VerificationToken> findAllTokensForUserByEmail(@Param("email") String email);

    @Query("MATCH(u:User{email:$email})-[r:VERIFIES]-(v:VerificationToken) RETURN v")
    Optional<VerificationToken> findTokenForUserByEmail(@Param("email") String email);
}
