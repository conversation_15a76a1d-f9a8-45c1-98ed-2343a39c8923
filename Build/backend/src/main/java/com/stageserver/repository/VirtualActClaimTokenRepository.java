package com.stageserver.repository;

import com.stageserver.model.profile.VirtualActClaimToken;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;

import java.util.List;
import java.util.Optional;

public interface VirtualActClaimTokenRepository extends Neo4jRepository<VirtualActClaimToken, String>{
    @Query("MATCH (u:SystemUser)-[:HAS_VIRTUAL_ACT_CLAIM_TOKENS]-(v:VirtualActClaimToken) WHERE v.token=$token RETURN v")
    Optional<VirtualActClaimToken> findByTokenString(String token);

    @Query("MATCH (su:SystemUser)-[:HAS_VIRTUAL_ACT_CLAIM_TOKENS]-(t:VirtualActClaimToken) RETURN t")
    Optional<List<VirtualActClaimToken>> getVirtualTokensForSystemUser();

    @Query("MATCH (su:SystemUser)-[:HAS_VIRTUAL_ACT_CLAIM_TOKENS]-(t:VirtualActClaimToken) WHERE t.contactEmail=$email RETURN t")
    List<VirtualActClaimToken> findAllActsByContactEmail(String email);


}
