package com.stageserver.repository;

import com.stageserver.model.profile.VirtualContact;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;

import java.util.Optional;

public interface VirtualContactRepository extends Neo4jRepository<VirtualContact, String> {
    @Query("MATCH (a:Profile{profileId:$profileId})-[r:HAS_VIRTUAL_CONTACT]-(v:VirtualContact) RETURN v")
    Optional<VirtualContact> findByActProfileId(String profileId);
}
