package com.stageserver.repository;

import com.stageserver.model.profile.VirtualVenueClaimToken;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;

import java.util.List;
import java.util.Optional;

public interface VirtualVenueClaimTokenRepository extends Neo4jRepository<VirtualVenueClaimToken, String> {
    @Query("MATCH (u:SystemUser)-[:HAS_VIRTUAL_VENUE_CLAIM_TOKENS]-(v:VirtualVenueClaimToken) WHERE v.token=$token RETURN v")
    Optional<VirtualVenueClaimToken> findByTokenString(String token);

    @Query("MATCH (su:SystemUser)-[:HAS_VIRTUAL_VENUE_CLAIM_TOKENS]-(t:VirtualVenueClaimToken) RETURN t")
    Optional<List<VirtualVenueClaimToken>> getVirtualTokensForSystemUser();

    @Query("MATCH (su:SystemUser)-[:HAS_VIRTUAL_VENUE_CLAIM_TOKENS]-(t:VirtualVenueClaimToken) WHERE t.contactEmail=$email RETURN t")
    Optional<VirtualVenueClaimToken> findByContactEmail(String email);

    @Query("MATCH (su:SystemUser)-[:HAS_VIRTUAL_VENUE_CLAIM_TOKENS]-(t:VirtualVenueClaimToken) WHERE t.contactEmail=$email RETURN t")
    List<VirtualVenueClaimToken> findAllVenuesByContactEmail(String email);
}
