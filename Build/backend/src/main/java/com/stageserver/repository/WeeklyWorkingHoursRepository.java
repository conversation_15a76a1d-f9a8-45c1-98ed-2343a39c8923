package com.stageserver.repository;

import com.stageserver.model.profile.WeeklyWorkingHours;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface WeeklyWorkingHoursRepository extends Neo4jRepository<WeeklyWorkingHours, String>{
    @Query("MATCH (a:Profile{profileId:$profileId})-[r1:HAS_INFO]-(i:ProfileInfo)-[r:WEEKLY_WORKING_HOURS]-(v:WeeklyWorkingHours) RETURN v")
    Optional<WeeklyWorkingHours> findWeeklyWorkingHoursByProfileId(String profileId);

}
