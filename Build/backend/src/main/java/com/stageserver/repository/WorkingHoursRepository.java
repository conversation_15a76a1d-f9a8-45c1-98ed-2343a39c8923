package com.stageserver.repository;

import com.stageserver.model.profile.WorkingHours;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface WorkingHoursRepository extends Neo4jRepository<WorkingHours, String> {

    @Query("MATCH (a:Profile{profileId:$profileId})-[r1:HAS_INFO]-(i:ProfileInfo)-[r:WEEKLY_WORKING_HOURS]-(v:WeeklyWorkingHours) RETURN v")
    List<WorkingHours> getWorkingHoursProfileId(String profileId);

    @Query("MATCH (a:Profile{profileId:$profileId})-[r1:HAS_INFO]-(i:ProfileInfo)-[r:WEEKLY_WORKING_HOURS]-(v:WeeklyWorkingHours)-[:HAS_WORKING_HOURS]-(w:WorkingHours) RETURN w")
    List<WorkingHours> getWorkingHoursList(String profileId);
}
