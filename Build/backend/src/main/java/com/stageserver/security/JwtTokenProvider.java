package com.stageserver.security;

import com.stageserver.exceptions.RestApiException;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.UnsupportedJwtException;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.security.Key;
import java.util.Date;

@Component
public class JwtTokenProvider {

    @Value("${app.jwt-secret}")
    private String jwtSecret;

    @Value("${app.jwt-expiration-milliseconds}")
    private long jwtExpirationData;

    public String generateToken(Authentication authentication) {
        String userName = authentication.getName();
        Date currentDate = new Date();
        Date expirationDate = new Date(currentDate.getTime() + jwtExpirationData);
        return Jwts.builder()
                .subject(userName)
                .issuedAt(currentDate)
                .expiration(expirationDate)
                .signWith(getKey()).compact();
    }

    public String generateToken(String email) {
        Date currentDate = new Date();
        Date expirationDate = new Date(currentDate.getTime() + jwtExpirationData);

        return Jwts.builder()
                .subject(email)  // Store email instead of username
                .issuedAt(currentDate)
                .expiration(expirationDate)
                .signWith(getKey())
                .compact();
    }

    private Key getKey() {
        return Keys.hmacShaKeyFor(Decoders.BASE64.decode(jwtSecret));
    }

    public String getUserNameFromToken(String token) {
        return Jwts.parser()
                .verifyWith((SecretKey)getKey())
                .build()
                .parseSignedClaims(token)
                .getPayload()
                .getSubject();
    }

    public boolean validateToken(String token) {
        try {
            Jwts.parser()
                    .verifyWith((SecretKey) getKey())
                    .build()
                    .parse(token);
            return true;
        }
        catch(MalformedJwtException malformedJwtException){
            throw new RestApiException(HttpStatus.BAD_REQUEST, "Invalid Token");
        }
        catch(ExpiredJwtException expiredJwtException) {
            throw new RestApiException(HttpStatus.BAD_REQUEST, "Expired JWT token");
        }
        catch(UnsupportedJwtException unsupportedJwtException) {
            throw new RestApiException(HttpStatus.BAD_REQUEST, "Unsupported JWT exception");
        }
        catch(IllegalArgumentException illegalArgumentException) {
            throw new RestApiException(HttpStatus.BAD_REQUEST, "Null or empty");
        }
    }
}
