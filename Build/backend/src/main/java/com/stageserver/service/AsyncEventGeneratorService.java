package com.stageserver.service;

import com.stageserver.dto.event.SpecialEventDto;
import com.stageserver.dto.mapper.SpecialEventDtoMapper;
import com.stageserver.model.event.SpecialEvent;
import com.stageserver.model.profile.Profile;
import com.stageserver.model.schedule.ScheduleTime;
import com.stageserver.repository.ProfileRepository;
import com.stageserver.repository.SpecialEventRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class AsyncEventGeneratorService {

    @Autowired
    private SpecialEventRepository specialEventRepository;

    @Autowired
    private UtilityService utilityService;

    @Autowired
    private ProfileRepository profileRepository;

    @Async
    public void generateFutureEventsAsync(
            String profileId,
            SpecialEventDto dto,
            List<ScheduleTime> remainingOccurrences,
            String baseEventId
    ) {
        log.info("Async generation started for {} additional events (profileId: {}, baseEventId: {})",
                remainingOccurrences.size(), profileId, baseEventId);

        Optional<Profile> optionalProfile = profileRepository.findByProfileId(profileId);
        if (optionalProfile.isEmpty()) {
            log.warn("Profile not found for profileId: {}. Aborting async event generation.", profileId);
            return;
        }

        Profile profile = optionalProfile.get();
        List<SpecialEvent> events = new ArrayList<>();
        int count = 0;

        for (ScheduleTime occurrence : remainingOccurrences) {
            SpecialEvent event = buildEventFromSchedule(profileId, dto, occurrence, baseEventId);
            events.add(event);
            profile.getSpecialEvents().add(event); // ✅ Link to profile
            count++;

            if (count % 10 == 0) {
                log.info("Prepared {} of {} events for async save...", count, remainingOccurrences.size());
            }
        }

        profileRepository.save(profile); // ✅ Saves relationships too
        log.info("Async save completed: {} events saved and linked to profileId: {}", events.size(), profileId);
    }


    private SpecialEvent buildEventFromSchedule(String profileId, SpecialEventDto dto, ScheduleTime scheduleTime, String baseEventId) {
        SpecialEventDtoMapper mapper = new SpecialEventDtoMapper();
        SpecialEvent event = mapper.toSpecialEvent(dto);
        event.setSpecialEventId(utilityService.generateUniqueUUID());
        event.setOwnerProfileId(profileId);
        event.setScheduleTime(scheduleTime);
        event.setBaseForRecurringEvent(false);
        event.setBaseSpecialEventId(baseEventId);
        event.setTotalImageSize(0L);
        return event;
    }
}


