package com.stageserver.service;

import com.stageserver.dto.calendar.*;
import com.stageserver.dto.event.SpecialEventDto;
import com.stageserver.dto.mapper.ScheduleTimeDtoMapper;
import com.stageserver.dto.mapper.SpecialEventDtoMapper;
import com.stageserver.dto.schedule.ScheduleType;
import com.stageserver.model.common.DateRange;
import com.stageserver.model.common.EventStatus;
import com.stageserver.model.contract.Contract;
import com.stageserver.model.contract.ContractState;
import com.stageserver.model.event.Event;
import com.stageserver.model.event.EventMediaInfo;
import com.stageserver.model.event.SpecialEvent;
import com.stageserver.model.login.User;
import com.stageserver.model.profile.Profile;
import com.stageserver.model.schedule.ScheduleTime;
import com.stageserver.repository.ProfileMediaRepository;
import com.stageserver.repository.ProfileRepository;
import com.stageserver.repository.SpecialEventRepository;
import com.stageserver.repository.UserRepository;
import com.stageserver.repository.contract.ContractRepository;
import com.stageserver.repository.event.EventMediaInfoRepository;
import com.stageserver.repository.event.EventRepository;
import com.stageserver.repository.schedule.ScheduleTimeRepository;
import com.stageserver.service.interfaces.I_CalendarService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class CalendarService implements I_CalendarService {

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private EventRepository eventRepository;

    @Autowired
    private ScheduleTimeRepository scheduleTimeRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private ProfileMediaRepository profileMediaRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private SpecialEventRepository specialEventRepository;

    @Autowired
    private EventMediaInfoRepository eventMediaRepository;

    @Override
    public UserCalendarViewDto getUserCalendarView(String email) {
        UserCalendarViewDto userCalendarView = new UserCalendarViewDto();
        return userCalendarView;
    }

    private List<ProfileCalendarViewDto> getProfileCalendarViewList(List<Profile> profileList, DateRange dateRange, String email) {
        List<ProfileCalendarViewDto> profileCalendarViewList = new ArrayList<>();
        if (!profileList.isEmpty()) {
            for (Profile profile : profileList) {
                Optional<List<Event>> optEventList = eventRepository.findAllEventsForProfileIdByDateRange(profile.getProfileId(), dateRange.getStartDate(), dateRange.getEndDate());
                if (optEventList.isPresent()) {
                    ProfileCalendarViewDto profileCalendarView = getProfileCalendarViewDto(optEventList.get(), profile);
                    profileCalendarViewList.add(profileCalendarView);
                }
            }
        } else {
            log.info("User {} does not have any own profiles", email);
        }
        return profileCalendarViewList;
    }

    private ProfileCalendarViewDto getProfileCalendarViewDto(List<Event> eventList, Profile profile) {
        ProfileCalendarViewDto profileCalendarView = new ProfileCalendarViewDto();
        profileCalendarView.setProfileId(profile.getProfileId());
        profileCalendarView.setProfileName(profile.getProfileName());
        profileCalendarView.setProfileType(profile.getProfileType());
        profileCalendarView.setOwnProfile(true);

        profileMediaRepository.findByProfileId(profile.getProfileId()).ifPresent(profileMedia -> {
            profileCalendarView.setProfileImageUrls(profileMedia.getImageUrls());
        });

        profileCalendarView.setEventList(getAllEventsForProfile(eventList, profile.getProfileId(), true));


        return profileCalendarView;
    }

    //find all events associated with the given profile
    private List<EventCalendarViewDto> getAllEventsForProfile(List<Event> eventList, String profileId, boolean ownProfile) {

        List<EventCalendarViewDto> eventCalendarViewList = new ArrayList<>();
        for (Event event : eventList) {
            EventCalendarViewDto eventCalendarView = new EventCalendarViewDto();
            eventCalendarView.setEventId(event.getEventId());
            eventCalendarView.setEventName(event.getEventName());
            eventCalendarView.setOwnEvent(ownProfile);

            Optional<EventMediaInfo> optEventMediaInfo = eventMediaRepository.findByEventId(event.getEventId());
            if (optEventMediaInfo.isPresent()) {
                EventMediaInfo eventMediaInfo = optEventMediaInfo.get();
                eventCalendarView.setEventImageUrls(eventMediaInfo.getImageUrls());
            }
            Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findContractTimeWithEventId(event.getEventId());
            ScheduleTimeDtoMapper scheduleTimeDtoMapper = new ScheduleTimeDtoMapper();
            if (optScheduleTime.isPresent()) {
                ScheduleTime scheduleTime = optScheduleTime.get();
                if (scheduleTime.getScheduleType() == ScheduleType.EVENT) {
                    eventCalendarView.setScheduleTime(scheduleTimeDtoMapper.toScheduleTimeDto(scheduleTime));
                }
            }
            eventCalendarViewList.add(eventCalendarView);
        }

        return eventCalendarViewList;
    }

    private List<ProfileCalendarViewDto> getFavouriteProfiles(String email, DateRange dateRange) {
        List<ProfileCalendarViewDto> profileCalendarViewList = new ArrayList<>();
        Optional<User> optUser = userRepository.findByEmail(email);
        ProfileCalendarViewDto profileCalendarView = new ProfileCalendarViewDto();
        if (optUser.isPresent()) {
            User user = optUser.get();
            for (String profileId : user.getFavouriteActProfiles()) {

                Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);

                if (optProfile.isPresent()) {
                    Profile profile = optProfile.get();
                    profileCalendarView.setProfileId(optProfile.get().getProfileId());
                    profileCalendarView.setProfileName(optProfile.get().getProfileName());
                    profileCalendarView.setProfileType(optProfile.get().getProfileType());
                    profileCalendarView.setOwnProfile(false);
                    profileMediaRepository.findByProfileId(profileId).ifPresent(profileMedia -> {
                        profileCalendarView.setProfileImageUrls(profileMedia.getImageUrls());
                    });

                    Optional<List<Event>> optEventList = eventRepository.findAllEventsForProfileIdByDateRange(profile.getProfileId(), dateRange.getStartDate(), dateRange.getEndDate());
                    if (optEventList.isPresent()) {
                        profileCalendarView.setEventList(getAllEventsForProfile(optEventList.get(), profile.getProfileId(), false));
                    }
                    profileCalendarViewList.add(profileCalendarView);
                }
            }
        }
        return profileCalendarViewList;
    }

    private List<ContractCalendarViewDto> getContractCalendarViewList(List<Contract> contractList, String email, DateRange dateRange) {
        List<ContractCalendarViewDto> contractCalendarViewList = new ArrayList<>();
        ScheduleTimeDtoMapper scheduleTimeDtoMapper = new ScheduleTimeDtoMapper();

        for (Contract contract : contractList) {
            ContractState state = contract.getContractState();

            // Skip contracts that are CANCELLED or DECLINED
            if (state == ContractState.CANCELLED || state == ContractState.DECLINED) {
                continue;
            }

            // If the contract is CONFIRMED, check if there is a published event
            if (state == ContractState.CONFIRMED) {
                Optional<Event> optEvent = eventRepository.findEventByContractId(contract.getContractId());
                if (optEvent.isPresent() && optEvent.get().getStatus() == EventStatus.STATUS_PUBLISHED) {
                    continue; // Skip this contract
                }
            }

            // Create and populate the ContractCalendarViewDto object
            ContractCalendarViewDto contractCalendarView = new ContractCalendarViewDto();
            contractCalendarView.setContractId(contract.getContractId());
            contractCalendarView.setContractState(state);
            contractCalendarView.setActProfileId(contract.getActProfileId());
            contractCalendarView.setVenueProfileId(contract.getVenueProfileId());

            // Retrieve schedule time if it's an EVENT type
            Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findByContractId(contract.getContractId());
            if (optScheduleTime.isPresent() && optScheduleTime.get().getScheduleType() == ScheduleType.EVENT) {
                contractCalendarView.setScheduleTime(scheduleTimeDtoMapper.toScheduleTimeDto(optScheduleTime.get()));
            }

            // Add the DTO to the list
            contractCalendarViewList.add(contractCalendarView);
        }

        log.info("ContractCalendarViewDto list size: {}", contractCalendarViewList.size());
        return contractCalendarViewList;
    }


    private List<SpecialEventDto> getSpecialEventsForProfile(DateRange dateRange) {
        SpecialEventDtoMapper mapper = new SpecialEventDtoMapper();
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        Optional<List<SpecialEvent>> optSpecialEventList = specialEventRepository.findSpecialEventsForUserByDateRangeAndFavourites(auth.getName(), dateRange.getStartDate(), dateRange.getEndDate());
        if (optSpecialEventList.isPresent()) {
            for (SpecialEvent specialEvent : optSpecialEventList.get()) {
                Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findForSpecialEventId(specialEvent.getSpecialEventId());
                optScheduleTime.ifPresent(specialEvent::setScheduleTime);
            }
            return mapper.toSpecialEventDtoList(optSpecialEventList.get());
        }
        log.info("No special events found for the given date range");
        return new ArrayList<>();
    }

    @Override
    public UserCalendarViewDto getUserCalendarViewByPeriod(String email, ZonedDateTime startDate, QueryPeriod period) {
        UserCalendarViewDto userCalendarView = new UserCalendarViewDto();

        DateRange dateRange = calculateDateRange(startDate, period);

        List<Profile> profileList = profileRepository.findAllProfilesForUser(email);
        userCalendarView.setProfileList(getProfileCalendarViewList(profileList, dateRange, email));

        userCalendarView.setFavoriteProfileList(getFavouriteProfiles(email, dateRange));

        List<Contract> initiatedContracts = contractRepository.findAllByOriginatingUserInDateRange(email, dateRange.getStartDate(), dateRange.getEndDate());
        List<Contract> receivedContracts = contractRepository.findAllByReceivingUserInDateRange(email, dateRange.getStartDate(), dateRange.getEndDate());
        initiatedContracts.addAll(receivedContracts);
        userCalendarView.setContractList(getContractCalendarViewList(initiatedContracts, email, dateRange));

        userCalendarView.setSpecialEventList(getSpecialEventsForProfile(dateRange));
        return userCalendarView;
    }

    public DateRange calculateDateRange(ZonedDateTime startDate, QueryPeriod period) {

        ZonedDateTime endDate = null;

        switch (period) {
            case DAY:
                endDate = startDate.with(LocalTime.MAX);
                break;
            case WEEK:
                startDate = startDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));
                endDate = startDate.with(TemporalAdjusters.nextOrSame(DayOfWeek.SATURDAY)).with(LocalTime.MAX);
                break;
            case MONTH:
                startDate = startDate.with(TemporalAdjusters.firstDayOfMonth());
                endDate = startDate.with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);
                break;
            default:
                throw new IllegalArgumentException("Invalid period");
        }
        return new DateRange(startDate, endDate);
    }
}
