package com.stageserver.service;

import com.stageserver.dto.contracts.*;
import com.stageserver.dto.event.EventActInfoDto;
import com.stageserver.dto.event.EventMainInfoDto;
import com.stageserver.dto.event.EventVenueInfoDto;
import com.stageserver.dto.feedback.FeedbackMsgDto;
import com.stageserver.dto.login.UserInfoDto;
import com.stageserver.dto.profile.ProfileMediaDto;
import com.stageserver.dto.schedule.ScheduleTimeDto;
import com.stageserver.model.profile.EntertainmentType;
import com.stageserver.model.common.ProfileStatus;
import com.stageserver.model.location.City;
import com.stageserver.model.location.Country;
import com.stageserver.model.location.State;
import com.stageserver.model.supported.*;
import com.stageserver.repository.*;
import com.stageserver.service.interfaces.I_ContentValidatorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class ContentValidatorService implements I_ContentValidatorService {

    public static final int MAX_EMAIL_LENGTH = 50;
    public static final int MAX_GUID_LENGTH = 36;
    public static final int MAX_SMS_CODE_LENGTH = 6;
    public static final int MAX_FILE_NAME_LENGTH = 100;
    public static final int MAX_URL_LENGTH = 2000;
    public static final int MAX_FILE_SIZE = 10485760;
    public static final int MAX_NAME_LENGTH = 50;
    public static final int MAX_INPUT_MSG_LENGTH = 500;


    @Autowired
    private SupportedOptionsRepository supportedOptionsRepository;

    @Autowired
    private EntertainmentTypeRepository entertainmentTypeRepository;


    @Autowired
    private SupportedActRolesRepository supportedActRolesRepository;

    @Autowired
    private SupportedRegionsRepository supportedRegionsRepository;

    @Autowired
    private MusicGenreRepository musicGenreRepository;

    @Autowired
    private SupportedLanguagesRepository supportedLanguagesRepository;

    @Autowired
    private SupportedMusicGenreRepository supportedMusicGenresRepository;

    @Override
    public boolean currencyValidator(String currency) {
        if(currency == null || currency.isEmpty() || currency.length() >= MAX_NAME_LENGTH){
            log.warn("Currency is invalid");
            return false;
        }
        for (SupportedOptions supportedOptions : supportedOptionsRepository.findAll()) {
            if (supportedOptions.getCurrencies().contains(currency)) {
                return true;
            }
        }
        return false;
    }


    @Override
    public boolean entertainmentTypeValidator(String entertainmentType) {
        if(entertainmentType == null || entertainmentType.isEmpty() || entertainmentType.length() >= MAX_NAME_LENGTH){
            log.warn("Entertainment type is invalid");
            return false;
        }
        for (EntertainmentType type : entertainmentTypeRepository.findAll()) {
            if (type.getName().equals(entertainmentType)) {
                return true;
            }
        }
        return false;
    }


    @Override
    public boolean actRoleValidator(String actRole) {
        if(actRole == null || actRole.isEmpty() || actRole.length() >= MAX_NAME_LENGTH){
            log.warn("Act role is invalid");
            return false;
        }
        for (SupportedActRoles supportedActRoles : supportedActRolesRepository.findAll()) {
            if (supportedActRoles.getRoles().contains(actRole)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean musicGenreValidator(String musicGenre) {
        if(musicGenre == null || musicGenre.isEmpty() || musicGenre.length() >= MAX_NAME_LENGTH){
            log.warn("Music genre is invalid");
            return false;
        }
        List<SupportedMusicGenre> supportedMusicGenres = supportedMusicGenresRepository.findAll();
        for (SupportedMusicGenre supportedMusicGenre : supportedMusicGenres) {
            List<MusicGenre> genreList = supportedMusicGenre.getGenreList();
            for (MusicGenre genre : genreList) {
                if (genre.getName().equals(musicGenre)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean musicSubGenreValidator(String musicGenre, List<String> musicSubGenre) {
        if(musicGenre == null || musicGenre.isEmpty() || musicGenre.length() >= MAX_NAME_LENGTH){
            log.warn("Music genre is invalid");
            return false;
        }
        List<SupportedMusicGenre> supportedMusicGenres = supportedMusicGenresRepository.findAll();
        for (SupportedMusicGenre supportedMusicGenre : supportedMusicGenres) {
            List<MusicGenre> genreList = supportedMusicGenre.getGenreList();
            for (MusicGenre genre : genreList) {
                if(genre.getName().length() <= MAX_NAME_LENGTH) {
                    if (genre.getName().equals(musicGenre)) {
                        for (String subGenre : musicSubGenre) {
                            if (genre.getMembers().contains(subGenre)) {
                                return true;
                            }
                        }
                    }
                }
            }
        }
        return false;
    }


    @Override
    public boolean paymentOptionValidator(String paymentOption) {
        if(paymentOption == null || paymentOption.isEmpty() || paymentOption.length() >= MAX_NAME_LENGTH){
            log.warn("Payment option is invalid");
            return false;
        }
        for (SupportedOptions supportedOptions : supportedOptionsRepository.findAll()) {
            if (supportedOptions.getPaymentOptions().contains(paymentOption)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean paymentMethodValidator(String paymentMethod) {
        if(paymentMethod == null || paymentMethod.isEmpty() || paymentMethod.length() >= MAX_NAME_LENGTH){
            log.warn("Payment method is invalid");
            return false;
        }
        for (SupportedOptions supportedOptions : supportedOptionsRepository.findAll()) {
            if (supportedOptions.getPaymentMethods().contains(paymentMethod)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean paymentMethodListValidator(List<String> acceptablePaymentMethods) {
        for (String paymentMethod : acceptablePaymentMethods) {
            if (paymentMethodValidator(paymentMethod)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean stateValidator(String state) {
        if(state == null || state.isEmpty() || state.length() >= MAX_NAME_LENGTH){
            log.warn("State is invalid");
            return false;
        }
        for (SupportedRegions supportedRegions : supportedRegionsRepository.findAll()) {
            for (Country c : supportedRegions.getCountries()) {
                for (State s : c.getStates()) {
                    if (s.getName().equals(state)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    public boolean cityValidator(String state) {
        if(state == null || state.isEmpty() || state.length() >= MAX_NAME_LENGTH){
            log.warn("City is invalid");
            return false;
        }
        for (SupportedRegions supportedRegions : supportedRegionsRepository.findAll()) {
            for (Country c : supportedRegions.getCountries()) {
                for (State s : c.getStates()) {
                    if (s.getName().equals(state)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    public boolean regionValidator(String country, String state, String city) {
        if(country == null || country.isEmpty() || country.length() >= MAX_NAME_LENGTH){
            log.warn("Country is invalid");
            return false;
        }
        if(state == null || state.isEmpty() || state.length() >= MAX_NAME_LENGTH){
            log.warn("State is invalid");
            return false;
        }
        if(city == null || city.isEmpty() || city.length() >= MAX_NAME_LENGTH){
            log.warn("City is invalid");
            return false;
        }
//        for (SupportedRegions supportedRegions : supportedRegionsRepository.findAll()) {
//            for (Country c : supportedRegions.getCountries()) {
//                if (c.getName().equals(country)) {
//                    for (State s : c.getStates()) {
//                        for (City ci : s.getCities()) {
//                            if (ci.getName().equals(city)) {
//                                return true;
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        return false;
        return true;
    }

    @Override
    public boolean countryValidator(String country) {
        if(country == null || country.isEmpty() || country.length() >= MAX_NAME_LENGTH){
            log.warn("Country is invalid");
            return false;
        }
        for (SupportedRegions supportedRegions : supportedRegionsRepository.findAll()) {
            for (Country c : supportedRegions.getCountries()) {
                if (c.getName().equals(country)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean languageValidator(String language) {
        if(language == null || language.isEmpty() || language.length() >= MAX_NAME_LENGTH){
            log.warn("Language is invalid");
            return false;
        }
        for (SupportedLanguages supportedLanguages : supportedLanguagesRepository.findAll()) {
            if (supportedLanguages.getLanguages().contains(language)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean languageListValidator(List<String> languages) {
        for (String lang : languages) {
            if (languageValidator(lang)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean emailValidator(String email) {

        if(email == null || email.isEmpty() || email.length() >= MAX_EMAIL_LENGTH){
            log.warn("Email is invalid");
            return false;
        }
        String regexPattern = "^(?=.{1,64}@)[A-Za-z0-9_-]+(\\.[A-Za-z0-9_-]+)*@"
                + "[^-][A-Za-z0-9-]+(\\.[A-Za-z0-9-]+)*(\\.[A-Za-z]{2,})$";

        return Pattern.compile(regexPattern)
                .matcher(email)
                .matches();
    }


    @Override
    public boolean actStatusValidator(String actStatus) {
        if(actStatus == null || actStatus.isEmpty() || actStatus.length() >= MAX_NAME_LENGTH){
            log.warn("Act status is invalid");
            return false;
        }
        ProfileStatus[] statuses = ProfileStatus .values();
        for (ProfileStatus status : statuses) {
            if (status.getStatus().equals(actStatus)) {
                return true;
            }
        }
        return false;
    }

    public boolean validatePhoneNumber(String phoneNumber) {
        if(phoneNumber == null || phoneNumber.isEmpty() || phoneNumber.length() >= MAX_NAME_LENGTH ){
            log.warn("Phone number is empty");
            return false;
        }
        Pattern pattern = Pattern.compile("^((\\(\\d{3}\\))|\\d{3})[- .]?\\d{3}[- .]?\\d{4}$");
        Matcher matcher = pattern.matcher(phoneNumber);
        return matcher.matches();
    }

    @Override
    public boolean validateFeedbackMsg(FeedbackMsgDto feedbackMsgDto) {

        if(feedbackMsgDto == null){
            log.warn("Feedback message is null");
            return false;
        }
        if((feedbackMsgDto.getProfessionalismValue() < 0) || (feedbackMsgDto.getProfessionalismValue() > 5)){
            log.warn("Professionalism value is not in the range of 0 to 5");
            return false;
        }
        if((feedbackMsgDto.getEntertainmentValue() < 0) || (feedbackMsgDto.getEntertainmentValue() > 5)){
            log.warn("Entertainment value is not in the range of 0 to 5");
            return false;
        }
        if((feedbackMsgDto.getDrawAsExpectedValue() < 0) || (feedbackMsgDto.getDrawAsExpectedValue() > 5)){
            log.warn("Draw as expected value is not in the range of 0 to 5");
            return false;
        }
        return true;
    }

    @Override
    public boolean guidTokenLengthValidator(String token) {
        if(token == null || token.length() != MAX_GUID_LENGTH ){
            log.warn("Token length is invalid");
            return false;
        }
        return true;
    }

    @Override
    public boolean validateSMSCode(String code) {
        if(code == null || code.length() != MAX_SMS_CODE_LENGTH){
            log.warn("SMS code is invalid");
            return false;
        }
        return true;
    }

    @Override
    public boolean fileNameValidator(String imageName) {
        if(imageName == null || imageName.isEmpty() || imageName.length() > MAX_FILE_NAME_LENGTH){
            log.warn("Image name is invalid");
            return false;
        }
        return true;
    }

    @Override
    public boolean urlValidator(String url) {
        if(url == null || url.isEmpty() || url.length() >= MAX_URL_LENGTH){
            log.warn("URL is invalid");
            return false;
        }
        return true;
    }
    @Override
    public boolean profileMediaDtoValidator(ProfileMediaDto profileMediaDto) {
        if(profileMediaDto != null) {
            if(profileMediaDto.getImageUrls() != null){
                for(String imageUrl : profileMediaDto.getImageUrls()){
                    if(!urlValidator(imageUrl)){
                        return false;
                    }
                }
            }
            if(profileMediaDto.getVideoUrls() != null){
                for(String videoUrl : profileMediaDto.getVideoUrls()){
                    if(!urlValidator(videoUrl)){
                        return false;
                    }
                }
            }
            if(profileMediaDto.getAudioUrls() != null){
                for(String audioUrl : profileMediaDto.getAudioUrls()){
                    if(!urlValidator(audioUrl)){
                        return false;
                    }
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean fileSizeValidator(MultipartFile file) {
        if(file == null || file.isEmpty() || file.getSize() == 0){
            log.warn("File is empty");
            return false;
        }
        else if(file.getSize() > MAX_FILE_SIZE ){
            log.warn("File size is greater than 10MB");
            return false;
        }
        return true;
    }

    @Override
    public boolean validateEventName(String eventName) {
       if(eventName == null || eventName.isEmpty() || eventName.length() >= MAX_NAME_LENGTH){
           log.warn("Event name is invalid");
           return false;
       }
       return true;
    }

    @Override
    public boolean validateScheduleTime(ScheduleTimeDto scheduleTimeDto) {
        return true;
    }

    @Override
    public boolean validateEventMainInfo(EventMainInfoDto eventMainInfoDto) {
        if(eventMainInfoDto == null){
            log.warn("Event main info is null");
            return false;
        }
        if((eventMainInfoDto.getEventName().isEmpty()) || (eventMainInfoDto.getEventName().length() >= MAX_NAME_LENGTH)){
            log.warn("EventMainInfo - event name is invalid");
            return false;
        }
        if((eventMainInfoDto.getAboutEvent().isEmpty() || eventMainInfoDto.getAboutEvent().length() >= MAX_INPUT_MSG_LENGTH)){
            log.warn("EventMainInfo - about event is invalid");
            return false;
        }
        return true;
    }

    @Override
    public boolean validateEventVenueInfo(EventVenueInfoDto eventVenueInfoDto) {
        return true;
    }

    @Override
    public boolean validateActVenueInfo(EventActInfoDto eventActInfoDto) {
        return true;
    }

    @Override
    public boolean actRiderChangesValidator(ActRiderChangesDto actRiderChangesDto) {
        return actRiderChangesDto.getRiderConditions().length() <= MAX_INPUT_MSG_LENGTH;
    }

    @Override
    public boolean actRiderNotesValidator(ActRiderNotesDto actRiderNotesDto) {
        return actRiderNotesDto.getAcceptanceConditions().length() <= MAX_INPUT_MSG_LENGTH;
    }

    @Override
    public boolean venueRiderChangesValidator(VenueRiderChangesDto venueRiderChangesDto) {
        return venueRiderChangesDto.getParkingConditions().length() <= MAX_INPUT_MSG_LENGTH;
    }

    @Override
    public boolean venueRiderChangesNotes(VenueRiderNotesDto venueRiderNotesDto) {
        return venueRiderNotesDto.getAcceptanceConditions().length() <= MAX_INPUT_MSG_LENGTH;
    }

    @Override
    public boolean userDataValidator(UserInfoDto userInfoDto) {
        return true;
    }

    @Override
    public boolean stringNameValidator(String name) {
        return name.length() <= MAX_NAME_LENGTH;
    }

    @Override
    public boolean goodsAndServicesMessageValidator(GoodsAndServicesMessageDto goodsAndServicesMessageDto) {
        return (goodsAndServicesMessageDto.getName().length() <= MAX_NAME_LENGTH) &&
                (goodsAndServicesMessageDto.getMessage().length() <= MAX_INPUT_MSG_LENGTH);
    }

    @Override
    public boolean validateMessageId(String messageId) {

        String regex = "^IM-\\d+$";
        if (messageId == null || messageId.isEmpty()) {
            return false;
        }

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(messageId);
        return matcher.matches();
    }

    @Override
    public boolean validateStripeSessionId(String sessionId) {
        return true;
    }
}
