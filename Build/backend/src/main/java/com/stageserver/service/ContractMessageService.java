package com.stageserver.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.stageserver.dto.IM.InstantMessageDto;
import com.stageserver.dto.IM.MessageContentDto;
import com.stageserver.dto.contracts.ContractDetailsDto;
import com.stageserver.dto.contracts.NegotiateDataDto;
import com.stageserver.dto.mapper.GoodsAndServicesDtoMapper;
import com.stageserver.dto.mapper.InstantMessageDtoMapper;
import com.stageserver.events.VirtualActAddedEvent;
import com.stageserver.events.VirtualProfileBookedEvent;
import com.stageserver.model.IM.InstantMessage;
import com.stageserver.model.IM.MessageContent;
import com.stageserver.model.common.ProfileType;
import com.stageserver.model.contract.Contract;
import com.stageserver.model.contract.GoodsAndServices;
import com.stageserver.model.login.User;
import com.stageserver.model.profile.Profile;
import com.stageserver.model.schedule.ScheduleTime;
import com.stageserver.repository.ProfileRepository;
import com.stageserver.repository.UserRepository;
import com.stageserver.repository.contract.ContractRepository;
import com.stageserver.repository.schedule.ScheduleTimeRepository;
import com.stageserver.service.interfaces.I_ContractMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Slf4j
@Service
public class ContractMessageService implements I_ContractMessageService {

    @Autowired
    private SimpMessagingTemplate simpleMessagingTemplate;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private InstantMessageService instantMessageService;

    @Autowired
    private ContractStateService contractStateService;

    @Autowired
    private ContractService contractService;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private ContractServiceUtil contractServiceUtil;

    @Autowired
    private ScheduleTimeRepository scheduleTimeRepository;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private VirtualBookingMonitorService virtualBookingMonitorService;

    @Autowired
    private ScheduleTaskService scheduleTaskService;

    private String getJsonStringForContract(String contractId) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        try {
            Optional<ContractDetailsDto> details = contractService.getContractDetails(contractId);
            if (details.isPresent()) {
                return objectMapper.writeValueAsString(details.get());
            } else {
                log.error("ContractDetailsDtoError in converting contract details to JSON");
                return "";
            }
        } catch (Exception e) {
            log.error("Error in converting contract details to JSON" + e.getMessage());
            return "";
        }
    }

    private InstantMessage prepareContractMessage(String contractId, String sender, String receiver) {
        InstantMessage contractMessage = new InstantMessage();
        contractMessage.setMessageId("IM-" + System.currentTimeMillis());
        MessageContent content = new MessageContent();
        content.setMessageType(MessageContent.MessageType.BOOKING_REQUEST);
        content.setContractId(contractId);
        content.setContractState(contractService.getContractState(contractId));
        content.setContractMessageJson(getJsonStringForContract(contractId));
        contractMessage.setContent(content);

        // set sender and receiver in contractMessage
        contractMessage.setSender(sender);
        contractMessage.setReceiver(receiver);
        return contractMessage;
    }

    private void sendIMMessage(String contractId, String sender, String receiver) {
        String contractState = contractService.getContractState(contractId);
        log.info("Sending contract {} message from: {} to {} for state:{}", contractId, sender, receiver, contractState);
        InstantMessage message = instantMessageService.setupIMMessageForUser(prepareContractMessage(contractId, sender, receiver));
        InstantMessageDtoMapper mapper = new InstantMessageDtoMapper();
        InstantMessageDto dto = mapper.toInstantMessageDto(message);

        MessageContentDto msgContentDto = contractServiceUtil.populateContractMessageContent(message.getContent().getContractId());
        dto.setContent(msgContentDto);
        msgContentDto.getContractContentDto().setActionString(instantMessageService.generateActionString(dto, receiver));
        simpleMessagingTemplate.convertAndSendToUser(message.getReceiver(), "/queue/messages", dto);
        log.info("Contract {} message sent successfully", message.getContent().getContractId());
    }

    private boolean isVirtualBooking(String contractId) {
        boolean virtualAct = false;
        boolean virtualVenue = false;
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            Optional<Profile> optActProfile = profileRepository.findByProfileId(contract.getActProfileId());
            Optional<Profile> optVenueProfile = profileRepository.findByProfileId(contract.getVenueProfileId());
            if (optActProfile.isPresent()) {
                virtualAct = (optActProfile.get().getProfileType() == ProfileType.VIRTUAL_ACT_PROFILE);
            }
            if(optVenueProfile.isPresent()) {
                virtualVenue = (optVenueProfile.get().getProfileType() == ProfileType.VIRTUAL_VENUE_PROFILE);
            }
            return virtualAct || virtualVenue;
        }
        return false;
    }

    private boolean handleVirtualProfileBooking(String contractId) {
        log.info("Handling Virtual Profile Booking for contractId: {}", contractId);
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);

        if(optContract.isPresent()) {
            //handle virtual profile
            if (contractStateService.sendContract(contractId)) {
                // Now find the sender and receiver
                String sender = optContract.get().getOriginatingUser();
                String receiver = "";
                ContractDetailsDto.ContractParty otherParty = contractService.getOtherParty(contractId);
                if (otherParty == ContractDetailsDto.ContractParty.ACT) {
                    receiver = optContract.get().getActProfileId();
                } else if (otherParty == ContractDetailsDto.ContractParty.VENUE) {
                    receiver = optContract.get().getVenueProfileId();
                }
                saveIMMessageForVirtualBooking(contractId, sender, receiver);
            }
        }
        return false;
    }

    private void saveIMMessageForVirtualBooking(String contractId, String senderEmail, String receiverProfileId) {
        String contractState = contractService.getContractState(contractId);
        log.info("Sending contract {} message from: {} to virtual profile {} for state:{}", contractId, senderEmail, receiverProfileId, contractState);
        InstantMessage contractMessage = prepareContractMessage(contractId, senderEmail, "");
        InstantMessage message = instantMessageService.setupIMMessageToVirtualProfile(contractMessage, receiverProfileId);
        // Send email to the Virtual Profile Owner
        eventPublisher.publishEvent(new VirtualProfileBookedEvent(receiverProfileId));
        virtualBookingMonitorService.scheduleTask(() -> {
            System.out.println("Executed after 24 hours");
            // If the Virtual Profile is not claimed within 24 hours, cancel the contract
            // We will just check the profileType of the receiver - If the profileType is still VIRTUAL_ACT_PROFILE or
            // VIRTUAL_VENUE_PROFILE, cancel the contract
            Optional<Profile> optProfile = profileRepository.findByProfileId(receiverProfileId);
            if(optProfile.isPresent()) {
                Profile profile = optProfile.get();
                if(profile.getProfileType() == ProfileType.VIRTUAL_ACT_PROFILE || profile.getProfileType() == ProfileType.VIRTUAL_VENUE_PROFILE) {
                    log.info("Virtual Profile Booking contract {} cancelled", contractId);
                    if(contractStateService.cancelContract(contractId)) {
                        sendVirtualBookingCancelMessage(contractId, senderEmail, receiverProfileId/*is now the sender*/);
                    }
                    else {
                        log.warn("Virtual Profile Booking contract {} could not be cancelled", contractId);
                    }
                }
            }
        });
        log.info("Waiting for 24 hours response from Virtual Profile Owner");
    }

    private void sendVirtualBookingCancelMessage(String contractId, String receiverEmail, String senderProfileId) {
        Optional<Profile> optSenderProfile = profileRepository.findByProfileId(senderProfileId);
        if(optSenderProfile.isPresent()) {
            String contractState = contractService.getContractState(contractId);
            log.info("Sending VirtualBooking cancel message to {}", receiverEmail);
            InstantMessage message = instantMessageService.setupIMMessageFromVirtualProfile(prepareContractMessage(contractId, "", receiverEmail), senderProfileId);
            InstantMessageDtoMapper mapper = new InstantMessageDtoMapper();
            InstantMessageDto dto = mapper.toInstantMessageDto(message);
            // TODO: Do we need the following?  Can we do
            //dto.setContent(message.getContent());
            dto.setContent(contractServiceUtil.populateContractMessageContent(message.getContent().getContractId()));
            dto.getContent().getContractContentDto().setActionString("StageMinder has cancelled the Booking of " + optSenderProfile.get().getProfileName() + " as recipient did not respond");
            simpleMessagingTemplate.convertAndSendToUser(message.getReceiver(), "/queue/messages", dto);
            log.info("Contract {} CANCEL message sent", message.getContent().getContractId());
        }
        else {
            log.warn("Sender Profile {} not found to send Cancel message", senderProfileId);
        }
    }


    @Override
    public boolean sendContract(String contractId) {
        // Virtual Profile Booking needs special handling
        if (isVirtualBooking(contractId)) {
            return handleVirtualProfileBooking(contractId);
        }

        // determine the sender and receiver of the contract
        if (contractStateService.sendContract(contractId)) {
            String sender = "";
            String receiver = "";
            Optional<Contract> optContract = contractRepository.findByContractId(contractId);
            if (optContract.isPresent()) {
                sender = optContract.get().getOriginatingUser();
                if (optContract.get().getReceivingUser() == null) {
                    log.warn("Send-Contract {} does not have a receiving user", contractId);
                }
                receiver = optContract.get().getReceivingUser();
            } else {
                log.warn("Send-Contract {} is not present in the system", contractId);
            }
            if(receiverIsBlocked(receiver, contractId)) {
                log.warn("Receiver {} is blocked by sender {}", receiver, sender);
                return false;
            }
            receiver = contractService.setReceivingUserIfAbsent(contractId);
            sendIMMessage(contractId, sender, receiver);
            // Also send the receive-ack message so that we can skip the receive step
            if(contractStateService.receiveContract(contractId)) {
                log.info("Fake-Receive-Contract message sent for contractId: {}", contractId);
            }
            return true;
        }
        return false;
    }

    private boolean receiverIsBlocked(String receiver, String contractId) {
        Optional<User> optUser = userRepository.findByEmail(receiver);

        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if(optContract.isPresent()) {
            Contract contract = optContract.get();
            ContractDetailsDto.ContractParty otherParty = contractService.getOtherParty(contractId);
            String otherPartyProfileId = null;
            if (otherParty == ContractDetailsDto.ContractParty.ACT) {
                otherPartyProfileId = contract.getActProfileId();
            } else if (otherParty == ContractDetailsDto.ContractParty.VENUE) {
                otherPartyProfileId = contract.getVenueProfileId();
            }

            if (optUser.isPresent()) {
                User user = optUser.get();
                if (user.getBlockedProfiles() != null) {
                    return user.getBlockedProfiles().contains(otherPartyProfileId);
                }
            }
        }
        return false;
    }

    @Override
    public boolean receiveContract(String contractId) {
        // We don't need to send a message to other party
       // return contractStateService.receiveContract(contractId);
        //Make this a no-op operation for now. We will send the receive message when the contract is sent
        return true;
    }

    @Override
    @Transactional
    public boolean cancelContract(String contractId, String authenticatedUser) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isEmpty()) {
            log.warn("Cancel-Contract {} is not present in the system", contractId);
            return false;
        }

        Contract contract = optContract.get();
        if (contract.getReceivingUser() == null) {
            log.warn("Cancel-Contract {} does not have a receiving user", contractId);
            return false;
        }

        String sender = resolveSender(contract, authenticatedUser);
        String receiver = resolveReceiver(contract, authenticatedUser);

        if (contractStateService.cancelContract(contractId)) {
            sendIMMessage(contractId, sender, receiver);
            deleteScheduleTimeOnCancel(contractId);
            scheduleTaskService.cancelTask(contractId);
            return true;
        }
        return false;
    }

    @Transactional
    private void deleteScheduleTimeOnCancel(String contractId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findByContractId(contractId);

        if (optContract.isEmpty()) return;

        Contract contract = optContract.get();
        ScheduleTime scheduleTime = contract.getScheduleTime();

        if (contract.isScheduleMoved()) {
            ContractDetailsDto.ContractParty otherParty = contractService.getOtherParty(contractId);
            deleteScheduleFromParty(contractId, otherParty, contract, scheduleTime, "Other Party");
        }

        ContractDetailsDto.ContractParty bookingParty = contractService.getBookingParty(contractId);
        deleteScheduleFromParty(contractId, bookingParty, contract, scheduleTime, "Booking Party");
    }

    private void deleteScheduleFromParty(String contractId, ContractDetailsDto.ContractParty party,
                                         Contract contract, ScheduleTime scheduleTime, String partyLabel) {
        String profileId = getProfileIdByParty(party, contract);
        if (profileId == null) return;

        profileRepository.findByProfileId(profileId).ifPresent(profile -> {
            if (profile.getScheduleList() != null && scheduleTime != null) {
                profile.getScheduleList().removeIf(s -> s.getScheduleId().equals(scheduleTime.getScheduleId()));
            }
            profileRepository.save(profile);
            log.info("ScheduleTime deleted for contractId: {} and {} Profile: {}", contractId, partyLabel, profileId);
        });
    }

    private String getProfileIdByParty(ContractDetailsDto.ContractParty party, Contract contract) {
        return switch (party) {
            case ACT -> contract.getActProfileId();
            case VENUE -> contract.getVenueProfileId();
            default -> null;
        };
    }

    @Transactional
    private boolean moveScheduleToRecipientCalendar(String contractId, String receiver) {
        Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findByContractId(contractId);
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);

        if (optScheduleTime.isEmpty() || optContract.isEmpty()) {
            return true; // Nothing to move
        }

        ScheduleTime scheduleTime = optScheduleTime.get();
        Contract contract = optContract.get();

        log.info("Schedule with scheduleId: {} found for contractId: {}", scheduleTime.getScheduleId(), contractId);

        String profileToBeUpdated = resolveTargetProfile(contract, contractId);
        if (profileToBeUpdated.isEmpty()) return true;

        if (contractServiceUtil.addSchedule(receiver, profileToBeUpdated, scheduleTime)) {
            log.info("Schedule added to receiver: {} 's calendar", receiver);
            contract.setScheduleMoved(true);
            contractRepository.save(contract);
        } else {
            log.warn("Schedule could not be added to recipient's calendar");
            return false;
        }
        return true;
    }

    private String resolveTargetProfile(Contract contract, String contractId) {
        ContractDetailsDto.ContractParty contractParty = contractService.getOtherParty(contractId);
        String profileId = "";

        switch (contractParty) {
            case ACT -> {
                profileId = contract.getActProfileId();
                log.info("Schedule is being moved to Act: {} calendar", profileId);
            }
            case VENUE -> {
                profileId = contract.getVenueProfileId();
                log.info("Schedule is being moved to Venue: {} calendar", profileId);
            }
            default -> log.warn("Not sure where to move the schedule for contractId: {}", contractId);
        }

        return profileId != null ? profileId : "";
    }

    @Override
    @Transactional
    public boolean acceptContract(String contractId) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        String sendingUser = auth.getName();

        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isEmpty()) {
            log.warn("Accept-Contract {} is not present in the system", contractId);
            return false;
        }

        Contract contract = optContract.get();
        String sender = resolveSender(contract, sendingUser);
        String receiver = resolveReceiver(contract, sendingUser);

        if (contract.getReceivingUser() == null) {
            log.warn("Accept-Contract {} does not have a receiving user", contractId);
        }

        if (!contract.isScheduleMoved()) {
            moveScheduleIfNeeded(contractId, receiver);
        }

        if (contractStateService.acceptContract(contractId)) {
            sendIMMessage(contractId, sender, receiver);
            updateGigsStatistics(contractId);
            updateBookingStatistics(contractId);
            return true;
        }

        return false;
    }

    private String resolveSender(Contract contract, String sendingUser) {
        return sendingUser.equals(contract.getOriginatingUser())
                ? contract.getOriginatingUser()
                : contract.getReceivingUser();
    }

    private String resolveReceiver(Contract contract, String sendingUser) {
        return sendingUser.equals(contract.getOriginatingUser())
                ? contract.getReceivingUser()
                : contract.getOriginatingUser();
    }

    private void moveScheduleIfNeeded(String contractId, String receiver) {
        if (moveScheduleToRecipientCalendar(contractId, receiver)) {
            log.info("Accept-Schedule moved to recipient's calendar");
        } else {
            log.warn("Accept-Schedule could not be moved to recipient's calendar");
        }
    }

    private double getGigsPaymentForAct(GoodsAndServices goodsAndServices) {
        double payment = 0.0;
        if (goodsAndServices.getFlatRateAmount() > 0) {
            payment = goodsAndServices.getFlatRateAmount();
        } else if (goodsAndServices.getDoorGigEntryFee() > 0) {
            if(goodsAndServices.getDoorManagedBy() == GoodsAndServices.ActOrVenue.ACT) {
                payment = goodsAndServices.getDoorGigEntryFee() * goodsAndServices.getVenueCapacity();
                double paymentToVenue = goodsAndServices.getDoorGigEntryFee() *
                        goodsAndServices.getVenueCapacity() * goodsAndServices.getMaximumPercentage();

                if (goodsAndServices.getGuaranteedMaximum() < paymentToVenue) {
                    paymentToVenue = goodsAndServices.getGuaranteedMaximum();
                }
                payment = payment - paymentToVenue;
            }
            else if(goodsAndServices.getDoorManagedBy() == GoodsAndServices.ActOrVenue.VENUE) {
                payment = goodsAndServices.getDoorGigEntryFee() * goodsAndServices.getVenueCapacity();
                payment = goodsAndServices.getDoorGigEntryFee() *
                        goodsAndServices.getVenueCapacity() * goodsAndServices.getMaximumPercentage();

                if(goodsAndServices.getGuaranteedMaximum() < payment) {
                    payment = goodsAndServices.getGuaranteedMaximum();
                }

            }
        } else if (goodsAndServices.getExposureGigFee() > 0) {
            payment = goodsAndServices.getExposureGigFee();
        }
        return payment;
    }

    private double getBookingPriceForVenue(GoodsAndServices goodsAndServices) {
        double payment = 0.0;
        if (goodsAndServices.getFlatRateAmount() > 0) {
            payment = goodsAndServices.getFlatRateAmount();
        } else if (goodsAndServices.getDoorGigEntryFee() > 0) {
            payment = goodsAndServices.getDoorGigEntryFee() * goodsAndServices.getVenueCapacity();
            double paymentToAct = goodsAndServices.getDoorGigEntryFee() *
                    goodsAndServices.getVenueCapacity() * goodsAndServices.getMaximumPercentage();

            if (goodsAndServices.getGuaranteedMaximum() < paymentToAct) {
                paymentToAct = goodsAndServices.getGuaranteedMaximum();
            }
            payment = payment - paymentToAct;
        } else if (goodsAndServices.getExposureGigFee() > 0) {
            payment = goodsAndServices.getExposureGigFee();
        }
        return payment;
    }

    private void updateGigsStatistics(String contractId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            String actProfileId = contract.getActProfileId();
            Optional<Profile> optActProfile = profileRepository.findByProfileId(actProfileId);

            if (optActProfile.isPresent()) {
                Profile actProfile = optActProfile.get();
                double total = actProfile.getAverageGigsPrice() * actProfile.getNumberOfGigs();
                total = total + getGigsPaymentForAct(contract.getGoodsAndServices());
                actProfile.setNumberOfGigs(actProfile.getNumberOfGigs() + 1);
                actProfile.setAverageGigsPrice(Math.round(total / (actProfile.getNumberOfGigs())) * 100.0/100.0);
                double gigsPerMonth = (actProfile.getGigsPerMonth() + 1) / 12.0;
                actProfile.setGigsPerMonth(Math.round(gigsPerMonth * 10.0) / 10.0);
                profileRepository.save(actProfile);
                log.info("Gigs statistics updated for Act: {}", actProfileId);
            }
        }
    }

    private void updateBookingStatistics(String contractId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            String venueProfileId = contract.getVenueProfileId();
            Optional<Profile> optVenueProfile = profileRepository.findByProfileId(venueProfileId);

            if (optVenueProfile.isPresent()) {
                Profile venueProfile = optVenueProfile.get();
                double total = venueProfile.getAverageBookingPrice() * venueProfile.getNumberOfBookings();
                total = total + getBookingPriceForVenue(contract.getGoodsAndServices());
                venueProfile.setNumberOfBookings(venueProfile.getNumberOfBookings() + 1);
                venueProfile.setAverageBookingPrice(Math.round(total / (venueProfile.getNumberOfBookings())) * 100.0/100.0);
                double bookingsPerMonth = (venueProfile.getNumberOfBookingsPerMonth() + 1) / 12.0;
                venueProfile.setNumberOfBookingsPerMonth(Math.round(bookingsPerMonth * 10.0) / 10.0);


                profileRepository.save(venueProfile);
                log.info("Booking statistics updated for Venue: {}", venueProfileId);
            }
        }
    }

    @Override
    @Transactional
    public boolean declineContract(String contractId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isEmpty()) {
            log.warn("Decline-Contract {} is not present in the system", contractId);
            return false;
        }

        Contract contract = optContract.get();
        String sender = contract.getReceivingUser();
        String receiver = contract.getOriginatingUser();

        if (sender == null) {
            log.warn("Decline-Contract {} does not have a receiving user", contractId);
        }

        if (contractStateService.declineContract(contractId)) {
            sendIMMessage(contractId, sender, receiver);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean negotiateContract(String contractId, NegotiateDataDto negotiateDataDto, String authenticatedUser) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isEmpty()) {
            log.warn("Negotiate-Contract {} is not present in the system", contractId);
            return false;
        }

        Contract contract = optContract.get();
        if (contract.getReceivingUser() == null) {
            log.warn("Negotiate-Contract {} does not have a receiving user", contractId);
            return false;
        }

        String sender = resolveSender(contract, authenticatedUser);
        String receiver = resolveReceiver(contract, authenticatedUser);
        String negotiateOriginator = sender;

        contract.setNegotiateOriginatingUser(negotiateOriginator);
        contractRepository.save(contract);

        if (!contract.isScheduleMoved()) {
            handleScheduleMove(contractId, receiver);
        }

        if (!addNegotiatedGoodsAndServices(contractId, negotiateDataDto)) {
            return false;
        }

        if (contractStateService.negotiateContract(contractId)) {
            sendIMMessage(contractId, sender, receiver);
            return true;
        }

        return false;
    }

    private void handleScheduleMove(String contractId, String receiver) {
        if (moveScheduleToRecipientCalendar(contractId, receiver)) {
            log.info("Schedule moved to recipient's calendar");
        } else {
            log.warn("Schedule could not be moved to recipient's calendar");
        }
    }

    private boolean addNegotiatedGoodsAndServices(String contractId, NegotiateDataDto negotiateDataDto) {
        GoodsAndServicesDtoMapper mapper = new GoodsAndServicesDtoMapper();
        boolean success = contractService.addGoodsAndServices(contractId, mapper.toGoodsAndServices(negotiateDataDto.getGoodsAndServices()));

        if (success) {
            log.info("Goods and Services added to contract");
        } else {
            log.warn("Goods and Services could not be added to contract");
        }

        return success;
    }

}
