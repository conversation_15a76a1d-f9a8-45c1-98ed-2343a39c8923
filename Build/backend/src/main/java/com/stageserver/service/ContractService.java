package com.stageserver.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.stageserver.dto.contracts.ContractDataDto;
import com.stageserver.dto.contracts.ContractDetailsDto;
import com.stageserver.dto.login.UserInfoDto;
import com.stageserver.dto.mapper.*;
import com.stageserver.dto.payment.PaymentDisplayStatusDto;
import com.stageserver.dto.schedule.ScheduleTimeDto;
import com.stageserver.model.IM.MessageContent;
import com.stageserver.model.common.DateRange;
import com.stageserver.model.common.FinePrintData;
import com.stageserver.model.contract.*;
import com.stageserver.model.event.Event;
import com.stageserver.model.feedback.FeedbackMsg;
import com.stageserver.model.location.Location;
import com.stageserver.model.login.User;
import com.stageserver.model.payment.PaymentState;
import com.stageserver.model.profile.Profile;
import com.stageserver.model.schedule.ScheduleTime;
import com.stageserver.repository.FeedbackMsgRepository;
import com.stageserver.repository.MessageContentRepository;
import com.stageserver.repository.ProfileRepository;
import com.stageserver.repository.UserRepository;
import com.stageserver.repository.contract.*;
import com.stageserver.repository.event.EventRepository;
import com.stageserver.repository.schedule.ScheduleTimeRepository;
import com.stageserver.service.interfaces.I_ContractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.*;

@Slf4j
@Service
public class ContractService implements I_ContractService {

    @Autowired
    private UtilityService utilityService;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private UserService userService;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private FinePrintDataRepository finePrintDataRepository;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private ScheduleTimeRepository scheduleTimeRepository;

    @Autowired
    private GoodsAndServicesMessageRepository goodsAndServicesMessageRepository;

    @Autowired
    private ActRiderChangesRepository actRiderChangesRepository;

    @Autowired
    private ActRiderNotesRepository actRiderNotesRepository;

    @Autowired
    private VenueRiderChangesRepository venueRiderChangesRepository;

    @Autowired
    private VenueRiderNotesRepository venueRiderNotesRepository;

    @Autowired
    private GoodsAndServicesRepository goodsAndServicesRepository;

    @Autowired
    private MessageContentRepository messageContentRepository;

    @Autowired
    private ModifiedGoodsAndServicesRepository modifiedGoodsAndServicesRepository;

    @Autowired
    private EventRepository eventRepository;

    @Autowired
    private FeedbackMsgRepository feedbackMsgRepository;

    @Override
    public String createContract(ContractDataDto contractDataDto) {

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        Optional<User> optUser = userRepository.findByEmail(auth.getName());
        if (optUser.isPresent()) {
            User user = optUser.get();
            Contract contract = new Contract();
            String contractId = utilityService.generateUniqueUUID();
            log.info("new contract is being created with contractId {}", contractId);
            contract.setContractId(contractId);
            contract.setTimeStamp(ZonedDateTime.now());
            contract.setContractState(ContractState.CREATED);
            contract.setVenueProfileId(contractDataDto.getVenueProfileId());
            contract.setActProfileId(contractDataDto.getActProfileId());
            contract.setOriginatingUser(auth.getName());
            contract.setPaymentState(PaymentState.NO_PAYMENT);
            user.getInitiatedContractList().add(contract);
            contractRepository.save(contract);
            userRepository.save(user);
            // Create all the other nodes
            ModifiedGoodsAndServices modifiedGoodsAndServices = new ModifiedGoodsAndServices();
            modifiedGoodsAndServices.setContractId(contractId);
            modifiedGoodsAndServicesRepository.save(modifiedGoodsAndServices);
            return contractId;
        }
        return null;
    }

    @Override
    public boolean addSchedule(String email, String contractId, ScheduleTime scheduleTime) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        String scheduleId = utilityService.generateUniqueUUID();
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            scheduleTime.setScheduleId(scheduleId);
            log.info("new schedule is being created with scheduleId {}", scheduleId);
            contract.setScheduleTime(scheduleTime);
            contractRepository.save(contract);
            return true;
        }
        return false;
    }

    public ScheduleTime getScheduleTime(String contractId) {
        Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findByContractId(contractId);
        return optScheduleTime.orElse(null);
    }

    @Override
    public boolean deleteSchedule(String email, String contractId) {
        Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findByContractId(contractId);
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optScheduleTime.isPresent()) {
            scheduleTimeRepository.delete(optScheduleTime.get());
            if (optContract.isPresent()) {
                Contract contract = optContract.get();
                contract.setScheduleTime(null);
                contractRepository.save(contract);
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean addPrivateAddress(String email, String contractId, Location location) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            if (contract.getOriginatingUser().equals(email)) {
                contract.setUsingPrivateVenue(true);
                contract.setPrivateVenueLocation(location);
                contractRepository.save(contract);
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean addVenueInfo(String contractId, String profileId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            contract.setUsingPrivateVenue(false);
            contract.setVenueProfileId(profileId);
            contractRepository.save(contract);
            // We have added venue profile, now we can add schedule if he is originating user
            Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findByContractId(contractId);
            if (optScheduleTime.isPresent()) {
                ScheduleTime scheduleTime = optScheduleTime.get();
                if (contract.getOriginatingUser() != null) {
                    if (profileService.isMyProfile(contract.getOriginatingUser(), profileId)) {
                        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
                        if (optProfile.isPresent()) {
                            Profile profile = optProfile.get();
                            profile.getScheduleList().add(scheduleTime);
                            log.info("Added schedule to venue profile: {}", profile.getProfileName());
                            profileRepository.save(profile);
                        }
                    }
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean addActInfo(String contractId, String profileId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            contract.setActProfileId(profileId);
            contractRepository.save(contract);

            // We have added act profile, now we can add schedule if he is originating user
            Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findByContractId(contractId);
            if (optScheduleTime.isPresent()) {
                ScheduleTime scheduleTime = optScheduleTime.get();
                if (contract.getOriginatingUser() != null) {
                    if (profileService.isMyProfile(contract.getOriginatingUser(), profileId)) {
                        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
                        if (optProfile.isPresent()) {
                            Profile profile = optProfile.get();
                            profile.getScheduleList().add(scheduleTime);
                            log.info("Added schedule to act profile: {}", profile.getProfileName());
                            profileRepository.save(profile);
                        }
                    }
                }
            }
            return true;
        }
        return false;
    }

    private boolean saveModifiedGoodsAndServicesData(String contractId, GoodsAndServices oldGoodsAndServices,
                                                     GoodsAndServices newGoodsAndServices) {
        if ((oldGoodsAndServices == null) || (newGoodsAndServices == null)) {
            log.warn("Goods and services data is null");
            return false;
        }
        ModifiedGoodsAndServices newModifiedData = oldGoodsAndServices.compareWith(newGoodsAndServices);
        Optional<ModifiedGoodsAndServices> optModifiedData = modifiedGoodsAndServicesRepository.findByContractId(contractId);
        if (optModifiedData.isPresent()) {
            newModifiedData.setElementId(optModifiedData.get().getElementId());
            newModifiedData.setContractId(contractId);
            modifiedGoodsAndServicesRepository.save(newModifiedData);
            return true;
        }
        return false;
    }

    @Override
    public boolean addGoodsAndServices(String contractId, GoodsAndServices goodsAndServices) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            Optional<GoodsAndServices> optExistingGoodsAndServices = goodsAndServicesRepository.findByContractId(contractId);
            if (optExistingGoodsAndServices.isPresent()) {
                GoodsAndServices existingGoodsAndServices = optExistingGoodsAndServices.get();
                if (saveModifiedGoodsAndServicesData(contractId, existingGoodsAndServices, goodsAndServices)) {
                    log.info("Modified goods and services data saved");
                } else {
                    log.warn("Modified goods and services data cannot be saved");
                }
                goodsAndServices.setElementId(existingGoodsAndServices.getElementId());
            }
            contract.setGoodsAndServices(goodsAndServices);
            contractRepository.save(contract);
            return true;
        }
        return false;
    }

    @Override
    public boolean addVenueRiderChanges(String contractId, VenueRiderChanges venueRiderChanges) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            VenueRiderChanges oldVenueRiderChanges = contract.getVenueRiderChanges();
            if (oldVenueRiderChanges != null) {
                venueRiderChanges.setElementId(oldVenueRiderChanges.getElementId());
            }
            contract.setVenueRiderChanges(venueRiderChanges);
            contractRepository.save(contract);
            return true;
        }
        return false;
    }

    @Override
    public boolean addVenueRiderNotes(String contractId, VenueRiderNotes venueRiderNotes) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            VenueRiderNotes oldVenueRiderNotes = contract.getVenueRiderNotes();
            if (oldVenueRiderNotes != null) {
                venueRiderNotes.setElementId(oldVenueRiderNotes.getElementId());
            }
            contract.setVenueRiderNotes(venueRiderNotes);
            contractRepository.save(contract);
            return true;
        }
        return false;
    }

    @Override
    public boolean addActRiderChanges(String contractId, ActRiderChanges actRiderChanges) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            ActRiderChanges oldActRiderChanges = contract.getActRiderChanges();
            if (oldActRiderChanges != null) {
                actRiderChanges.setElementId(oldActRiderChanges.getElementId());
            }
            contract.setActRiderChanges(actRiderChanges);
            contractRepository.save(contract);
            return true;
        }
        return false;
    }

    @Override
    public boolean addPurchaserInfo(String email, String contractId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            contract.setOriginatingUser(email);
            contractRepository.save(contract);
            return true;
        }
        return false;
    }

    @Override
    public boolean addGoodsAndServicesMessage(GoodsAndServicesMessage message, String email) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            user.getGoodsAndServicesMessages().add(message);
            userRepository.save(user);
            return true;
        }
        return false;
    }

    @Override
    public boolean deleteGoodsAndServicesMessage(String name, String email) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            List<GoodsAndServicesMessage> goodsAndServicesMessages = user.getGoodsAndServicesMessages();
            for (GoodsAndServicesMessage message : goodsAndServicesMessages) {
                if (message.getName().equals(name)) {
                    goodsAndServicesMessages.remove(message);
                    userRepository.save(user);
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean updateGoodsAndServicesMessage(GoodsAndServicesMessage message, String email) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            List<GoodsAndServicesMessage> goodsAndServicesMessages = user.getGoodsAndServicesMessages();
            for (GoodsAndServicesMessage messageItem : goodsAndServicesMessages) {
                if (messageItem.getName().equals(message.getName())) {
                    messageItem.setMessage(message.getMessage());
                    userRepository.save(user);
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public Optional<GoodsAndServicesMessage> getGoodsAndServicesMessage(String name) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        return goodsAndServicesMessageRepository.findForUserWithName(auth.getName(), name);
    }

    @Override
    public Optional<List<GoodsAndServicesMessage>> getAllGoodsAndServicesMessages() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        return goodsAndServicesMessageRepository.findAllForUser(auth.getName());
    }

    @Override
    public boolean checkExistingGoodsAndServicesMessage(String name, String email) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            List<GoodsAndServicesMessage> goodsAndServicesMessages = user.getGoodsAndServicesMessages();
            for (GoodsAndServicesMessage message : goodsAndServicesMessages) {
                if (message.getName().equals(name)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean checkMessageLimits(String email) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            List<GoodsAndServicesMessage> goodsAndServicesMessages = user.getGoodsAndServicesMessages();
            return goodsAndServicesMessages.size() < 5;
        }
        return false;
    }

    @Override
    public boolean addActRiderNotes(String contractId, ActRiderNotes actRiderNotes) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            ActRiderNotes oldActRiderNotes = contract.getActRiderNotes();
            if (oldActRiderNotes != null) {
                actRiderNotes.setElementId(oldActRiderNotes.getElementId());
            }
            contract.setActRiderNotes(actRiderNotes);
            contractRepository.save(contract);
            return true;
        }
        return false;
    }

    @Override
    public ContractDetailsDto.ContractParty getBookingParty(String contractId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            return getBookingParty(contract);
        }
        return ContractDetailsDto.ContractParty.UNKNOWN;
    }

    private ContractDetailsDto.ContractParty getBookingParty(Contract contract) {
        ContractDetailsDto.ContractParty retValue = ContractDetailsDto.ContractParty.UNKNOWN;
        if ((contract.getActProfileId() != null) && (profileService.isMyProfile(contract.getOriginatingUser(), contract.getActProfileId()))) {
            retValue = ContractDetailsDto.ContractParty.ACT;
        } else if ((contract.getVenueProfileId() != null) && (profileService.isMyProfile(contract.getOriginatingUser(), contract.getVenueProfileId()))) {
            retValue = ContractDetailsDto.ContractParty.VENUE;
        } else {
            retValue = ContractDetailsDto.ContractParty.USER;
        }
        return retValue;
    }

    @Override
    public ContractDetailsDto.ContractParty getOtherParty(String contractId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            return getOtherParty(contract);
        }
        return ContractDetailsDto.ContractParty.UNKNOWN;
    }

    private ContractDetailsDto.ContractParty getOtherParty(Contract contract) {
        ContractDetailsDto.ContractParty retValue = ContractDetailsDto.ContractParty.UNKNOWN;
        ContractDetailsDto.ContractParty bookingParty = getBookingParty(contract);
        if (bookingParty == ContractDetailsDto.ContractParty.ACT) {
            if (contract.getVenueProfileId() == null) {
                log.warn("Act is booking but no venue profile is set");
            }
            retValue = ContractDetailsDto.ContractParty.VENUE;
        } else if (bookingParty == ContractDetailsDto.ContractParty.VENUE) {
            if (contract.getActProfileId() == null) {
                log.warn("Venue is booking but no act profile is set");
            }
            retValue = ContractDetailsDto.ContractParty.ACT;
        } else if (bookingParty == ContractDetailsDto.ContractParty.USER) {
            if ((contract.getActProfileId() != null) && (!contract.getActProfileId().isEmpty())) {
                retValue = ContractDetailsDto.ContractParty.ACT;
            } else if ((contract.getVenueProfileId() != null) && (!contract.getVenueProfileId().isEmpty())) {
                retValue = ContractDetailsDto.ContractParty.VENUE;
            }
        }
        return retValue;
    }

    private UserInfoDto getOtherPartyInfo(Contract contract) {

        User user = null;
        ContractDetailsDto.ContractParty otherParty = getOtherParty(contract);
        if (otherParty == ContractDetailsDto.ContractParty.ACT) {
            Optional<Profile> optProfile = profileRepository.findByProfileId(contract.getActProfileId());
            if (optProfile.isPresent()) {
                Profile profile = optProfile.get();
                UserInfoDtoMapper userInfoDtoMapper = new UserInfoDtoMapper();
                user = userService.getProfileOwner(contract.getActProfileId());
            }
        } else if (otherParty == ContractDetailsDto.ContractParty.VENUE) {
            Optional<Profile> optProfile = profileRepository.findByProfileId(contract.getVenueProfileId());
            if (optProfile.isPresent()) {
                Profile profile = optProfile.get();

                user = userService.getProfileOwner(contract.getVenueProfileId());
            }
        }
        if (user != null) {
            UserInfoDtoMapper userInfoDtoMapper = new UserInfoDtoMapper();
            return userInfoDtoMapper.toUserInfoDto(user);
        }
        return null;
    }

    public String setReceivingUserIfAbsent(String contractId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isEmpty()) return null;

        Contract contract = optContract.get();
        if (contract.getReceivingUser() != null) return null;

        ContractDetailsDto.ContractParty otherParty = getOtherParty(contract);
        String profileId = null;

        if (otherParty == ContractDetailsDto.ContractParty.ACT) {
            profileId = contract.getActProfileId();
        } else if (otherParty == ContractDetailsDto.ContractParty.VENUE) {
            profileId = contract.getVenueProfileId();
        }

        if (profileId != null) {
            User user = userService.getProfileOwner(profileId);
            if (user != null) {
                contract.setReceivingUser(user.getEmail());
                contractRepository.save(contract);
                log.info("Receiving user set for contract {}: {}", contractId, user.getEmail());
                return user.getEmail();
            }
        }
        return null;
    }

    @Override
    public List<String> getPossibleActions(String email, String contractId, ContractState state) {
        List<String> possibleActions = new ArrayList<>();
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();

            if (state == null) {
                return possibleActions;
            }
            switch (state) {
                case CREATED:
                    possibleActions.add(ContractAction.SEND.toString());
                    break;
                case SENT:
                case CONFIRMED:
                    possibleActions.add(ContractAction.CANCEL.toString());
                    break;
                case RECEIVED:
                    possibleActions.add(ContractAction.ACCEPT.toString());
                    possibleActions.add(ContractAction.DECLINE.toString());
                    possibleActions.add(ContractAction.NEGOTIATE.toString());
                    break;
                case NEGOTIATING:
                    if (contract.getNegotiateOriginatingUser().equals(email)) {
                        possibleActions.add(ContractAction.DECLINE.toString());
                        possibleActions.add(ContractAction.CANCEL.toString());
                    } else {
                        possibleActions.add(ContractAction.ACCEPT.toString());
                        possibleActions.add(ContractAction.DECLINE.toString());
                        possibleActions.add(ContractAction.CANCEL.toString());
                        possibleActions.add(ContractAction.NEGOTIATE.toString());
                    }
                    break;

                default:
                    break;
            }
        }
        return possibleActions;
    }

    @Override
    public Optional<ContractState> getCurrentContractState(String contractId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            return Optional.of(optContract.get().getContractState());
        }
        return Optional.empty();
    }

    @Override
    public Optional<List<String>> getCurrentPossibleActions(String contractId, String email) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            ContractState contractState = contract.getContractState();
            //SENT, RECEIVED states need to be checked against the user email
            if (contract.getOriginatingUser().equals(email)) {
                if (contractState == ContractState.RECEIVED) {
                    contractState = ContractState.SENT;
                    return Optional.of(getPossibleActions(email, contractId, contract.getContractState()));
                }
            } else if (contract.getReceivingUser().equals(email)) {
                if (contractState == ContractState.SENT) {
                    contractState = ContractState.RECEIVED;
                    return Optional.of(getPossibleActions(email, contractId, contract.getContractState()));
                }
                return Optional.of(getPossibleActions(email, contractId, contract.getContractState()));
            }
        }
        return Optional.empty();
    }

    /* move to profile service */
    @Override
    public List<String> getProfileImages(Profile profile) {
        List<String> imageUrls = new ArrayList<>();
        if ((profile.getProfileMedia() != null) && (profile.getProfileMedia().getImageUrls() != null)) {
            imageUrls = new ArrayList<>(profile.getProfileMedia().getImageUrls());
        }
        return imageUrls;
    }

    public ContractDetailsDto populateContractDetailsDto(Contract contract) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        ContractDetailsDto dto = new ContractDetailsDto();

        setBasicContractInfo(dto, contract, auth);
        setContractingParties(dto, contract);
        setPurchaserInfo(dto, contract);
        setOtherPartyInfo(dto, contract);
        setActProfileDetails(dto, contract);
        setVenueProfileDetails(dto, contract);
        setScheduleTime(dto, contract);
        setProfileNamesAndLocation(dto, contract);
        setGoodsAndRiders(dto, contract);
        setFinePrint(dto);
        dto.setPaymentStatus(getPaymentDisplayStatus(dto, contract.getPaymentState()));

        return dto;
    }

    private void setBasicContractInfo(ContractDetailsDto dto, Contract contract, Authentication auth) {
        dto.setContractId(contract.getContractId());
        dto.setTimeStamp(contract.getTimeStamp());
        // RECEIVED and SENT states needs special handling
        if((contract.getContractState() == ContractState.RECEIVED) && (contract.getOriginatingUser().equals(auth.getName()))) {
            dto.setContractState(ContractState.SENT);
        }
        else if((contract.getContractState() == ContractState.SENT) && (contract.getReceivingUser() == null)) {
            //Must ne virtual booking
            dto.setContractState(ContractState.SENT);
        }
        else if((contract.getContractState() == ContractState.SENT) && (contract.getReceivingUser().equals(auth.getName()))) {
            dto.setContractState(ContractState.RECEIVED);
        }
        else {
            dto.setContractState(contract.getContractState());
        }
        dto.setPossibleActions(getPossibleActions(auth.getName(), contract.getContractId(), dto.getContractState()));
    }

    private void setContractingParties(ContractDetailsDto dto, Contract contract) {
        dto.setOriginatingUser(contract.getOriginatingUser());
        dto.setBookingParty(getBookingParty(contract));
        dto.setOtherParty(getOtherParty(contract));
    }

    private void setPurchaserInfo(ContractDetailsDto dto, Contract contract) {
        if (contract.getOriginatingUser() != null) {
            userRepository.findByEmail(contract.getOriginatingUser()).ifPresent(user -> {
                UserInfoDtoMapper mapper = new UserInfoDtoMapper();
                dto.setPurchaserInfo(mapper.toUserInfoDto(user));
            });
        }
    }

    private void setOtherPartyInfo(ContractDetailsDto dto, Contract contract) {
        UserInfoDto info = getOtherPartyInfo(contract);
        if (info != null) {
            dto.setOtherPartyInfo(info);
        }
    }

    private void setActProfileDetails(ContractDetailsDto dto, Contract contract) {
        if (contract.getActProfileId() != null) {
            profileService.getProfile(contract.getActProfileId()).ifPresent(profile -> {
                dto.setActRating(profile.getProfileRating().getOverallRating());
                dto.setNumberOfActReviews(profile.getProfileRating().getNumberOfRatings());
                dto.setActProfileImageUrls(getProfileImages(profile));
            });
        }
    }

    private void setVenueProfileDetails(ContractDetailsDto dto, Contract contract) {
        if (contract.getVenueProfileId() != null) {
            profileService.getProfile(contract.getVenueProfileId()).ifPresent(profile -> {
                dto.setVenueRating(profile.getProfileRating().getOverallRating());
                dto.setNumberOfVenueReviews(profile.getProfileRating().getNumberOfRatings());
                dto.setVenueProfileImageUrls(getProfileImages(profile));
            });
        }
    }

    private void setScheduleTime(ContractDetailsDto dto, Contract contract) {
        ScheduleTime scheduleTime = contract.getScheduleTime();
        if (scheduleTime != null) {
            dto.setScheduleTime(new ScheduleTimeDtoMapper().toScheduleTimeDto(scheduleTime));
        }
    }

    private void setProfileNamesAndLocation(ContractDetailsDto dto, Contract contract) {
        String actProfileId = contract.getActProfileId();
        dto.setActProfileId(actProfileId);
        if (actProfileId != null) {
            profileRepository.findByProfileId(actProfileId).ifPresent(profile -> {
                dto.setActProfileName(profile.getProfileName());
            });
        }

        String venueProfileId = contract.getVenueProfileId();
        dto.setVenueProfileId(venueProfileId);
        if (venueProfileId != null) {
            profileRepository.findByProfileId(venueProfileId).ifPresent(profile -> {
                dto.setVenueProfileName(profile.getProfileName());
                if (profile.getLocation() != null) {
                    dto.setVenueLocation(new LocationDtoMapper().toLocationDto(profile.getLocation()));
                }
            });
        }
    }

    private void setGoodsAndRiders(ContractDetailsDto dto, Contract contract) {
        if (contract.getGoodsAndServices() != null) {
            dto.setGoodsAndServices(new GoodsAndServicesDtoMapper().toGoodsAndServicesDto(contract.getGoodsAndServices()));
        }
        if (contract.getVenueRiderChanges() != null) {
            dto.setVenueRiderChanges(new VenueRiderChangesDtoMapper().toVenueRiderChangesDto(contract.getVenueRiderChanges()));
        }
        if (contract.getVenueRiderNotes() != null) {
            dto.setVenueRiderNotes(new VenueRiderNotesDtoMapper().toVenueRiderNotesDto(contract.getVenueRiderNotes()));
        }
        if (contract.getActRiderChanges() != null) {
            dto.setActRiderChanges(new ActRiderChangesDtoMapper().toActRiderChangesDto(contract.getActRiderChanges()));
        }
        if (contract.getActRiderNotes() != null) {
            dto.setActRiderNotes(new ActRiderNotesDtoMapper().toActRiderNotesDto(contract.getActRiderNotes()));
        }
    }

    private void setFinePrint(ContractDetailsDto dto) {
        String locale = LocaleContextHolder.getLocale().getLanguage();
        finePrintDataRepository.findByLocale(locale).ifPresent(dto::setFinePrint);
    }

    public PaymentDisplayStatusDto getPaymentDisplayStatus(ContractDetailsDto contractDetailsDto, PaymentState paymentState) {
        //If the current user owns the ACT....
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        ContractDetailsDto.ContractParty bookingParty = contractDetailsDto.getBookingParty();
        if (paymentState != null) {
            switch (paymentState) {
                case NO_PAYMENT:
                    if((contractDetailsDto.getBookingParty() == ContractDetailsDto.ContractParty.USER) &&
                            (auth.getName().equals(contractDetailsDto.getOriginatingUser()))) {
                        if (contractDetailsDto.getContractState() == ContractState.CONFIRMED) {
                            return PaymentDisplayStatusDto.PAYMENT_REQUIRED;
                        }
                    } else if ((contractDetailsDto.getBookingParty() != ContractDetailsDto.ContractParty.USER) &&
                            (profileService.isMyProfile(auth.getName(), contractDetailsDto.getActProfileId()))) {
                        if (contractDetailsDto.getContractState() == ContractState.CONFIRMED) {
                            return PaymentDisplayStatusDto.PAYMENT_REQUIRED;
                        }
                    }
                    else {
                        if (contractDetailsDto.getContractState() == ContractState.CONFIRMED) {
                            return PaymentDisplayStatusDto.WAITING_OTHER_PARTY_PAYMENT;
                        }
                    }
                    return PaymentDisplayStatusDto.NO_PAYMENT;
                case PAYMENT_MADE:
                    return PaymentDisplayStatusDto.FINALIZED;
            }
        }
        return null;
    }

    @Override
    public Optional<ContractDetailsDto> getContractDetails(String contractId) {

        // Populate the ContractDetailsDto object with the data from the Contract object
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            ContractDetailsDto contractDetailsDto = populateContractDetailsDto(contract);
            return Optional.of(contractDetailsDto);
        }
        return Optional.empty();
    }

    public ContractDetailsDto convertJsonToObject(String json) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        ContractDetailsDto contractDetails = null;
        if ((json != null) && (!json.isEmpty())) {
            try {
                contractDetails = objectMapper.readValue(json, ContractDetailsDto.class);
            } catch (Exception e) {
                log.warn("Error converting json to object: {} ", e.getMessage());
            }
        }
        return contractDetails;
    }

    @Override
    public Optional<ContractDetailsDto> getContractMessageDetails(String contractId, String messageId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        MessageContent content = messageContentRepository.getMessageContentForMessageId(messageId);
        if (content != null) {
            ContractDetailsDto contractDetailsDto = convertJsonToObject(content.getContractMessageJson());
            if (contractDetailsDto != null) {
                return Optional.of(contractDetailsDto);
            }
        }
        return Optional.empty();
    }

    @Override
    public Optional<FinePrintData> getFinePrintData() {
        String locale = LocaleContextHolder.getLocale().getLanguage();
        return finePrintDataRepository.findByLocale(locale);
    }

    private void handleCornerCase() {
        List<FeedbackMsg> feedbackMsgList = feedbackMsgRepository.findAll();
        if (feedbackMsgList.size() == 1) {
            FeedbackMsg feedbackMsg = feedbackMsgList.get(0);
            feedbackMsgRepository.delete(feedbackMsg);
        }
    }

    @Transactional
    private void handleInitiatorFeedbackLogic(ContractDetailsDto contractDetailsDto, String contractId, String email) {

        contractDetailsDto.setUpdateFeedback(false);
        contractDetailsDto.setDisplayFeedback(false);

        Optional<List<FeedbackMsg>> optFeedbackMsgList = feedbackMsgRepository.findByContractId(contractId);
        if (optFeedbackMsgList.isPresent() && !optFeedbackMsgList.get().isEmpty()) {
            List<FeedbackMsg> feedbackMsgList = optFeedbackMsgList.get();
            processExistingFeedbackList(contractDetailsDto, feedbackMsgList, contractId, email);
        } else {
            createFeedbackAndSetDisplay(contractDetailsDto, contractId, email);
        }
    }

    private void setEventInfo(ContractDetailsDto dto, String contractId, String email) {
        eventRepository.findEventsForContractId(contractId).ifPresent(event -> {
            dto.setEventId(event.getEventId());
            dto.setEditable(email.equals(event.getEventOwner()));
        });
    }

    private void processExistingFeedbackList(ContractDetailsDto dto, List<FeedbackMsg> feedbackList, String contractId, String email) {
        for (FeedbackMsg feedbackMsg : feedbackList) {
            if (feedbackMsg.isUserFeedback()) {
                if (feedbackMsg.getProviderName().equals(email)) {
                    setFeedbackDisplayFlags(dto, feedbackMsg);
                }
            } else {
                if (profileService.isMyProfile(email, feedbackMsg.getProviderProfileId())) {
                    setFeedbackDisplayFlags(dto, feedbackMsg);
                }
            }
        }

        if (feedbackList.size() == 1) {
            FeedbackMsg firstMsg = feedbackList.get(0);
            if (!firstMsg.isUserFeedback() &&
                    !profileService.isMyProfile(email, firstMsg.getProviderProfileId())) {
                createFeedbackAndSetDisplay(dto, contractId, email);
            }
        }
    }

    private void setFeedbackDisplayFlags(ContractDetailsDto dto, FeedbackMsg feedbackMsg) {
        dto.setFeedbackId(feedbackMsg.getFeedbackId());
        if (feedbackMsg.isUpdated()) {
            dto.setUpdateFeedback(true);
        } else {
            dto.setDisplayFeedback(true);
        }
    }

    private void createFeedbackAndSetDisplay(ContractDetailsDto dto, String contractId, String email) {
        dto.setFeedbackId(createNewFeedback(email, contractId));
        dto.setDisplayFeedback(true);
    }

    @Override
    public Page<ContractDetailsDto> getAllContractsInitiated(String email, int page, int size) {
        Pageable pageable = PageRequest.ofSize(size).withPage(page);
        Page<Contract> contractPage = contractRepository.findAllForOriginatingUserPage(email, pageable);

        List<ContractDetailsDto> contractDetailsDtoList = new ArrayList<>();
        for (Contract contract : contractPage) {
            ContractDetailsDto dto = buildInitiatedContractDetailsDto(contract.getContractId(), email);
            if (dto != null) {
                contractDetailsDtoList.add(dto);
            }
        }

        return new PageImpl<>(contractDetailsDtoList, pageable, contractPage.getTotalElements());
    }

    private ContractDetailsDto buildInitiatedContractDetailsDto(String contractId, String email) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isEmpty()) return null;

        Contract contract = optContract.get();
        ContractDetailsDto dto = populateContractDetailsDto(contract);

        if (dto.getContractState() == ContractState.RECEIVED) {
            dto.setContractState(ContractState.SENT);
            dto.setPossibleActions(getPossibleActions(email, contractId, ContractState.SENT));
        }
        setEventInfo(dto, contractId, email);
        if (dto.getContractState() == ContractState.COMPLETED) {
            handleInitiatorFeedbackLogic(dto, contractId, email);
        }

//    if (has24HoursPassedAfterContractCompleted(contract.getContractId())) {
//        dto.setDisplayFeedback(true);
//        dto.setFeedbackId(createNewFeedback(email, contract.getContractId()));
//    }

        return dto;
    }

    @Transactional
    private void handleReceiverFeedbackLogic(ContractDetailsDto contractDetailsDto, String contractId, String email) {

        contractDetailsDto.setUpdateFeedback(false);
        contractDetailsDto.setDisplayFeedback(false);

        Optional<List<FeedbackMsg>> optFeedbackMsgList = feedbackMsgRepository.findByContractId(contractId);
        if (optFeedbackMsgList.isPresent() && !optFeedbackMsgList.get().isEmpty()) {
            processReceiverFeedbackList(contractDetailsDto, optFeedbackMsgList.get(), contractId, email);
        } else {
            handleEmptyReceiverFeedbackList(contractDetailsDto, contractId, email);
        }
    }

    private void processReceiverFeedbackList(ContractDetailsDto dto, List<FeedbackMsg> feedbackList, String contractId, String email) {
        for (FeedbackMsg feedbackMsg : feedbackList) {
            if (!feedbackMsg.isUserFeedback()) {
                if (profileService.isMyProfile(email, feedbackMsg.getProviderProfileId())) {
                    setFeedbackDisplayFlags(dto, feedbackMsg);
                }
            }
        }

        if (feedbackList.size() == 1) {
            FeedbackMsg firstMsg = feedbackList.get(0);
            if (!firstMsg.isUserFeedback() &&
                    !profileService.isMyProfile(email, firstMsg.getProviderProfileId())) {
                dto.setFeedbackId(createNewFeedback(email, contractId));
                dto.setDisplayFeedback(true);
            }
        }
    }

    private void handleEmptyReceiverFeedbackList(ContractDetailsDto dto, String contractId, String email) {
        contractRepository.findByContractId(contractId).ifPresent(contract -> {
            if (isContractCreatedByUser(contract)) {
                log.info("Originator of the contract is a USER: Not setting feedbackId");
            } else {
                dto.setFeedbackId(createNewFeedback(email, contractId));
                dto.setDisplayFeedback(true);
            }
        });
    }

    private boolean isContractCreatedByUser(Contract contract) {
        return contract.getActProfileId() == null || contract.getActProfileId().isEmpty()
                || contract.getVenueProfileId() == null || contract.getVenueProfileId().isEmpty();
    }

    public Page<ContractDetailsDto> getAllContractsReceived(String email, int page, int size) {
        Pageable pageable = PageRequest.ofSize(size).withPage(page);
        Page<Contract> contractPage = contractRepository.findAllByReceivingUserPage(email, pageable);

        List<ContractDetailsDto> contractDetailsDtoList = new ArrayList<>();
        for (Contract contract : contractPage) {
            ContractDetailsDto dto = buildReceivedContractDetailsDto(contract.getContractId(), email);
            if (dto != null) {
                contractDetailsDtoList.add(dto);
            }
        }

        return new PageImpl<>(contractDetailsDtoList, pageable, contractPage.getTotalElements());
    }

    private ContractDetailsDto buildReceivedContractDetailsDto(String contractId, String email) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isEmpty()) return null;

        Contract contract = optContract.get();
        ContractDetailsDto dto = populateContractDetailsDto(contract);

        setEventInfo(dto, contractId, email);
        if (dto.getContractState() == ContractState.COMPLETED) {
            handleReceiverFeedbackLogic(dto, contractId, email);
        }

//    if (has24HoursPassedAfterContractCompleted(contractId)) {
//        dto.setDisplayFeedback(true);
//        dto.setFeedbackId(createNewFeedback(email, contractId));
//    }

        return dto;
    }

    @Override
    public boolean checkAvailability(String profileId, String contractId, ScheduleTimeDto newScheduleTime) {
        ZonedDateTime now = ZonedDateTime.now();
        if (newScheduleTime.getStartDate() == null || newScheduleTime.getEndDate() == null ||
                newScheduleTime.getStartDate().isBefore(now)) {
            log.warn("Add Schedule for profile:{} cannot be done as Start date is before current date");
            return false;
        }

        Optional<Profile> optProfile = profileService.getProfile(profileId);
        if (optProfile.isPresent()) {
            log.info("Checking availability for Profile {} with profileID:{}", optProfile.get().getProfileName(), profileId);
            Optional<List<ScheduleTime>> optList = scheduleTimeRepository.findByProfileId(profileId);

            if (optList.isPresent()) {
                for (ScheduleTime oldScheduleTime : optList.get()) {
                    if (oldScheduleTime.isOverlapping(newScheduleTime)) {
                        log.info("Schedule overlaps with existing schedule for profileId: {}", profileId);
                        return false;
                    }
                }
            }
        }

        return true; // No overlaps detected
    }


    @Override
    public boolean checkInitiatorAvailability(String profileId, String contractId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if(optContract.isPresent()) {
            Contract contract = optContract.get();
            ScheduleTime scheduleTime = contract.getScheduleTime();
            ScheduleTimeDtoMapper mapper = new ScheduleTimeDtoMapper();
            if(checkAvailability(profileId, contractId, mapper.toScheduleTimeDto(scheduleTime))) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean checkUserAvailability(String email, ScheduleTimeDto scheduleTimeDto) {

        if(profileService.isAFan(email)) {
            log.info("Checking FAN user {} availability", email);
            DateRange dateRange = new DateRange(scheduleTimeDto.getStartDate(), scheduleTimeDto.getEndDate());
            List<Contract> initiatedContracts = contractRepository.findAllByOriginatingUserInDateRange(email, dateRange.getStartDate(), dateRange.getEndDate());
            if(initiatedContracts.size()> 0 ) {
               for(Contract contract : initiatedContracts) {
                    Optional<ScheduleTime> optContractScheduleTime = scheduleTimeRepository.findByContractId(contract.getContractId());
                    if(optContractScheduleTime.isPresent()) {
                        ScheduleTime contractScheduleTime = optContractScheduleTime.get();
                        if(contractScheduleTime.isOverlapping(scheduleTimeDto)) {
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }

    @Override
    public Optional<ModifiedGoodsAndServices> getModifiedGoodsAndServicesData(String email, String contractId) {
        Optional<ModifiedGoodsAndServices> modifed = modifiedGoodsAndServicesRepository.findByContractId(contractId);
        return modifed;
    }

    @Override
    public String getContractState(String contractId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            return contract.getContractState().toString();
        }
        return "";
    }

    private boolean has24HoursPassedAfterContractCompleted(String contractId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isPresent()) {
            Contract contract = optContract.get();
            if (contract.getContractState() == ContractState.CONFIRMED) {
                ZonedDateTime completedTime = contract.getTimeStamp();
                ZonedDateTime currentTime = ZonedDateTime.now();
                // Check if less than 24 hours have passed
                return currentTime.isBefore(completedTime.plusDays(1));
            }
        }
        return false; // Return false if contract is not present or not completed
    }

    private boolean isNullOrEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    @Transactional
    public String createNewFeedback(String email, String contractId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if (optContract.isEmpty()) return null;

        Contract contract = optContract.get();
        String actProfileId = contract.getActProfileId();
        String venueProfileId = contract.getVenueProfileId();

        String providerId = resolveProviderId(email, actProfileId, venueProfileId);
        String receiverId = resolveReceiverId(providerId, actProfileId, venueProfileId);
        String providerName = providerId.isEmpty() ? email : null;

        FeedbackMsg feedbackMsg = createFeedbackMessage(contractId, providerId, receiverId, providerName);

        if (!providerId.isEmpty()) {
            updateProviderFeedback(feedbackMsg, providerId);
        }
        updateReceiverFeedback(feedbackMsg, receiverId);
        return feedbackMsg.getFeedbackId();
    }

    private String resolveProviderId(String email, String actProfileId, String venueProfileId) {
        if (profileService.isMyProfile(email, actProfileId)) {
            return actProfileId;
        } else if (profileService.isMyProfile(email, venueProfileId)) {
            return venueProfileId;
        }
        return "";
    }

    private String resolveReceiverId(String providerId, String actProfileId, String venueProfileId) {
        if (providerId.equals(actProfileId)) {
            return venueProfileId;
        } else if (providerId.equals(venueProfileId)) {
            return actProfileId;
        }
        return (actProfileId != null) ? actProfileId : venueProfileId;
    }

    private FeedbackMsg createFeedbackMessage(String contractId, String providerId, String receiverId, String providerName) {
        FeedbackMsg feedbackMsg = new FeedbackMsg();
        feedbackMsg.setFeedbackId(utilityService.generateUniqueUUID());
        feedbackMsg.setProviderProfileId(providerId);
        feedbackMsg.setReceiverProfileId(receiverId);
        feedbackMsg.setProviderImageUrls(new ArrayList<>());
        feedbackMsg.setReceiverImageUrls(new ArrayList<>());
        feedbackMsg.setContractId(contractId);

        if (providerId.isEmpty()) {
            feedbackMsg.setProviderName(providerName);
            feedbackMsg.setUserFeedback(true);
        }
        return feedbackMsg;
    }

    private void updateProviderFeedback(FeedbackMsg feedbackMsg, String providerId) {
        profileRepository.findByProfileId(providerId).ifPresent(providerProfile -> {
            feedbackMsg.setProviderName(providerProfile.getProfileName());
            providerProfile.getProvidedFeedbacks().add(feedbackMsg);
            profileRepository.save(providerProfile);
        });
    }

    private void updateReceiverFeedback(FeedbackMsg feedbackMsg, String receiverId) {
        profileRepository.findByProfileId(receiverId).ifPresent(receiverProfile -> {
            feedbackMsg.setOtherPartyName(receiverProfile.getProfileName());
            receiverProfile.getReceivedFeedbacks().add(feedbackMsg);
            profileRepository.save(receiverProfile);
        });
    }
}
