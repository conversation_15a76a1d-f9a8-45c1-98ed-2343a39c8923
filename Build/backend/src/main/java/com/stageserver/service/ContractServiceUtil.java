package com.stageserver.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stageserver.dto.IM.ContractContentDto;
import com.stageserver.dto.IM.GenericMsgContentDto;
import com.stageserver.dto.IM.MessageContentDto;
import com.stageserver.dto.contracts.ContractDetailsDto;
import com.stageserver.model.contract.Contract;
import com.stageserver.model.contract.ContractState;
import com.stageserver.model.login.User;
import com.stageserver.model.profile.Profile;
import com.stageserver.model.schedule.ScheduleTime;
import com.stageserver.repository.UserRepository;
import com.stageserver.repository.contract.ContractRepository;
import com.stageserver.service.interfaces.IContractServiceUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
@Getter
@Setter
public class ContractServiceUtil implements IContractServiceUtil {

    @Autowired
    private ContractService contractService;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private ContractStateService contractStateService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ScheduleService scheduleService;

    public MessageContentDto populateContractMessageContentFromJson(String json) {
        ContractDetailsDto contractDetails = contractService.convertJsonToObject(json);
        MessageContentDto content = new MessageContentDto();
        if(contractDetails != null) {
            content.setMessageType(MessageContentDto.MessageType.BOOKING_REQUEST);
            ContractContentDto contractContent = new ContractContentDto();
            contractContent.setContractId(contractDetails.getContractId());
            contractContent.setBookingParty(contractDetails.getBookingParty());
            contractContent.setOtherParty(contractDetails.getOtherParty());
            contractContent.setActProfileName(contractDetails.getActProfileName());
            contractContent.setVenueProfileName(contractDetails.getVenueProfileName());
            contractContent.setContractState(contractDetails.getContractState());

            String originatingUserEmail = contractDetails.getOriginatingUser();
            Optional<User> optUser = userRepository.findByEmail(originatingUserEmail);
            optUser.ifPresent(user -> contractContent.setUserName(user.getFirstName() + " " + user.getLastName()));

            contractContent.setContractState(contractDetails.getContractState());
            // contractContent.setNegotiateData(contractDetails.getNegotiateData());

            Optional<Profile> optActProfile = profileService.getProfile(contractDetails.getActProfileId());
            optActProfile.ifPresent(profile -> contractContent.setActImgList(contractService.getProfileImages(profile)));

            Optional<Profile> optVenueProfile = profileService.getProfile(contractDetails.getVenueProfileId());
            optVenueProfile.ifPresent(profile -> contractContent.setVenueImgList(contractService.getProfileImages(profile)));

            content.setContractContentDto(contractContent);
        }
        return content;
    }

    @Override
    public MessageContentDto populateContractMessageContent(String contractId) {
        MessageContentDto content = new MessageContentDto();
        content.setMessageType(MessageContentDto.MessageType.BOOKING_REQUEST);

        ContractContentDto contractContent = new ContractContentDto();
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if(optContract.isPresent()) {
            Contract contract = optContract.get();
            contractContent.setContractId(contractId);
            contractContent.setBookingParty(contractService.getBookingParty(contractId));
            contractContent.setOtherParty(contractService.getOtherParty(contractId));
            if(contractStateService.getCurrentState(contractId) == ContractState.SENT) {
                contractContent.setContractState(ContractState.RECEIVED);
            }
            else {
                contractContent.setContractState(contractStateService.getCurrentState(contractId));
            }

            Optional<Profile> optVenueProfile = profileService.getProfile(contract.getVenueProfileId());
            optVenueProfile.ifPresent(profile -> contractContent.setVenueImgList(contractService.getProfileImages(profile)));
            Optional<Profile> optActProfile = profileService.getProfile(contract.getActProfileId());
            optActProfile.ifPresent(profile -> contractContent.setActImgList(contractService.getProfileImages(profile)));
            optActProfile.ifPresent(profile -> contractContent.setActProfileName(profile.getProfileName()));
            optVenueProfile.ifPresent(profile -> contractContent.setVenueProfileName(profile.getProfileName()));
            String originatingUserEmail = contract.getOriginatingUser();
            Optional<User> optUser = userRepository.findByEmail(originatingUserEmail);
            optUser.ifPresent(user -> contractContent.setUserName(user.getFirstName() + " " + user.getLastName()));
            content.setContractContentDto(contractContent);
        }

        return content;
    }

    public MessageContentDto populateGenericMessageContentFromJson(String json, String sender, String receiver) {
        ObjectMapper objectMapper = new ObjectMapper();
        GenericMsgContentDto genericMsgContent = null;
        try {
            genericMsgContent = objectMapper.readValue(json, GenericMsgContentDto.class);
        } catch (Exception e) {
            log.warn("Error converting generic message content json to object: {} ", e.getMessage());
            return null;
        }
        MessageContentDto content = new MessageContentDto();
        content.setMessageType(MessageContentDto.MessageType.GENERIC_MESSAGE);

        Optional<User> optSender = userRepository.findByEmail(sender);
        if(optSender.isPresent()) {
            User user = optSender.get();
            genericMsgContent.setSendingUserName(user.getFirstName() + " " + user.getLastName());
        }

        Optional<User> optReceiver = userRepository.findByEmail(receiver);
        if(optReceiver.isPresent()) {
            User user = optReceiver.get();
            genericMsgContent.setSendingUserName(user.getFirstName() + " " + user.getLastName());
        }

        content.setGenericMsgContentDto(genericMsgContent);
        return content;
    }

    @Override
    public MessageContentDto populateGenericMessageContent(String message, String sender, String receiver) {
        MessageContentDto content = new MessageContentDto();
        content.setMessageType(MessageContentDto.MessageType.GENERIC_MESSAGE);
        GenericMsgContentDto messageContent = new GenericMsgContentDto();
        messageContent.setMessage(message);
        Optional<User> optSender = userRepository.findByEmail(sender);
        optSender.ifPresent(user -> messageContent.setSendingUserName(user.getFirstName() + " " + user.getLastName()));
        Optional<User> optReceiver = userRepository.findByEmail(receiver);
        optReceiver.ifPresent(user -> messageContent.setReceivingUserName(user.getFirstName() + " " + user.getLastName()));
        content.setGenericMsgContentDto(messageContent);
        return content;
    }

    public boolean addSchedule(String email, String profileId, ScheduleTime scheduleTime) {
        String scheduleId = scheduleService.addSchedule(email, profileId, scheduleTime);
        log.info("Schedule with scheduleId: {} added for profileId: {}", scheduleId, profileId);
        return scheduleId != null;
    }
}
