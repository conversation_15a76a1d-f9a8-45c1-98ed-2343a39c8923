package com.stageserver.service;

import com.stageserver.model.contract.Contract;
import com.stageserver.model.contract.ContractAction;
import com.stageserver.model.contract.ContractState;
import com.stageserver.repository.contract.ContractRepository;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.EnumMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
@Getter
@Setter
public class ContractStateMachine {
    @Autowired
    private ContractRepository contractRepository;

    private final Map<ContractState, Map<ContractAction, ContractState>> transitions = new EnumMap<>(ContractState.class);
    private ContractState currentState;


    public ContractStateMachine() {
        // Initialize the state transition map
        initializeTransitions();

        // Set the initial state
        this.currentState = ContractState.CREATED;
    }

    private void initializeTransitions() {

        /* Transitions from CREATED state */
        Map<ContractAction, ContractState> createdTransitions = new EnumMap<>(ContractAction.class);
        createdTransitions.put(ContractAction.SEND, ContractState.SENT);

        /* Transitions from SENT state */
        Map<ContractAction, ContractState> sentTransitions = new EnumMap<>(ContractAction.class);
        sentTransitions.put(ContractAction.RECEIVE_ACKED, ContractState.RECEIVED);
        sentTransitions.put(ContractAction.CANCEL, ContractState.CANCELLED);

        /* Transitions from RECEIVED state */
        Map<ContractAction, ContractState> receivedTransitions = new EnumMap<>(ContractAction.class);
        receivedTransitions.put(ContractAction.ACCEPT, ContractState.CONFIRMED);
        receivedTransitions.put(ContractAction.DECLINE, ContractState.DECLINED);
        receivedTransitions.put(ContractAction.CANCEL, ContractState.CANCELLED);
        receivedTransitions.put(ContractAction.NEGOTIATE, ContractState.NEGOTIATING);

        /* Transitions from NEGOTIATING state */
        Map<ContractAction, ContractState> negotiatingTransitions = new EnumMap<>(ContractAction.class);
        negotiatingTransitions.put(ContractAction.ACCEPT, ContractState.CONFIRMED);
        negotiatingTransitions.put(ContractAction.DECLINE, ContractState.DECLINED);
        negotiatingTransitions.put(ContractAction.CANCEL, ContractState.CANCELLED);
        negotiatingTransitions.put(ContractAction.NEGOTIATE, ContractState.NEGOTIATING);

        /* Transitions from CONFIRMED state */
        Map<ContractAction, ContractState> confirmedTransitions = new EnumMap<>(ContractAction.class);
        confirmedTransitions.put(ContractAction.CANCEL, ContractState.CANCELLED);
        confirmedTransitions.put(ContractAction.START_PAYMENT, ContractState.AWAITING_PAYMENT);

        /* Transitions from AWAITING_PAYMENT state */
        Map<ContractAction, ContractState> paymentTransitions = new EnumMap<>(ContractAction.class);
        paymentTransitions.put(ContractAction.COMPLETE_PAYMENT, ContractState.FINALIZED);
        paymentTransitions.put(ContractAction.FAIL_PAYMENT, ContractState.AWAITING_PAYMENT);
        paymentTransitions.put(ContractAction.CANCEL, ContractState.CANCELLED);

        Map<ContractAction, ContractState> finalizedTransitions = new EnumMap<>(ContractAction.class);
        finalizedTransitions.put(ContractAction.EVENT_DONE, ContractState.COMPLETED);

        /* Add the transitions to the map */
        transitions.put(ContractState.CREATED, createdTransitions);
        transitions.put(ContractState.SENT, sentTransitions);
        transitions.put(ContractState.RECEIVED, receivedTransitions);
        transitions.put(ContractState.NEGOTIATING, negotiatingTransitions);
        transitions.put(ContractState.CONFIRMED, confirmedTransitions);
        transitions.put(ContractState.AWAITING_PAYMENT, paymentTransitions);
        transitions.put(ContractState.FINALIZED, finalizedTransitions);

    }

    public boolean sendEvent(ContractAction action, String contractId) {
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);

        if (optContract.isEmpty()) {
            log.warn("sendEvent:: Contract {} is not present in the system", contractId);
            return false;
        }

        currentState = optContract.get().getContractState();
        Map<ContractAction, ContractState> possibleTransitions = transitions.get(currentState);
        if (possibleTransitions != null && possibleTransitions.containsKey(action)) {
            currentState = possibleTransitions.get(action);
        } else {
            log.warn("Invalid Action " + action + " for state " + currentState + " in contract " + contractId);
            return false;
        }
        return true;
    }

}
