package com.stageserver.service;

import com.stageserver.model.contract.Contract;
import com.stageserver.model.login.ConfirmPhoneNumberToken;
import com.stageserver.model.login.ForgotPasswordToken;
import com.stageserver.model.login.TwoFactorAuthToken;
import com.stageserver.model.login.VerificationToken;
import com.stageserver.repository.ConfirmPhoneNumberTokenRepository;
import com.stageserver.repository.ForgotPasswordTokenRepository;
import com.stageserver.repository.TwoFactorAuthTokenRepository;
import com.stageserver.repository.VerificationTokenRepository;
import com.stageserver.repository.contract.ContractRepository;
import com.stageserver.service.interfaces.I_DbCleanerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.Calendar;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class DbCleanerService implements I_DbCleanerService {

    @Autowired
    private VerificationTokenRepository verificationTokenRepository;

    @Autowired
    private ForgotPasswordTokenRepository forgotPasswordTokenRepository;

    @Autowired
    private ConfirmPhoneNumberTokenRepository confirmPhoneNumberTokenRepository;

    @Autowired
    private TwoFactorAuthTokenRepository twoFactorAuthTokenRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Override
    @Scheduled(fixedRate = 1200000)
    public void execute() {

        log.debug("Running periodic DB cleanup task");
        deleteExpiredTokens();
        deletePartiallyCreatedContracts();
    }

    @Transactional
    private void deletePartiallyCreatedContracts() {
        Optional<List<Contract>> optContractsToDelete = contractRepository.findAllContractsInCreatedState();
        if (optContractsToDelete.isPresent()) {
            List<Contract> contractsToDelete = optContractsToDelete.get();
            for (Contract contract : contractsToDelete)
            {
                //Re-check the state
                ZonedDateTime createdTime = contract.getTimeStamp();
                ZonedDateTime currentTime = ZonedDateTime.now();
                Duration duration = Duration.between(createdTime, currentTime);
                if( duration.toMinutes() < 30)
                {
                    log.info("Partially created contract with contractId {} exists", contract.getContractId());
                    continue;
                }
                log.info("Deleting partially created contract with contractId {}", contract.getContractId());
                contractRepository.deleteContractAndOutgoingNodes(contract.getContractId());
            }
        }
    }

    @Transactional
    private void deleteExpiredTokens() {
        List<VerificationToken> verificationTokenList = verificationTokenRepository.findAll();
        if (!verificationTokenList.isEmpty()) {
            for (VerificationToken verificationToken : verificationTokenList) {
                Calendar calendar = Calendar.getInstance();
                if ((verificationToken.getExpirationTime().getTime() - calendar.getTime().getTime()) < 0) {
                    // the token has expired - delete it
                    log.info("Deleting expired verification tokens");
                    verificationTokenRepository.deleteExpired(verificationToken.getToken());
                }
            }
        }

        List<ForgotPasswordToken> forgotPasswordTokenList = forgotPasswordTokenRepository.findAll();
        if (!forgotPasswordTokenList.isEmpty()) {
            for (ForgotPasswordToken forgotPasswordToken : forgotPasswordTokenList) {
                Calendar calendar = Calendar.getInstance();
                if ((forgotPasswordToken.getExpirationTime().getTime() - calendar.getTime().getTime()) < 0) {
                    // the token has expired - delete it
                    log.info("Deleting expired forgot password tokens");
                    forgotPasswordTokenRepository.deleteExpired(forgotPasswordToken.getToken());
                }
            }
        }

        List<ConfirmPhoneNumberToken> confirmPhoneNumberTokenList = confirmPhoneNumberTokenRepository.findAll();
        if (!confirmPhoneNumberTokenList.isEmpty()) {
            for (ConfirmPhoneNumberToken confirmPhoneNumberToken : confirmPhoneNumberTokenList) {
                Calendar calendar = Calendar.getInstance();
                if ((confirmPhoneNumberToken.getExpirationTime().getTime() - calendar.getTime().getTime()) < 0) {
                    // the token has expired - delete it
                    log.info("Deleting expired confirm phone number tokens");
                    confirmPhoneNumberTokenRepository.deleteExpired(confirmPhoneNumberToken.getToken());
                }
            }
        }

        List<TwoFactorAuthToken> twoFactorAuthTokenList = twoFactorAuthTokenRepository.findAll();
        if (!twoFactorAuthTokenList.isEmpty()) {
            for (TwoFactorAuthToken twoFactorAuthToken : twoFactorAuthTokenList) {
                Calendar calendar = Calendar.getInstance();
                if ((twoFactorAuthToken.getExpirationTime().getTime() - calendar.getTime().getTime()) < 0) {
                    // the token has expired - delete it
                    log.info("Deleting expired two factor authentication tokens");
                    twoFactorAuthTokenRepository.deleteExpired(twoFactorAuthToken.getToken());
                }
            }
        }
    }

}
