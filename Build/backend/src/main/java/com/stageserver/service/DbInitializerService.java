package com.stageserver.service;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stageserver.exceptions.DBInitializerException;
import com.stageserver.model.common.FinePrintData;
import com.stageserver.model.common.SystemUser;
import com.stageserver.model.supported.*;
import com.stageserver.repository.*;
import com.stageserver.repository.contract.FinePrintDataRepository;
import com.stageserver.service.interfaces.I_DbInitializerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class DbInitializerService implements I_DbInitializerService {
    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SupportedMusicGenreRepository supportedMusicGenreRepository;

    @Autowired
    private SupportedOptionsRepository supportedOptionsRepository;

    @Autowired
    private SupportedLanguagesRepository supportedLanguagesRepository;

    @Autowired
    private SupportedActRolesRepository actRolesRepository;

    @Autowired
    private SupportedEntertainmentTypesRepository supportedEntertainmentTypesRepository;

    @Autowired
    private SupportedRegionsRepository supportedRegionsRepository;

    @Autowired
    private SystemUserRepository systemUserRepository;

    @Autowired
    private FinePrintDataRepository finePrintDataRepository;

    @Value("${json.files.location}")
    private String jsonFilesLocation;

    private Resource[] jsonResources(String fileNamePattern) throws IOException {
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        return resolver.getResources("file:" + jsonFilesLocation + "/" + fileNamePattern);
    }

    private String getJsonString(File file) {
        try {
            return FileUtils.readFileToString(file, "UTF-8");
        } catch (IOException ex) {
            log.warn("Unable to read file: {}", file.getName());
            return null;
        }
    }

    @Override
    public void loadSupportedMusicGenre() {

        try {
            Resource[] resources = jsonResources("supported_music_genre*.json");
            for (Resource resource : resources) {

                List<SupportedMusicGenre> genreList = supportedMusicGenreRepository.findAll();
                objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
                SupportedMusicGenre newGenre = objectMapper.readValue(getJsonString(resource.getFile()), SupportedMusicGenre.class);

                if (genreList.isEmpty()) {
                    // We have a clean DB.  Save the new object
                    log.info("Saving new version-{} of {} in database", newGenre.getVersion(), resource.getFilename());
                    supportedMusicGenreRepository.save(newGenre);
                } else {
                    Optional<SupportedMusicGenre> optOldGenre = supportedMusicGenreRepository.findByLocaleWithGenreList(newGenre.getLocale());

                    if(optOldGenre.isEmpty()) {
                        // We have a new Locale.  Save the new object
                        log.info("Saving new version-{} of {} in database", newGenre.getVersion(), resource.getFilename());
                        supportedMusicGenreRepository.save(newGenre);
                    } else {
                        SupportedMusicGenre oldGenre = optOldGenre.get();
                        log.info("Old {} version is {} and the new version is {}", resource.getFilename(), oldGenre.getVersion(), newGenre.getVersion());
                        if (!oldGenre.getVersion().equals(newGenre.getVersion())) {
                            supportedMusicGenreRepository.delete(oldGenre);
                            supportedMusicGenreRepository.save(newGenre);
                        }
                    }
                }
            }
        } catch (IOException ex) {
            log.error("Unable to read supported_music_genre json file {}", ex.getMessage());
            throw new DBInitializerException(HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error" + ex.getMessage());
        }
    }

    @Override
    public void loadSupportedLanguages() {
        try {
            Resource[] resources = jsonResources("supported_languages*.json");
            for (Resource resource : resources) {
                List<SupportedLanguages> languagesList = supportedLanguagesRepository.findAll();
                SupportedLanguages newSupportedLanguages = objectMapper.readValue(getJsonString(resource.getFile()), SupportedLanguages.class);

                if (languagesList.isEmpty()) {
                    // We have a clean DB.  Save the new object
                    log.info("Saving new version-{} of {} in database", newSupportedLanguages.getVersion(), resource.getFilename());
                    supportedLanguagesRepository.save(newSupportedLanguages);
                } else {
                    Optional<SupportedLanguages> optOldSupportedLanguages = supportedLanguagesRepository.findByLocale(newSupportedLanguages.getLocale());

                    if (optOldSupportedLanguages.isEmpty()) {
                        // We have a new Locale.  Save the new object
                        log.info("Saving new version-{} of {} in database", newSupportedLanguages.getVersion(), resource.getFilename());
                        supportedLanguagesRepository.save(newSupportedLanguages);
                    } else {
                        SupportedLanguages oldSupportedLanguages = optOldSupportedLanguages.get();
                        log.info("Old {} version is {} and the new version is {}", resource.getFilename(), oldSupportedLanguages.getVersion(), newSupportedLanguages.getVersion());
                        if (!oldSupportedLanguages.getVersion().equals(newSupportedLanguages.getVersion())) {
                            supportedLanguagesRepository.delete(oldSupportedLanguages);
                            supportedLanguagesRepository.save(newSupportedLanguages);
                        }
                    }
                }
            }
        } catch (IOException ex) {
            log.error("Unable to read supported_languages json file {}", ex.getMessage());
            throw new DBInitializerException(HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error");
        }
    }

    @Override
    public void loadSupportedActRoles() {
        try {
            Resource[] resources = jsonResources("supported_act_roles*.json");
            for (Resource resource : resources) {
                List<SupportedActRoles> rolesList = actRolesRepository.findAll();
                SupportedActRoles newSupportedActRoles = objectMapper.readValue(getJsonString(resource.getFile()), SupportedActRoles.class);

                if (rolesList.isEmpty()) {
                    // We have a clean DB.  Save the new object
                    log.info("Saving new version-{} of {} in database", newSupportedActRoles.getVersion(), resource.getFilename());
                    actRolesRepository.save(newSupportedActRoles);
                } else {
                    Optional<SupportedActRoles> optOldSupportedActRoles = actRolesRepository.findByLocale(newSupportedActRoles.getLocale());

                    if (optOldSupportedActRoles.isEmpty()) {
                        // We have a new Locale.  Save the new object
                        log.info("Saving new version-{} of {} in database", newSupportedActRoles.getVersion(), resource.getFilename());
                        actRolesRepository.save(newSupportedActRoles);
                    } else {
                        SupportedActRoles oldSupportedActRoles = optOldSupportedActRoles.get();
                        log.info("Old {} version is {} and the new version is {}", resource.getFilename(), oldSupportedActRoles.getVersion(), newSupportedActRoles.getVersion());
                        if (!oldSupportedActRoles.getVersion().equals(newSupportedActRoles.getVersion())) {
                            actRolesRepository.delete(oldSupportedActRoles);
                            actRolesRepository.save(newSupportedActRoles);
                        }
                    }
                }
            }
        } catch (IOException ex) {
            log.error("Unable to read supported_act_roles json file {}", ex.getMessage());
            throw new DBInitializerException(HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error");
        }
    }

    private Resource jsonResource(String fileNamePattern) throws IOException {
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        return resolver.getResource("file:" + jsonFilesLocation + "/" + fileNamePattern);
    }

    @Override
    public void loadEntertainmentTypes() {
        try {
            Resource resource = jsonResource("supported_entertainment_types.json");
            List<SupportedEntertainmentTypes> supportedEntertainmentTypesList = supportedEntertainmentTypesRepository.findAll();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            SupportedEntertainmentTypes newSupportedEntertainmentTypes = objectMapper.readValue(getJsonString(resource.getFile()), SupportedEntertainmentTypes.class);

            if(supportedEntertainmentTypesList.isEmpty()) {
                // We have a clean DB.  Save the new object
                log.info("Saving new version-{} of {} in database", newSupportedEntertainmentTypes.getVersion(), resource.getFilename());
                supportedEntertainmentTypesRepository.save(newSupportedEntertainmentTypes);
            } else {
                List<SupportedEntertainmentTypes> optOldSupportedEntertainmentTypesList = supportedEntertainmentTypesRepository.findAll();

                if(optOldSupportedEntertainmentTypesList.isEmpty()) {
                    log.info("Saving new version-{} of {} in database", newSupportedEntertainmentTypes.getVersion(), resource.getFilename());
                    supportedEntertainmentTypesRepository.save(newSupportedEntertainmentTypes);
                } else if(optOldSupportedEntertainmentTypesList.size() == 1){
                    SupportedEntertainmentTypes oldSupportedEntertainmentTypes = optOldSupportedEntertainmentTypesList.get(0);
                    log.info("Old {} version is {} and the new version is {}", resource.getFilename(), oldSupportedEntertainmentTypes.getVersion(), newSupportedEntertainmentTypes.getVersion());
                    if (!oldSupportedEntertainmentTypes.getVersion().equals(newSupportedEntertainmentTypes.getVersion())) {
                        supportedEntertainmentTypesRepository.delete(oldSupportedEntertainmentTypes);
                        supportedEntertainmentTypesRepository.save(newSupportedEntertainmentTypes);
                    }
                }
                else {
                    log.error("Multiple entries found in supported_entertainment_types");
                    throw new DBInitializerException(HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error");
                }
            }
        } catch (IOException ex) {
            log.error("Unable to read supported_entertainment_types json file {}", ex.getMessage());
            throw new DBInitializerException(HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error");
        }
    }

    @Override
    public void loadCountries() {

        try {
            Resource[] resources = jsonResources("supported_regions*.json");
            for (Resource resource : resources) {
                List<SupportedRegions> regionsList = supportedRegionsRepository.findAll();
                SupportedRegions newRegions = objectMapper.readValue(getJsonString(resource.getFile()), SupportedRegions.class);

                if (regionsList.isEmpty()) {
                    // We have a clean DB.  Save the new object
                    log.info("Saving new version-{} of {} in database", newRegions.getVersion(), resource.getFilename());
                    supportedRegionsRepository.save(newRegions);
                } else {
                    Optional<SupportedRegions> optOldRegions = supportedRegionsRepository.findByLocale(newRegions.getLocale());

                    if (optOldRegions.isEmpty()) {
                        // We have a new Locale.  Save the new object
                        log.info("Saving new version-{} of {} in database", newRegions.getVersion(), resource.getFilename());
                        supportedRegionsRepository.save(newRegions);
                    } else {
                        SupportedRegions oldARegions = optOldRegions.get();
                        log.info("Old {} version is {} and the new version is {}", resource.getFilename(), oldARegions.getVersion(), newRegions.getVersion());
                        if (!oldARegions.getVersion().equals(newRegions.getVersion())) {
                            supportedRegionsRepository.delete(oldARegions);
                            supportedRegionsRepository.save(newRegions);
                        }
                    }
                }
            }
        } catch (IOException ex) {
            log.error("Unable to read supported_regions json file {}", ex.getMessage());
            throw new DBInitializerException(HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error");
        }
    }


    @Override
    public void loadSupportedOptions() {

        try {
            Resource[] resources = jsonResources("supported_options*.json");
            for (Resource resource : resources) {
                List<SupportedOptions> optionsList = supportedOptionsRepository.findAll();
                SupportedOptions newOptions = objectMapper.readValue(getJsonString(resource.getFile()), SupportedOptions.class);

                if (optionsList.isEmpty()) {
                    // We have a clean DB.  Save the new object
                    log.info("Saving new version-{} of {} in database", newOptions.getVersion(), resource.getFilename());
                    supportedOptionsRepository.save(newOptions);
                } else {
                    Optional<SupportedOptions> optOldOptions = supportedOptionsRepository.findByLocale(newOptions.getLocale());

                    if (optOldOptions.isEmpty()) {
                        // We have a new Locale.  Save the new object
                        log.info("Saving new version-{} of {} in database", newOptions.getVersion(), resource.getFilename());
                        supportedOptionsRepository.save(newOptions);
                    } else {
                        SupportedOptions oldAOptions = optOldOptions.get();
                        log.info("Old {} version is {} and the new version is {}", resource.getFilename(), oldAOptions.getVersion(), newOptions.getVersion());
                        if (!oldAOptions.getVersion().equals(newOptions.getVersion())) {
                            supportedOptionsRepository.delete(oldAOptions);
                            supportedOptionsRepository.save(newOptions);
                        }
                    }
                }
            }
        } catch (IOException ex) {
            log.error("Unable to read supported_options json file {}", ex.getMessage());
            throw new DBInitializerException(HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error");
        }

    }

    @Override
    public void addSystemUser() {
        Optional<SystemUser> optSysUser = systemUserRepository.findSystemUser();
        if(optSysUser.isPresent()) {
            log.info("System User already exists in the database");
            return;
        }
        else {
            log.info("Saving new System User in the DB");
            SystemUser systemUser = new SystemUser();
            systemUserRepository.save(systemUser);
        }
    }

    @Override
    public void loadFinePrintData() {
        try {
            Resource[] resources = jsonResources("fine_print_data*.json");
            for( Resource resource : resources) {
                FinePrintData newFinePrintData = objectMapper.readValue(getJsonString(resource.getFile()), FinePrintData.class);
                List<FinePrintData> finePrintDataList = finePrintDataRepository.findAll();

                if (finePrintDataList.isEmpty()) {
                    log.info("Saving fine print data in the DB");
                    finePrintDataRepository.save(newFinePrintData);
                } else {
                    Optional<FinePrintData> optOldFinePrintData = finePrintDataRepository.findByLocale(newFinePrintData.getLocale());
                    if (optOldFinePrintData.isEmpty()) {
                        log.info("Saving new version-{} of {} in database", newFinePrintData.getVersion(), resource.getFilename());
                        finePrintDataRepository.save(newFinePrintData);
                    } else {
                        FinePrintData oldFinePrintData = optOldFinePrintData.get();
                        log.info("Old fine print data version is {} and the new version is {}", oldFinePrintData.getVersion(), newFinePrintData.getVersion());
                        if (!oldFinePrintData.getVersion().equals(newFinePrintData.getVersion())) {
                            finePrintDataRepository.delete(oldFinePrintData);
                            finePrintDataRepository.save(newFinePrintData);
                        }
                    }
                }
            }
        } catch (IOException ex) {
            log.error("Unable to read fine_print_data json file {}", ex.getMessage());
            throw new DBInitializerException(HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error");
        }
    }
}
