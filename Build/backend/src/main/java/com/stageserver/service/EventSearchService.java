package com.stageserver.service;

import com.stageserver.dto.event.EventDto;
import com.stageserver.dto.event.EventMainInfoDto;
import com.stageserver.dto.event.EventMediaInfoDto;
import com.stageserver.dto.mapper.EventDtoMapper;
import com.stageserver.dto.mapper.EventMainInfoDtoMapper;
import com.stageserver.dto.mapper.LocationDtoMapper;
import com.stageserver.dto.mapper.ScheduleTimeDtoMapper;
import com.stageserver.dto.profile.ProfileMinimizedViewDto;
import com.stageserver.model.common.ProfileType;
import com.stageserver.model.event.Event;
import com.stageserver.model.event.EventMainInfo;
import com.stageserver.model.event.EventMediaInfo;
import com.stageserver.model.location.Location;
import com.stageserver.model.location.SearchLocation;
import com.stageserver.model.profile.Profile;
import com.stageserver.model.profile.ProfileMedia;
import com.stageserver.model.schedule.ScheduleTime;
import com.stageserver.model.search.SearchData;
import com.stageserver.repository.ProfileMediaRepository;
import com.stageserver.repository.ProfileRepository;
import com.stageserver.repository.SearchLocationRepository;
import com.stageserver.repository.event.EventMainInfoRepository;
import com.stageserver.repository.event.EventMediaInfoRepository;
import com.stageserver.repository.event.EventRepository;
import com.stageserver.repository.schedule.ScheduleTimeRepository;
import com.stageserver.service.interfaces.I_EventSearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class EventSearchService implements I_EventSearchService {

    @Autowired
    private EventRepository eventRepository;

    @Autowired
    private ScheduleTimeRepository scheduleTimeRepository;

    @Autowired
    private SearchLocationRepository searchLocationRepository;

    @Autowired
    private LocationService locationService;

    @Autowired
    private EventMainInfoRepository eventMainInfoRepository;

    @Autowired
    private EventMediaInfoRepository eventMediaInfoRepository;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private ProfileMediaRepository profileMediaRepository;

    private void prepareSearchData(SearchData searchData) {

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        if (((searchData.getCountryName() == null) || searchData.getCountryName().isEmpty()) &&
                (searchData.getStateName() == null || searchData.getStateName().isEmpty()) &&
                (searchData.getCityName() == null || searchData.getCityName().isEmpty())) {
            if (auth.isAuthenticated()) {
                log.info("We have a logged in user {}, so we can use the saved location", auth.getName());
                //Read the save default location and set it in the search data
                if (!auth.getName().equals("anonymousUser")) {
                    Optional<SearchLocation> optSearchLocation = searchLocationRepository.findSearchLocationByEmail(auth.getName());
                    if (optSearchLocation.isPresent()) {
                        log.info("searchLocation is present for user {}", auth.getName());
                        SearchLocation searchLocation = optSearchLocation.get();
                        searchData.setCountryName(searchLocation.getCountryName());
                        searchData.setStateName(searchLocation.getStateName());
                        searchData.setCityName(searchLocation.getCityName());
                        searchData.setDistance(100);
                    }
                } else {
                    log.info("anonymousUser is searching without location");
                }
            }
        }

        try {
            if ((searchData.getCountryName() != null) || (searchData.getStateName() != null) || (searchData.getCityName() != null)) {
                if (searchData.getDistance() > 0) {
                    // We need to compute the longitude and latitude for the search city
                    Location location = locationService.computeGeocode(searchData.getCountryName(), searchData.getStateName(), searchData.getCityName());
                    searchData.setLatitude(location.getLatitude());
                    searchData.setLongitude(location.getLongitude());
                }
            }
        } catch (Exception e) {
            log.error("Public Profile search Error in computing geocode for search location: {}", e.getMessage());
        }
    }

    @Override
    public Page<EventDto> doEventSearchWithFilters(SearchData searchData, int page, int size) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        Pageable pageable = PageRequest.ofSize(size).withPage(page);
        prepareSearchData(searchData);
        List<String> searchStrings = searchData.getSearchStrings();
        searchStrings = removeCommonWords(searchStrings);
        searchData.setSearchStrings(searchStrings);
        Page<Event> eventPage = eventRepository.findEventsBySearchData(searchData, pageable);
        return populateEventDtoPage(eventPage);
    }

    private Page<EventDto> populateEventDtoPage(Page<Event> eventPage) {
        List<EventDto> eventDtoList = new ArrayList<>();
        EventDtoMapper eventDtoMapper = new EventDtoMapper();
        EventMainInfoDtoMapper eventMainInfoDtoMapper = new EventMainInfoDtoMapper();
        ScheduleTimeDtoMapper scheduleTimeDtoMapper = new ScheduleTimeDtoMapper();

        for (Event event : eventPage.getContent()) {
            Optional<EventMainInfo> optEventMainInfo = eventMainInfoRepository.findByEventId(event.getEventId());
            if(optEventMainInfo.isPresent()) {
                EventMainInfo eventMainInfo = optEventMainInfo.get();
                event.setEventMainInfo(eventMainInfo);
            }

            Optional<EventMediaInfo> optEventMediaInfo = eventMediaInfoRepository.findByEventId(event.getEventId());
            if(optEventMediaInfo.isPresent()) {
                EventMediaInfo eventMediaInfo = optEventMediaInfo.get();
                event.setEventMediaInfo(eventMediaInfo);
            }
            EventDto eventDto = eventDtoMapper.toEventDto(event);
            Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findByContractId(event.getPrimeContractId());
            if (optScheduleTime.isPresent()) {
                ScheduleTime scheduleTime = optScheduleTime.get();
                eventDto.setScheduleTime(scheduleTimeDtoMapper.toScheduleTimeDto(scheduleTime));
            }
            if ((event.getVenueProfileId() != null) && (!event.getVenueProfileId().isEmpty())) {
                Optional<Profile> optProfile = profileRepository.findByProfileId(event.getVenueProfileId());
                if (optProfile.isPresent()) {
                    Profile profile = optProfile.get();
                    LocationDtoMapper locationDtoMapper = new LocationDtoMapper();
                    eventDto.setVenueLocation(locationDtoMapper.toLocationDto(profile.getLocation()));
                    eventDto.setVenueName(profile.getProfileName());
                    Optional<ProfileMedia> optProfileMedia = profileMediaRepository.findByProfileId(profile.getProfileId());
                    if(optProfileMedia.isPresent()) {
                        ProfileMedia profileMedia = optProfileMedia.get();
                        eventDto.setVenueImageUrls(profileMedia.getImageUrls());
                    }
                }
            }
            eventDtoList.add(eventDto);
        }
        return new PageImpl<>(eventDtoList, eventPage.getPageable(), eventPage.getTotalElements());
    }

    public List<String> removeCommonWords(List<String> searchStrings) {

        if (searchStrings == null) {
            // return an empty string member so that when search strings is not provided, we can still return results
            List<String> list = new ArrayList<>();
            list.add("");
            return list;
        }
        List<String> stopWords = Arrays.asList(
                "is", "a", "&", "for", "and", "the", "to", "in", "of", "on", "with", "by", "at", "from", "about", "as", "into", "like",
                "through", "over", "between", "out", "against", "during", "without", "before", "after", "above", "below", "around",
                "among", "under", "behind", "inside", "beneath", "beside", "beyond", "but", "except", "since", "until", "upon",
                "within", "so", "nor", "yet", "or"
        );

        // Filter out stop words
        return searchStrings.stream()
                .filter(word -> !stopWords.contains(word))
                .toList();
    }

}
