package com.stageserver.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.stageserver.dto.IM.MessageContentDto;
import com.stageserver.dto.feedback.FeedbackMsgDto;
import com.stageserver.dto.feedback.FeedbackResultsDto;
import com.stageserver.dto.mapper.FeedbackMsgDtoMapper;
import com.stageserver.dto.mapper.ProfileRatingDtoMapper;
import com.stageserver.model.IM.InstantMessage;
import com.stageserver.model.IM.MessageContent;
import com.stageserver.model.feedback.FeedbackMsg;
import com.stageserver.model.profile.Profile;
import com.stageserver.model.profile.ProfileMedia;
import com.stageserver.model.profile.ProfileRating;
import com.stageserver.repository.FeedbackMsgRepository;
import com.stageserver.repository.ProfileMediaRepository;
import com.stageserver.repository.ProfileRatingRepository;
import com.stageserver.repository.ProfileRepository;
import com.stageserver.service.interfaces.I_FeedbackService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class FeedbackService implements I_FeedbackService {

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private UtilityService utilityService;

    @Autowired
    private ProfileRatingRepository profileRatingRepository;

    @Autowired
    private FeedbackMsgRepository feedbackMessageRepository;

    @Autowired
    private ProfileMediaRepository profileMediaRepository;

    private boolean isGoldBadgeCandidate(ProfileRating rating) {
        // Return gold if rating is over 4.5 and has more than 10 ratings
        return (rating.getNumberOfRatings() >= 10) &&
                (rating.getEntertainmentValueRating() >= 4.5) &&
                (rating.getProfessionalismRating() >= 4.5) &&
                (rating.getDrawRating() >= 4.5);
    }

    private double roundToOneDecimal(double value) {
        return Math.round(value * 10.0) / 10.0;
    }

    @Transactional
    private void updateFeedbackReceiversRating(String receiversProfileId, FeedbackMsg feedbackMsg) {
        profileRatingRepository.findByProfileId(receiversProfileId).ifPresent(rating -> {
            int previousCount = rating.getNumberOfRatings();
            int newCount = previousCount + 1;

            double avgFeedback = average(
                    feedbackMsg.getEntertainmentValue(),
                    feedbackMsg.getProfessionalismValue(),
                    feedbackMsg.getDrawAsExpectedValue()
            );

            // Update overall rating
            double updatedOverall = weightedAverage(rating.getOverallRating(), previousCount, avgFeedback);
            rating.setOverallRating(roundToOneDecimal(updatedOverall));

            // Update individual category ratings
            rating.setEntertainmentValueRating(roundToOneDecimal(
                    weightedAverage(rating.getEntertainmentValueRating(), previousCount, feedbackMsg.getEntertainmentValue())));

            rating.setProfessionalismRating(roundToOneDecimal(
                    weightedAverage(rating.getProfessionalismRating(), previousCount, feedbackMsg.getProfessionalismValue())));

            rating.setDrawRating(roundToOneDecimal(
                    weightedAverage(rating.getDrawRating(), previousCount, feedbackMsg.getDrawAsExpectedValue())));

            rating.setNumberOfRatings(newCount);
            rating.setGoldBannerMember(isGoldBadgeCandidate(rating));

            profileRatingRepository.save(rating);
        });
    }

    private double average(double... values) {
        return Arrays.stream(values).average().orElse(0.0);
    }

    private double weightedAverage(double currentAverage, int count, double newValue) {
        return ((currentAverage * count) + newValue) / (count + 1);
    }

    @Override
    public FeedbackResultsDto getFeedbackResults(String profileId) {
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if (optProfile.isEmpty()) return null;

        Profile profile = optProfile.get();
        FeedbackResultsDto dto = new FeedbackResultsDto();

        profileRatingRepository.findByProfileId(profileId)
                .map(new ProfileRatingDtoMapper()::toProfileRatingDto)
                .ifPresent(dto::setActRating);

        FeedbackMsgDtoMapper msgMapper = new FeedbackMsgDtoMapper();

        Optional<List<FeedbackMsg>> optFeedbacksReceived = feedbackMessageRepository.findAllReceivedFeedbacks(profileId);
        Optional<List<FeedbackMsg>> optFeedbacksProvided = feedbackMessageRepository.findAllProvidedFeedbacks(profileId);

        if(optFeedbacksReceived.isPresent()) {
            List<FeedbackMsg> feedbacksReceived = optFeedbacksReceived.get();
            List<FeedbackMsg> filteredReceived = filterAndAttachMediaForReceived(feedbacksReceived);
            dto.setReceivedFeedbacks(msgMapper.toFeedbackMsgDtoList(filteredReceived));
        } else {
            dto.setReceivedFeedbacks(new ArrayList<>());
        }

        if(optFeedbacksProvided.isPresent()) {
            List<FeedbackMsg> feedbacksProvided = optFeedbacksProvided.get();
            List<FeedbackMsg> filteredProvided = filterAndAttachMediaForProvided(feedbacksProvided);
            dto.setProvidedFeedbacks(msgMapper.toFeedbackMsgDtoList(filteredProvided));
        } else {
            dto.setProvidedFeedbacks(new ArrayList<>());
        }

        return dto;
    }

    private List<FeedbackMsg> filterAndAttachMediaForProvided(List<FeedbackMsg> feedbacks) {
        return feedbacks.stream()
                .filter(f -> f.isUpdated() && !f.isDeleted())
                .peek(f -> profileMediaRepository.findByProfileId(f.getReceiverProfileId())
                        .map(ProfileMedia::getImageUrls)
                        .ifPresent(f::setReceiverImageUrls))
                .collect(Collectors.toList());
    }


    private List<FeedbackMsg> filterAndAttachMediaForReceived(List<FeedbackMsg> feedbacks) {
        return feedbacks.stream()
                .filter(f -> f.isUpdated() && !f.isDeleted())
                .peek(f -> profileMediaRepository.findByProfileId(f.getProviderProfileId())
                        .map(ProfileMedia::getImageUrls)
                        .ifPresent(f::setProviderImageUrls))
                .collect(Collectors.toList());
    }


    private boolean isFeedbackProvidedByUser(FeedbackMsg feedbackMsg) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (feedbackMsg.isUserFeedback()) {
            return feedbackMsg.getProviderName().equals(auth.getName());
        } else {
            //provider must be a profile owned by the user
            return profileService.isMyProfile(auth.getName(), feedbackMsg.getProviderProfileId());
        }
    }

    @Override
    public Optional<ProfileRating> readProfileRating(String username, String profileId) {
        return profileService.getProfileRating(profileId);
    }

    @Override
    @Transactional
    public boolean deleteFeedback(String email, String feedbackId) {
        Optional<FeedbackMsg> optFeedbackMsg = feedbackMessageRepository.findByFeedbackId(feedbackId);
        if (optFeedbackMsg.isPresent()) {
            FeedbackMsg feedbackMsg = optFeedbackMsg.get();
            if (!isFeedbackProvidedByUser(feedbackMsg)) {
                log.warn("User {} is not authorized to delete feedback {}", email, feedbackId);
                return false;
            }
            feedbackMsg.setDeleted(true);
            feedbackMessageRepository.save(feedbackMsg);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public HttpStatus updateFeedback(String email, String feedbackId, FeedbackMsgDto feedbackMsg) {
        Optional<FeedbackMsg> optFeedback = feedbackMessageRepository.findByFeedbackId(feedbackId);
        if (optFeedback.isPresent()) {
            FeedbackMsg feedback = optFeedback.get();
            log.info("feedbackId: {}  values: draw = {}, entertainment = {}, professionalism={}", feedbackId,
                    feedback.getDrawAsExpectedValue(), feedback.getEntertainmentValue(), feedback.getProfessionalismValue());
            log.info("feedbackId: {} providerProfileId = {} receiverProfileId = {}", feedbackId, feedback.getProviderProfileId(), feedback.getReceiverProfileId());

            if (!isFeedbackProvidedByUser(feedback)) {
                log.warn("User {} is not authorized to update feedback {}", email, feedbackId);
                return HttpStatus.UNAUTHORIZED;
            }
            // We are updating the feedback, so we need to adjust the previous ratings
            adjustPreviousFeedbackRatings(feedback);
            // now update the feedback
            feedback.setEntertainmentValue(feedbackMsg.getEntertainmentValue());
            feedback.setProfessionalismValue(feedbackMsg.getProfessionalismValue());
            feedback.setDrawAsExpectedValue(feedbackMsg.getDrawAsExpectedValue());
            feedback.setPrivateMessage(feedbackMsg.getPrivateMessage());
            feedback.setPublicMessage(feedbackMsg.getPublicMessage());
            feedback.setUpdated(true);
            feedbackMessageRepository.save(feedback);
            updateFeedbackReceiversRating(feedback.getReceiverProfileId(), feedback);
            return HttpStatus.OK;
        }
        return HttpStatus.NOT_FOUND;
    }

    @Transactional
    private void adjustPreviousFeedbackRatings(FeedbackMsg feedbackMsg) {
        Optional<ProfileRating> optRating = profileRatingRepository.findByProfileId(feedbackMsg.getReceiverProfileId());
        if (optRating.isPresent()) {
            ProfileRating rating = optRating.get();
            double totalOverallRating = rating.getNumberOfRatings() * rating.getOverallRating();
            double totalCurrentRating = (feedbackMsg.getEntertainmentValue() + feedbackMsg.getProfessionalismValue() + feedbackMsg.getDrawAsExpectedValue()) / 3;

            if(rating.getNumberOfRatings() <= 1) {
                rating.setOverallRating(0);
                rating.setEntertainmentValueRating(0);
                rating.setProfessionalismRating(0);
                rating.setDrawRating(0);
                rating.setNumberOfRatings(0);
                rating.setGoldBannerMember(false);
            }
            else {
                rating.setOverallRating((totalOverallRating - totalCurrentRating) / (rating.getNumberOfRatings() - 1));
                double totalEntertainmentValueRating = rating.getNumberOfRatings() * rating.getEntertainmentValueRating();
                double totalProfessionalismRating = rating.getNumberOfRatings() * rating.getProfessionalismRating();
                double totalDrawRating = rating.getNumberOfRatings() * rating.getDrawRating();
                rating.setEntertainmentValueRating((totalEntertainmentValueRating - feedbackMsg.getEntertainmentValue()) / (rating.getNumberOfRatings() - 1));
                rating.setProfessionalismRating((totalProfessionalismRating - feedbackMsg.getProfessionalismValue()) / (rating.getNumberOfRatings() - 1));
                rating.setDrawRating((totalDrawRating - feedbackMsg.getDrawAsExpectedValue()) / (rating.getNumberOfRatings() - 1));
                rating.setNumberOfRatings(rating.getNumberOfRatings() - 1);
                rating.setGoldBannerMember(isGoldBadgeCandidate(rating));
            }
            profileRatingRepository.save(rating);
        }
    }
    @Override
    public Optional<FeedbackMsg> getFeedback(String feedbackId, String email) {
        return feedbackMessageRepository.findByFeedbackId(feedbackId);
    }

    public InstantMessage prepareFeedbackMessage(String feedbackId, String sender, String receiver) {
        InstantMessage feedbackIMMessage = new InstantMessage();
        feedbackIMMessage.setMessageId("IM-" + System.currentTimeMillis());
        MessageContent content = new MessageContent();
        content.setMessageType(MessageContent.MessageType.FEEDBACK_MESSAGE);
        content.setFeedbackId(feedbackId);
        content.setFeedbackMessageJson(getJsonStringForFeedback(feedbackId));
        feedbackIMMessage.setContent(content);

        // set sender and receiver in contractMessage
        feedbackIMMessage.setSender(sender);
        feedbackIMMessage.setReceiver(receiver);
        return feedbackIMMessage;
    }

    private String getJsonStringForFeedback(String feedbackId) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        try {
            Optional<FeedbackMsg> optFeedbackMsg = feedbackMessageRepository.findByFeedbackId(feedbackId);
            if (optFeedbackMsg.isPresent()) {
                return objectMapper.writeValueAsString(optFeedbackMsg.get());
            } else {
                log.error("Error in converting feedback msg to JSON");
                return "";
            }
        } catch (Exception e) {
            log.error("Error in converting feedback message to JSON");
            return "";
        }
    }

    public MessageContentDto populateFeedbackMessageContent(String feedbackId) {
        MessageContentDto content = new MessageContentDto();
        content.setMessageType(MessageContentDto.MessageType.FEEDBACK_MESSAGE);
        Optional<FeedbackMsg> optFeedbackMsg = feedbackMessageRepository.findByFeedbackId(feedbackId);
        if (optFeedbackMsg.isPresent()) {
            FeedbackMsg feedbackMsg = optFeedbackMsg.get();
            FeedbackMsgDtoMapper mapper = new FeedbackMsgDtoMapper();
            FeedbackMsgDto feedbackMsgDto = mapper.toFeedbackMsgDto(feedbackMsg);
            feedbackMsgDto.setActionString(getFeedbackMsgActionString(feedbackMsgDto));
            content.setFeedbackMsgDto(feedbackMsgDto);
        }
        return content;
    }

    public MessageContentDto populateFeedbackMessageContentFromJson(String json) {
        MessageContentDto content = new MessageContentDto();
        content.setMessageType(MessageContentDto.MessageType.FEEDBACK_MESSAGE);
        FeedbackMsgDtoMapper mapper = new FeedbackMsgDtoMapper();
        FeedbackMsgDto feedbackMsgDto = mapper.toFeedbackMsgDto(convertJsonToObject(json));
        feedbackMsgDto.setActionString(getFeedbackMsgActionString(feedbackMsgDto));
        content.setFeedbackMsgDto(feedbackMsgDto);
        return content;
    }

    private String getFeedbackMsgActionString(FeedbackMsgDto feedbackMsg) {
        return "Feedback received from " + feedbackMsg.getProviderName() + " for " + feedbackMsg.getOtherPartyName();
    }

    public FeedbackMsg convertJsonToObject(String json) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        FeedbackMsg feedbackMsg = null;
        if ((json != null) && (!json.isEmpty())) {
            try {
                feedbackMsg = objectMapper.readValue(json, FeedbackMsg.class);
            } catch (Exception e) {
                log.warn("Error converting json to object: {} ", e.getMessage());
            }
        }
        return feedbackMsg;
    }

    public MessageContentDto populateFeedbackMsgContentFromJson(String json) {
        FeedbackMsg feedbackMsg = convertJsonToObject(json);
        MessageContentDto content = new MessageContentDto();
        if (feedbackMsg != null) {
            content.setMessageType(MessageContentDto.MessageType.FEEDBACK_MESSAGE);
            FeedbackMsgDto feedbackContent = new FeedbackMsgDto();
            feedbackContent.setFeedbackId(feedbackMsg.getFeedbackId());
            feedbackContent.setEntertainmentValue(feedbackMsg.getEntertainmentValue());
            feedbackContent.setProfessionalismValue(feedbackMsg.getProfessionalismValue());
            feedbackContent.setDrawAsExpectedValue(feedbackMsg.getDrawAsExpectedValue());
            feedbackContent.setPrivateMessage(feedbackMsg.getPrivateMessage());
            feedbackContent.setPublicMessage(feedbackMsg.getPublicMessage());
            feedbackContent.setProviderName(feedbackMsg.getProviderName());
            feedbackContent.setOtherPartyName(feedbackMsg.getOtherPartyName());
            feedbackContent.setProviderProfileId(feedbackMsg.getProviderProfileId());
            feedbackContent.setReceiverProfileId(feedbackMsg.getReceiverProfileId());
            feedbackContent.setUserFeedback(feedbackMsg.isUserFeedback());
            content.setFeedbackMsgDto(feedbackContent);
        }
        return content;
    }

}
