package com.stageserver.service;

import com.stageserver.model.location.Location;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;

@Slf4j
@Service
public class LocationSanitizer {
    // Regex patterns for ZIP codes
    private final Pattern US_ZIP_PATTERN = Pattern.compile("^\\d{5}(-\\d{4})?$");
    private final Pattern CA_ZIP_PATTERN = Pattern.compile("^[A-Za-z]\\d[A-Za-z] \\d[A-Za-z]\\d$");
    private final Pattern CA_ZIP_NO_SPACE_PATTERN = Pattern.compile("^[A-Za-z]\\d[A-Za-z]\\d[A-Za-z]\\d$");

    public boolean sanitizeLocation(Location location) {
        boolean retValue = true;
        if (location == null || location.getCountry() == null ) {
            log.warn("Location and country cannot be null");
            return false;
        }

        String country = location.getCountry().trim().toUpperCase();
        if(location.getZipCode() == null) {
            log.warn("Ignoring ZIP code validation as it is null");
            return true;
        }
        String zipCode = location.getZipCode().trim();

        switch (country) {
            case "UNITED STATES":
                if (!US_ZIP_PATTERN.matcher(zipCode).matches()) {
                    log.warn("Invalid ZIP code format for USA: {} ", zipCode);
                    retValue = false;
                }
                break;

            case "CANADA":
                zipCode = zipCode.toUpperCase().replaceAll("\\s+", "");
                if (CA_ZIP_NO_SPACE_PATTERN.matcher(zipCode).matches()) {
                    zipCode = zipCode.substring(0, 3) + " " + zipCode.substring(3);
                }
                if (!CA_ZIP_PATTERN.matcher(zipCode).matches()) {
                   log.warn("Invalid ZIP code format for Canada: {}", zipCode);
                    retValue = false;
                }
                location.setZipCode(zipCode); // Update the postal code with correct format
                break;

            default:
                log.warn("Unsupported country for ZIP code validation: {} ", country);
                return false;
        }
        return retValue;
    }
}
