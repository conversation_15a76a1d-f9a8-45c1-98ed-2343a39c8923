package com.stageserver.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stageserver.dto.location.LocationDto;
import com.stageserver.dto.location.LocationTupleDto;
import com.stageserver.model.location.*;
import com.stageserver.model.profile.Profile;
import com.stageserver.model.supported.SupportedMusicGenre;
import com.stageserver.repository.ProfileRepository;
import com.stageserver.service.interfaces.I_LocationService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;


import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class LocationService implements I_LocationService {


    private List<Country> countries;

    @Value("${json.files.location}")
    private String jsonFilesLocation;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${google.api.key}")
    private String googleApiKey;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private LocationSanitizer locationSanitizer;

    public LocationService(RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }

    public Location computeGeocode(Location location) throws IOException {
        log.info("Location is to be geo-computed: {}", location.toString());

        if (!locationSanitizer.sanitizeLocation(location)) {
            return null;
        }

        String formattedAddress = formatGoogleAddress(location);
        log.info("Formatted address: {}", formattedAddress);

        JsonNode root = callGoogleGeocode(formattedAddress);
        JsonNode results = root.path("results");

        if (!results.isArray() || results.size() == 0) {
            log.warn("No results returned for geocode. Full API response:\n{}", root.toPrettyString());
            return null;
        }

        JsonNode result = results.get(0);

        double lat = result.path("geometry").path("location").path("lat").asDouble();
        double lng = result.path("geometry").path("location").path("lng").asDouble();
        String matchedAddress = result.path("formatted_address").asText();
        String locationType = result.path("geometry").path("location_type").asText();
        boolean partialMatch = result.path("partial_match").asBoolean(false);

        location.setLatitude(lat);
        location.setLongitude(lng);

        log.info("Matched address: {}", matchedAddress);
        log.info("Latitude: {}, Longitude: {}", lat, lng);
        log.info("Location type: {}, Partial match: {}", locationType, partialMatch);

        return location;
    }

    public JsonNode geocodeByPlaceId(String placeId) throws IOException {
        String url = UriComponentsBuilder.fromHttpUrl("https://maps.googleapis.com/maps/api/geocode/json")
                .queryParam("place_id", placeId)
                .queryParam("key", googleApiKey)
                .toUriString();

        String response = restTemplate.getForObject(url, String.class);
        return objectMapper.readTree(response);
    }

    private String formatGoogleAddress(Location location) {
        List<String> parts = new ArrayList<>();
        if (location.getStreetAddress() != null && !location.getStreetAddress().isBlank())
            parts.add(location.getStreetAddress().trim());
        if (location.getCity() != null && !location.getCity().isBlank())
            parts.add(location.getCity().trim());

        if (location.getState() != null && !location.getState().isBlank()) {
            StringBuilder stateZip = new StringBuilder(location.getState().trim());
            if (location.getZipCode() != null && !location.getZipCode().isBlank()) {
                stateZip.append(" ").append(location.getZipCode().trim());
            }
            parts.add(stateZip.toString());
        }

        if (location.getCountry() != null && !location.getCountry().isBlank())
            parts.add(location.getCountry().trim());

        return String.join(", ", parts);
    }

    private JsonNode callGoogleGeocode(String address) throws IOException {
        String encodedAddress = URLEncoder.encode(address, StandardCharsets.UTF_8);

        String url = "https://maps.googleapis.com/maps/api/geocode/json"
                + "?address=" + encodedAddress
                + "&region=ca"
                + "&key=" + googleApiKey;

        log.info("Calling Google Geocode API with URL: {}", url);

        String response = restTemplate.getForObject(url, String.class);
        return objectMapper.readTree(response);
    }

    public String reverseGeocodeByCoordinates(double latitude, double longitude) throws IOException {
        String url = UriComponentsBuilder.fromHttpUrl("https://maps.googleapis.com/maps/api/geocode/json")
                .queryParam("latlng", latitude + "," + longitude)
                .queryParam("key", googleApiKey)
                .toUriString();

        log.info("Calling reverse geocoding API with URL: {}", url);

        String response = restTemplate.getForObject(url, String.class);
        JsonNode root = objectMapper.readTree(response);
        JsonNode results = root.path("results");

        if (results.isArray() && results.size() > 0) {
            return results.get(0).path("formatted_address").asText();
        }

        log.warn("No reverse geocoding result found for lat={}, lng={}", latitude, longitude);
        return "Unknown";
    }

    public Location computeGeocode(String country, String state, String city) throws IOException {
        log.info("Computing geocode for Country: {}, State: {}, City: {}", country, state, city);

        // Build the address string from country, state, and city
        String address = String.join(",", city, state, country);
        JsonNode root = callGoogleGeocode(address);
        JsonNode results = root.path("results");

        if (!results.isArray() || results.size() == 0) {
            log.warn("No results returned for geocode. Full API response:\n{}", root.toPrettyString());
            return null;
        }

        JsonNode result = results.get(0);

        double lat = result.path("geometry").path("location").path("lat").asDouble();
        double lng = result.path("geometry").path("location").path("lng").asDouble();
        String matchedAddress = result.path("formatted_address").asText();
        String locationType = result.path("geometry").path("location_type").asText();
        boolean partialMatch = result.path("partial_match").asBoolean(false);

        Location location = new Location();
        location.setLatitude(lat);
        location.setLongitude(lng);

        log.info("Matched address: {}", matchedAddress);
        log.info("Latitude: {}, Longitude: {}", lat, lng);
        log.info("Location type: {}, Partial match: {}", locationType, partialMatch);

        log.info("Computed coordinates - Latitude: {}, Longitude: {}", location.getLatitude(), location.getLongitude());
        return location;
    }

    private Resource jsonResource(String fileNamePattern) throws IOException {
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        return resolver.getResource("file:" + jsonFilesLocation + "/" + fileNamePattern);
    }

    private String getJsonString(File file) {
        try {
            return FileUtils.readFileToString(file, "UTF-8");
        } catch (IOException ex) {
            log.warn("Unable to read file: {}", file.getName());
            return null;
        }
    }

    @PostConstruct
    public void init() throws IOException {

        File file = jsonResource("canada_usa.json").getFile();
        String jsonString = getJsonString(file);
        if (jsonString != null) {
            ObjectMapper mapper = new ObjectMapper();
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            countries = mapper.readValue(jsonString, new TypeReference<List<Country>>() {
            });
        }
        log.info("Total countries supported: {}", countries.size());
    }


    public List<Country> getCountriesWithLocale() {
        String lang = LocaleContextHolder.getLocale().getLanguage();
        List<Country> countryList = new ArrayList<>();
        for (Country country : countries) {
            country.setName(country.getTranslations().get(lang));
            countryList.add(country);
        }
        return countryList;
    }

    public Optional<Country> getCountry(String countryName) {
        String lang = LocaleContextHolder.getLocale().getLanguage();
        log.info("Locale language is: {} ", lang);
        Optional<Country> optCountry = countries.stream().filter(country -> country.getName().equalsIgnoreCase(countryName)).findFirst();

        if (optCountry.isPresent()) {
            Country country = optCountry.get();
            if (!lang.equalsIgnoreCase("en")) {
                country.setName(country.getTranslations().get(lang));
            }

            return Optional.of(country);
        }
        return Optional.empty();
    }

    @Override
    public List<LocationTupleDto> searchLocations(String searchString) {
        List<LocationTupleDto> results = new ArrayList<>();

        for (Country country : countries) {
            if (country.getName().toLowerCase().contains(searchString.toLowerCase())) {
                results.add(new LocationTupleDto(country.getName(), null, null));
            }
            for (State state : country.getStates()) {
                if (state.getName().toLowerCase().contains(searchString.toLowerCase())) {
                    results.add(new LocationTupleDto(country.getName(), state.getName(), null));
                }
                for (City city : state.getCities()) {
                    if (city.getName().toLowerCase().contains(searchString.toLowerCase())) {
                        results.add(new LocationTupleDto(country.getName(), state.getName(), city.getName()));
                    }
                }
            }
        }

        return results;
    }

    @Override
    public Optional<List<Country>> getCountries() {
        return Optional.of(countries);
    }

}
