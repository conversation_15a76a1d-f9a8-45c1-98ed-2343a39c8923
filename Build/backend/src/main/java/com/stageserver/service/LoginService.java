package com.stageserver.service;

import com.stageserver.config.Constants;
import com.stageserver.events.RegistrationCompleteEvent;
import com.stageserver.events.TwoFactorAuthenticationEvent;
import com.stageserver.events.VirtualActAddedEvent;
import com.stageserver.events.VirtualVenueAddedEvent;
import com.stageserver.exceptions.UserAlreadyExistsException;
import com.stageserver.model.common.ProfileType;
import com.stageserver.model.login.*;
import com.stageserver.model.profile.Profile;
import com.stageserver.repository.*;
import com.stageserver.security.JwtTokenProvider;
import com.stageserver.security.UserRegistrationDetails;
import com.stageserver.service.interfaces.I_LoginService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class LoginService implements I_LoginService {

    @Autowired
    private final UserRepository userRepository;

    @Autowired
    private final VerificationTokenRepository verificationTokenRepository;

    @Autowired
    private final ForgotPasswordTokenRepository forgotPasswordTokenRepository;

    @Autowired
    private final TwoFactorAuthTokenRepository twoFactorAuthTokenRepository;

    @Autowired
    private final PasswordEncoder passwordEncoder;

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private Constants constants;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private UtilityService utilityService;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtTokenRepository jwtTokenRepository;

    @Autowired
    private ProfileService profileService;

    @Override
    @Transactional
    public String login(String email, String password) {
        Authentication authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(
                email, password));
        SecurityContextHolder.getContext().setAuthentication(authentication);
        Optional<User> optUser = userService.getUser(email);
        User user = optUser.get();
        if (user.isTwoFaEnabled()) {
            String jwtToken = jwtTokenProvider.generateToken(authentication);
            JwtToken token = new JwtToken();
            token.setToken(jwtToken);
            //token.setUser(user);
            token.setTokenType("Bearer");
            token.setRevoked(false);
            token.setExpired(false);

            user.getJwtTokenList().add(token);
            userRepository.save(user);
            JwtToken result = jwtTokenRepository.save(token);
            log.warn("Saved: " + result.getToken());

            return null;
        }
        return jwtTokenProvider.generateToken(authentication);
    }


    @Override
    @Transactional
    public String loginTfa(String email) {
        List<JwtToken> tokenList = jwtTokenRepository.findAllTokensByUser(email);
        if (tokenList.isEmpty()) {
            log.warn("No Bearer tokens exist for the user {}", email);
            return null;
        }
        jwtTokenRepository.delete(tokenList.get(0));
        return tokenList.get(0).getToken();
    }

    @Override
    @Transactional
    public boolean verifyForgotPasswordTwoFa(String email, String code) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            Optional<TwoFactorAuthToken> optTwoFaToken = twoFactorAuthTokenRepository.findTokenForUserByEmail(email);
            if (optTwoFaToken.isPresent()) {
                TwoFactorAuthToken token = optTwoFaToken.get();
                if (token.getToken().equals(code)) {
                    log.info("Forgot password Two FA token is verified successfully");
                    twoFactorAuthTokenRepository.delete(token);
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean checkIfTwoFaVerified(String email) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            Optional<TwoFactorAuthToken> optTwoFaToken = twoFactorAuthTokenRepository.findTokenForUserByEmail(email);
            if (optTwoFaToken.isPresent()) {
                log.info("User still has a two fa token. User is not verified yet");
                return false;
            }
            log.info("user is already verified");
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public User registerUser(RegistrationRequest request) {
        Optional<User> user = this.findByEmail(request.getEmail());
        if (user.isPresent()) {
            throw new UserAlreadyExistsException("User with email " + request.getEmail() + " already exists");
        }
        User newUser = new User();
        newUser.setFirstName(request.getFirstName());
        newUser.setLastName(request.getLastName());
        newUser.setPassword(passwordEncoder.encode(request.getPassword()));
        newUser.setEmail(request.getEmail());
        newUser.setRole(request.getRole());
        newUser.setSocialLoginUser(false);
        // 🔥 添加这一行：直接启用用户，跳过邮件验证
        newUser.setEnabled(true);
        return userRepository.save(newUser);
    }

    private void sendEmailToVirtualProfileOwner(String email) {
        log.info("Sending email to virtual profile owner with email {}", email);
        List<Profile> profileList = profileService.getAllVirtualProfilesWithContactEmail(email);
        log.info("Found {} virtual profiles for email {}", profileList.size(), email);
        for(Profile profile : profileList) {
            if(profile.getProfileType() == ProfileType.VIRTUAL_ACT_PROFILE) {
                eventPublisher.publishEvent(new VirtualActAddedEvent(email, profile.getProfileId()));
            }
            else {
                eventPublisher.publishEvent(new VirtualVenueAddedEvent(email, profile.getProfileId()));
            }
        }
    }

    @Override
    public boolean verifyRegistrationEmail(String token) {
        VerificationToken theToken = verificationTokenRepository.findByToken(token);
        if ((theToken != null) && (theToken.getUser() != null)) {
            if ((theToken.getUser().isEnabled()) || (validateToken(token))) {
                sendEmailToVirtualProfileOwner(theToken.getUser().getEmail());
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean validNewPassword(User user, String newPassword) {
        if (passwordEncoder.matches(newPassword, user.getPassword())) {
            log.warn("Old and new password were same - rejecting reset/change password");
            return false;
        }
        return true;
    }

    @Transactional
    public HttpStatus changePassword(String oldPassword, String newPassword) {

        if (oldPassword.equals(newPassword)) {
            log.warn("Old and new passwords are same - rejecting change password");
            return HttpStatus.NOT_ACCEPTABLE;
        }
        if (!validatePassword(newPassword)) {
            log.info("new password does not satisfy password policy");
            return HttpStatus.NOT_ACCEPTABLE;
        }

        UserRegistrationDetails principal = (UserRegistrationDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        log.warn("Name:" + principal.getUsername());
        Optional<User> user = userRepository.findByEmail(principal.getUsername());
        if (user.isPresent()) {
            log.debug("User with email {} found", user.get().getEmail());
            if (!passwordEncoder.matches(oldPassword, user.get().getPassword())) {
                log.warn("old password does not match - rejecting reset request");
                return HttpStatus.EXPECTATION_FAILED;
            } else {
                log.debug("Updating password for username: {}", user.get().getEmail());
                user.get().setPassword(passwordEncoder.encode(newPassword));
                userRepository.save(user.get());
                return HttpStatus.OK;
            }
        }
        return HttpStatus.NOT_FOUND;
    }

    @Override
    public Optional<User> findByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    private int getTokenExpiryTime() {
        int expiryTime = Constants.DEFAULT_EXPIRY_TIME;
        if (constants != null) {
            int expTime = constants.getTokenExpirationTime();
            if (expTime != Constants.ERROR_RESULT) {
                expiryTime = expTime;
            }
        } else {
            log.warn("Constants is null; Using default value for token expiration");
        }
        return expiryTime;
    }

    @Override
    @Transactional
    public void saveUserVerificationToken(User newUser, String verificationToken) {

        int expiryTime = Constants.DEFAULT_EXPIRY_TIME;
        if (constants != null) {
            int expTime = constants.getTokenExpirationTime();
            if (expTime != Constants.ERROR_RESULT) {
                expiryTime = expTime;
            }
        }
        var token = new VerificationToken(verificationToken, newUser, expiryTime);
        verificationTokenRepository.save(token);
    }

    @Override
    @Transactional
    public void saveUserForgotPasswordToken(User newUser, String passwordToken) {
        int expiryTime = Constants.DEFAULT_EXPIRY_TIME;
        if (constants != null) {
            int expTime = constants.getTokenExpirationTime();
            if (expTime != Constants.ERROR_RESULT) {
                expiryTime = expTime;
            }
        }
        var token = new ForgotPasswordToken(passwordToken, newUser, expiryTime);
        forgotPasswordTokenRepository.save(token);
    }

    @Override
    @Transactional
    public void saveUserTwoFactorAuthToken(User user, String twoFactorToken) {
        int expiryTime = Constants.DEFAULT_EXPIRY_TIME;
        if (constants != null) {
            int expTime = constants.getTokenExpirationTime();
            if (expTime != Constants.ERROR_RESULT) {
                expiryTime = expTime;
            }
        }
        var token = new TwoFactorAuthToken(twoFactorToken, user, expiryTime);
        twoFactorAuthTokenRepository.save(token);
    }

    @Override
    @Transactional
    public boolean validateToken(String token) {
        VerificationToken theToken = verificationTokenRepository.findByToken(token);
        if (theToken != null) {
            User user = theToken.getUser();
            if (user != null) {
                Calendar calendar = Calendar.getInstance();
                if ((theToken.getExpirationTime().getTime() - calendar.getTime().getTime()) > 0) {
                    user.setEnabled(true);
                    userRepository.save(user);
                    // We need to delete the token after successful login
                    verificationTokenRepository.delete(theToken);
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean validatePassword(String password) {
        int len = password.length();

        if ((len > constants.getPasswordMaxLength()) || (len < constants.getPasswordMinLength())) {
            return false;
        }

        if ((constants.getPasswordMinUpperCaseChars() > 0) && !utilityService.stringContainsUpperCase(password)) {
            return false;
        }

        if ((constants.getPasswordMinSpecialChars() > 0) && !utilityService.stringContainsSpecialCharacters(password)) {
            return false;
        }

        if ((constants.getPasswordMinSpecialChars() > 0) && !utilityService.stringContainsSpecialCharacters(password)) {
            return false;
        }
        return true;
    }

    @Override
    @Transactional
    public boolean verifyTokenAndResetPassword(String token, String newPassword) {
        ForgotPasswordToken theToken = forgotPasswordTokenRepository.findByToken(token);
        if (theToken != null) {
            User user = theToken.getUser();
            if (user != null) {
                Calendar calendar = Calendar.getInstance();
                if ((theToken.getExpirationTime().getTime() - calendar.getTime().getTime()) > 0) {
                    user.setPassword(passwordEncoder.encode(newPassword));
                    // We need to delete the token after successful verification
                    userRepository.save(user);
                    forgotPasswordTokenRepository.delete(theToken);
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    @Transactional
    public boolean verifyTwoFactorAuthentication(String email, String token) {
        TwoFactorAuthToken theToken = twoFactorAuthTokenRepository.findByToken(token);
        if ((theToken != null) && (theToken.getUser() != null)) {
            if (theToken.getUser().getEmail().equals(email)) {
                Calendar calendar = Calendar.getInstance();
                if ((theToken.getExpirationTime().getTime() - calendar.getTime().getTime()) > 0) {
                    // We need to delete the token after successful verification
                    twoFactorAuthTokenRepository.delete(theToken);
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public HttpStatus resendRegisterEmail(String email) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            Optional<VerificationToken> optVerificationToken = verificationTokenRepository.findTokenForUserByEmail(email);
            if (optVerificationToken.isPresent()) {
                //We have a token for the user already.
                log.debug("user {} has a token - deleting it", email);
                verificationTokenRepository.delete(optVerificationToken.get());
                RegistrationCompleteEvent event = new RegistrationCompleteEvent(user);
                publishEvent(event);
                log.debug("Sending new verification email for user {}", email);
                return HttpStatus.OK;
            } else {
                log.debug("No verification token exist for user {}", email);
                RegistrationCompleteEvent event = new RegistrationCompleteEvent(user);
                publishEvent(event);
                return HttpStatus.OK;
            }
        } else {
            log.debug("User for email {} is not found", email);
            return HttpStatus.NOT_FOUND;
        }
    }

    @Override
    @Transactional
    public HttpStatus resendTwoFactorToken(String email) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            if(!user.isTwoFaEnabled()) {
                log.debug("user {} is not two factor enabled", email);
                return HttpStatus.EXPECTATION_FAILED;
            }
            Optional<TwoFactorAuthToken> optTwoFaToken = twoFactorAuthTokenRepository.findTokenForUserByEmail(email);

            if (optTwoFaToken.isPresent()) {
                //We have a token for the user already.
                log.debug("user {} already has a TwoFactorAuth token - deleting it", email);
                twoFactorAuthTokenRepository.delete(optTwoFaToken.get());
                TwoFactorAuthenticationEvent event = new TwoFactorAuthenticationEvent(user.getEmail());
                publishEvent(event);
                log.debug("Sending new TwoFactor Auth token for user {}", email);
                return HttpStatus.OK;
            } else {
                log.debug("No TwoFactorAuth token exist for user {}", email);
                TwoFactorAuthenticationEvent event = new TwoFactorAuthenticationEvent(user.getEmail());
                publishEvent(event);
                return HttpStatus.OK;
            }
        } else {
            log.debug("User for email {} is not found to send TwoFactorAuth token", email);
            return HttpStatus.NOT_FOUND;
        }
    }

    @Override
    @Transactional
    public boolean resetPasswordWithTfa(String email, String forgotPasswordToken, String newPassword, String code) {

        Optional<ForgotPasswordToken> optToken = forgotPasswordTokenRepository.findTokenForUserByEmail(email);
        Optional<TwoFactorAuthToken> optTwoFaToken = twoFactorAuthTokenRepository.findTokenForUserByEmail(email);
        Optional<User> optUser = userRepository.findByEmail(email);

        if (!optUser.isPresent()) {
            log.debug("Cannot find a user for email {}", email);
            return false;
        }
        if (!optToken.isPresent()) {
            log.warn("ForgotPassword token {} is not found for the user {}", forgotPasswordToken, email);
            return false;
        }

        if (!optTwoFaToken.isPresent()) {
            log.warn("TwoFactorAuthentication token {} is not found for the user {}", code, email);
            return false;
        }

        if (!optToken.get().getToken().equals(forgotPasswordToken)) {
            log.warn("ForgotPassword token is incorrect");
            return false;
        }

        if (!optTwoFaToken.get().getToken().equals(code)) {
            log.warn("Two Facvtor Authentication token is incorrect");
            return false;
        }

        Calendar calendar = Calendar.getInstance();
        if ((optToken.get().getExpirationTime().getTime() - calendar.getTime().getTime()) <= 0) {
            log.warn("Expired ForgotPassword token");
            return false;
        }
        if ((optTwoFaToken.get().getExpirationTime().getTime() - calendar.getTime().getTime()) <= 0) {
            log.warn("Expired Two factor authentication token");
            return false;
        }

        User user = optUser.get();
        user.setPassword(passwordEncoder.encode(newPassword));

        userRepository.save(user);
        // We need to delete the token after successful verification
        forgotPasswordTokenRepository.delete(optToken.get());
        twoFactorAuthTokenRepository.delete(optTwoFaToken.get());
        log.info("Successfully updated the password for user {}", email);
        return true;
    }

    private void publishEvent(ApplicationEvent event) {
        eventPublisher.publishEvent(event);
    }

    @Override
    @Transactional
    public void enableUser(String email) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            user.setEnabled(true);
            userRepository.save(user);
        }
    }

    @Override
    public boolean checkUserExists(String email) {
        Optional<User> optUser = userRepository.findByEmail(email);
        return optUser.isPresent();
    }
}
