package com.stageserver.service;

import com.stageserver.config.Constants;
import com.stageserver.exceptions.FileStorageException;
import com.stageserver.exceptions.RestApiException;
import com.stageserver.model.event.Event;
import com.stageserver.model.event.EventMediaInfo;
import com.stageserver.model.event.SpecialEvent;
import com.stageserver.model.profile.Profile;
import com.stageserver.model.profile.ProfileMedia;
import com.stageserver.model.profile.RiderDetails;
import com.stageserver.repository.ProfileMediaRepository;
import com.stageserver.repository.ProfileRepository;
import com.stageserver.repository.RiderDetailsRepository;
import com.stageserver.repository.SpecialEventRepository;
import com.stageserver.repository.event.EventMediaInfoRepository;
import com.stageserver.repository.event.EventRepository;
import com.stageserver.service.interfaces.I_MediaStorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.BiConsumer;

@Slf4j
@Service
public class MediaStorageService implements I_MediaStorageService {

    public static final int MAX_ALLOWED_FILE_SIZE = 10485760; //10MB
    private static final int MAX_RIDER_DOCUMENTS = 5;
    private static final int MAX_PDF_SIZE = 10485760 * 20; // 200MB

    public enum MediaTypeKey {
        PROFILE_IMAGE,
        RIDER_DOC,
        EVENT_IMAGE,
        SPECIAL_EVENT_IMAGE
    }

    @Value("${aws.s3.bucket-name}")
    private String bucketName;

    @Autowired
    private S3Client s3Client;

    @Autowired
    private ProfileMediaRepository profileMediaRepository;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private RiderDetailsRepository riderDetailsRepository;

    @Autowired
    private EventRepository eventRepository;

    @Autowired
    private EventMediaInfoRepository eventMediaInfoRepository;

    @Autowired
    private SpecialEventRepository specialEventRepository;

    @Autowired
    private final Constants constants;

    public MediaStorageService(Constants constants) {
        this.constants = constants;
    }

    private String validateAndEncodeFilename(String originalFilename) {
        if (originalFilename == null) return null;
        String encoded = URLEncoder.encode(originalFilename, StandardCharsets.UTF_8);
        String clean = StringUtils.cleanPath(encoded);
        if (clean.contains("..")) throw new FileStorageException("Invalid path sequence: " + originalFilename);
        return clean;
    }

    private void uploadFileToS3(String key, MultipartFile file) throws IOException {
        PutObjectRequest request = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(key)
                .contentType(file.getContentType())
                .build();
        s3Client.putObject(request, RequestBody.fromInputStream(file.getInputStream(), file.getSize()));
    }

    private boolean deleteFromS3(String key) {
        try {
            s3Client.deleteObject(DeleteObjectRequest.builder().bucket(bucketName).key(key).build());
            return true;
        } catch (NoSuchKeyException e) {
            return false;
        } catch (S3Exception e) {
            throw new FileStorageException("S3 delete failed");
        }
    }

    public Long getFileSize(String key) {
        try {
            HeadObjectResponse response = s3Client.headObject(
                    HeadObjectRequest.builder()
                            .bucket(bucketName)
                            .key(key)
                            .build()
            );
            return response.contentLength(); // size in bytes
        } catch (NoSuchKeyException e) {
            System.err.println("File not found: " + key);
            return null;
        } catch (S3Exception e) {
            throw new RuntimeException("Failed to get file size from S3", e);
        }
    }

    private String generateMediaUrl(MediaTypeKey type, String profileId, String fileName) {
        return switch (type) {
            case PROFILE_IMAGE, EVENT_IMAGE -> String.format("%s/api/v1/public/act/profiles/%s/media/%s", constants.getBackEndUrl(), profileId, fileName);
            case RIDER_DOC -> String.format("%s/api/v1/private/act/profiles/%s/rider/view/%s", constants.getBackEndUrl(), profileId, fileName);
            case SPECIAL_EVENT_IMAGE -> String.format("%s/api/v1/public/special-events/%s/image/%s", constants.getBackEndUrl(), profileId, fileName);
        };
    }

    private HttpStatus storeProfileImage(String email, MultipartFile file, String profileId, String s3Key, String fileName) throws IOException {
        uploadFileToS3(s3Key, file);
        String url = generateMediaUrl(MediaTypeKey.PROFILE_IMAGE, profileId, fileName);
        handleImageMedia(email, profileId, file, url);
        return HttpStatus.OK;
    }

    private HttpStatus storeRiderDoc(MultipartFile file, String profileId, String s3Key, String fileName) throws IOException {
        uploadFileToS3(s3Key, file);
        String url = generateMediaUrl(MediaTypeKey.RIDER_DOC, profileId, fileName);
        handleRiderDetails(profileId, url, file.getSize());
        return HttpStatus.OK;
    }

    private HttpStatus storeEventImage(MultipartFile file, String eventId, String s3Key, String fileName) throws IOException {
        uploadFileToS3(s3Key, file);
        String url = generateMediaUrl(MediaTypeKey.EVENT_IMAGE, eventId, fileName);
        handleEventImageFile(eventId, url, file.getSize());
        return HttpStatus.OK;
    }

    @Override
    public HttpStatus storeFile(String email, MultipartFile file, String profileId) {
        String fileName = validateAndEncodeFilename(file.getOriginalFilename());
        if (fileName == null) {
            return HttpStatus.BAD_REQUEST;
        }
        if (file.getSize() >= MAX_ALLOWED_FILE_SIZE) {
            return HttpStatus.BANDWIDTH_LIMIT_EXCEEDED;
        }
        String s3Key = String.format("images/%s/%s", profileId, fileName);
        try {
            return storeProfileImage(email, file, profileId, s3Key, fileName);
        } catch (IOException e) {
            throw new FileStorageException("Failed to upload image to S3");
        }
    }

    @Override
    public HttpStatus storePdfFile(String email, MultipartFile file, String profileId) {
        String fileName = validateAndEncodeFilename(file.getOriginalFilename());
        if (fileName == null) {
            return HttpStatus.BAD_REQUEST;
        }
        if (file.getSize() >= MAX_PDF_SIZE) {
            return HttpStatus.BANDWIDTH_LIMIT_EXCEEDED;
        }
        String s3Key = String.format("pdfs/%s/%s", profileId, fileName);
        try {
            return storeRiderDoc(file, profileId, s3Key, fileName);
        } catch (IOException e) {
            throw new FileStorageException("Failed to upload PDF to S3");
        }
    }

    @Override
    public HttpStatus storeEventImageFile(String email, MultipartFile file, String eventId) {
        String fileName = validateAndEncodeFilename(file.getOriginalFilename());
        if (fileName == null) {
            return HttpStatus.BAD_REQUEST;
        }
        if (file.getSize() >= MAX_ALLOWED_FILE_SIZE) {
            return HttpStatus.BANDWIDTH_LIMIT_EXCEEDED;
        }
        String s3Key = String.format("images/%s/%s", eventId, fileName);
        try {
            return storeEventImage(file, eventId, s3Key, fileName);
        } catch (IOException e) {
            throw new FileStorageException("Failed to upload event image to S3");
        }
    }

    @Override
    public String storeSpecialEventImageFile(String username, MultipartFile file, String profileId, String specialEventId) {
        String fileName = validateAndEncodeFilename(file.getOriginalFilename());
        if (fileName == null) {
            return null;
        }
        String s3Key = String.format("images/%s/se/%s", specialEventId, fileName);
        try {
            uploadFileToS3(s3Key, file);
            String url = generateMediaUrl(MediaTypeKey.SPECIAL_EVENT_IMAGE, specialEventId, fileName);
            handleSpecialEventImageFile(specialEventId, url, file.getSize());
            return url;
        } catch (IOException e) {
            throw new FileStorageException("Failed to upload special event image");
        }
    }

    @Override
    public boolean deleteFile(String email, String profileId, String imageName) {
        String s3Key = String.format("images/%s/%s", profileId, imageName);
        boolean deleted = deleteFromS3(s3Key);

        Long fileSize = getFileSize(s3Key);
        if(deleted) {
            updateProfileMediaOnDeletion(profileId, imageName, fileSize);
        }
        return deleted;
    }

    @Transactional
    private void updateProfileMediaOnDeletion(String profileId, String fileName, Long fileSize) {
        Optional<ProfileMedia> optMedia = profileMediaRepository.findByProfileId(profileId);
        optMedia.ifPresent(media -> {
            Long totalImageSize = media.getTotalImageSize();
            if(fileSize != null) {
                totalImageSize -= fileSize;
                media.setTotalImageSize(totalImageSize);
            }
            media.getImageUrls().removeIf(url -> url.contains(fileName));
            profileMediaRepository.save(media);
        });
    }

    @Override
    public boolean deleteRiderDocument(String email, String profileId, String fileName) {
        String s3Key = String.format("pdfs/%s/%s", profileId, fileName);
        boolean deleted = deleteFromS3(s3Key);
        String url = generateMediaUrl(MediaTypeKey.RIDER_DOC, profileId, fileName);
        Long fileSize = getFileSize(s3Key);

        if (deleted) {
            return updateRiderDetailsOnDeletion(profileId, url, fileSize);
        }
        return false;
    }

    @Override
    public boolean deleteEventImageFile(String email, String eventId, String fileName) {
        String s3Key = String.format("images/%s/%s", eventId, fileName);
        boolean deleted = deleteFromS3(s3Key);
        Long fileSize = getFileSize(s3Key);
        if (deleted) {
            updateEventImageFileOnDeletion(eventId, fileName, fileSize);
        }
        return deleted;
    }

    @Override
    public boolean deleteSpecialEventImageFile(String fileName, String profileId, String email, String specialEventId) {
        String s3Key = String.format("images/%s/se/%s", profileId, fileName);
        boolean deleted = deleteFromS3(s3Key);

        Long fileSize = getFileSize(s3Key);
        if (deleted) {
            updateSpecialEventImageFileOnDeletion(specialEventId, fileName, fileSize);
        }
        return deleted;
    }


    @Transactional
    private void handleImageMedia(String email, String profileId, MultipartFile file, String imageUrl) {
        Optional<ProfileMedia> optMedia = profileMediaRepository.findByProfileId(profileId);

        if (optMedia.isPresent()) {
            ProfileMedia media = optMedia.get();
            if (media.getTotalImageSize() + file.getSize() > MAX_ALLOWED_FILE_SIZE) {
                throw new FileStorageException("Total image size exceeds 10MB");
            }
            media.getImageUrls().add(imageUrl);
            media.setTotalImageSize(media.getTotalImageSize() + file.getSize());
            profileMediaRepository.save(media);
            profileService.addProfileMedia(email, profileId, media);
        } else {
            ProfileMedia media = new ProfileMedia();
            media.setImageUrls(new ArrayList<>());
            media.getImageUrls().add(imageUrl);
            media.setTotalImageSize(file.getSize());
            profileMediaRepository.save(media);
            profileService.addProfileMedia(email, profileId, media);
        }
    }

    @Transactional
    private void handleRiderDetails(String profileId, String riderDocumentUrl, long fileSize) {

        Profile profile = profileRepository.findByProfileId(profileId).orElse(null);
        if (profile != null) {
            Optional<ProfileMedia> optMedia = profileMediaRepository.findByProfileId(profileId);
            ProfileMedia media = optMedia.orElse(new ProfileMedia());

            List<RiderDetails> riderDetailsList = riderDetailsRepository.findByRideDetailsForAct(profileId).orElse(new ArrayList<>());

            if (riderDetailsList.size() >= MAX_RIDER_DOCUMENTS) {
                throw new FileStorageException("Cannot store more than 5 rider documents");
            }

            RiderDetails details = new RiderDetails();
            details.setRiderDocument(riderDocumentUrl);
            details.setUploadedTime(ZonedDateTime.now());
            riderDetailsList.add(details);

            media.setRideDetailsList(riderDetailsList);
            profileMediaRepository.save(media);
        } else {
            log.warn("Unable to find the profile to store rider document");
        }
    }

    @Transactional
    private boolean updateRiderDetailsOnDeletion(String profileId, String riderDocumentUrl, Long fileSize) {
        Optional<ProfileMedia> optProfileMedia = profileMediaRepository.findByProfileId(profileId);
        Optional<List<RiderDetails>> optRiderDetails = riderDetailsRepository.findByRideDetailsForAct(profileId);
        if ((optProfileMedia.isPresent()) && (optRiderDetails.isPresent())) {
            ProfileMedia profileMedia = optProfileMedia.get();

            Long totalImageSize = profileMedia.getTotalImageSize();
            if(fileSize != null) {
                totalImageSize -= fileSize;
                profileMedia.setTotalImageSize(totalImageSize);
            }

            List<RiderDetails> riderDetails = optRiderDetails.get();
            riderDetails.removeIf(rider -> rider.getRiderDocument().equals(riderDocumentUrl));
            riderDetailsRepository.saveAll(riderDetails);
            profileMedia.setRideDetailsList(riderDetails);
            profileMediaRepository.save(profileMedia);
            return true;
        }
        return false;
    }

    @Transactional
    private void handleEventImageFile(String eventId, String eventImageFileUrl, long fileSize) {
        Optional<Event> optEvent = eventRepository.findByEventId(eventId);
        if (optEvent.isPresent()) {
            Event event = optEvent.get();
            Optional<EventMediaInfo> optEventMediaInfo = eventMediaInfoRepository.findByEventId(eventId);
            EventMediaInfo eventMediaInfo;
            if (optEventMediaInfo.isPresent()) {
                eventMediaInfo = optEventMediaInfo.get();
                if (eventMediaInfo.getTotalImageSize() + fileSize > MAX_ALLOWED_FILE_SIZE) {
                    throw new FileStorageException("Total Events image size exceeds 10MB");
                }
                eventMediaInfo.getImageUrls().add(eventImageFileUrl);
                eventMediaInfo.setTotalImageSize(eventMediaInfo.getTotalImageSize() + fileSize);
            } else {
                eventMediaInfo = new EventMediaInfo();
                eventMediaInfo.setImageUrls(new ArrayList<>());
                eventMediaInfo.getImageUrls().add(eventImageFileUrl);
                eventMediaInfo.setTotalImageSize(fileSize);
            }
            eventMediaInfoRepository.save(eventMediaInfo);
            event.setEventMediaInfo(eventMediaInfo);
            eventRepository.save(event);
        }
    }

    @Transactional
    private void updateEventImageFileOnDeletion(String eventId, String fileName, Long fileSize) {
        Optional<Event> optEvent = eventRepository.findByEventId(eventId);
        if (optEvent.isPresent()) {
            Event event = optEvent.get();
            Optional<EventMediaInfo> optEventMediaInfo = eventMediaInfoRepository.findByEventId(eventId);
            if (optEventMediaInfo.isPresent()) {
                EventMediaInfo eventMediaInfo = optEventMediaInfo.get();
                Long totalImageSize = eventMediaInfo.getTotalImageSize();
                if(fileSize != null) {
                    totalImageSize -= fileSize;
                    eventMediaInfo.setTotalImageSize(totalImageSize);
                }
                eventMediaInfo.getImageUrls().removeIf(url -> url.contains(fileName));
                eventMediaInfoRepository.save(eventMediaInfo);
                event.setEventMediaInfo(eventMediaInfo);
                eventRepository.save(event);
            }
        }
    }

    @Transactional
    private void handleSpecialEventImageFile(String specialEventId, String eventImageFileUrl, long fileSize) {
        Optional<SpecialEvent> optEvent = specialEventRepository.findBySpecialEventIdWithScheduleTime(specialEventId);

        if (optEvent.isPresent()) {
            SpecialEvent specialEvent = optEvent.get();

            if (specialEvent.getTotalImageSize() + fileSize > MAX_ALLOWED_FILE_SIZE) {
                throw new FileStorageException("Total SpecialEvents image size exceeds 10MB");
            }

            String profileId = specialEvent.getOwnerProfileId();

            Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
            if (optProfile.isEmpty()) {
                log.warn("Profile not found for id: {}", profileId);
                return;
            }

            Profile profile = optProfile.get();

            List<SpecialEvent> specialEvents = profile.getSpecialEvents();
            if (specialEvents == null) {
                log.warn("No special events found for profile: {}", profileId);
                return;
            }

            // Update the base special event
            for (SpecialEvent event : specialEvents) {
                if (event.getSpecialEventId().equals(specialEventId)) {
                    if (event.getEventImageUrls() == null) {
                        event.setEventImageUrls(new ArrayList<>());
                    }
                    event.getEventImageUrls().add(eventImageFileUrl);
                    event.setTotalImageSize(event.getTotalImageSize() + fileSize);
                }
            }

            // If base event has recurring children, update them too
            if (specialEvent.isBaseForRecurringEvent()) {
                for (SpecialEvent event : specialEvents) {
                    if (specialEventId.equals(event.getBaseSpecialEventId())) {
                        if (event.getEventImageUrls() == null) {
                            event.setEventImageUrls(new ArrayList<>());
                        }
                        event.getEventImageUrls().add(eventImageFileUrl);
                        event.setTotalImageSize(event.getTotalImageSize() + fileSize);
                    }
                }
            }

            // Save the entire profile once at the end
            profileRepository.save(profile);
        }
    }

    @Transactional
    private void updateSpecialEventImageFileOnDeletion(String eventId, String fileName, Long fileSize) {
        Optional<SpecialEvent> optEvent = specialEventRepository.findBySpecialEventId(eventId);
        if (optEvent.isPresent()) {
            SpecialEvent specialEvent = optEvent.get();
            Long totalImageSize = specialEvent.getTotalImageSize();
            if(fileSize != null) {
                totalImageSize -= fileSize;
                specialEvent.setTotalImageSize(totalImageSize);
            }
            specialEvent.getEventImageUrls().removeIf(url -> url.contains(fileName));
            specialEventRepository.save(specialEvent);
        }
    }

    @Override
    @Transactional
    public boolean updateEventMediaInfo(String email, String eventId, EventMediaInfo eventMediaInfo) {
        Optional<Event> optEvent = eventRepository.findByEventId(eventId);
        if (optEvent.isPresent()) {
            Optional<EventMediaInfo> optEventMediaInfo = eventMediaInfoRepository.findByEventId(eventId);
            log.info("Updating media for Event {}", eventId);
            if (optEventMediaInfo.isPresent()) {
                updateExistingEventMediaInfo(optEventMediaInfo.get(), eventMediaInfo, eventId);
            } else {
                EventMediaInfo mediaInfo = new EventMediaInfo();
                mediaInfo.setImageUrls(new ArrayList<>());
                mediaInfo.setVideoUrls(new ArrayList<>());
                mediaInfo.getVideoUrls().addAll(eventMediaInfo.getVideoUrls());
                mediaInfo.setAudioUrls(new ArrayList<>());
                mediaInfo.getAudioUrls().addAll(eventMediaInfo.getAudioUrls());
//                mediaInfo.setSocialMediaUrls(new ArrayList<>());
//                mediaInfo.getSocialMediaUrls().addAll(eventMediaInfo.getSocialMediaUrls());
                eventMediaInfoRepository.save(mediaInfo);
                Event event = optEvent.get();
                event.setEventMediaInfo(mediaInfo);
                eventRepository.save(event);
            }
            return true;
        }
        return false;
    }

    @Transactional
    private void updateExistingEventMediaInfo(EventMediaInfo oldEventMediaInfo, EventMediaInfo newEventMediaInfo, String eventId) {
        Optional<Event> optEvent = eventRepository.findByEventId(eventId);
        if (optEvent.isPresent()) {
            Event event = optEvent.get();
            Optional<EventMediaInfo> optEventMediaInfo = eventMediaInfoRepository.findByEventId(eventId);

            if (oldEventMediaInfo.getAudioUrls() != null) {
                oldEventMediaInfo.getAudioUrls().addAll(newEventMediaInfo.getAudioUrls());
            } else {
                oldEventMediaInfo.setAudioUrls(new ArrayList<>());
                oldEventMediaInfo.getAudioUrls().addAll(newEventMediaInfo.getAudioUrls());
            }

//            if (oldEventMediaInfo.getSocialMediaUrls() != null) {
//                oldEventMediaInfo.getSocialMediaUrls().addAll(newEventMediaInfo.getSocialMediaUrls());
//            } else {
//                oldEventMediaInfo.setSocialMediaUrls(new ArrayList<>());
//                oldEventMediaInfo.getSocialMediaUrls().addAll(newEventMediaInfo.getSocialMediaUrls());
//            }

            if (oldEventMediaInfo.getVideoUrls() != null) {
                oldEventMediaInfo.getVideoUrls().addAll(newEventMediaInfo.getVideoUrls());
            } else {
                oldEventMediaInfo.setVideoUrls(new ArrayList<>());
                oldEventMediaInfo.getVideoUrls().addAll(newEventMediaInfo.getVideoUrls());
            }
            eventMediaInfoRepository.save(oldEventMediaInfo);
            event.setEventMediaInfo(oldEventMediaInfo);
            eventRepository.save(event);
        }
    }

    @Override
    @Transactional
    public boolean updateActMedia(String email, String profileId, ProfileMedia profileMedia) {
        Optional<ProfileMedia> optMedia = profileMediaRepository.findByProfileId(profileId);
        Profile profile = profileRepository.findByProfileId(profileId).orElse(null);
        log.info("Updating media for profile {}", profileId);
        if (profile == null) {
            log.warn("Cannot update media as profile {} does not exist", profileId);
            return false;
        }
        if (optMedia.isPresent()) {
            updateExistingActMedia(optMedia.get(), profileMedia, profileId);
        } else {
            ProfileMedia media = new ProfileMedia();
            media.setAudioUrls(new ArrayList<>());
            media.getImageUrls().addAll(profileMedia.getAudioUrls());
            media.setVideoUrls(new ArrayList<>());
            media.getVideoUrls().addAll(profileMedia.getVideoUrls());
            profile.setProfileMedia(media);
            profileMediaRepository.save(media);
        }
        return true;
    }

    @Override
    public Optional<ProfileMedia> readProfileMedia(String profileId) {
        return profileMediaRepository.findByProfileId(profileId);
    }

    @Transactional
    private void updateExistingActMedia(ProfileMedia oldProfileMedia, ProfileMedia newProfileMedia, String profileId) {

        Optional<List<RiderDetails>> optRiderDetails = riderDetailsRepository.findByRideDetailsForAct(profileId);

        if (oldProfileMedia.getAudioUrls() != null) {
            oldProfileMedia.getAudioUrls().addAll(newProfileMedia.getAudioUrls());
        } else {
            oldProfileMedia.setAudioUrls(new ArrayList<>());
            oldProfileMedia.getAudioUrls().addAll(newProfileMedia.getAudioUrls());
        }
        if (oldProfileMedia.getAudioUrls() != null) {
            oldProfileMedia.getAudioUrls().addAll(newProfileMedia.getAudioUrls());
        } else {
            oldProfileMedia.setAudioUrls(new ArrayList<>());
            oldProfileMedia.getAudioUrls().addAll(newProfileMedia.getAudioUrls());
        }

        if (oldProfileMedia.getVideoUrls() != null) {
            oldProfileMedia.getVideoUrls().addAll(newProfileMedia.getVideoUrls());
        } else {
            oldProfileMedia.setVideoUrls(new ArrayList<>());
            oldProfileMedia.getVideoUrls().addAll(newProfileMedia.getVideoUrls());
        }

        optRiderDetails.ifPresent(riderDetails -> oldProfileMedia.getRideDetailsList().addAll(riderDetails));
        profileMediaRepository.save(oldProfileMedia);
    }

    @Override
    public Optional<List<RiderDetails>> getRiderList(String email, String profileId) {
        return riderDetailsRepository.findByRideDetailsForAct(profileId);
    }
}
