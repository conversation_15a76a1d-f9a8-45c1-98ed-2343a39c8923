package com.stageserver.service;

import org.springframework.scheduling.Trigger;
import org.springframework.scheduling.TriggerContext;

import java.time.Instant;
import java.util.concurrent.atomic.AtomicBoolean;

public class OneTimeTrigger implements Trigger {

    private final Instant executionInstant;
    private final AtomicBoolean executed = new AtomicBoolean(false);

    public OneTimeTrigger(Instant executionInstant) {
        this.executionInstant = executionInstant;
    }

    @Override
    public Instant nextExecution(TriggerContext triggerContext) {
        // Only return the executionInstant once. Subsequent calls will return null.
        if (executed.compareAndSet(false, true)) {
            return executionInstant;
        }
        return null;
    }
}