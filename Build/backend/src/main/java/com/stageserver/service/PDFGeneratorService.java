package com.stageserver.service;

import com.stageserver.dto.contracts.ContractDetailsDto;
import com.stageserver.model.login.User;
import com.stageserver.repository.UserRepository;
import com.stageserver.repository.contract.ContractRepository;
import com.stageserver.service.interfaces.I_PDFGeneratorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.util.Optional;

@Service
@Slf4j
public class PDFGeneratorService implements I_PDFGeneratorService {

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ContractService contractService;

    @Value("${pdf.files.location}")
    private String pdfFilesLocation;

    private static final int TOP_LEFT_X = 0;
    private static final int TOP_LEFT_Y = 700;

    private String setupDirectory() {
        String directoryPath = pdfFilesLocation + File.separator;
        // Create the directory if it doesn't exist
        File directory = new File(directoryPath);
        if (!directory.exists()) {
            if (directory.mkdirs()) {
                log.info("Directory created: {}", directoryPath);
            } else {
                log.warn("Failed to create directory: {}", directoryPath);
            }
        }
        return directoryPath;
    }

    private void addTitle(PDPageContentStream contentStream, String title) {
        try {
            contentStream.setNonStrokingColor(Color.BLACK);
            contentStream.beginText();
            contentStream.setFont(PDType1Font.HELVETICA_BOLD, 18);
            contentStream.newLineAtOffset(200, TOP_LEFT_Y);
            contentStream.showText(title);
            contentStream.endText();
        } catch (IOException ex) {
            log.warn("Error while adding title to PDF document: {}", ex.getMessage());
        }
    }

    private void addParagraph(PDPageContentStream contentStream, String text, float x, float y, float width, float leading, PDType1Font font, float fontSize) throws IOException {
        String[] words = text.split(" ");
        StringBuilder line = new StringBuilder();
        float currentWidth = 0;

        for (String word : words) {
            float wordWidth = (font.getStringWidth(word + " ") / 1000) * fontSize;

            if (currentWidth + wordWidth > width) {
                contentStream.beginText();
                contentStream.newLineAtOffset(x, y);
                contentStream.showText(line.toString());
                contentStream.endText();
                line = new StringBuilder(word + " ");
                y -= leading;
                currentWidth = wordWidth;
            } else {
                line.append(word).append(" ");
                currentWidth += wordWidth;
            }
        }

        // Draw the last line
        contentStream.beginText();
        contentStream.newLineAtOffset(x, y);
        contentStream.showText(line.toString());
        contentStream.endText();
    }

    private String getPurchaserName(ContractDetailsDto contractDetails) {
        String email = contractDetails.getOriginatingUser();
        Optional<User> optUser = userRepository.findByEmail(email);
        return optUser.map(user -> user.getFirstName() + " " + user.getLastName()).orElse("Unknown");
    }

    private String getActOrVenueName(ContractDetailsDto contractDetails) {
        if(contractDetails.getActProfileName() != null) {
            return contractDetails.getActProfileName();
        }
        else if(contractDetails.getVenueProfileName() != null){
            return contractDetails.getVenueProfileName();
        }
        else {
            return "Unknown";
        }
    }

    private void addParticipants(PDPageContentStream contentStream, PDPage page, ContractDetailsDto contractDetails) {
        try {
            // Set font and size
            contentStream.setFont(PDType1Font.HELVETICA, 12);
            PDType1Font font = PDType1Font.HELVETICA;
            float fontSize = 12;

            // Define the starting positions for the paragraphs
            float margin = 50;
            float yStart = 650;
            float paragraphWidth = (page.getMediaBox().getWidth() - 3 * margin) / 2; // 2 columns with margin in between
            float yPosition = yStart;

            addParagraph(contentStream, "Purchaser:", margin, yPosition, paragraphWidth, 15, font, fontSize);
            addParagraph(contentStream, getPurchaserName(contractDetails), margin, yPosition - 20, paragraphWidth, 15, font, fontSize);
            addParagraph(contentStream, "123 Rodeo Drive, Ottawa, ON, Canada", margin, yPosition - 40, paragraphWidth, 15, font, fontSize);
            addParagraph(contentStream, getActOrVenueName(contractDetails), margin + paragraphWidth + margin, yPosition, paragraphWidth, 15, font, fontSize);
            addHorizontalLine(contentStream, yPosition - 60, page);
        }
        catch(IOException ex) {
            log.warn("Error while adding participants to PDF document: {}", ex.getMessage());
        }

    }

    private void addHorizontalLine(PDPageContentStream contentStream, float yPosition, PDPage page) {
        try {
            contentStream.setLineWidth(1.5f);
            contentStream.setStrokingColor(Color.BLACK);

            // Define the position of the horizontal line
            float startX = 50;
            float endX = page.getMediaBox().getWidth() - 50;


            // Draw the horizontal line
            contentStream.moveTo(startX, yPosition);
            contentStream.lineTo(endX, yPosition);
            contentStream.stroke();

        } catch (IOException ex) {
            log.warn("Error while drawing horizontal line in PDF document: {}", ex.getMessage());
        }
    }

    @Override
    public PDDocument generateContractPDFDocument(String contractId) {

        try {
            // Create a new PDF document
            PDDocument document = new PDDocument();
            String fileName = "final_output.pdf";
            String directoryPath = setupDirectory();
            // Add a new page
            PDPage page = new PDPage();
            document.addPage(page);

            // Start a new content stream
            PDPageContentStream contentStream = new PDPageContentStream(document, page);
            Optional<ContractDetailsDto> optContractDetails = contractService.getContractDetails(contractId);

            if (optContractDetails.isPresent()) {

                contentStream.setNonStrokingColor(Color.LIGHT_GRAY);

                // Draw the rectangle covering the entire page
                contentStream.addRect(0, 0, page.getMediaBox().getWidth(), page.getMediaBox().getHeight());
                contentStream.fill();

                addTitle(contentStream, "Contract Details");
                addParticipants(contentStream, page, optContractDetails.get());

                // Add a section title and paragraph
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA, 14);
                contentStream.newLineAtOffset(50, 550);
                contentStream.showText("Section 1: Introduction");
                contentStream.endText();

                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA, 12);
                contentStream.newLineAtOffset(50, 530);
                contentStream.showText("This is a paragraph in the first section of the document.");
                contentStream.endText();

                // Add another section title and paragraph
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA, 14);
                contentStream.newLineAtOffset(50, 500);
                contentStream.showText("Section 2: Content");
                contentStream.endText();

                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA, 12);
                contentStream.newLineAtOffset(50, 480);
                contentStream.showText("This section includes some intended data and information.");
                contentStream.endText();

                // Close the content stream
                contentStream.close();

                // Save the document
                document.save(directoryPath + "output.pdf");

                // Incorporate content from another PDF
                PDDocument sourceDocument = PDDocument.load(new File(directoryPath + "riderOne.pdf"));
                PDFMergerUtility merger = new PDFMergerUtility();
                merger.appendDocument(document, sourceDocument);
                sourceDocument.close();

                // Save the final document
                document.save(directoryPath + "final_output.pdf");
                return document;
            }
        } catch (IOException ex) {
            log.warn("Error while generating PDF document: {}", ex.getMessage());
        }
        return null;
    }
}
