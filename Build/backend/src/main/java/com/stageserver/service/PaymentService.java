package com.stageserver.service;

import com.nimbusds.jose.shaded.gson.JsonSyntaxException;
import com.stageserver.config.Constants;
import com.stageserver.dto.payment.StripePaymentRequestDto;
import com.stageserver.dto.payment.StripePaymentResponseDto;
import com.stageserver.model.contract.Contract;
import com.stageserver.model.payment.PaymentAction;
import com.stageserver.model.payment.StripePaymentStatus;
import com.stageserver.repository.StripePaymentStatusRepository;
import com.stageserver.repository.contract.ContractRepository;
import com.stageserver.service.interfaces.I_PaymentService;
import com.stripe.Stripe;
import com.stripe.exception.SignatureVerificationException;
import com.stripe.exception.StripeException;
import com.stripe.model.Event;
import com.stripe.model.EventDataObjectDeserializer;
import com.stripe.model.checkout.Session;
import com.stripe.net.Webhook;
import com.stripe.param.checkout.SessionCreateParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;


@Slf4j
@Service
public class PaymentService implements I_PaymentService {

    @Value("${stripe.payment.secret.key}")
    private String apiKey;

    @Value("${stripe.webhook.secret}")
    private String webhookSecret;

    @Value("${stage-server-stripe-payment-amount}")
    private String paymentAmount;

    @Value("${stage-server-stripe-payment-currency}")
    private String paymentCurrency;

    @Value("${stage-server-stripe-payment-quantity}")
    private String paymentQuantity;

    @Value("${stage-server-stripe-payment-name}")
    private String paymentName;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private StripePaymentStatusRepository stripePaymentStatusRepository;

    @Autowired
    private PaymentStateMachine paymentStateMachine;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private EventService eventService;

    @Autowired
    private Constants constants;


    public StripePaymentResponseDto processPayment(String email, String contractId) {
        StripePaymentRequestDto request = new StripePaymentRequestDto();
        request.setQuantity(Long.parseLong(paymentQuantity));
        request.setAmount(Long.parseLong(paymentAmount));
        request.setCurrency(paymentCurrency);
        request.setName(paymentName);
        request.setContractId(contractId);

        Stripe.apiKey = apiKey;

        SessionCreateParams.LineItem.PriceData.ProductData productData = SessionCreateParams.LineItem.PriceData.ProductData.builder()
                .setName(request.getName())
                .build();

        SessionCreateParams.LineItem.PriceData priceData = SessionCreateParams.LineItem.PriceData.builder()
                .setCurrency(request.getCurrency())
                .setUnitAmount(request.getAmount())
                .setProductData(productData)
                .build();
        SessionCreateParams.LineItem lineItem = SessionCreateParams.LineItem.builder()
                .setPriceData(priceData)
                .setQuantity(request.getQuantity())
                .build();

        String frontEndBaseUrl = constants.getFrontEndUrl() + "/en/booking-details";
        String successUrl = String.format("%s?contract-id=%s&status=success", frontEndBaseUrl, contractId);
        String cancelUrl = String.format("%s?contract-id=%s&status=failed", frontEndBaseUrl, contractId);

        SessionCreateParams params = SessionCreateParams.builder()
                .setMode(SessionCreateParams.Mode.PAYMENT)
                .setSuccessUrl(successUrl)
                .setCancelUrl(cancelUrl)
                .addLineItem(lineItem)
                .putMetadata("orderId", request.getContractId())
                .build();

        Session session = null;
        try {
            session = Session.create(params);
            StripePaymentStatus status = new StripePaymentStatus();
            status.setSessionId(session.getId());
            status.setContractId(request.getContractId());
            status.setPaymentSuccess(false);
            status.setUserEmail(email);
            stripePaymentStatusRepository.save(status);
            log.info("Session {} created and saved in the database for contractId {}",session.getId(), request.getContractId());

        } catch (StripeException e) {
            log.error("Error creating session: {} ",e.getMessage());
            return StripePaymentResponseDto.builder()
                    .status("error")
                    .message("Error creating session")
                    .build();
        }
        return StripePaymentResponseDto.builder()
                .status("success")
                .sessionId(session.getId())
                .sessionUrl(session.getUrl())
                .build();

    }

    @Override
    public boolean processWebhookEvent(String payload, String signature) {
        try {
            Event event = Webhook.constructEvent(payload, signature, webhookSecret);
            if ("checkout.session.completed".equals(event.getType())) {
                return handleCheckoutSessionCompleted(event);
            }
            return true; // Other event types (no-op)
        } catch (SignatureVerificationException e) {
            log.warn("Invalid webhook signature: {}", e.getMessage());
            return false;
        } catch (JsonSyntaxException e) {
            log.warn("Invalid webhook payload: {}", e.getMessage());
            return false;
        }
    }

    private boolean handleCheckoutSessionCompleted(Event event) {
        log.info("Handling event: {}", event.getType());

        EventDataObjectDeserializer deserializer = event.getDataObjectDeserializer();
        if (!deserializer.getObject().isPresent()) {
            log.warn("Deserialization failed. Raw object type: {}", event.getData().getObject().getClass().getName());
            return false;
        }

        Object deserializedObject = deserializer.getObject().get();

        if (!(deserializedObject instanceof Session)) {
            log.warn("Unexpected object type: {}", deserializedObject.getClass().getName());
            return false;
        }

        Session session = (Session) deserializedObject;
        log.info("Stripe Webhook received for session: {}", session.getId());

        StripePaymentStatus status = stripePaymentStatusRepository.findBySessionId(session.getId());
        if (status == null) {
            log.warn("No matching StripePaymentStatus found for sessionId: {}", session.getId());
            return false;
        }

        updatePaymentStatusFromSession(session, status);
        return true;
    }

    @Transactional
    private void updatePaymentStatusFromSession(Session session, StripePaymentStatus status) {
        String email = status.getUserEmail();

        status.setPaymentSuccess(true);
        status.setOrderId(session.getMetadata().get("orderId"));
        status.setAmountPaid(session.getAmountTotal());
        status.setCurrency(session.getCurrency());

        stripePaymentStatusRepository.save(status);

        log.info("Payment successful for session: {}", session.getId());
        updatePaymentStatus(email, status.getContractId(), session.getId());
    }


    @Override
    public boolean getPaymentStatus(String sessionId, String email) {
        StripePaymentStatus status = stripePaymentStatusRepository.findBySessionId(sessionId);
        if(status != null) {
            return status.isPaymentSuccess();
        }
        return false;
    }

    private void updatePaymentStatus(String email, String contractId, String sessionId) {
        //Determine who has to pay - i.e the payer
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if(optContract.isPresent()) {
            Contract contract = optContract.get();
            String originator = contract.getOriginatingUser();
            if((contract.getActProfileId() != null) && (!contract.getActProfileId().isEmpty())) {
                //We have an ACT in the contract he has to pay
                //Check if the user owns the ACT
                if(profileService.isMyProfile(email, contract.getActProfileId())) {
                    if(paymentStateMachine.sendEvent(PaymentAction.PAYER_PAYS, contractId)) {
                        updatePaymentState(contractId);
                    }
                }
                else {
                   // A user is booking an ACT.
                    //Check if the user is the originator of the contract
                    if(originator.equals(email)) {
                        if(paymentStateMachine.sendEvent(PaymentAction.PAYER_PAYS, contractId)) {
                            updatePaymentState(contractId);
                        }
                    }
                    else {
                        log.warn("updatePaymentState:: User {} is not authorized to pay for ACT - contract {}", email, contractId);
                    }
                }
            }
            else {
                //SO, A User is booking a Venue. The user has to pay - He is the originator
                if(originator.equals(email)) {
                    if(paymentStateMachine.sendEvent(PaymentAction.PAYER_PAYS, contractId)) {
                        updatePaymentState(contractId);
                    }
                }
                else {
                    log.warn("updatePaymentState:: User {} is not authorized to pay for Venue - contract {}", email, contractId);
                }
            }
        }
    }

    @Transactional
    private void updatePaymentState(String contractId) {
       Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if(optContract.isPresent()) {
            Contract contract = optContract.get();
            contract.setPaymentState(paymentStateMachine.getCurrentState());
            log.info("Setting Payment state to {} for contractId {}", paymentStateMachine.getCurrentState(), contractId);
            contractRepository.save(contract);
            com.stageserver.model.event.Event event = eventService.createEvent(contractId);
            if (event != null) {
                log.info("Event created for contract: {} with eventId: {}", contractId, event.getEventId());
            } else {
                log.info("No event created for for contract: {} - (FAN account)", contractId);
            }
        }
        else {
            log.warn("updatePaymentState:: Contract {} is not present in the system", contractId);
        }
    }
}
