package com.stageserver.service;

import com.stageserver.config.Constants;
import com.stageserver.dto.profile.ProfileDetailedViewDto;
import com.stageserver.dto.profile.ProfileMinimizedViewDto;
import com.stageserver.dto.mapper.*;
import com.stageserver.events.VirtualActAddedEvent;
import com.stageserver.events.VirtualVenueAddedEvent;
import com.stageserver.model.event.SpecialEvent;
import com.stageserver.model.profile.*;
import com.stageserver.model.common.ProfileStatus;
import com.stageserver.model.common.ProfileType;
import com.stageserver.model.common.SystemUser;
import com.stageserver.model.distribution.Distribution;
import com.stageserver.model.distribution.DistributionMember;
import com.stageserver.model.feedback.FeedbackMsg;
import com.stageserver.model.location.Location;
import com.stageserver.model.login.User;
import com.stageserver.model.schedule.Recurrence;
import com.stageserver.model.schedule.RecurrenceEndType;
import com.stageserver.model.schedule.ScheduleTime;
import com.stageserver.model.supported.*;
import com.stageserver.repository.*;
import com.stageserver.repository.schedule.RecurrenceEndTypeRepository;
import com.stageserver.repository.schedule.RecurrenceRepository;
import com.stageserver.repository.schedule.ScheduleTimeRepository;
import com.stageserver.service.interfaces.I_ProfileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.*;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class ProfileService implements I_ProfileService {

    @Autowired
    private SupportedMusicGenreRepository supportedMusicGenreRepository;

    @Autowired
    private SupportedLanguagesRepository supportedLanguagesRepository;

    @Autowired
    private SupportedActRolesRepository supportedActRolesRepository;

    @Autowired
    private SupportedRegionsRepository supportedRegionsRepository;

    @Autowired
    private SupportedOptionsRepository supportedOptionsRepository;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UtilityService utilityService;

    @Autowired
    private LocationRepository locationRepository;

    @Autowired
    private ProfileInfoRepository profileInfoRepository;

    @Autowired
    private ActSkillsRepository actSkillsRepository;

    @Autowired
    private ProfilePaymentsRepository profilePaymentsRepository;

    @Autowired
    private ProfileMediaRepository profileMediaRepository;

    @Autowired
    private EntertainmentTypeRepository entertainmentTypeRepository;

    @Autowired
    private EntertainmentTypeMemberRepository entertainmentTypeMemberRepository;

    @Autowired
    private MusicGenreRepository musicGenreRepository;

    @Autowired
    private ScheduleTimeRepository scheduleTimeRepository;

    @Autowired
    private DistributionRepository distributionRepository;

    @Autowired
    private RecurrenceRepository recurrenceRepository;

    @Autowired
    private RecurrenceEndTypeRepository recurrenceEndTypeRepository;

    @Autowired
    private DistributionMemberRepository distributionMemberRepository;

    @Autowired
    private RiderDetailsRepository riderDetailsRepository;

    @Autowired
    private FeedbackMsgRepository feedbackRepository;

    @Autowired
    private ProfileRatingRepository profileRatingRepository;

    @Autowired
    private SupportedEntertainmentTypesRepository supportedEntertainmentTypesRepository;

    @Autowired
    private VirtualContactRepository virtualContactRepository;

    @Autowired
    private VirtualVenueClaimTokenRepository virtualVenueClaimTokenRepository;

    @Autowired
    private VirtualActClaimTokenRepository virtualActClaimTokenRepository;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private SystemUserRepository systemUserRepository;

    @Autowired
    private LocationService locationService;

    @Autowired
    private WeeklyWorkingHoursRepository weeklyWorkingHoursRepository;

    @Autowired
    private WorkingHoursRepository workingHoursRepository;

    @Autowired
    private MessageBoxRepository messageBoxRepository;

    @Autowired
    private StatsService statsService;

    @Autowired
    private Constants constants;

    @Autowired
    private SpecialEventRepository specialEventRepository;

    @Value("${stage-server-back-end-url}")
    private String backEndUrl;


    private static final String UNPUBLISHED_NAME = "_UNPUBLISHED";

    @Override
    public Optional<SupportedMusicGenre> getSupportedMusicGenre() {

        //TODO: Fetching directly using locale is not working. Need to fix this.
        List<SupportedMusicGenre> musicGenreList = supportedMusicGenreRepository.findAll();
        if (musicGenreList.isEmpty()) {
            return Optional.empty();
        } else {
            for (SupportedMusicGenre supportedMusicGenre : musicGenreList) {
                if (supportedMusicGenre.getLocale().equals(LocaleContextHolder.getLocale().getLanguage())) {
                    List<MusicGenre> musicGenres = supportedMusicGenre.getGenreList();
                    musicGenres.forEach(musicGenre -> {
                        String url = backEndUrl + "/api/v1/public/act/icons/" + musicGenre.getIconUrl();
                        musicGenre.setIconUrl(url);

                    });
                    return Optional.of(supportedMusicGenre);
                }
            }
        }
        return Optional.empty();
    }

    @Override
    public Optional<SupportedLanguages> getSupportedLanguages() {
        List<SupportedLanguages> supportedLanguagesList = supportedLanguagesRepository.findAll();
        if (supportedLanguagesList.isEmpty()) {
            return Optional.empty();
        } else {
            for (SupportedLanguages supportedLanguages : supportedLanguagesList) {
                if (supportedLanguages.getLocale().equals(LocaleContextHolder.getLocale().getLanguage())) {
                    return Optional.of(supportedLanguages);
                }
            }
        }
        return Optional.empty();
    }

    @Override
    public Optional<SupportedRegions> getSupportedRegions() {
        List<SupportedRegions> supportedRegionsList = supportedRegionsRepository.findAll();
        if (supportedRegionsList.isEmpty()) {
            return Optional.empty();
        } else {
            for (SupportedRegions supportedRegions : supportedRegionsList) {
                if (supportedRegions.getLocale().equals(LocaleContextHolder.getLocale().getLanguage())) {
                    return Optional.of(supportedRegions);
                }
            }
        }
        return Optional.empty();
    }

    @Override
    public boolean checkIfNameAvailableInRadius(String profileId, String email, String profileName) {
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if (optProfile.isPresent()) {
            Optional<Location> optLocation = locationRepository.findByProfileId(profileId);
            if (optLocation.isPresent()) {
                double longitude = optLocation.get().getLongitude();
                double latitude = optLocation.get().getLatitude();
                Optional<List<Profile>> optProfileList = profileRepository.findProfilesWithinRadius(latitude, longitude, 100000);
                optProfileList.ifPresent(profiles -> log.info("Found {} profiles within radius: {}", profiles.size(), 100000));
                if (optProfileList.isPresent()) {
                    for (Profile profile : optProfileList.get()) {
                        if ((profile.getProfileName().equals(profileName)) && (!profile.getProfileId().equals(profileId))) {
                            log.warn("Profile name {} already exists in the radius", profileName);
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }

    @Override
    @Transactional
    public boolean updateProfileName(String profileId, String email, String newProfileName) {
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if (optProfile.isPresent()) {
            Profile profile = optProfile.get();
            profile.setProfileName(newProfileName);
            profileRepository.save(profile);
            return true;
        }
        return false;
    }

    @Override
    public boolean checkIfNameUpdateAllowed(String email, String profileId, String actProfileName) {
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if(optProfile.isPresent()) {
            Profile profile = optProfile.get();
            if((profileId.equals(profile.getProfileId()) && profile.getProfileName().equals(actProfileName))) {
                // Updating the same profile.
                return true;
            }
        }
        return checkIfNameAvailableInRadius(profileId, email, actProfileName);
    }

    @Override
    public Optional<SupportedOptions> getSupportedOptions() {
        List<SupportedOptions> supportedOptionsList = supportedOptionsRepository.findAll();
        if (supportedOptionsList.isEmpty()) {
            return Optional.empty();
        } else {
            for (SupportedOptions supportedOptions : supportedOptionsList) {
                if (supportedOptions.getLocale().equals(LocaleContextHolder.getLocale().getLanguage())) {
                    return Optional.of(supportedOptions);
                }
            }
        }
        return Optional.empty();
    }

    @Override
    public Optional<SupportedActRoles> getSupportedActRoles() {
        List<SupportedActRoles> supportedActRolesListList = supportedActRolesRepository.findAll();
        if (supportedActRolesListList.isEmpty()) {
            return Optional.empty();
        } else {
            for (SupportedActRoles supportedActRoles : supportedActRolesListList) {
                if (supportedActRoles.getLocale().equals(LocaleContextHolder.getLocale().getLanguage())) {
                    return Optional.of(supportedActRoles);
                }
            }
        }
        return Optional.empty();
    }

    @Override
    public Optional<SupportedEntertainmentTypes> getSupportedEntertainmentTypes() {
        List<SupportedEntertainmentTypes> entertainmentTypesList = supportedEntertainmentTypesRepository.findAll();

        if (entertainmentTypesList.isEmpty()) {
            return Optional.empty();
        } else if (entertainmentTypesList.size() == 1) {
            SupportedEntertainmentTypes entertainmentTypes = entertainmentTypesList.get(0);
            entertainmentTypes.getEntertainmentTypeList().forEach(entertainmentType -> {
                String url = backEndUrl + "/api/v1/public/act/icons/" + entertainmentType.getIconUrl();
                entertainmentType.setIconUrl(url);
            });

            return Optional.of(entertainmentTypesList.get(0));
        } else {
            log.warn("Multiple SupportedEntertainmentTypes found");
        }
        return Optional.empty();
    }

    @Override
    public String createProfile(String email, Profile profile) {

        return switch (profile.getProfileType()) {
            case ACT_PROFILE -> createActProfile(email, profile);
            case VIRTUAL_ACT_PROFILE -> createVirtualActProfile(email, profile);
            case VENUE_PROFILE -> createVenueProfile(email, profile);
            case VIRTUAL_VENUE_PROFILE -> createVirtualVenueProfile(email, profile);
            default -> {
                log.warn("Invalid Profile Type {}", profile.getProfileType());
                yield null;
            }
        };
    }

    @Transactional
    private String createActProfile(String email, Profile newProfile) {
        log.info("creating Act Profile for {}", email);
        newProfile.setStatus(ProfileStatus.STATUS_CREATED);
        newProfile.setProfileType(ProfileType.ACT_PROFILE);
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            String profileId = utilityService.generateUniqueUUID();
            newProfile.setProfileId(profileId);
            if (newProfile.isUseMyEmail()) {
                newProfile.setProfileEmail(email);
            }
            newProfile.setVirtualContact(null);
            User user = optUser.get();
            List<Profile> profiles = user.getProfileList();
            profiles.add(newProfile);
            user.setProfileList(profiles);
            userRepository.save(user);
            profileRepository.save(newProfile);
            return profileId;
        }
        return null;
    }

    private Optional<SystemUser> getSystemUser() {
        List<SystemUser> systemUsers = systemUserRepository.findAll();
        if (systemUsers.isEmpty()) {
            log.warn("No system user found");
            return Optional.empty();
        } else if (systemUsers.size() > 1) {
            log.warn("Multiple system users found");
            return Optional.empty();
        }
        return Optional.of(systemUsers.get(0));
    }

    @Transactional
    private String createVirtualActProfile(String email, Profile actVirtualProfile) {

        log.info("creating Virtual Act Profile for {}", email);
        actVirtualProfile.setStatus(ProfileStatus.STATUS_CREATED);
        actVirtualProfile.setProfileType(ProfileType.VIRTUAL_ACT_PROFILE);
        Optional<SystemUser> optSystemUser = getSystemUser();
        if (optSystemUser.isPresent()) {
            String profileId = utilityService.generateUniqueUUID();
            actVirtualProfile.setProfileId(profileId);
            if (actVirtualProfile.isUseMyEmail()) {
                actVirtualProfile.setProfileEmail(email);
            }

            SystemUser systemUser = optSystemUser.get();

            List<Profile> profiles = systemUser.getProfileList();
            profiles.add(actVirtualProfile);
            systemUser.setProfileList(profiles);
            systemUserRepository.save(systemUser);
            eventPublisher.publishEvent(new VirtualActAddedEvent(actVirtualProfile.getVirtualContact().getContactEmail(), profileId));
            return profileId;
        }
        return null;
    }

    @Transactional
    private String createVenueProfile(String email, Profile newProfile) {

        log.info("creating Venue Profile for {}", email);
        newProfile.setStatus(ProfileStatus.STATUS_CREATED);
        newProfile.setProfileType(ProfileType.VENUE_PROFILE);
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            String profileId = utilityService.generateUniqueUUID();
            newProfile.setProfileId(profileId);
            if (newProfile.isUseMyEmail()) {
                newProfile.setProfileEmail(email);
            }
            newProfile.setVirtualContact(null);
            User user = optUser.get();
            List<Profile> profiles = user.getProfileList();
            profiles.add(newProfile);
            user.setProfileList(profiles);
            userRepository.save(user);
            profileRepository.save(newProfile);
            return profileId;
        }
        return null;
    }

    @Transactional
    private String createVirtualVenueProfile(String email, Profile virtualVenueProfile) {
        log.info("creating Virtual Venue Profile for {}", email);
        virtualVenueProfile.setStatus(ProfileStatus.STATUS_CREATED);
        virtualVenueProfile.setProfileType(ProfileType.VIRTUAL_VENUE_PROFILE);
        Optional<SystemUser> optSystemUser = getSystemUser();
        if (optSystemUser.isPresent()) {
            String profileId = utilityService.generateUniqueUUID();
            virtualVenueProfile.setProfileId(profileId);
            if (virtualVenueProfile.isUseMyEmail()) {
                virtualVenueProfile.setProfileEmail(email);
            }
            SystemUser systemUser = optSystemUser.get();

            List<Profile> profiles = systemUser.getProfileList();
            profiles.add(virtualVenueProfile);
            systemUser.setProfileList(profiles);
            systemUserRepository.save(systemUser);
            eventPublisher.publishEvent(new VirtualVenueAddedEvent(virtualVenueProfile.getVirtualContact().getContactEmail(), profileId));
            return profileId;
        }
        return null;
    }

    @Override
    public Page<Profile> readAllProfiles(String email, int page, int size) {

        Pageable pageable = PageRequest.ofSize(size).withPage(page);
        return profileRepository.findAllForUser(email, pageable);
    }

    @Override
    public Profile readProfile(String email, String profileId) {
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        return optProfile.orElse(null);
    }

    @Override
    @Transactional
    public boolean updateProfile(String email, String profileId, Profile newProfile) {
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);

        Optional<ActSkills> optActSkills = actSkillsRepository.findByProfileId(profileId);
        Optional<Location> optLocation = locationRepository.findByProfileId(profileId);
        Optional<ProfileInfo> optActInfo = profileInfoRepository.findByProfileId(profileId);
        Optional<ProfilePayments> optActPayments = profilePaymentsRepository.findByProfileId(profileId);
        Optional<ProfileMedia> optActMedia = profileMediaRepository.findByProfileId(profileId);
        Optional<List<ScheduleTime>> optScheduleTimeList = scheduleTimeRepository.findByProfileId(profileId);
        Optional<Distribution> optDistribution = distributionRepository.findByProfileId(profileId);
        Optional<List<FeedbackMsg>> optFeedbacksReceived = feedbackRepository.findAllReceivedFeedbacks(profileId);
        Optional<List<FeedbackMsg>> optFeedbacksProvided = feedbackRepository.findAllProvidedFeedbacks(profileId);
        Optional<ProfileRating> optActRating = profileRatingRepository.findByProfileId(profileId);
        Optional<VirtualContact> optVirtualContact = virtualContactRepository.findByActProfileId(profileId);
        Optional<MessageBox> optMessageBox = messageBoxRepository.findByProfileId(profileId);

        if (optProfile.isPresent()) {
            optActInfo.ifPresent(info -> updateProfileInfo(newProfile, profileId, info));
            optActInfo.ifPresent(newProfile::setProfileInfo);
            optActPayments.ifPresent(newProfile::setProfilePayments);
            optLocation.ifPresent(newProfile::setLocation);
            optActSkills.ifPresent(skills -> updateActSkills(newProfile, profileId, skills));
            optScheduleTimeList.ifPresent(scheduleTimes -> updateScheduleTime(newProfile, profileId, scheduleTimes));
            optDistribution.ifPresent(distribution -> updateDistribution(newProfile, profileId, distribution));
            optActMedia.ifPresent(actMedia -> updateActMedia(newProfile, profileId, actMedia));
            optFeedbacksReceived.ifPresent(newProfile::setReceivedFeedbacks);
            optFeedbacksProvided.ifPresent(newProfile::setProvidedFeedbacks);
            optActRating.ifPresent(newProfile::setProfileRating);
            optVirtualContact.ifPresent(newProfile::setVirtualContact);
            optMessageBox.ifPresent(newProfile::setMessageBox);
            Profile oldProfile = optProfile.get();
            newProfile.setElementId(oldProfile.getElementId());
            newProfile.setProfileId(oldProfile.getProfileId());

            newProfile.setStatus(oldProfile.getStatus());
            profileRepository.save(newProfile);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public HttpStatus deleteProfile(String email, String profileId) {

       // Profile profile = updateAllProfileNodes(profileId);
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if(optProfile.isPresent()) {
            Profile profile = optProfile.get();
            if (isMyProfile(email, profileId)) {
                // Only allow to delete if own profile
                if (profile.getStatus().equals(ProfileStatus.STATUS_PUBLISHED)) {
                    profile.setStatus(ProfileStatus.STATUS_DELETED);
                    profile.setProfileName(profile.getProfileName() + UNPUBLISHED_NAME);
                    profileRepository.save(profile);
                } else {
                    // We need to delete everything related to this profile
                    // Check if this profile has any Contracts that is active, if not we can delete

                    if(profileRepository.getContractsCountForProfileId(profileId) == 0) {
                        profileRepository.deleteProfileAndNodes(profileId);
                    }
                    else {
                        log.warn("Profile {} has active contracts, cannot delete", profileId);
                        return HttpStatus.FORBIDDEN;
                    }
                }
                return HttpStatus.OK;
            }
        }
        return HttpStatus.BAD_REQUEST;
    }

    @Override
    public boolean validateAddress(Location location) {
        try {
            location = locationService.computeGeocode(location);
            return (location != null) && (location.getLatitude() != 0) && (location.getLongitude() != 0);
        } catch (Exception e) {
            log.warn("validateAddress - Geocode computation failed for : {}", location.toString());
            return false;
        }
    }

    @Override
    @Transactional
    public Location addProfileLocation(String email, String profileId, Location location) {

        try {
            location = locationService.computeGeocode(location);
        } catch (Exception e) {
            log.warn("addActLocation - Geocode computation failed for : {}", location.toString());
        }
        Profile profile = profileRepository.findByProfileId(profileId).orElse(null);
        if ((!locationAlreadyExist(email, profileId)) && (profile != null)) {
            profile.setLocation(location);
            locationRepository.save(location);
            profileRepository.save(profile);
            return location;
        }
        return null;
    }

    @Override
    public boolean locationAlreadyExist(String email, String profileId) {
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if (optProfile.isPresent()) {
            Profile profile = optProfile.get();
            Optional<Location> optLocation = locationRepository.findByProfileId(profileId);
            return optLocation.isPresent();
        }
        return false;
    }

    @Override
    @Transactional
    public Location updateProfileLocation(String email, String profileId, Location location) {
        Optional<Location> optLocation = locationRepository.findByProfileId(profileId);

        try {
            location = locationService.computeGeocode(location);
        } catch (Exception e) {
            log.warn("updateActLocation - Geocode computation failed for : {}", location.toString());
        }

        if (optLocation.isPresent()) {
            Location oldLocation = optLocation.get();
            location.setElementId(oldLocation.getElementId());
            locationRepository.save(location);
            return location;
        }
        return null;
    }

    @Override
    public Optional<Location> readProfileLocation(String email, String profileId) {
        return locationRepository.findByProfileId(profileId);
    }

    @Override
    @Transactional
    public boolean deleteProfileLocation(String username, String profileId) {
        Optional<Location> optLocation = locationRepository.findByProfileId(profileId);
        if (optLocation.isPresent()) {
            locationRepository.delete(optLocation.get());
            return true;
        }
        return false;

    }

    @Override
    public boolean infoAlreadyExist(String email, String profileId) {
        // check if exists
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if (optProfile.isPresent()) {
            Profile profile = optProfile.get();
            Optional<ProfileInfo> optActInfo = profileInfoRepository.findByProfileId(profileId);
            return optActInfo.isPresent();
        }
        return false;
    }

    @Override
    @Transactional
    public boolean addProfileInfo(String email, String profileId, ProfileInfo profileInfo) {

        Profile profile = profileRepository.findByProfileId(profileId).orElse(null);
        if (profile != null) {
            profile.setProfileInfo(profileInfo);
            profileInfoRepository.save(profileInfo);
            profileRepository.save(profile);
            return true;
        }
        return false;
    }

    @Override
    public Optional<ProfileInfo> readProfileInfo(String email, String profileId) {
        Optional<ProfileInfo> optProfileInfo = profileInfoRepository.findByProfileId(profileId);
        if (optProfileInfo.isPresent()) {
            ProfileInfo profileInfo = optProfileInfo.get();
            Optional<WeeklyWorkingHours> optWeeklyWorkingHours = weeklyWorkingHoursRepository.findWeeklyWorkingHoursByProfileId(profileId);
            if (optWeeklyWorkingHours.isPresent()) {
                WeeklyWorkingHours weeklyWorkingHours = optWeeklyWorkingHours.get();
                List<WorkingHours> workingHoursList = workingHoursRepository.getWorkingHoursList(profileId);
                List<WorkingHours> newWorkingHoursList = new ArrayList<>(workingHoursList);
                weeklyWorkingHours.setWorkingHoursList(newWorkingHoursList);
                profileInfo.setWeeklyWorkingHours(weeklyWorkingHours);

            }
            return Optional.of(profileInfo);
        }
        return profileInfoRepository.findByProfileId(profileId);
    }

    @Override
    @Transactional
    public boolean updateProfileInfo(String email, String profileId, ProfileInfo profileInfo) {
        Optional<ProfileInfo> optActInfo = profileInfoRepository.findByProfileId(profileId);
        Profile profile = profileRepository.findByProfileId(profileId).orElse(null);
        if ((profile != null) && (optActInfo.isPresent())) {
            ProfileInfo oldProfileInfo = optActInfo.get();
            profileInfo.setElementId(oldProfileInfo.getElementId());
            profileInfoRepository.save(profileInfo);
            profile.setProfileInfo(profileInfo);
            profileRepository.save(profile);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean addActSkills(String email, String profileId, ActSkills actSkills) {
        Profile profile = profileRepository.findByProfileId(profileId).orElse(null);
        if (profile != null) {
            profile.setActSkills(actSkills);
            actSkillsRepository.save(actSkills);
            profileRepository.save(profile);
            return true;
        }
        return false;
    }

    @Override
    public Optional<ActSkills> readActSkills(String email, String profileId) {
        Optional<ActSkills> optActSkills = actSkillsRepository.findByProfileId(profileId);
        Optional<EntertainmentType> optEntertainmentType = entertainmentTypeRepository.findByProfileId(profileId);
        Optional<List<MusicGenre>> optMusicGenreList = musicGenreRepository.findByProfileId(profileId);
        Optional<List<EntertainmentTypeMember>> optEntTypeMembers = entertainmentTypeMemberRepository.findMembersByProfileId(profileId);

        if (optActSkills.isPresent()) {
            ActSkills skills = optActSkills.get();
            optEntertainmentType.ifPresent(skills::setEntertainmentType);
            optEntTypeMembers.ifPresent(entertainmentTypeMembers -> skills.getEntertainmentType().setMembers(entertainmentTypeMembers));
            optMusicGenreList.ifPresent(skills::setMusicGenreList);
            return Optional.of(skills);
        }
        return Optional.empty();
    }

    @Override
    @Transactional
    public boolean addProfilePayments(String email, String profileId, ProfilePayments profilePayments) {
        Profile profile = profileRepository.findByProfileId(profileId).orElse(null);
        if (profile != null) {
            profile.setProfilePayments(profilePayments);
            profilePaymentsRepository.save(profilePayments);
            profileRepository.save(profile);
            return true;
        }
        return false;
    }

    @Override
    public Optional<ProfilePayments> readActPayments(String email, String profileId) {
        return profilePaymentsRepository.findByProfileId(profileId);
    }

    @Override
    @Transactional
    public boolean addProfileMedia(String email, String profileId, ProfileMedia profileMedia) {
        Profile profile = profileRepository.findByProfileId(profileId).orElse(null);
        if (profile != null) {
            profile.setProfileMedia(profileMedia);
            profileMediaRepository.save(profileMedia);
            profileRepository.save(profile);
            return true;
        }
        log.warn("Unable to add ProfileMedia as profile {} does not exist", profileId);
        return false;
    }

    @Override
    @Transactional
    public boolean updateProfilePayments(String email, String profileId, ProfilePayments profilePayments) {
        Optional<ProfilePayments> optActPayments = profilePaymentsRepository.findByProfileId(profileId);
        if (optActPayments.isPresent()) {
            ProfilePayments oldProfilePayments = optActPayments.get();
            profilePayments.setElementId(oldProfilePayments.getElementId());
            profilePaymentsRepository.save(profilePayments);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean updateNotForRent(String email, String profileId, boolean enable) {
        Optional<ProfilePayments> optActPayments = profilePaymentsRepository.findByProfileId(profileId);
        if (optActPayments.isPresent()) {
            ProfilePayments profilePayments = optActPayments.get();
            profilePayments.setForRent(enable);
            profilePaymentsRepository.save(profilePayments);
            return true;
        } else {
            Profile profile = profileRepository.findByProfileId(profileId).orElse(null);
            if (profile != null) {
                ProfilePayments newProfilePayments = new ProfilePayments();
                newProfilePayments.setForRent(enable);
                profile.setProfilePayments(newProfilePayments);
                profilePaymentsRepository.save(newProfilePayments);
                profileRepository.save(profile);
                return true;
            }
        }
        return false;
    }

    public boolean isFavorite(String email, String profileId) {
        if ((email != null) && (profileId != null) && (!email.isEmpty())) {
            Optional<User> optUser = userRepository.findByEmail(email);
            if (optUser.isPresent()) {
                User user = optUser.get();
                return user.getFavouriteActProfiles().contains(profileId);
            }
        }
        return false;
    }

    public List<ProfileMinimizedViewDto> populateMinimizedActProfiles(List<Profile> profileList) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        LocationDtoMapper locationDtoMapper = new LocationDtoMapper();
        List<ProfileMinimizedViewDto> list = new ArrayList<>();
        for (Profile profile : profileList) {
            ProfileMinimizedViewDto dto = new ProfileMinimizedViewDto();
            dto.setProfileType(profile.getProfileType());
            dto.setProfileId(profile.getProfileId());
            dto.setProfileName(profile.getProfileName());
            Optional<Location> optLocation = locationRepository.findByProfileId(profile.getProfileId());
            optLocation.ifPresent(location -> dto.setLocationDto(locationDtoMapper.toLocationDto(location)));
            Optional<ProfileMedia> media = profileMediaRepository.findByProfileId(profile.getProfileId());
            media.ifPresent(actMedia -> dto.setProfileImageUrls(actMedia.getImageUrls()));
            Optional<ProfileInfo> optInfo = profileInfoRepository.findByProfileId(profile.getProfileId());
            optInfo.ifPresent(actInfo -> dto.setSocialMediaLinks(actInfo.getSocialMediaLinks()));

            Optional<ProfilePayments> optPayments = profilePaymentsRepository.findByProfileId(profile.getProfileId());
            optPayments.ifPresent(actPayments -> dto.setMinimumPrice(actPayments.getMinimumPrice()));
            optPayments.ifPresent(actPayments -> dto.setTypicalPrice(actPayments.getTypicalPrice()));
            ProfilePaymentsDtoMapper mapper = new ProfilePaymentsDtoMapper();
            optPayments.ifPresent(actPayments -> dto.setMinPriceChargingType(mapper.toChargingTypeDto(actPayments.getMinPriceChargingType())));
            optPayments.ifPresent(actPayments -> dto.setTypicalPriceChargingType(mapper.toChargingTypeDto(actPayments.getTypicalPriceChargingType())));
            optPayments.ifPresent(actPayments -> dto.setCurrency(actPayments.getCurrency()));
            dto.setNumberOfFollowers(profile.getNumberOfFollowers());
            dto.setProfileStatus(profile.getStatus());
            dto.setProfileType(profile.getProfileType());
            if ((profile.getProfileType() == ProfileType.VIRTUAL_ACT_PROFILE) || (profile.getProfileType() == ProfileType.VIRTUAL_VENUE_PROFILE)) {
                Optional<VirtualContact> optVirtualActContact = virtualContactRepository.findByActProfileId(profile.getProfileId());
                optVirtualActContact.ifPresent(virtualContact -> dto.setVirtualContactDto(new VirtualContactDtoMapper().toVirtualContactDto(virtualContact)));
            }
            Optional<ProfileRating> optActRating = profileRatingRepository.findByProfileId(profile.getProfileId());
            if (optActRating.isPresent()) {
                dto.setPopularityStars(optActRating.get().getOverallRating());
                dto.setNumberOfReviews(optActRating.get().getNumberOfRatings());
            }

            if (profile.getProfileType() == ProfileType.VENUE_PROFILE) {
                if (optInfo.isPresent()) {
                    Optional<WeeklyWorkingHours> optWeeklyWorkingHours = weeklyWorkingHoursRepository.findWeeklyWorkingHoursByProfileId(profile.getProfileId());
                    if (optWeeklyWorkingHours.isPresent()) {
                        WeeklyWorkingHours weeklyWorkingHours = optWeeklyWorkingHours.get();
                        List<WorkingHours> workingHoursList = workingHoursRepository.getWorkingHoursList(profile.getProfileId());
                        List<WorkingHours> newWorkingHoursList = new ArrayList<>(workingHoursList);
                        weeklyWorkingHours.setWorkingHoursList(newWorkingHoursList);
                        dto.setWeeklyWorkingHoursDto(new WeeklyWorkingHoursDtoMapper().toWeeklyWorkingHoursDto(weeklyWorkingHours));
                    }
                }
            }
            if (isFavorite(auth.getName(), profile.getProfileId())) {
                dto.setFavouriteSelected(true);
            }

            if (optActRating.isPresent()) {
                BigDecimal bd = BigDecimal.valueOf(optActRating.get().getOverallRating());
                // Set the scale to 2 decimal places and round using HALF_UP
                bd = bd.setScale(2, RoundingMode.HALF_UP);
                dto.setPopularityStars(bd.doubleValue());
                dto.setNumberOfReviews(optActRating.get().getNumberOfRatings());
            }
            dto.setAveragePayForGig(profile.getAverageGigsPrice());
            dto.setAveragePricePerBooking(profile.getAverageBookingPrice());
            list.add(dto);
        }
        return list;
    }

    @Override
    public Page<ProfileMinimizedViewDto> searchAllInCurrentUserProfiles(String email, int page, int size, ProfileType profileType) {
        Pageable pageable = PageRequest.ofSize(size).withPage(page);
        Page<Profile> profileList = null;
        if (profileType == ProfileType.ALL) {
            profileList = profileRepository.findAllForUser(email, pageable);
        } else {
            profileList = profileRepository.findAllProfilesByTypeForUser(email, profileType.name(), pageable);
        }

        return new PageImpl<ProfileMinimizedViewDto>(populateMinimizedActProfiles(profileList.stream().toList()), pageable, profileList.getTotalElements());
    }

    @Transactional
    private void addRecentlyVisitedProfile(String email, String profileId) {

        if(!isMyProfile(email, profileId)) {
            Optional<User> optUser = userRepository.findByEmail(email);
            if (optUser.isPresent()) {
                User user = optUser.get();

                if (!user.getRecentlyVisitedProfiles().contains(profileId)) {
                    if (user.getRecentlyVisitedProfiles().size() >= Constants.MAX_RECENTLY_VIEWED) {
                        user.getRecentlyVisitedProfiles().remove(0);
                    }
                    user.getRecentlyVisitedProfiles().add(profileId);
                    userRepository.save(user);
                }
            }
        }
    }

    private boolean isBlockedProfile(String email, String profileId) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            return user.getBlockedProfiles().contains(profileId);
        }
        return false;
    }

    @Override
    public ProfileDetailedViewDto readProfileDetailedView(String email, String profileId) {

        ProfileDetailedViewDto detailedViewDto = new ProfileDetailedViewDto();
        Optional<Profile> optActProfile = profileRepository.findByProfileId(profileId);
        ProfileDtoMapper profileDtoMapper = new ProfileDtoMapper();
        optActProfile.ifPresent(actProfile -> detailedViewDto.setProfileDto(profileDtoMapper.toActProfileDto(actProfile)));
        optActProfile.ifPresent(actProfile -> detailedViewDto.setProfileStatus(actProfile.getStatus()));
        optActProfile.ifPresent(actProfile -> detailedViewDto.setProfileType(actProfile.getProfileType()));

        if (optActProfile.isPresent()) {
            Profile profile = optActProfile.get();
            if (profile.getProfileType() == ProfileType.VIRTUAL_ACT_PROFILE) {
                Optional<VirtualContact> optVirtualActContact = virtualContactRepository.findByActProfileId(profile.getProfileId());
                optVirtualActContact.ifPresent(virtualContact -> detailedViewDto.setVirtualContactDto(new VirtualContactDtoMapper().toVirtualContactDto(virtualContact)));
            }
        }
        Optional<Location> optLocation = locationRepository.findByProfileId(profileId);
        optLocation.ifPresent(location -> detailedViewDto.setLocationDto(new LocationDtoMapper().toLocationDto(location)));

        Optional<ProfileInfo> optProfileInfo = profileInfoRepository.findByProfileId(profileId);
        if (optProfileInfo.isPresent()) {
            ProfileInfo profileInfo = optProfileInfo.get();
            Optional<WeeklyWorkingHours> optWeeklyWorkingHours = weeklyWorkingHoursRepository.findWeeklyWorkingHoursByProfileId(profileId);
            if (optWeeklyWorkingHours.isPresent()) {
                WeeklyWorkingHours weeklyWorkingHours = optWeeklyWorkingHours.get();
                List<WorkingHours> workingHoursList = workingHoursRepository.getWorkingHoursList(profileId);
                List<WorkingHours> newWorkingHoursList = new ArrayList<>(workingHoursList);
                weeklyWorkingHours.setWorkingHoursList(newWorkingHoursList);
                profileInfo.setWeeklyWorkingHours(weeklyWorkingHours);
            }
            detailedViewDto.setInfoDto(new ProfileInfoDtoMapper().toActInfoDto(profileInfo));
        }

        Optional<ActSkills> optActSkills = actSkillsRepository.findByProfileId(profileId);
        if (optActSkills.isPresent()) {
            Optional<EntertainmentType> optEntertainmentType = entertainmentTypeRepository.findByProfileId(profileId);
            optActSkills.get().setEntertainmentType(optEntertainmentType.orElse(null));
            Optional<List<MusicGenre>> optMusicGenreList = musicGenreRepository.findByProfileId(profileId);
            optActSkills.get().setMusicGenreList(optMusicGenreList.orElse(null));
            optActSkills.ifPresent(actSkills -> detailedViewDto.setSkillsDto(new ActSkillsDtoMapper().toActSkillsDto(actSkills)));

        }
        Optional<ProfilePayments> optActPayments = profilePaymentsRepository.findByProfileId(profileId);
        optActPayments.ifPresent(actPayments -> detailedViewDto.setPaymentsDto(new ProfilePaymentsDtoMapper().toActPaymentsDto(actPayments)));

        Optional<ProfileMedia> optActMedia = profileMediaRepository.findByProfileId(profileId);
        optActMedia.ifPresent(actMedia -> detailedViewDto.setMediaDto(new ProfileMediaDtoMapper().toProfileMediaDto(actMedia)));
        detailedViewDto.setOwnProfile(isMyProfile(email, profileId));

        Optional<ProfileRating> optProfileRating = getProfileRating(profileId);
        optProfileRating.ifPresent(profileRating -> detailedViewDto.setProfileRatingDto(new ProfileRatingDtoMapper().toProfileRatingDto(profileRating)));
        detailedViewDto.setCurrentUsersFavorite(isFavorite(email, profileId));
        addRecentlyVisitedProfile(email, profileId);
        detailedViewDto.setBlockedByCurrentUser(isBlockedProfile(email, profileId));
        return detailedViewDto;
    }

    @Override
    public String readProfileStatus(String email, String profileId) {
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if (optProfile.isPresent()) {
            Profile profile = optProfile.get();
            return profile.getStatus().getStatus();
        }
        return null;
    }

    @Override
    @Transactional
    public boolean updateProfileStatus(String email, String profileId, String status) {
        Profile profile = profileRepository.findByProfileId(profileId).orElse(null);
        if (profile != null) {
            if (profile.getStatus() == ProfileStatus.STATUS_DELETED) {
                String name = profile.getProfileName();
                profile.setProfileName(name.replace(UNPUBLISHED_NAME, ""));
            }
            profile.setStatus(ProfileStatus.valueOfStatus(status));
            profileRepository.save(profile);
            return true;
        }
        return false;
    }

    @Override
    public Optional<Profile> getProfile(String profileId) {
        return profileRepository.findByProfileId(profileId);
    }


    @Override
    public boolean isMyProfile(String email, String profileId) {
        Optional<User> optUser = userRepository.findUserByProfileId(profileId);
        if (optUser.isPresent()) {
            User user = optUser.get();
            return user.getEmail().equals(email);
        }
        return false;
    }

    @Override
    public boolean isValidProfileId(String profileId) {
        return profileRepository.findByProfileId(profileId).isPresent();
    }

    @Override
    @Transactional
    public boolean addDistributionMember(String profileId, DistributionMember distributionMember) {

        Profile profile = profileRepository.findByProfileId(profileId).orElse(null);
        if (profile != null) {
            Distribution distribution = profile.getDistribution();
            if (distribution != null) {
                profile.getDistribution().getMembers().add(distributionMember);
                distributionRepository.save(distribution);
            }
            profileRepository.save(profile);
            return true;
        }
        return false;
    }

    @Override
    public Optional<List<DistributionMember>> getDistributionList(String profileId) {
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if (optProfile.isPresent()) {
            Profile profile = optProfile.get();
            log.info("getDistributionList Members size: {}", profile.getDistribution().getMembers().size());
            return Optional.of(profile.getDistribution().getMembers());
        }
        return Optional.empty();
    }

    @Override
    public Optional<List<DistributionMember>> searchDistributionList(String profileId, String searchString) {
        List<DistributionMember> searchResults = new ArrayList<>();
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if (optProfile.isPresent()) {
            Profile profile = optProfile.get();
            log.info("searchDistributionList Members size: {}", profile.getDistribution().getMembers().size());
            profile.getDistribution().getMembers().forEach(member -> {
                if (member.getReceiverEmail().contains(searchString)) {
                    searchResults.add(member);
                }
            });
            return searchResults.isEmpty() ? Optional.empty() : Optional.of(searchResults);
        }
        return Optional.empty();
    }

    @Override
    public boolean actSkillsAlreadyExists(String email, String profileId) {
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if (optProfile.isPresent()) {
            Profile profile = optProfile.get();
            Optional<ActSkills> optSkills = actSkillsRepository.findByProfileId(profileId);
            return optSkills.isPresent();
        }
        return false;
    }

    @Override
    @Transactional
    public boolean updateActSkillsForProfile(String email, String profileId, ActSkills newActSkills) {
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if (optProfile.isPresent()) {
            Profile profile = optProfile.get();
            Optional<ActSkills> optActSkills = actSkillsRepository.findByProfileId(profileId);
            if (optActSkills.isPresent()) {
                ActSkills oldActSkills = optActSkills.get();

                //Delete old entertainment Type and members
                Optional<EntertainmentType> optEntertainmentType = entertainmentTypeRepository.findByProfileId(profileId);
                if (optEntertainmentType.isPresent()) {
                    EntertainmentType entertainmentType = optEntertainmentType.get();
                    List<EntertainmentTypeMember> members = entertainmentTypeMemberRepository.findMembersByProfileId(profileId).orElse(null);
                    if (members != null) {
                        members.forEach(m -> entertainmentTypeMemberRepository.delete(m));
                    }
                    entertainmentTypeRepository.delete(entertainmentType);
                }

                //Save new entertainment Type and members
                EntertainmentType newEntertainmentType = newActSkills.getEntertainmentType();
                if (newEntertainmentType != null) {
                    entertainmentTypeRepository.save(newEntertainmentType);
                    List<EntertainmentTypeMember> members = newEntertainmentType.getMembers();
                    if (members != null) {
                        members.forEach(m -> entertainmentTypeMemberRepository.save(m));
                    }
                }
                oldActSkills.setEntertainmentType(newEntertainmentType);

                //Delete old music genres
                Optional<List<MusicGenre>> optOldMusicGenres = musicGenreRepository.findByProfileId(profileId);
                if (optOldMusicGenres.isPresent()) {
                    List<MusicGenre> oldMusicGenres = optOldMusicGenres.get();
                    oldMusicGenres.forEach(m -> musicGenreRepository.delete(m));
                }

                oldActSkills.setMusicGenreList(newActSkills.getMusicGenreList());
                actSkillsRepository.save(oldActSkills);
                profile.setActSkills(oldActSkills);
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean actPaymentAlreadyExists(String email, String profileId) {
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if (optProfile.isPresent()) {
            Profile profile = optProfile.get();
            Optional<ProfilePayments> optPayments = profilePaymentsRepository.findByProfileId(profileId);
            return optPayments.isPresent();
        }
        return false;
    }

    /* Utility methods for properly updating the Neo4J nodes which in turn have child nodes of their own */



    public void updateEntertainmentType(Profile profile, String profileId, EntertainmentType entertainmentType) {
        ActSkills skills = actSkillsRepository.findByProfileId(profileId).orElse(null);
        Optional<List<EntertainmentTypeMember>> optMembers = entertainmentTypeMemberRepository.findMembersByProfileId(profileId);
        if (skills != null) {
            optMembers.ifPresent(entertainmentTypeMembers -> skills.getEntertainmentType().setMembers(entertainmentTypeMembers));
            skills.setEntertainmentType(entertainmentType);
            actSkillsRepository.save(skills);
            profile.setActSkills(skills);
        }
    }

    public void updateScheduleTime(Profile profile, String profileId, List<ScheduleTime> scheduleTimeList) {

        for (ScheduleTime st : scheduleTimeList) {
            Recurrence r = recurrenceRepository.getRecurrenceByProfileId(profileId, st.getElementId());
            RecurrenceEndType re = recurrenceEndTypeRepository.getRecurrenceEndTypeByProfileId(profileId, st.getElementId());
            if (r != null) {
                r.setRecurrenceEndType(re);
                st.setRecurrence(r);
            }
        }
        profile.setScheduleList(scheduleTimeList);
    }

    public void updateActSkills(Profile profile, String profileId, ActSkills skills) {
        Optional<EntertainmentType> optEntertainmentType = entertainmentTypeRepository.findByProfileId(profileId);
        Optional<List<MusicGenre>> musicGenreList = musicGenreRepository.findByProfileId(profileId);
        Optional<List<EntertainmentTypeMember>> optEntertainmentTypeMembers = entertainmentTypeMemberRepository.findMembersByProfileId(profileId);
        optEntertainmentTypeMembers.ifPresent(entertainmentTypeMembers -> skills.getEntertainmentType().setMembers(entertainmentTypeMembers));
        optEntertainmentType.ifPresent(skills::setEntertainmentType);
        musicGenreList.ifPresent(skills::setMusicGenreList);
        actSkillsRepository.save(skills);
        profile.setActSkills(skills);
    }

    public void updateActMedia(Profile profile, String profileId, ProfileMedia profileMedia) {
        profile.setProfileMedia(profileMedia);
        Optional<List<RiderDetails>> optRiderDetailsList = riderDetailsRepository.findByRideDetailsForAct(profileId);
        optRiderDetailsList.ifPresent(profileMedia::setRideDetailsList);
        profileMediaRepository.save(profileMedia);
        profile.setProfileMedia(profileMedia);

    }

    public void updateDistribution(Profile profile, String profileId, Distribution distribution) {
        List<DistributionMember> distributionMembers = distributionMemberRepository.findMembersByProfileId(profileId);
        distribution.setMembers(distributionMembers);
        distributionRepository.save(distribution);
        profile.setDistribution(distribution);
    }

    public void updateProfileInfo(Profile profile, String profileId, ProfileInfo profileInfo) {
        Optional<WeeklyWorkingHours> optWeeklyWorkingHours = weeklyWorkingHoursRepository.findWeeklyWorkingHoursByProfileId(profileId);
        if (optWeeklyWorkingHours.isPresent()) {
            WeeklyWorkingHours weeklyWorkingHours = optWeeklyWorkingHours.get();
            List<WorkingHours> workingHours = workingHoursRepository.getWorkingHoursList(profileId);
            weeklyWorkingHours.setWorkingHoursList(workingHours);
            profileInfo.setWeeklyWorkingHours(weeklyWorkingHours);
        }
        profile.setProfileInfo(profileInfo);
    }

    public boolean userEmailAlreadyExists(String email) {
        Optional<User> optUser = userRepository.findByEmail(email);
        return optUser.isPresent();
    }

    public List<Profile> getAllVirtualProfilesWithContactEmail(String email) {
        List<Profile> resultList = new ArrayList<>();
        List<VirtualActClaimToken> actTokenList = virtualActClaimTokenRepository.findAllActsByContactEmail(email);
        if (!actTokenList.isEmpty()) {
            for (VirtualActClaimToken token : actTokenList) {
                Optional<Profile> optProfile = profileRepository.findByProfileId(token.getProfileId());
                optProfile.ifPresent(resultList::add);
            }
        }

        List<VirtualVenueClaimToken> venueTokens = virtualVenueClaimTokenRepository.findAllVenuesByContactEmail(email);
        if (!venueTokens.isEmpty()) {
            for (VirtualVenueClaimToken token : venueTokens) {
                Optional<Profile> optProfile = profileRepository.findByProfileId(token.getProfileId());
                optProfile.ifPresent(resultList::add);
            }
        }

        return resultList;
    }

    @Override
    public Optional<User> getProfileOwner(String profileId) {
        return userRepository.findUserByProfileId(profileId);
    }

    @Override
    public Optional<ProfileRating> getProfileRating(String profileId) {
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if (optProfile.isPresent()) {
            ProfileRating profileRating = new ProfileRating();
            Profile profile = optProfile.get();
            Optional<List<FeedbackMsg>> optFeedbacks = feedbackRepository.findAllReceivedFeedbacks(profileId);
            if (optFeedbacks.isPresent()) {
                List<FeedbackMsg> feedbacks = optFeedbacks.get();
                double totalProfessionalismValue = 0;
                double totalEntertainmentValue = 0;
                double totalDrawAsExpectedValue = 0;
                int numberOfRatings = 0;
                for (FeedbackMsg feedback : feedbacks) {
                    if ((!feedback.isDeleted()) && (feedback.isUpdated())) {
                        numberOfRatings++;
                        totalProfessionalismValue += feedback.getProfessionalismValue();
                        totalEntertainmentValue += feedback.getEntertainmentValue();
                        totalDrawAsExpectedValue += feedback.getDrawAsExpectedValue();
                    }
                }
                if (numberOfRatings > 0) {
                    double overallRating = (totalProfessionalismValue + totalEntertainmentValue + totalDrawAsExpectedValue) / (3 * numberOfRatings);
                    profileRating.setOverallRating(Math.round(overallRating) * 10.0 / 10.0);
                    profileRating.setDrawRating(Math.round((totalDrawAsExpectedValue / numberOfRatings) * 10.0) / 10.0);
                    profileRating.setEntertainmentValueRating(Math.round((totalEntertainmentValue / numberOfRatings) * 10.0) / 10.0);
                    profileRating.setProfessionalismRating(Math.round((totalProfessionalismValue / numberOfRatings) * 10.0) / 10.0);
                    profileRating.setNumberOfRatings(numberOfRatings);
                    profileRating.setGoldBannerMember((numberOfRatings > 10) && (overallRating > 4.5));
                }
                return Optional.of(profileRating);
            }
        }
        return Optional.empty();
    }

    @Override
    public boolean isAFan(String email) {
        return profileRepository.countProfilesByUserEmail(email) <= 0;
    }
}
