package com.stageserver.service;

import com.stageserver.model.contract.Contract;
import com.stageserver.model.contract.ContractState;
import com.stageserver.model.event.ScheduledTask;
import com.stageserver.repository.contract.ContractRepository;
import com.stageserver.repository.event.ScheduledTaskRepository;
import com.stageserver.service.interfaces.I_ScheduleTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ScheduledFuture;

@Slf4j
@Service
public class ScheduleTaskService implements I_ScheduleTaskService {

    @Autowired
    private TaskScheduler taskScheduler;

    @Autowired
    private ScheduledTaskRegistry scheduledTaskRegistry;

    @Autowired
    private ScheduledTaskRepository scheduledTaskRepository;

    @Autowired
    private ContractRepository contractRepository;

    public enum TaskStatus {
        PENDING,
        COMPLETED,
        CANCELLED
    }

    @Transactional
    public void scheduleTask(String contractId, ZonedDateTime endDate, Runnable task) {

        Instant triggerInstant = endDate.toInstant();
        OneTimeTrigger trigger = new OneTimeTrigger(triggerInstant);
        ScheduledFuture<?> future = taskScheduler.schedule(task, trigger);
        scheduledTaskRegistry.register(contractId, future);

        // Persist the scheduled task metadata in Neo4j.
        ScheduledTask scheduledTask = new ScheduledTask(contractId, triggerInstant, TaskStatus.PENDING.name());
        scheduledTaskRepository.save(scheduledTask);
    }

    public void cancelTask(String contractId) {
        ScheduledFuture<?> future = scheduledTaskRegistry.unregister(contractId);
        if (future != null) {
            future.cancel(false); // 'false' means do not interrupt if running.
        }

        // Update the persistent scheduled task record.
        Optional<ScheduledTask> optTask = scheduledTaskRepository.findByContractId(contractId);
        if (optTask.isPresent()) {
            ScheduledTask task = optTask.get();
            task.setStatus("CANCELLED");
            scheduledTaskRepository.save(task);
        }
        log.info("Cancelled scheduled task for eventId {} ", contractId);
    }

    public void resumePendingTasks() {
        Instant now = Instant.now();
        List<ScheduledTask> pendingTasks =
                scheduledTaskRepository.findByTriggerTimeAfterAndStatus(now, TaskStatus.PENDING.name());
        for (ScheduledTask task : pendingTasks) {
            log.info("Rescheduling pending task for eventId: {}", task.getContractId());
            resumeTask(task.getContractId(), ZonedDateTime.ofInstant(task.getTriggerTime(), ZoneId.systemDefault()));
        }
    }

    public void resumeTask(String contractId, ZonedDateTime triggerTime) {
        Runnable task = () -> onTaskExecution(contractId);
        scheduleTask(contractId, triggerTime, task);
    }

    @Transactional
    public void onTaskExecution(String contractId) {
        Optional<ScheduledTask> optTask = scheduledTaskRepository.findByContractId(contractId);
        if (optTask.isPresent()) {
            ScheduledTask task = optTask.get();
            task.setStatus(TaskStatus.COMPLETED.name());
            scheduledTaskRepository.save(task);
        }
        scheduledTaskRegistry.unregister(contractId);
        Optional<Contract> optContract = contractRepository.findByContractId(contractId);
        if(optContract.isPresent()) {
            Contract contract = optContract.get();
            contract.setContractState(ContractState.COMPLETED);
            contractRepository.save(contract);
        }
        log.info("Scheduled task executed for contractId: {}", contractId);
    }
}
