package com.stageserver.service;

import com.stageserver.dto.mapper.WeeklyWorkingHoursDtoMapper;
import com.stageserver.dto.profile.ProfileMinimizedViewDto;
import com.stageserver.dto.mapper.ProfilePaymentsDtoMapper;
import com.stageserver.dto.mapper.VirtualContactDtoMapper;
import com.stageserver.dto.search.SearchLocationDto;
import com.stageserver.dto.mapper.LocationDtoMapper;
import com.stageserver.model.profile.*;
import com.stageserver.model.common.ProfileStatus;
import com.stageserver.model.common.ProfileType;
import com.stageserver.model.distribution.DistributionMember;
import com.stageserver.model.location.Location;
import com.stageserver.model.location.SearchLocation;
import com.stageserver.model.login.User;
import com.stageserver.model.profile.EntertainmentType;
import com.stageserver.model.profile.Profile;
import com.stageserver.model.profile.ProfilePayments;
import com.stageserver.model.search.SearchData;
import com.stageserver.model.supported.MusicGenre;
import com.stageserver.model.supported.SupportedMusicGenre;
import com.stageserver.repository.*;
import com.stageserver.service.interfaces.I_SearchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SearchService implements I_SearchService {

    @Autowired
    private SupportedMusicGenreRepository supportedMusicGenreRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private LocationRepository locationRepository;

    @Autowired
    private ProfileInfoRepository profileInfoRepository;

    @Autowired
    private ProfilePaymentsRepository profilePaymentsRepository;

    @Autowired
    private ProfileMediaRepository profileMediaRepository;

    @Autowired
    private EntertainmentTypeRepository entertainmentTypeRepository;

    @Autowired
    private MusicGenreRepository musicGenreRepository;

    @Autowired
    private SearchDataRepository searchDataRepository;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private LocationService locationService;

    @Autowired
    private SearchLocationRepository searchLocationRepository;
    @Autowired
    private ProfileRatingRepository profileRatingRepository;

    @Autowired
    private VirtualContactRepository virtualContactRepository;

    @Autowired
    private WeeklyWorkingHoursRepository weeklyWorkingHoursRepository;

    @Autowired
    private WorkingHoursRepository workingHoursRepository;

    @Override
    public List<MusicGenre> searchMusicGenre(String searchString) {
        List<SupportedMusicGenre> genreList = supportedMusicGenreRepository.findAll();

        SupportedMusicGenre supportedMusicGenre = genreList.get(0);

        return supportedMusicGenre.getGenreList().stream()
                .filter(item -> item.getName().toLowerCase().contains(searchString.toLowerCase()))
                .collect(Collectors.toList());
    }

    @Override
    public boolean updateSearchLocation(SearchLocationDto searchLocationDto) {
        return false;
    }

    @Override
    public boolean isSearchNameExists(String email, String searchName) {
        Optional<SearchData> optData = searchDataRepository.findByNameForUser(email, searchName);
        return optData.isPresent();
    }

    @Override
    @Transactional
    public boolean addSearchData(String email, SearchData searchData) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            user.getSearchDataList().add(searchData);
            userRepository.save(user);
            searchDataRepository.save(searchData);
            return true;
        }
        return false;
    }

    @Override
    public List<SearchData> getSearchData(String email) {
        return searchDataRepository.findForUser(email);
    }

    @Override
    @Transactional
    public boolean updateSearchData(String email, SearchData searchData) {
        String searchName = searchData.getSearchName();
        if ((searchName == null) || (searchName.isEmpty())) {
            return false;
        }
        Optional<SearchData> optSearchData = searchDataRepository.findByNameForUser(email, searchName);
        if (optSearchData.isPresent()) {
            SearchData existingSearchData = optSearchData.get();
            searchData.setElementId(existingSearchData.getElementId());
            searchDataRepository.save(searchData);
            return true;
        }

        return false;
    }

    @Override
    public Optional<SearchData> getSearchDataByName(String email, String searchName) {
        return searchDataRepository.findByNameForUser(email, searchName);
    }

    @Override
    @Transactional
    public boolean deleteSearchData(String email, String searchName) {
        Optional<SearchData> optData = searchDataRepository.findByNameForUser(email, searchName);
        if (optData.isPresent()) {
            searchDataRepository.delete(optData.get());
            return true;
        } else {
            return false;
        }
    }

    private void prepareSearchData(SearchData searchData, boolean select) {
        // select boolean value is used to determine if the search location should be used or not
        // used in Search and Select functionality
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        if (((searchData.getCountryName() == null) || searchData.getCountryName().isEmpty()) &&
                (searchData.getStateName() == null || searchData.getStateName().isEmpty()) &&
                (searchData.getCityName() == null || searchData.getCityName().isEmpty())) {
            if (auth.isAuthenticated()) {
                log.info("We have a logged in user {}, so we can use the saved location", auth.getName());
                //Read the save default location and set it in the search data
                if (!select) {
                    if (!auth.getName().equals("anonymousUser")) {
                        Optional<SearchLocation> optSearchLocation = searchLocationRepository.findSearchLocationByEmail(auth.getName());
                        if (optSearchLocation.isPresent()) {
                            log.info("searchLocation is present for user {}", auth.getName());
                            SearchLocation searchLocation = optSearchLocation.get();
                            searchData.setCountryName(searchLocation.getCountryName());
                            searchData.setStateName(searchLocation.getStateName());
                            searchData.setCityName(searchLocation.getCityName());
                            searchData.setDistance(100);
                        }
                    } else {
                        log.info("anonymousUser is searching without location");
                    }
                }

            }
        }

        try {
            if ((searchData.getCountryName() != null) || (searchData.getStateName() != null) || (searchData.getCityName() != null)) {
                if (searchData.getDistance() > 0) {
                    // We need to compute the longitude and latitude for the search city
                    Location location = locationService.computeGeocode(searchData.getCountryName(), searchData.getStateName(), searchData.getCityName());
                    searchData.setLatitude(location.getLatitude());
                    searchData.setLongitude(location.getLongitude());
                }
            }
        } catch (Exception e) {
            log.error("Public Profile search Error in computing geocode for search location: {}", e.getMessage());
        }
    }

    @Override
    public Page<ProfileMinimizedViewDto> doGenericSearchWithFilters(SearchData searchData, ProfileType profileType, int page, int size, boolean select) {
        Pageable pageable = PageRequest.ofSize(size).withPage(page);
        prepareSearchData(searchData, select);
        List<String> searchStrings = searchData.getSearchStrings();
        searchStrings = removeCommonWords(searchStrings);
        searchData.setSearchStrings(searchStrings);

        try {
            if (searchData.getDistance() > 0) {
                // We need to compute the longitude and latitude for the search city
                Location location = locationService.computeGeocode(searchData.getCountryName(), searchData.getStateName(), searchData.getCityName());
                searchData.setLatitude(location.getLatitude());
                searchData.setLongitude(location.getLongitude());
            }
        } catch (Exception e) {
            log.error("Error in computing geocode for search location: {}", e.getMessage());
        }
        Page<Profile> profileList = profileRepository.findProfilesBySearchData(searchData, profileType, pageable);
        log.info("Using searchData Found {} profiles", profileList.getTotalElements());
        return new PageImpl<>(profileService.populateMinimizedActProfiles(profileList.stream().toList()), pageable, profileList.getTotalElements());
    }

    @Override
    public Page<ProfileMinimizedViewDto> doUserProfileSearchWithFilters(SearchData searchData, ProfileType profileType, int page, int size) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        Pageable pageable = PageRequest.ofSize(size).withPage(page);
        prepareSearchData(searchData, false);
        List<String> searchStrings = searchData.getSearchStrings();
        searchStrings = removeCommonWords(searchStrings);
        searchData.setSearchStrings(searchStrings);

        Page<Profile> profileList = profileRepository.findOthersProfilesBySearchData(auth.getName(), searchData, profileType, pageable);
        log.info("doUserProfileSearch:Using searchData Found {} profiles", profileList.getTotalElements());
        return new PageImpl<>(profileService.populateMinimizedActProfiles(profileList.getContent()), pageable, profileList.getTotalElements());
    }

    @Override
    public Page<ProfileMinimizedViewDto> doSearchAndSelectWithFilters(SearchData serialize, ProfileType profileType, int page, int size) {
        Pageable pageable = PageRequest.ofSize(size).withPage(page);
        Page<ProfileMinimizedViewDto> dtoList = doGenericSearchWithFilters(serialize, profileType, page, size, true);
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        List<ProfileMinimizedViewDto> resultList = new ArrayList<>();
        for (ProfileMinimizedViewDto dto : dtoList) {
            if (!profileService.isMyProfile(auth.getName(), dto.getProfileId())) {
                continue;
            } else {
                resultList.add(dto);
            }
        }

        return new PageImpl<>(resultList, pageable, dtoList.getTotalElements());
    }

    @Override
    public Page<ProfileMinimizedViewDto> searchStringsInCurrentUserProfiles(String email, List<String> searchStrings,
                                                                            int page, int size, ProfileType profileType) {

        Pageable pageable = PageRequest.ofSize(size).withPage(page);
        Page<Profile> profilePage = null;
        if (profileType == ProfileType.ALL) {
            profilePage = profileRepository.findAllForUser(email, pageable);
        } else {
            profilePage = profileRepository.findAllProfilesByTypeForUserX(email, profileType, searchStrings, pageable);
        }
        return new PageImpl<>(profileService.populateMinimizedActProfiles(profilePage.stream().toList()), pageable, profilePage.getTotalElements());
    }

    @Override
    public List<String> removeCommonWords(List<String> searchStrings) {

        if (searchStrings == null) {
            // return an empty string member so that when search strings is not provided, we can still return results
            List<String> list = new ArrayList<>();
            list.add("");
            return list;
        }
        List<String> stopWords = Arrays.asList(
                "is", "a", "&", "for", "and", "the", "to", "in", "of", "on", "with", "by", "at", "from", "about", "as", "into", "like",
                "through", "over", "between", "out", "against", "during", "without", "before", "after", "above", "below", "around",
                "among", "under", "behind", "inside", "beneath", "beside", "beyond", "but", "except", "since", "until", "upon",
                "within", "so", "nor", "yet", "or"
        );

        // Filter out stop words
        return searchStrings.stream()
                .filter(word -> !stopWords.contains(word))
                .toList();
    }

    @Override
    public Optional<List<DistributionMember>> searchDistributionList(String profileId, String searchString) {
        return profileService.getDistributionList(profileId);

    }

    @Override
    @Transactional
    public boolean setDefaultSearchLocation(SearchLocation searchLocation) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        Optional<User> optUser = userRepository.findByEmail(auth.getName());
        Optional<SearchLocation> optSearchLocation = searchLocationRepository.findSearchLocationByEmail(auth.getName());
        if (optUser.isPresent()) {
            User user = optUser.get();
            if(optSearchLocation.isPresent()) {
                SearchLocation oldSearchLocation = optSearchLocation.get();
                searchLocation.setElementId(oldSearchLocation.getElementId());
            }
            user.setSearchLocation(searchLocation);
            userRepository.save(user);
            return true;
        }
        return false;
    }

    @Override
    public Optional<SearchLocation> getDefaultSearchLocation() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        return searchLocationRepository.findSearchLocationByEmail(auth.getName());
    }

    @Override
    @Transactional
    public boolean deleteDefaultSearchLocation(String email) {

        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            user.setSearchLocation(null);
            searchLocationRepository.deleteDefaultLocationByEmail(email);
            userRepository.save(user);
            return true;
        }
        return false;
    }
}

