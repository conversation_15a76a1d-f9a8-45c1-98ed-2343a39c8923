package com.stageserver.service;

import com.stageserver.dto.event.SpecialEventDto;
import com.stageserver.dto.mapper.ScheduleTimeDtoMapper;
import com.stageserver.dto.mapper.SpecialEventDtoMapper;
import com.stageserver.dto.schedule.RecurrenceDto;
import com.stageserver.dto.schedule.RecurrenceType;
import com.stageserver.dto.schedule.ScheduleTimeDto;
import com.stageserver.model.event.SpecialEvent;
import com.stageserver.model.profile.Profile;
import com.stageserver.model.profile.ProfileMedia;
import com.stageserver.model.schedule.Recurrence;
import com.stageserver.model.schedule.RecurrenceEndType;
import com.stageserver.model.schedule.ScheduleTime;
import com.stageserver.repository.ProfileRepository;
import com.stageserver.repository.SpecialEventRepository;
import com.stageserver.repository.schedule.RecurrenceEndTypeRepository;
import com.stageserver.repository.schedule.RecurrenceRepository;
import com.stageserver.repository.schedule.ScheduleTimeRepository;
import com.stageserver.service.interfaces.I_SpecialEventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

@Slf4j
@Service
public class SpecialEventService implements I_SpecialEventService {

    @Autowired
    private SpecialEventRepository specialEventRepository;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private UtilityService utilityService;

    @Autowired
    private ScheduleTimeRepository scheduleTimeRepository;

    @Autowired
    private RecurrenceRepository recurrenceRepository;

    @Autowired
    private RecurrenceEndTypeRepository recurrenceEndTypeRepository;

    @Autowired
    private AsyncEventGeneratorService asyncEventGeneratorService;


    @Override
    @Transactional
    public Optional<SpecialEvent> createSpecialEvent(String profileId, String email, SpecialEventDto specialEventDto) {
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if (optProfile.isEmpty()) return Optional.empty();

        Profile profile = optProfile.get();
        SpecialEventDtoMapper mapper = new SpecialEventDtoMapper();
        SpecialEvent baseEvent = mapper.toSpecialEvent(specialEventDto);
        String baseEventId = utilityService.generateUniqueUUID();

        baseEvent.setSpecialEventId(baseEventId);
        baseEvent.setOwnerName(profile.getProfileName());
        baseEvent.setOwnerProfileId(profileId);
        baseEvent.setTotalImageSize(0L);
        ProfileMedia profileMedia = profile.getProfileMedia();
        if (profileMedia != null) {
            baseEvent.setOwnerImageUrls(profileMedia.getImageUrls());
        }

        ScheduleTimeDto scheduleDto = specialEventDto.getScheduleTime();
        ScheduleTimeDtoMapper scheduleTimeDtoMapper = new ScheduleTimeDtoMapper();
        ScheduleTime originalSchedule = scheduleTimeDtoMapper.toScheduleTime(scheduleDto);

        if (scheduleDto.isRecurring() && scheduleDto.getRecurrence() != null) {
            RecurrenceDto recurrence = scheduleDto.getRecurrence();
            List<DayOfWeek> days = new ArrayList<>(recurrence.getDaysOfWeek());
            days.sort(Comparator.naturalOrder());

            ZonedDateTime baseStart = originalSchedule.getStartDate();
            Duration duration = Duration.between(originalSchedule.getStartDate(), originalSchedule.getEndDate());

            ZonedDateTime adjustedStart = null;
            for (DayOfWeek day : days) {
                ZonedDateTime candidate = baseStart.with(TemporalAdjusters.nextOrSame(day));
                if (!candidate.isBefore(baseStart)) {
                    adjustedStart = candidate
                            .withHour(baseStart.getHour())
                            .withMinute(baseStart.getMinute())
                            .withSecond(baseStart.getSecond());
                    break;
                }
            }

            if (adjustedStart != null && !adjustedStart.equals(baseStart)) {
                originalSchedule.setStartDate(adjustedStart);
                originalSchedule.setEndDate(adjustedStart.plus(duration));
            }
        }

        if (scheduleDto.isRecurring()) {
            baseEvent.setBaseForRecurringEvent(true);
            baseEvent.setScheduleTime(originalSchedule);
            profile.getSpecialEvents().add(baseEvent);
            profileRepository.save(profile);
            specialEventDto.setOwnerName(baseEvent.getOwnerName());
            specialEventDto.setOwnerImageUrls(baseEvent.getOwnerImageUrls());
            generateInitialAndDeferredRecurringEvents(baseEvent, specialEventDto, originalSchedule);
            return Optional.of(baseEvent);
        } else {
            baseEvent.setBaseForRecurringEvent(false);
            baseEvent.setScheduleTime(originalSchedule);
            profile.getSpecialEvents().add(baseEvent);
            profileRepository.save(profile);
            return Optional.of(baseEvent);
        }
    }

    @Transactional
    public void generateInitialAndDeferredRecurringEvents( SpecialEvent baseEvent,
                                                           SpecialEventDto specialEventDto,
                                                           ScheduleTime baseSchedule) {
        List<ScheduleTime> recurrenceTimes = generateRecurringSchedule(baseSchedule);
        ZonedDateTime baseStart = baseSchedule.getStartDate();
        ZonedDateTime oneMonthLater = baseStart.plusMonths(1);

        List<ScheduleTime> firstMonth = new ArrayList<>();
        List<ScheduleTime> remaining = new ArrayList<>();

        for (ScheduleTime occurrence : recurrenceTimes) {
            ZonedDateTime start = occurrence.getStartDate();
            if (start.isBefore(oneMonthLater)) {
                firstMonth.add(occurrence);
            } else {
                remaining.add(occurrence);
            }
        }

        List<SpecialEvent> eventsToSaveNow = new ArrayList<>();
        for (ScheduleTime time : firstMonth) {
            if (!time.getStartDate().isEqual(baseStart)) {
                SpecialEvent event = buildEventFromSchedule(
                        baseEvent.getOwnerProfileId(),
                        specialEventDto,
                        time,
                        baseEvent.getSpecialEventId()
                );

                //Save the SpecialEvent immediately after setting its ScheduleTime
                SpecialEvent savedEvent = specialEventRepository.save(event);

                eventsToSaveNow.add(savedEvent);
            }
        }

        // Now associate events to Profile
        Optional<Profile> optProfile = profileRepository.findByProfileId(baseEvent.getOwnerProfileId());
        if (optProfile.isEmpty()) {
            log.warn("Profile not found for profileId: {} during child generation", baseEvent.getOwnerProfileId());
            return;
        }

        Profile profile = optProfile.get();

        if (profile.getSpecialEvents() == null) {
            profile.setSpecialEvents(new ArrayList<>());
        }

        List<SpecialEvent> current = new ArrayList<>(profile.getSpecialEvents());
        current.addAll(eventsToSaveNow);
        profile.setSpecialEvents(current);

        profileRepository.save(profile);

        log.info("Saved {} first-month recurring events (baseEventId: {})", eventsToSaveNow.size(), baseEvent.getSpecialEventId());

        // Async generation for remaining events
        if (!remaining.isEmpty()) {
            log.info("Submitting {} events for async generation (baseEventId: {})", remaining.size(), baseEvent.getSpecialEventId());
            asyncEventGeneratorService.generateFutureEventsAsync(
                    baseEvent.getOwnerProfileId(),
                    specialEventDto,
                    remaining,
                    baseEvent.getSpecialEventId()
            );
        }
    }

    @Transactional
    private SpecialEvent buildEventFromSchedule(String profileId, SpecialEventDto dto, ScheduleTime scheduleTime, String baseEventId) {
        SpecialEventDtoMapper mapper = new SpecialEventDtoMapper();
        SpecialEvent event = mapper.toSpecialEvent(dto);
        event.setSpecialEventId(utilityService.generateUniqueUUID());
        event.setOwnerProfileId(profileId);

        // Save the scheduleTime separately first
        ScheduleTime savedScheduleTime = scheduleTimeRepository.save(scheduleTime);

        // Set the saved scheduleTime on the event
        event.setScheduleTime(savedScheduleTime);

        event.setBaseForRecurringEvent(false);
        event.setBaseSpecialEventId(baseEventId);
        event.setTotalImageSize(0L);
        return event;
    }


    private void createEventForSchedule(String profileId, SpecialEventDto dto, ScheduleTime scheduleTime, String baseEventId, ZonedDateTime baseStart) {
        if (scheduleTime.getStartDate().isEqual(baseStart)) return; // Skip duplicate of base

        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if (optProfile.isEmpty()) return;

        Profile profile = optProfile.get();
        SpecialEventDtoMapper mapper = new SpecialEventDtoMapper();
        SpecialEvent event = mapper.toSpecialEvent(dto);

        event.setSpecialEventId(utilityService.generateUniqueUUID());
        event.setOwnerProfileId(profileId);
        event.setScheduleTime(scheduleTime);
        event.setBaseForRecurringEvent(false);
        event.setBaseSpecialEventId(baseEventId);

        profile.getSpecialEvents().add(event);
        profileRepository.save(profile);
    }

    private List<ScheduleTime> generateRecurringSchedule(ScheduleTime baseSchedule) {
        if (!baseSchedule.isRecurring()) return List.of();

        Recurrence recurrence = baseSchedule.getRecurrence();
        RecurrenceEndType endType = recurrence.getRecurrenceEndType();

        // Use the provided endDate from recurrenceEndType
        if (endType.getEndDate() == null) {
            throw new IllegalArgumentException("RecurrenceEndType.endDate must be provided for recurring events.");
        }

        switch (recurrence.getRecurrenceType()) {
            case WEEKLY:
            case BI_WEEKLY:
                return generateWeeklyOrBiWeeklyOccurrences(baseSchedule, recurrence, endType);
            case MONTHLY:
                return generateMonthlyOccurrencesByEndDate(baseSchedule, recurrence, endType);
            default:
                return List.of();
        }
    }

    private List<ScheduleTime> generateWeeklyOrBiWeeklyOccurrences(ScheduleTime original, Recurrence recurrence, RecurrenceEndType endType) {
        List<ScheduleTime> results = new ArrayList<>();
        List<DayOfWeek> days = new ArrayList<>(recurrence.getDaysOfWeek());
        if (days.isEmpty()) return results;
        days.sort(Comparator.naturalOrder());

        ZonedDateTime baseStart = original.getStartDate();
        ZonedDateTime baseEnd = original.getEndDate();
        ZonedDateTime recurrenceEnd = endType.getEndDate();

        int weekInterval = (recurrence.getRecurrenceType() == RecurrenceType.BI_WEEKLY) ? 2 : 1;
        Duration duration = Duration.between(baseStart, baseEnd);
        ZonedDateTime weekStart = baseStart;

        // Align to the next valid day in daysOfWeek
        boolean firstOccurrenceFound = false;
        while (!firstOccurrenceFound) {
            for (DayOfWeek day : days) {
                ZonedDateTime candidate = weekStart.with(TemporalAdjusters.nextOrSame(day));
                if (!candidate.isBefore(baseStart)) {
                    weekStart = candidate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
                    firstOccurrenceFound = true;
                    break;
                }
            }
            if (!firstOccurrenceFound) {
                weekStart = weekStart.plusWeeks(1);
            }
        }

        while (!weekStart.isAfter(recurrenceEnd)) {
            for (DayOfWeek day : days) {
                ZonedDateTime occurrenceDate = weekStart.with(TemporalAdjusters.nextOrSame(day));

                if (occurrenceDate.isBefore(baseStart) || occurrenceDate.isAfter(recurrenceEnd)) continue;

                ZonedDateTime occurrenceStart = occurrenceDate
                        .withHour(baseStart.getHour())
                        .withMinute(baseStart.getMinute())
                        .withSecond(baseStart.getSecond());

                if (occurrenceStart.isEqual(baseStart)) continue; // Skip duplicate of base

                ScheduleTime occurrence = createScheduleTimeInstance(original, occurrenceStart, occurrenceStart.plus(duration));
                results.add(occurrence);
            }

            weekStart = weekStart.plusWeeks(weekInterval);
        }
        return results;
    }

    private List<ScheduleTime> generateMonthlyOccurrencesByEndDate(ScheduleTime original, Recurrence recurrence, RecurrenceEndType endType) {
        List<ScheduleTime> results = new ArrayList<>();
        ZonedDateTime baseStart = original.getStartDate();
        ZonedDateTime baseEnd = original.getEndDate();
        ZonedDateTime recurrenceEnd = endType.getEndDate();

        Duration duration = Duration.between(baseStart, baseEnd);
        int dayOfMonth = baseStart.getDayOfMonth();
        ZonedDateTime current = baseStart;

        while (!current.isAfter(recurrenceEnd)) {
            try {
                ZonedDateTime occurrenceStart = ZonedDateTime.of(
                        current.getYear(),
                        current.getMonthValue(),
                        dayOfMonth,
                        baseStart.getHour(),
                        baseStart.getMinute(),
                        baseStart.getSecond(),
                        0,
                        baseStart.getZone()
                );

                if (!occurrenceStart.isBefore(baseStart) && !occurrenceStart.isAfter(recurrenceEnd)) {
                    if (occurrenceStart.isEqual(baseStart)) {
                        current = current.plusMonths(1);
                        continue;
                    }

                    ScheduleTime occurrence = createScheduleTimeInstance(original, occurrenceStart, occurrenceStart.plus(duration));
                    results.add(occurrence);
                }
            } catch (DateTimeException ignored) {
            }

            current = current.plusMonths(1);
        }

        return results;
    }

    private ScheduleTime createScheduleTimeInstance(ScheduleTime original, ZonedDateTime start, ZonedDateTime end) {
        ScheduleTime schedule = new ScheduleTime();
        schedule.setScheduleId(original.getScheduleId());
        schedule.setProfileType(original.getProfileType());
        schedule.setScheduleName(original.getScheduleName());
        schedule.setScheduleDescription(original.getScheduleDescription());
        schedule.setScheduleType(original.getScheduleType());
        schedule.setTimeZone(original.getTimeZone());
        schedule.setRecurring(false);
        schedule.setStartTime(start);
        schedule.setEndTime(end);
        schedule.setStartDate(start);
        schedule.setEndDate(end);
        return schedule;
    }

    @Override
    public Optional<SpecialEvent> readSpecialEvent(String specialEventId, String email) {
        return specialEventRepository.findBySpecialEventId(specialEventId);
    }

    @Override
    @Transactional
    public Optional<SpecialEvent> updateSpecialEvent(String email, SpecialEventDto newDto) {
        Optional<SpecialEvent> existingOpt = specialEventRepository.findBySpecialEventId(newDto.getSpecialEventId());
        if (existingOpt.isEmpty()) {
            return Optional.empty();
        }

        SpecialEvent existing = existingOpt.get();

        if (!isAuthorized(existing, newDto)) {
            return Optional.empty();
        }

        Optional<Profile> optProfile = profileRepository.findByProfileId(existing.getOwnerProfileId());
        if (optProfile.isEmpty()) {
            log.warn("Profile not found for profileId: {}. Cannot update event.", existing.getOwnerProfileId());
            return Optional.empty();
        }

        Profile profile = optProfile.get();

        List<String> existingImageUrls = existing.getEventImageUrls();
        boolean wasRecurringBefore = existing.isBaseForRecurringEvent();

        updateEventDetails(existing, newDto, existingImageUrls);

        boolean isRecurringNow = existing.isBaseForRecurringEvent();

        // 👇 Add this adjustment before saving
        if (isRecurringNow && !wasRecurringBefore) {
            adjustBaseEventStartDate(existing);
        }

        updateProfileWithUpdatedEvent(profile, existing);

        if (isRecurringNow) {
            regenerateChildEventsAfterBaseUpdate(existing, newDto);
        } else if (wasRecurringBefore) {
            deletePreviouslyGeneratedChildren(existing);
        }

        return Optional.of(existing);
    }

    private void adjustBaseEventStartDate(SpecialEvent event) {
        ScheduleTime schedule = event.getScheduleTime();
        Recurrence recurrence = schedule.getRecurrence();
        if (recurrence == null || recurrence.getDaysOfWeek() == null || recurrence.getDaysOfWeek().isEmpty()) {
            return; // Nothing to adjust
        }

        List<DayOfWeek> sortedDays = new ArrayList<>(recurrence.getDaysOfWeek());
        sortedDays.sort(Comparator.naturalOrder());

        ZonedDateTime originalStart = schedule.getStartDate();
        ZonedDateTime adjustedStart = null;

        for (DayOfWeek day : sortedDays) {
            ZonedDateTime candidate = originalStart.with(TemporalAdjusters.nextOrSame(day))
                    .withHour(originalStart.getHour())
                    .withMinute(originalStart.getMinute())
                    .withSecond(originalStart.getSecond());

            if (!candidate.isBefore(originalStart)) {
                adjustedStart = candidate;
                break;
            }
        }

        if (adjustedStart != null && !adjustedStart.equals(originalStart)) {
            log.info("Adjusting base event startDate from {} to {}", originalStart, adjustedStart);

            Duration duration = Duration.between(schedule.getStartDate(), schedule.getEndDate());
            schedule.setStartDate(adjustedStart);
            schedule.setEndDate(adjustedStart.plus(duration));
        }
    }

    private boolean isAuthorized(SpecialEvent existing, SpecialEventDto newDto) {
        return existing.getOwnerProfileId().equals(newDto.getOwnerProfileId());
    }

    private void updateEventDetails(SpecialEvent existing, SpecialEventDto newDto, List<String> existingImageUrls) {
        existing.setEventName(newDto.getEventName());
        existing.setAboutTheEvent(newDto.getAboutTheEvent());

        ScheduleTime existingScheduleTime = existing.getScheduleTime();
        ScheduleTimeDtoMapper mapper = new ScheduleTimeDtoMapper();
        ScheduleTime updatedScheduleTime = mapper.toScheduleTime(newDto.getScheduleTime());
        updatedScheduleTime.setElementId(existingScheduleTime.getElementId());
        existing.setScheduleTime(updatedScheduleTime);

        // Preserve
        existing.setEventImageUrls(existingImageUrls);
        newDto.setEventImageUrls(existingImageUrls);
        newDto.setOwnerName(existing.getOwnerName());
        newDto.setOwnerImageUrls(existing.getOwnerImageUrls());

        existing.setBaseForRecurringEvent(
                updatedScheduleTime.isRecurring() && updatedScheduleTime.getRecurrence() != null
        );
    }

    @Transactional
    private void updateProfileWithUpdatedEvent(Profile profile, SpecialEvent updatedEvent) {
        List<SpecialEvent> updatedSpecialEvents = new ArrayList<>();
        for (SpecialEvent event : profile.getSpecialEvents()) {
            if (!event.getSpecialEventId().equals(updatedEvent.getSpecialEventId())) {
                updatedSpecialEvents.add(event);
            }
        }
        updatedSpecialEvents.add(updatedEvent);
        profile.setSpecialEvents(updatedSpecialEvents);

        profileRepository.save(profile);
    }

    @Transactional
    public void regenerateChildEventsAfterBaseUpdate(SpecialEvent existing, SpecialEventDto newDto) {
        specialEventRepository.deleteByBaseSpecialEventId(existing.getSpecialEventId());
        log.info("Deleted previous recurring events for baseEventId: {}", existing.getSpecialEventId());

        Optional<Profile> optUpdatedProfile = profileRepository.findByProfileId(existing.getOwnerProfileId());
        if (optUpdatedProfile.isEmpty()) {
            log.warn("Profile not found for profileId: {} during child regeneration", existing.getOwnerProfileId());
            return;
        }
        Profile updatedProfile = optUpdatedProfile.get();

        ScheduleTime updatedScheduleTime = existing.getScheduleTime();
        List<ScheduleTime> allOccurrences = generateRecurringSchedule(updatedScheduleTime);
        ZonedDateTime baseStart = updatedScheduleTime.getStartDate();
        ZonedDateTime oneMonthLater = baseStart.plusMonths(1);

        List<ScheduleTime> firstMonth = new ArrayList<>();
        List<ScheduleTime> remaining = new ArrayList<>();

        for (ScheduleTime occurrence : allOccurrences) {
            ZonedDateTime start = occurrence.getStartDate();
            if (start.isBefore(oneMonthLater)) {
                firstMonth.add(occurrence);
            } else {
                remaining.add(occurrence);
            }
        }

        List<SpecialEvent> eventsToSaveNow = new ArrayList<>();
        for (ScheduleTime time : firstMonth) {
            if (!time.getStartDate().isEqual(baseStart)) {
                SpecialEvent event = buildEventFromSchedule(
                        newDto.getOwnerProfileId(),
                        newDto,
                        time,
                        existing.getSpecialEventId()
                );
                eventsToSaveNow.add(event);
            }
        }

        // Attach children to profile BEFORE saving!
        if (updatedProfile.getSpecialEvents() == null) {
            updatedProfile.setSpecialEvents(new ArrayList<>());
        }

        List<SpecialEvent> mergedEvents = new ArrayList<>();
        for (SpecialEvent event : updatedProfile.getSpecialEvents()) {
            if (!existing.getSpecialEventId().equals(event.getBaseSpecialEventId())) {
                mergedEvents.add(event);
            }
        }
        mergedEvents.addAll(eventsToSaveNow); //  attach all first month children
        updatedProfile.setSpecialEvents(mergedEvents);

        // Save profile only — it will create nodes + relationships
        profileRepository.save(updatedProfile);

        log.info("update-Saved {} first-month recurring events (baseEventId: {})",
                eventsToSaveNow.size(), existing.getSpecialEventId());

        // Async for later events
        if (!remaining.isEmpty()) {
            log.info("update-Submitting {} events for async generation (baseEventId: {})",
                    remaining.size(), existing.getSpecialEventId());

            asyncEventGeneratorService.generateFutureEventsAsync(
                    newDto.getOwnerProfileId(),
                    newDto,
                    remaining,
                    existing.getSpecialEventId()
            );
        }
    }

    @Transactional
    private void deletePreviouslyGeneratedChildren(SpecialEvent existing) {
        specialEventRepository.deleteByBaseSpecialEventId(existing.getSpecialEventId());
        log.info("Deleted previously generated child events because event is now non-recurring (baseEventId: {})", existing.getSpecialEventId());
    }

    @Override
    @Transactional
    public boolean deleteSpecialEvent(String specialEventId, String email) {
        Optional<SpecialEvent> eventOpt = specialEventRepository.findBySpecialEventId(specialEventId);
        if (eventOpt.isEmpty()) return false;

        SpecialEvent event = eventOpt.get();

        if (event.isBaseForRecurringEvent()) {
            // Delete all child events first
            specialEventRepository.deleteByBaseSpecialEventId(specialEventId);
            // Then delete the base event
            specialEventRepository.deleteBySpecialEventId(specialEventId);
            return true;
        }

        // For standalone or child event
        specialEventRepository.deleteStandAloneBySpecialEventId(specialEventId);
        return true;
    }

    @Override
    public Page<SpecialEvent> getAllSpecialEvents(String profileId, String email, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<SpecialEvent> result = specialEventRepository.findPageByProfileId(profileId, pageable);
        for (SpecialEvent specialEvent : result.getContent()) {
            scheduleTimeRepository.findForSpecialEventId(specialEvent.getSpecialEventId())
                    .ifPresent(specialEvent::setScheduleTime);
        }
        return result;
    }

    @Override
    public boolean scheduleChanged(String specialEventId, SpecialEventDto incomingDto) {
        Optional<SpecialEvent> optExisting = specialEventRepository.findBySpecialEventId(specialEventId);
        if(optExisting.isPresent()) {
            SpecialEvent existing = optExisting.get();
            if (existing.getScheduleTime() == null || incomingDto.getScheduleTime() == null) {
                return true; // can't compare properly, assume changed
            }

            ScheduleTimeDtoMapper mapper = new ScheduleTimeDtoMapper();
            ScheduleTime incomingSchedule = mapper.toScheduleTime(incomingDto.getScheduleTime());
            return !Objects.equals(existing.getScheduleTime().getStartDate(), incomingSchedule.getStartDate())
                    || !Objects.equals(existing.getScheduleTime().getEndDate(), incomingSchedule.getEndDate())
                    || !Objects.equals(existing.getScheduleTime().getScheduleId(), incomingSchedule.getScheduleId());
        }
        return true;
    }

    @Override
    public boolean checkSpecialEventScheduleAvailability(String profileId, SpecialEventDto specialEventDto) {
        Optional<List<SpecialEvent>> optResultList = specialEventRepository.findAllSpecialEventsForProfileId(profileId);
        if (optResultList.isPresent()) {
            List<SpecialEvent> specialEvents = optResultList.get();
            for (SpecialEvent specialEvent : specialEvents) {
                Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findForSpecialEventId(specialEvent.getSpecialEventId());
                if (optScheduleTime.isPresent()) {
                    ScheduleTime scheduleTime = optScheduleTime.get();
                    if (scheduleTime.isOverlapping(specialEventDto.getScheduleTime())) {
                        return false;
                    }
                }
            }
            return true;
        }
        return false;
    }
}
