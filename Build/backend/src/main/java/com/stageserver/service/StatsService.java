package com.stageserver.service;

import com.stageserver.model.common.ProfileType;
import com.stageserver.model.event.Event;
import com.stageserver.model.login.User;
import com.stageserver.model.profile.Profile;
import com.stageserver.repository.ProfileRepository;
import com.stageserver.repository.UserRepository;
import com.stageserver.repository.event.EventMainInfoRepository;
import com.stageserver.repository.event.EventMediaInfoRepository;
import com.stageserver.repository.event.EventRepository;
import com.stageserver.service.interfaces.I_StatsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class StatsService implements I_StatsService {

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private EventRepository eventRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EventMediaInfoRepository eventMediaInfoRepository;

    @Autowired
    private EventMainInfoRepository eventMainInfoRepository;

    @Override
    public List<Profile> getSimilarProfiles(String profileId) {
        List<Profile> similarProfiles = new ArrayList<>();
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if(optProfile.isPresent()) {
            Profile profile = optProfile.get();
            if(profile.getProfileType() == ProfileType.ACT_PROFILE ) {
                similarProfiles = getSimilarActProfiles(profileId);
            }
            else if(profile.getProfileType() == ProfileType.VENUE_PROFILE) {
                similarProfiles = getSimilarVenueProfiles(profileId);
            }

        }
        return similarProfiles;
    }

    private List<Profile> getSimilarActProfiles(String profileId) {
        // Temporary implementation
        return profileRepository.get20ActProfiles();
    }

    private List<Profile> getSimilarVenueProfiles(String profileId) {
        // Temporary implementation
        return profileRepository.get20VenueProfiles();
    }

    @Override
    public List<Event> getSimilarEvents(String eventId) {
        // Returns the 10 future events
        List<Event> eventList = eventRepository.findFutureEvents();
        for (Event event : eventList) {
            String similarEventId = event.getEventId();
            if(similarEventId.equals(eventId)) {
                eventList.remove(event);
                break;
            }
            eventMediaInfoRepository.findByEventId(similarEventId).ifPresent(event::setEventMediaInfo);
            eventMainInfoRepository.findByEventId(similarEventId).ifPresent(event::setEventMainInfo);
        }
        return eventList;
    }

    @Override
    public List<Profile> getVisitedProfiles(String email) {
        List<Profile> visitedProfiles = new ArrayList<>();
        Optional<User> optUser = userRepository.findByEmail(email);
        if(optUser.isPresent()) {
            User user = optUser.get();
            List<String> visitedProfileIds = user.getRecentlyVisitedProfiles();

            for(String visitedProfileId: visitedProfileIds) {
                Optional<Profile> optVisitedProfile = profileRepository.findByProfileId(visitedProfileId);
                optVisitedProfile.ifPresent(visitedProfiles::add);
            }
        }
        return visitedProfiles;
    }
}
