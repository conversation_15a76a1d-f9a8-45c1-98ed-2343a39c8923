package com.stageserver.service;

import com.stageserver.model.common.ProfileType;
import com.stageserver.model.profile.Profile;
import com.stageserver.model.profile.ProfilePayments;
import com.stageserver.model.profile.VirtualActClaimToken;
import com.stageserver.model.common.SystemUser;
import com.stageserver.model.login.User;
import com.stageserver.model.profile.VirtualVenueClaimToken;
import com.stageserver.repository.*;
import com.stageserver.service.interfaces.I_SystemUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class SystemUserService implements I_SystemUserService {

    @Autowired
    private SystemUserRepository systemUserRepository;

    @Autowired
    private VirtualActClaimTokenRepository virtualActClaimTokenRepository;

    @Autowired
    private VirtualVenueClaimTokenRepository virtualVenueClaimTokenRepository;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private InstantMessageService instanceMessageService;

    private Optional<SystemUser> getSystemUser() {
        List<SystemUser> systemUsers = systemUserRepository.findAll();
        if (systemUsers.isEmpty()) {
            log.warn("No system user found");
            return Optional.empty();
        } else if (systemUsers.size() > 1) {
            log.warn("Multiple system users found");
            return Optional.empty();
        }
        return Optional.of(systemUsers.get(0));
    }

    @Override
    @Transactional
    public boolean claimVirtualAct(String token) {
        boolean profileExists = false;
        Optional<VirtualActClaimToken> optVirtualActClaimToken = virtualActClaimTokenRepository.findByTokenString(token);
        log.info("Claiming request for virtualAct with token: {}", token);
        if (optVirtualActClaimToken.isPresent()) {
            VirtualActClaimToken virtualActClaimToken = optVirtualActClaimToken.get();
            Optional<SystemUser> optSystemUser = getSystemUser();
            if (optSystemUser.isPresent()) {
                SystemUser systemUser = optSystemUser.get();
                List<Profile> profileList = systemUser.getProfileList();

                for (Profile profile : profileList) {
                    if (profile.getProfileId().equals(virtualActClaimToken.getProfileId())) {
                        profileExists = true;
                        break;
                    }
                }
                if (profileExists) {
                    virtualActClaimTokenRepository.delete(virtualActClaimToken);
                    // Now move the profile to the current user
                    moveProfileToCurrentUser(virtualActClaimToken.getProfileId(), virtualActClaimToken.getContactEmail());
                    return true;
                }
                else {
                    log.info("Virtual Act Profile does not exist");
                }
            }
        }
        return false;
    }

    @Transactional
    private void moveProfileToCurrentUser(String profileId, String email) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isEmpty()) {
            log.warn("Cannot move virtual profile as no no user found for email: {}", email);
            return;
        }
        profileRepository.moveProfileToGivenUser(profileId, email);
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if (optProfile.isPresent()) {
            Profile profile = optProfile.get();
            if (profile.getProfileType() == ProfileType.VIRTUAL_VENUE_PROFILE) {
                profile.setProfileType(ProfileType.VENUE_PROFILE);
            } else if (profile.getProfileType() == ProfileType.VIRTUAL_ACT_PROFILE) {
                profile.setProfileType(ProfileType.ACT_PROFILE);
            }
            profileRepository.save(profile);
        }
        // TODO: Check if we have pending contracts - send message to user
        instanceMessageService.sendVirtualBookingMessagesAfterClaim(email, profileId);
        log.info("Moved VirtualProfile {} to user {}", profileId, email);
        addPaymentInfo(profileId);
    }

    @Transactional
    private void addPaymentInfo(String profileId) {
        Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
        if (optProfile.isPresent()) {
            Profile profile = optProfile.get();
            ProfilePayments profilePayments = new ProfilePayments();
            profilePayments.setCurrency("CAD");
            profilePayments.setTypicalPrice(0);
            profile.setProfilePayments(profilePayments);
            profileRepository.save(profile);
        }
    }



    @Override
    @Transactional
    public void saveVirtualActClaimToken(String profileId, String email, String token) {

        Optional<SystemUser> optSystemUser = getSystemUser();
        if (optSystemUser.isPresent()) {
            SystemUser systemUser = optSystemUser.get();
            VirtualActClaimToken virtualActClaimToken = new VirtualActClaimToken(profileId, email, token, systemUser, 365 * 24 * 60);
            systemUser.getVirtualActClaimTokenList().add(virtualActClaimToken);
            systemUserRepository.save(systemUser);
        }
    }

    @Override
    @Transactional
    public void saveVirtualVenueClaimToken(String profileId, String email, String token) {

        Optional<SystemUser> optSystemUser = getSystemUser();
        if (optSystemUser.isPresent()) {
            SystemUser systemUser = optSystemUser.get();
            VirtualVenueClaimToken virtualVenueClaimToken = new VirtualVenueClaimToken(profileId, email, token, systemUser, 365 * 24 * 60);
            systemUser.getVirtualVenueClaimTokenList().add(virtualVenueClaimToken);
            systemUserRepository.save(systemUser);
        }
    }

    @Override
    public boolean claimVirtualVenue(String token) {
        boolean profileExists = false;
        Optional<VirtualVenueClaimToken> optVirtualActClaimToken = virtualVenueClaimTokenRepository.findByTokenString(token);
        log.info("Claiming Virtual Venue with token: {}", token);
        if (optVirtualActClaimToken.isPresent()) {
            VirtualVenueClaimToken virtualVenueClaimToken = optVirtualActClaimToken.get();
            Optional<SystemUser> optSystemUser = getSystemUser();
            if (optSystemUser.isPresent()) {
                List<Profile> profileList = optSystemUser.get().getProfileList();

                for (Profile profile : profileList) {
                    if (profile.getProfileId().equals(virtualVenueClaimToken.getProfileId())) {
                        profileExists = true;
                        break;
                    }
                }
                if (profileExists) {
                    virtualVenueClaimTokenRepository.delete(virtualVenueClaimToken);
                    // Now move the profile to the current user
                    moveProfileToCurrentUser(virtualVenueClaimToken.getProfileId(), virtualVenueClaimToken.getContactEmail());
                    return true;
                }
                else {
                    log.info("Virtual Venue Profile does not exist");
                }
            }
        }
        return false;
    }
}
