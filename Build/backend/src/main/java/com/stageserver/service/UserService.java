package com.stageserver.service;

import com.stageserver.config.Constants;
import com.stageserver.dto.event.EventDto;
import com.stageserver.dto.login.BlockedProfileInfoDto;
import com.stageserver.dto.login.UserDto;
import com.stageserver.dto.login.UserInfoDto;
import com.stageserver.dto.mapper.*;
import com.stageserver.dto.profile.ProfileMinimizedViewDto;
import com.stageserver.dto.schedule.ScheduleTimeDto;
import com.stageserver.model.common.EventStatus;
import com.stageserver.model.common.ProfileStatus;
import com.stageserver.model.common.ProfileType;
import com.stageserver.model.event.Event;
import com.stageserver.model.event.EventMainInfo;
import com.stageserver.model.event.EventMediaInfo;
import com.stageserver.model.location.Location;
import com.stageserver.model.login.ConfirmPhoneNumberToken;
import com.stageserver.model.login.ForgotPasswordToken;
import com.stageserver.model.login.User;
import com.stageserver.model.profile.Profile;
import com.stageserver.model.profile.ProfileMedia;
import com.stageserver.model.schedule.Recurrence;
import com.stageserver.model.schedule.RecurrenceEndType;
import com.stageserver.model.schedule.ScheduleTime;
import com.stageserver.repository.*;
import com.stageserver.repository.contract.ContractRepository;
import com.stageserver.repository.event.EventMainInfoRepository;
import com.stageserver.repository.event.EventMediaInfoRepository;
import com.stageserver.repository.event.EventRepository;
import com.stageserver.repository.schedule.RecurrenceEndTypeRepository;
import com.stageserver.repository.schedule.RecurrenceRepository;
import com.stageserver.repository.schedule.ScheduleTimeRepository;
import com.stageserver.service.interfaces.I_UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Service
public class UserService implements I_UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private Constants constants;

    @Autowired
    ConfirmPhoneNumberTokenRepository confirmPhoneNumberTokenRepository;

    @Autowired
    private ForgotPasswordTokenRepository forgotPasswordTokenRepository;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private LocationService locationService;

    @Autowired
    private LocationRepository locationRepository;

    @Autowired
    private EventRepository eventRepository;

    @Autowired
    private ScheduleTimeRepository scheduleTimeRepository;

    @Autowired
    private EventMediaInfoRepository eventMediaInfoRepository;

    @Autowired
    private EventMainInfoRepository eventMainInfoRepository;

    @Autowired
    private RecurrenceRepository recurrenceRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private RecurrenceEndTypeRepository recurrenceEndTypeRepository;

    @Autowired
    private ProfileMediaRepository profileMediaRepository;

    @Override
    public Optional<User> getUser(String email) {
        return userRepository.findByEmail(email);
    }

    @Override
    public Optional<UserDto> getUserDto(String email) {

        Optional<User> optUser = userRepository.findByEmail(email);
        if(optUser.isPresent()) {
            User user = optUser.get();
            UserDtoMapper userDtoMapper = new UserDtoMapper();
            UserDto result = userDtoMapper.toUserDto(user);
            List<String> blockedProfiles = user.getBlockedProfiles();
            List<BlockedProfileInfoDto> blockedProfilesInfoList = new ArrayList<>();
            for(String profileId : blockedProfiles) {
                Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
                if(optProfile.isPresent()) {
                    Profile profile = optProfile.get();
                    BlockedProfileInfoDto blockedProfileInfoDto = new BlockedProfileInfoDto();
                    blockedProfileInfoDto.setProfileId(profile.getProfileId());
                    blockedProfileInfoDto.setProfileName(profile.getProfileName());
                    blockedProfileInfoDto.setProfileType(profile.getProfileType().toString());
                    Optional<ProfileMedia> optProfileMedia = profileMediaRepository.findByProfileId(profile.getProfileId());
                    if(optProfileMedia.isPresent()) {
                        ProfileMedia profileMedia = optProfileMedia.get();
                        blockedProfileInfoDto.setImageUrls(profileMedia.getImageUrls());
                    }
                    blockedProfilesInfoList.add(blockedProfileInfoDto);
                }
            }
            result.setBlockedProfilesInfoList(blockedProfilesInfoList);
            return Optional.of(result);
        }
        return Optional.empty();
    }

    @Override
    public void saveCurrentUser(User user) {
        userRepository.save(user);
    }

    @Override
    public List<User> getUsers() {
        return userRepository.findAll();
    }

    @Override
    @Transactional
    public boolean addPhoneNumber(String email, String phoneNumber) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            user.setPhoneNumber(phoneNumber);
            user.setTwoFaEnabled(false);
            userRepository.save(user);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean deleteUser(String email) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            userRepository.forceDelete(email);
            return true;
        } else {
            return false;
        }
    }

    @Override
    @Transactional
    public void saveConfirmPhoneNumberToken(User user, String token) {
        int expiryTime = Constants.DEFAULT_EXPIRY_TIME;
        if (constants != null) {
            int expTime = constants.getTokenExpirationTime();
            if (expTime != Constants.ERROR_RESULT) {
                expiryTime = expTime;
            }
        }
        var confirmToken = new ConfirmPhoneNumberToken(token, user, expiryTime);
        confirmPhoneNumberTokenRepository.save(confirmToken);
    }

    @Override
    public Optional<User> findUserWithPasswordToken(String token) {
        ForgotPasswordToken passwordToken = forgotPasswordTokenRepository.findByToken(token);
        if (passwordToken != null) {
            return Optional.of(passwordToken.getUser());
        }
        return Optional.empty();
    }

    @Override
    @Transactional
    public boolean confirmPhoneNumber(String email, String code) {
        ConfirmPhoneNumberToken theToken = confirmPhoneNumberTokenRepository.findByToken(code);
        if ((theToken != null) && (theToken.getUser() != null)) {
            if (theToken.getUser().getEmail().equals(email)) {
                Calendar calendar = Calendar.getInstance();
                if ((theToken.getExpirationTime().getTime() - calendar.getTime().getTime()) > 0) {
                    User user = theToken.getUser();
                    user.setTwoFaEnabled(true);
                    userRepository.save(user);
                    // We need to delete the token after successful verification
                    confirmPhoneNumberTokenRepository.delete(theToken);
                    return true;
                }
            }
        }
        return false;
    }


    @Override
    @Transactional
    public boolean addFavorite(String email, String profileId) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            if (!user.getFavouriteActProfiles().contains(profileId)) {
                user.getFavouriteActProfiles().add(profileId);
                userRepository.save(user);
                Optional<Profile> optActProfile = profileRepository.findByProfileId(profileId);
                if (optActProfile.isPresent()) {
                    Profile profile = optActProfile.get();
                    int num = profile.getNumberOfFollowers() + 1;
                    profile.setNumberOfFollowers(num);
                    log.info("Number of Followers for Profile {} : {}", profileId, profile.getNumberOfFollowers());
                    profileRepository.save(profile);
                    return true;
                }
                log.warn("Profile with ID {} not found", profileId);
                return true;
            }
        }
        return false;
    }


    @Override
    @Transactional
    public boolean addFavoriteEvent(String email, String eventId) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            if(!user.getFavouriteEventList().contains(eventId)){
                user.getFavouriteEventList().add(eventId);
                userRepository.save(user);
                Optional<Event> optEvent = eventRepository.findByEventId(eventId);
                if (optEvent.isPresent()) {
                    Event event = optEvent.get();
                    int num = event.getNumberOfFollowers() + 1;
                    event.setNumberOfFollowers(num);
                    log.info("Number of Followers for Event {} : {}", eventId, event.getNumberOfFollowers());
                    eventRepository.save(event);
                    return true;
                }
                log.warn("Event with ID {} not found", eventId);
                return true;
            }
        }
        return false;
    }

    private List<EventDto> populateEventDetails(List<Event> eventList) {
        List<EventDto> result = new ArrayList<>();

        for (Event event : eventList) {
            EventDtoMapper eventDtoMapper = new EventDtoMapper();
            EventDto eventDto = eventDtoMapper.toEventDto(event);
            Optional<ScheduleTime> optScheduleTime = scheduleTimeRepository.findContractTimeWithEventId(event.getEventId());
            if (optScheduleTime.isPresent()) {

                ScheduleTime scheduleTime = optScheduleTime.get();
                Recurrence r = recurrenceRepository.getRecurrenceByEventId(event.getEventId(), scheduleTime.getElementId());
                RecurrenceEndType re = recurrenceEndTypeRepository.getRecurrenceEndTypeByEventId(event.getEventId(), scheduleTime.getElementId());
                if (r != null) {
                    r.setRecurrenceEndType(re);
                    scheduleTime.setRecurrence(r);
                }

                ScheduleTimeDtoMapper scheduleTimeDtoMapper = new ScheduleTimeDtoMapper();
                ScheduleTimeDto scheduleTimeDto = scheduleTimeDtoMapper.toScheduleTimeDto(scheduleTime);
                eventDto.setScheduleTime(scheduleTimeDto);
            }

            Optional<EventMediaInfo> optEventMediaInfo = eventMediaInfoRepository.findByEventId(event.getEventId());
            if(optEventMediaInfo.isPresent()) {
                EventMediaInfo eventMediaInfo = optEventMediaInfo.get();
                EventMediaInfoDtoMapper eventMediaInfoDtoMapper = new EventMediaInfoDtoMapper();
                eventDto.setEventMediaInfo(eventMediaInfoDtoMapper.toEventMediaInfoDto(eventMediaInfo));
            }
            Optional<EventMainInfo> optEventMainInfo = eventMainInfoRepository.findByEventId(event.getEventId());
            if(optEventMainInfo.isPresent()) {
                EventMainInfo eventMainInfo = optEventMainInfo.get();
                EventMainInfoDtoMapper eventMainInfoDtoMapper = new EventMainInfoDtoMapper();
                eventDto.setEventMainInfo(eventMainInfoDtoMapper.toEventMainInfoDto(eventMainInfo));
            }
            if ((event.getVenueProfileId() != null) && (!event.getVenueProfileId().isEmpty())) {
                Optional<Profile> optProfile = profileRepository.findByProfileId(event.getVenueProfileId());
                if (optProfile.isPresent()) {
                    Profile profile = optProfile.get();
                    LocationDtoMapper locationDtoMapper = new LocationDtoMapper();
                    eventDto.setVenueLocation(locationDtoMapper.toLocationDto(profile.getLocation()));
                    eventDto.setVenueName(profile.getProfileName());
                    Optional<ProfileMedia> optProfileMedia = profileMediaRepository.findByProfileId(profile.getProfileId());
                    if(optProfileMedia.isPresent()) {
                        ProfileMedia profileMedia = optProfileMedia.get();
                        eventDto.setVenueImageUrls(profileMedia.getImageUrls());
                    }
                }
            }
            result.add(eventDto);
        }
        return result;
    }

    @Override
    public Page<EventDto> getFavoriteEventsList(String email, int page, int size) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            List<Event> eventList = new ArrayList<>();
            for (String eventId : user.getFavouriteEventList()) {
                Optional<Event> optEvent = eventRepository.findByEventId(eventId);
                if (optEvent.isPresent()) {
                    Event event = optEvent.get();
                    if (event.getStatus() == EventStatus.STATUS_PUBLISHED){
                        eventList.add(event);
                    }
                }
            }
            Pageable pageable = PageRequest.ofSize(size).withPage(page);
            return new PageImpl<EventDto>(populateEventDetails(eventList), pageable, eventList.size());
        }
        return null;
    }

    public Page<ProfileMinimizedViewDto> getFavoritesList(ProfileType profileType, String email, int page, int size) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            List<Profile> profileList = new ArrayList<>();

            for (String profileId : user.getFavouriteActProfiles()) {
                profileRepository.findByProfileId(profileId)
                        .filter(profile -> profile.getStatus() == ProfileStatus.STATUS_PUBLISHED)
                        .filter(profile -> (profileType == ProfileType.ACT_PROFILE &&
                                (profile.getProfileType() == ProfileType.ACT_PROFILE || profile.getProfileType() == ProfileType.VIRTUAL_ACT_PROFILE))
                                || (profileType == ProfileType.VENUE_PROFILE &&
                                (profile.getProfileType() == ProfileType.VENUE_PROFILE || profile.getProfileType() == ProfileType.VIRTUAL_VENUE_PROFILE)))
                        .ifPresent(profileList::add);
            }

            // Create Pageable object
            Pageable pageable = PageRequest.of(page, size);

            // Get the total count for pagination metadata
            int total = profileList.size();

            // Perform manual pagination
            int fromIndex = page * size;
            int toIndex = Math.min(fromIndex + size, total);
            List<Profile> paginatedList = (fromIndex < total) ? profileList.subList(fromIndex, toIndex) : Collections.emptyList();

            // Convert to DTOs
            List<ProfileMinimizedViewDto> minimizedViewDtoList = profileService.populateMinimizedActProfiles(paginatedList);

            return new PageImpl<>(minimizedViewDtoList, pageable, total);
        }
        return Page.empty();
    }

    @Override
    @Transactional
    public boolean removeFromFavourites(String email, String profileId) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            user.getFavouriteActProfiles().remove(profileId);
            userRepository.save(user);
            Optional<Profile> optProfile = profileRepository.findByProfileId(profileId);
            if(optProfile.isPresent()) {
                Profile profile = optProfile.get();
                int num = Math.max(0, profile.getNumberOfFollowers() - 1);
                profile.setNumberOfFollowers(num);
                log.info("Reduced Number of Followers for Profile  {} : {}", profileId, num);
                profileRepository.save(profile);
                return true;
            }
            log.warn("removeFromFavourites : Profile with ID {} not found", profileId);
            return true;
        }
        return false;
    }


    @Override
    public boolean removeFromFavouriteEvent(String email, String eventId) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            user.getFavouriteEventList().remove(eventId);
            userRepository.save(user);
            Optional<Event> optEvent = eventRepository.findByEventId(eventId);
            if(optEvent.isPresent()) {
                Event event = optEvent.get();
                int num = event.getNumberOfFollowers() - 1;
                if(num < 0) {
                    num = 0;
                }
                event.setNumberOfFollowers(num);
                log.info("Reduced Number of Followers for Event  {} : {}", eventId, num);
                eventRepository.save(event);
                return true;
            }
            log.warn("removeFromFavouriteEvent: Event with ID {} not found", eventId);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean addRecentlyVisitedProfile(String email, String profileId) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if(optUser.isPresent()) {
            User user = optUser.get();

            if(!user.getRecentlyVisitedProfiles().contains(profileId)) {
                if(user.getRecentlyVisitedProfiles().size() >= Constants.MAX_RECENTLY_VIEWED) {
                    user.getRecentlyVisitedProfiles().remove(0);
                }
                user.getRecentlyVisitedProfiles().add(profileId);
                userRepository.save(user);
                return true;
            }
        }
        return false;
    }

    @Override
    @Transactional
    public boolean blockProfile(String email, String profileId) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if(optUser.isPresent()) {
            User user = optUser.get();
            if(!user.getBlockedProfiles().contains(profileId)) {
                user.getBlockedProfiles().add(profileId);
                userRepository.save(user);
                return true;
            }
        }
        return false;
    }

    @Override
    @Transactional
    public boolean unblockProfile(String email, String profileId) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if(optUser.isPresent()) {
            User user = optUser.get();
            if(user.getBlockedProfiles().contains(profileId)) {
                user.getBlockedProfiles().remove(profileId);
                userRepository.save(user);
                return true;
            }
        }
        return false;
    }

    @Override
    public Optional<List<User>> searchUsers(String searchString) {
        return userRepository.searchUsers(searchString);
    }

    @Override
    public boolean reconfirmPassword(String email, String password) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User user = optUser.get();
            return passwordEncoder.matches(password, user.getPassword());
        }
        return false;
    }

    @Override
    @Transactional
    public boolean updateUserInformation(String email, UserInfoDto infoDto) {
        Optional<User> optUser = userRepository.findByEmail(email);
        if (optUser.isPresent()) {
            User theUser = optUser.get();
            theUser.setFirstName(infoDto.getFirstName());
            theUser.setLastName(infoDto.getLastName());
            theUser.setPhoneNumber(infoDto.getPhoneNumber());
            LocationDtoMapper locationDtoMapper = new LocationDtoMapper();

            Optional<Location> optOldLocation = locationRepository.findForUser(theUser.getEmail());

            try {
                Location location = locationService.computeGeocode(locationDtoMapper.toLocation(infoDto.getLocation()));
                optOldLocation.ifPresent(value -> location.setElementId(value.getElementId()));
                theUser.setLocation(location);
            } catch (Exception e) {
                log.warn("updateActLocation - Geocode computation failed for : {}", locationDtoMapper.toLocation(infoDto.getLocation()).toString());
                return false;
            }

            theUser.setFirstAlternativePhoneNumber(infoDto.getFirstAlternativePhoneNumber());
            theUser.setSecondAlternativePhoneNumber(infoDto.getSecondAlternativePhoneNumber());
            userRepository.save(theUser);
            return true;
        } else {
            return false;
        }
    }

    @Override
    public User getProfileOwner(String profileId) {
        Optional<User> optUser = userRepository.findOwnerByProfileId(profileId);
        return optUser.orElse(null);
    }

}
