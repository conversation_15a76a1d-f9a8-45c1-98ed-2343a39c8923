package com.stageserver.service;

import com.stageserver.config.Constants;
import com.stageserver.service.interfaces.I_UtilityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Random;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
@RequiredArgsConstructor
public class UtilityService implements I_UtilityService {

    private static final int DEFAULT_SMS_CODE_LEN = 6;

    @Autowired
    private final Constants constants;

    @Override
    public String generateUniqueToken() {
        return UUID.randomUUID().toString();
    }

    @Override
    public String generateNumericToken() {
        Random rnd = new Random();
        int number = rnd.nextInt(999999);
        return String.format("%06d", number);
    }

    @Override
    public String generateUniqueUUID() {
        return UUID.randomUUID().toString();
    }

    public boolean stringContainsUpperCase(String password) {
        Pattern p = Pattern.compile("(?=.*[A-Z])");
        Matcher m = p.matcher(password);
        return m.find();
    }

    public boolean stringContainsSpecialCharacters(String password) {
        Pattern p = Pattern.compile("[^A0Za-z0-9]");
        Matcher m = p.matcher(password);
        return m.find();
    }

    public boolean stringContainsNumber(String password) {
        Pattern p = Pattern.compile("(?=.*\\d)");
        Matcher m = p.matcher(password);
        return m.find();
    }

    public boolean validNumericCode(String str) {
        int smsCodeLen = DEFAULT_SMS_CODE_LEN;
        if(constants.getMaxSMSCodeLength() != Constants.ERROR_RESULT) {
            smsCodeLen = constants.getMaxSMSCodeLength();
        }
        return str.matches("[0-9]+") && str.length() == smsCodeLen ;
    }
}

