package com.stageserver.service;

import com.stageserver.service.interfaces.I_VirtualBookingMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class VirtualBookingMonitorService implements I_VirtualBookingMonitorService {

    @Value("${virtual-profile-booking-cancel-time}")
    private int bookingCancellationTime;

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    public void scheduleTask(Runnable task) {
        if (bookingCancellationTime <= 0) {
            log.info("Booking cancellation time is not set - using default value of 15 minutes");
            bookingCancellationTime = 15;
        }
        else {
            log.info("Booking cancellation time is set to {} minutes", bookingCancellationTime);
        }
        scheduler.schedule(task, bookingCancellationTime, TimeUnit.MINUTES);
    }

}
