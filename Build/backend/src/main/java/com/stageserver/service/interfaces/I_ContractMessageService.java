package com.stageserver.service.interfaces;

import com.stageserver.dto.IM.MessageContentDto;
import com.stageserver.dto.contracts.NegotiateDataDto;

public interface I_ContractMessageService {

    boolean sendContract(String contractId);

    boolean receiveContract(String contractId);

    boolean cancelContract(String contractId, String authenticatedUser);

    boolean acceptContract(String contractId);

    boolean declineContract(String contractId);

    boolean negotiateContract(String contractId, NegotiateDataDto negotiateDataDt, String authenticatedUser);

}
