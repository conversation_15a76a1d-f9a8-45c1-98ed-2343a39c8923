package com.stageserver.service.interfaces;

import com.stageserver.dto.IM.ContractContentDto;
import com.stageserver.dto.common.UserDataDto;
import com.stageserver.dto.contracts.ContractDataDto;
import com.stageserver.dto.contracts.ContractDetailsDto;
import com.stageserver.dto.schedule.ScheduleTimeDto;
import com.stageserver.model.common.FinePrintData;
import com.stageserver.model.contract.*;
import com.stageserver.model.location.Location;
import com.stageserver.model.profile.Profile;
import com.stageserver.model.schedule.ScheduleTime;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface I_ContractService {

    String createContract(ContractDataDto contractDataDto);

    boolean addPrivateAddress(String email, String contractId, Location location);

    boolean addVenueInfo(String contractId, String profileId);

    boolean addActInfo(String contractId, String profileId);

    boolean addGoodsAndServices(String contractId, GoodsAndServices goodsAndServices);

    boolean addVenueRiderChanges(String contractId, VenueRiderChanges venueRiderChanges);

    boolean addActRiderNotes(String contractId, ActRiderNotes actRiderNotes);

    boolean addSchedule(String email, String contractId, ScheduleTime scheduleTime);

    ScheduleTime getScheduleTime(String contractId);

    Optional<ContractDetailsDto> getContractDetails(String contractId);

    Optional<ContractDetailsDto> getContractMessageDetails(String contractId, String messageId);

    ContractDetailsDto convertJsonToObject(String json);

    Optional<FinePrintData> getFinePrintData();

    Page<ContractDetailsDto> getAllContractsInitiated(String email, int page, int size);

    Page<ContractDetailsDto> getAllContractsReceived(String email, int page, int size);

    boolean addVenueRiderNotes(String contractId, VenueRiderNotes venueRiderNotes);

    boolean addActRiderChanges(String contractId, ActRiderChanges actRiderChanges);

    boolean addPurchaserInfo(String email, String contractId);

    Optional<GoodsAndServicesMessage> getGoodsAndServicesMessage(String name);

    Optional<List<GoodsAndServicesMessage>> getAllGoodsAndServicesMessages();

    boolean addGoodsAndServicesMessage(GoodsAndServicesMessage message, String email);

    boolean deleteGoodsAndServicesMessage(String name, String email);

    boolean updateGoodsAndServicesMessage(GoodsAndServicesMessage message, String email);

    boolean checkExistingGoodsAndServicesMessage(String name, String email);

    boolean checkMessageLimits(String email);

    boolean deleteSchedule(String email, String contractId);

    ContractDetailsDto.ContractParty getBookingParty(String contractId);

    ContractDetailsDto.ContractParty getOtherParty(String contractId);

    List<String> getProfileImages(Profile profile);

    boolean checkAvailability(String profileId, String contractId, ScheduleTimeDto scheduleTimeDto);

    Optional<ModifiedGoodsAndServices> getModifiedGoodsAndServicesData(String email, String contractId);

    String getContractState(String contractId);

    List<String> getPossibleActions(String email, String contractId, ContractState state);

    Optional<ContractState> getCurrentContractState(String contractId);

    Optional<List<String>> getCurrentPossibleActions(String contractId, String email);

    boolean checkInitiatorAvailability(String profileId, String contractId);

    boolean checkUserAvailability(String name, ScheduleTimeDto scheduleTimeDto);

    String setReceivingUserIfAbsent(String contractId);
}
