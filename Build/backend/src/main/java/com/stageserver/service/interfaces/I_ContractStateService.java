package com.stageserver.service.interfaces;

import com.stageserver.model.contract.ContractState;

public interface I_ContractStateService {

    boolean sendContract(String contractId);

    boolean acceptContract(String contractId);

    boolean declineContract(String contractId);

    boolean cancelContract(String contractId);

    boolean negotiateContract(String contractId);

    boolean receiveContract(String contractId);

    ContractState getCurrentState(String contractId);

}
