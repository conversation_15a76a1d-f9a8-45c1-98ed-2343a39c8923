package com.stageserver.service.interfaces;

import com.stageserver.dto.contracts.ContractDetailsDto;
import com.stageserver.dto.event.*;
import com.stageserver.model.common.EventStatus;
import com.stageserver.model.event.Event;
import com.stageserver.model.schedule.ScheduleTime;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Optional;

public interface I_EventService {

    EventDto readEvent(String eventId, String email);

    boolean updateEvent(String eventId, String email, Event event);

    boolean deleteEvent(String eventId, String email);

    Page<EventDto> readAllEvents(EventStatus status, String email, int page, int size);

    boolean addEventMainInfo(String eventId, String email, EventMainInfoDto eventMainInfo);

    EventMainInfoDto readEventMainInfo(String eventId, String email);

    boolean updateEventMainInfo(String eventId, String email, EventMainInfoDto eventMainInfo);

    boolean deleteEventMainInfo(String eventId, String email);

    Optional<EventVenueInfoDto> readEventVenueInfo(String eventId, String email);

    Optional<EventActInfoDto> readEventActInfo(String eventId, String name);

    String readEventStatus(String eventId, String name);

    boolean updateEventStatus(String eventId, String name);

    boolean addSchedule(String eventId, String email, ScheduleTime scheduleTime);

    boolean mainInfoAlreadyExists(String eventId);

    Optional<ContractInfoDetailsDto> getContractInfo(String eventId, String email);

    boolean updateContractInfo(String eventId, ContractInfoRequestDto contractInfoDto, String email);

    Page<ContractDetailsDto> getContractList(String eventId, String email, int page, int size);

    Optional<EventDto> readDefaultEvent(String eventId);

    EventMediaInfoDto readEventMediaInfo(String eventId, String email);

    Page<EventDto> readAllEventsForProfileId(String profileId, EventStatus status, String email, int page, int size);
}
