package com.stageserver.service.interfaces;

import com.stageserver.dto.feedback.FeedbackMsgDto;
import com.stageserver.dto.feedback.FeedbackResultsDto;
import com.stageserver.model.profile.ProfileRating;
import com.stageserver.model.feedback.FeedbackMsg;
import org.springframework.http.HttpStatus;

import java.util.Optional;

public interface I_FeedbackService {

    FeedbackResultsDto getFeedbackResults(String profileId);

    Optional<ProfileRating> readProfileRating(String username, String profileId);

    boolean deleteFeedback(String email, String feedbackId);

    HttpStatus updateFeedback(String email, String feedbackId, FeedbackMsgDto feedbackMsg);

    Optional<FeedbackMsg> getFeedback(String feedbackId, String email);
}
