package com.stageserver.service.interfaces;

import com.stageserver.dto.IM.InstantMessageDto;
import com.stageserver.dto.contracts.ContractDetailsDto;
import com.stageserver.dto.feedback.FeedbackMsgDto;
import com.stageserver.model.IM.InstantMessage;
import com.stageserver.model.feedback.FeedbackMsg;

import java.util.List;
import java.util.Optional;

public interface I_InstantMessageService {

    List<InstantMessageDto> getReceivedMessagesForUser(String email);

    List<InstantMessageDto> getSentMessagesForUser(String email);

    List<InstantMessageDto> getReceivedMessagesForProfile(String email, String profileId);

    List<InstantMessageDto> getSentMessagesForProfile(String email, String profileId);

    boolean setMessagesAsSeen(String email, String messageId);

    InstantMessage setupIMMessageForUser(InstantMessage message);

    InstantMessage setupIMMessageToVirtualProfile(InstantMessage message, String receiverProfileId);

    InstantMessage setupIMMessageFromVirtualProfile(InstantMessage message, String senderProfileId);

    boolean deleteNotification(String email, String messageId);

    Optional<ContractDetailsDto> getContractDetailsForMessageId(String messageId, String email);

    void sendFeedbackMessage(String feedbackId, FeedbackMsgDto feedbackMsgDto);

    void sendVirtualBookingMessagesAfterClaim(String email, String profileId);

    String generateActionString(InstantMessageDto instantMessageDto,String email);
}
