package com.stageserver.service.interfaces;

import com.stageserver.dto.location.LocationTupleDto;
import com.stageserver.model.location.Country;
import com.stageserver.model.location.Location;
import com.stageserver.model.profile.Profile;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

public interface I_LocationService {

    Optional<Country> getCountry(String countryName);

    List<LocationTupleDto> searchLocations(String searchString);

    Optional<List<Country>> getCountries();

    Location computeGeocode(String country, String state, String city) throws IOException;

}
