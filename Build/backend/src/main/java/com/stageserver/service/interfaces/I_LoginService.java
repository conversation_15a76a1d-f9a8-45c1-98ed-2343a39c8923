package com.stageserver.service.interfaces;

import com.stageserver.model.login.RegistrationRequest;
import com.stageserver.model.login.User;
import org.springframework.http.HttpStatus;

import java.util.Optional;

public interface I_LoginService {

    User registerUser(RegistrationRequest request);

    String login(String email, String password);

    Optional<User> findByEmail(String email);

    void saveUserVerificationToken(User newUser, String verificationToken);

    void saveUserForgotPasswordToken(User newUser, String verificationToken);

    void saveUserTwoFactorAuthToken(User user, String forgotPasswordToken);

    boolean validateToken(String token);

    HttpStatus changePassword(String oldPassword, String newPassword);

    boolean validatePassword(String password);

    boolean verifyTokenAndResetPassword(String token, String newPassword);

    boolean verifyTwoFactorAuthentication(String email, String token);

    HttpStatus resendRegisterEmail(String email);

    HttpStatus resendTwoFactorToken(String email);

    boolean resetPasswordWithTfa(String email, String token, String newPassword, String code);

    String loginTfa(String email);

    boolean verifyForgotPasswordTwoFa(String email, String code);

    boolean checkIfTwoFaVerified(String email);

    boolean verifyRegistrationEmail(String token);

    boolean validNewPassword(User user, String newPassword);

    void enableUser(String email);

    boolean checkUserExists(String email);
}
