package com.stageserver.service.interfaces;

import com.stageserver.model.event.EventMediaInfo;
import com.stageserver.model.profile.ProfileMedia;
import com.stageserver.model.profile.RiderDetails;
import org.springframework.http.HttpStatus;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;

public interface I_MediaStorageService {
    
    HttpStatus storeFile(String email, MultipartFile file, String profileId);

    HttpStatus storeEventImageFile(String email, MultipartFile file, String eventId);

    HttpStatus storePdfFile(String email, MultipartFile file, String profileId);

    boolean updateActMedia(String email, String profileId, ProfileMedia profileMedia);

    boolean updateEventMediaInfo(String email, String eventId, EventMediaInfo eventMediaInfo);

    Optional<ProfileMedia> readProfileMedia(String profileId);

    boolean deleteFile(String email, String profileId, String imageName);

    boolean deleteEventImageFile(String email, String eventId, String imageName);

    Optional<List<RiderDetails>> getRiderList(String email, String profileId);

    boolean deleteRiderDocument(String email, String profileId, String fileName);

    String storeSpecialEventImageFile(String username, MultipartFile file, String profileId, String specialEventId);

    boolean deleteSpecialEventImageFile(String name, String profileId, String imageName, String specialEventId);
}
