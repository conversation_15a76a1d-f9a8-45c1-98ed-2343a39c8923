package com.stageserver.service.interfaces;

import com.stageserver.dto.payment.StripePaymentRequestDto;
import com.stageserver.dto.payment.StripePaymentResponseDto;
import com.stripe.model.Event;

public interface I_PaymentService {

    StripePaymentResponseDto processPayment(String name, String contractId);

    boolean processWebhookEvent(String payload, String signature);

    boolean getPaymentStatus(String sessionId, String email);
}
