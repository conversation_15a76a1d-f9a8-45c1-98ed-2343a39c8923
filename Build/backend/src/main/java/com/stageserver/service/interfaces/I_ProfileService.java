package com.stageserver.service.interfaces;

import com.stageserver.dto.profile.ProfileDetailedViewDto;
import com.stageserver.dto.profile.ProfileMinimizedViewDto;
import com.stageserver.model.common.ProfileType;
import com.stageserver.model.login.User;
import com.stageserver.model.profile.*;
import com.stageserver.model.distribution.Distribution;
import com.stageserver.model.distribution.DistributionMember;
import com.stageserver.model.location.Location;
import com.stageserver.model.supported.*;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;

import java.util.List;
import java.util.Optional;

public interface I_ProfileService {

    Optional<SupportedMusicGenre> getSupportedMusicGenre();

    Optional<SupportedLanguages> getSupportedLanguages();

    Optional<SupportedEntertainmentTypes> getSupportedEntertainmentTypes();

    String createProfile(String email, Profile profile);

    Page<Profile> readAllProfiles(String userName, int page, int size);

    Profile readProfile(String email, String profileId);

    HttpStatus deleteProfile(String email, String profileId);

    Optional<SupportedActRoles> getSupportedActRoles();

    Location addProfileLocation(String email, String profileId, Location location);

    boolean locationAlreadyExist(String email, String profileId);

    Location updateProfileLocation(String email, String profileId, Location location);

    Optional<Location> readProfileLocation(String email, String profileId);

    boolean deleteProfileLocation(String email, String profileId);

    Optional<SupportedRegions> getSupportedRegions();

    Optional<SupportedOptions> getSupportedOptions();

    boolean updateProfile(String email, String profileId, Profile newProfile);

    boolean infoAlreadyExist(String email, String profileId);

    boolean addProfileInfo(String email, String profileId, ProfileInfo profileInfo);

    Optional<ProfileInfo> readProfileInfo(String email, String profileId);

    boolean updateProfileInfo(String email, String profileId, ProfileInfo profileInfo);

    boolean addActSkills(String email, String profileId, ActSkills actSkills);

    Optional<ActSkills> readActSkills(String email, String profileId);

    boolean addProfilePayments(String email, String profileId, ProfilePayments profilePayments);

    Optional<ProfilePayments> readActPayments(String email, String profileId);

    boolean addProfileMedia(String email, String profileId, ProfileMedia profileMedia);

    boolean updateProfilePayments(String email, String profileId, ProfilePayments profilePayments);

    Page<ProfileMinimizedViewDto> searchAllInCurrentUserProfiles(String email, int page, int size, ProfileType profileType);

    ProfileDetailedViewDto readProfileDetailedView(String email, String profileId);

    String readProfileStatus(String email, String profileId);

    boolean updateProfileStatus(String email, String profileId, String status);

    Optional<Profile> getProfile(String profileId);

    boolean isValidProfileId(String profileId);

    boolean addDistributionMember(String profileId, DistributionMember distributionMember);

    Optional<List<DistributionMember>> getDistributionList(String profileId);

    boolean actSkillsAlreadyExists(String email, String profileId);

    boolean updateActSkillsForProfile(String email, String profileId, ActSkills actSkills);

    boolean actPaymentAlreadyExists(String email, String profileId);

    void updateActSkills(Profile profile, String profileId, ActSkills skills );

    void updateActMedia(Profile profile, String profileId, ProfileMedia profileMedia);

    void updateDistribution(Profile profile, String profileId, Distribution distribution);

    boolean isFavorite(String email, String profileId);

    Optional<List<DistributionMember>> searchDistributionList(String profileId, String searchString);

    boolean checkIfNameUpdateAllowed(String email, String profileId, String actProfileName);

    boolean isMyProfile(String email, String profileId);

    boolean checkIfNameAvailableInRadius(String profileId, String email, String profileName);

    boolean updateProfileName(String profileId, String email, String newProfileName);

    boolean updateNotForRent(String email, String profileId, boolean enable);

    boolean userEmailAlreadyExists(String email);

    List<Profile> getAllVirtualProfilesWithContactEmail(String email);

    boolean validateAddress(Location location);

    Optional<User> getProfileOwner(String profileId);

    Optional<ProfileRating> getProfileRating(String profileId);

    boolean isAFan(String email);
}
