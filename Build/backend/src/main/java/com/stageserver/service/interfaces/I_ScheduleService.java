package com.stageserver.service.interfaces;

import com.stageserver.dto.calendar.QueryPeriod;
import com.stageserver.dto.profile.ProfileCalendarDto;
import com.stageserver.model.common.DateRange;
import com.stageserver.model.schedule.ScheduleTime;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

public interface I_ScheduleService {

    String addSchedule(String email, String profileId, ScheduleTime scheduleTime);

    Optional<List<ScheduleTime>> getScheduleList(String email, String profileId);

    boolean updateSchedule(String email, String profileId, String scheduleId, ScheduleTime scheduleTime);

    boolean deleteSchedule(String email, String profileId, String scheduleId);

    ScheduleTime getSchedule(String email, String profileId, String scheduleId);

    Optional<ProfileCalendarDto> getProfileCalendar(String email, String profileId, ZonedDateTime startDate, QueryPeriod period);
}
