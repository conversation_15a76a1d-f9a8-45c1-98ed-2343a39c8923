package com.stageserver.service.interfaces;

import com.stageserver.dto.profile.ProfileMinimizedViewDto;
import com.stageserver.dto.search.SearchLocationDto;
import com.stageserver.model.common.ProfileType;
import com.stageserver.model.distribution.DistributionMember;
import com.stageserver.model.location.SearchLocation;
import com.stageserver.model.supported.MusicGenre;
import com.stageserver.model.search.SearchData;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Optional;

public interface I_SearchService {

    List<MusicGenre> searchMusicGenre(String searchString);

    Page<ProfileMinimizedViewDto> searchStringsInCurrentUserProfiles(String username, List<String> searchStrings, int page, int size, ProfileType profileType);

    boolean updateSearchLocation(SearchLocationDto searchLocation);

    boolean isSearchNameExists(String email, String searchName);

    boolean addSearchData(String email, SearchData searchData);

    List<SearchData> getSearchData(String email);

    boolean deleteSearchData(String email, String searchName);

    Optional<SearchData> getSearchDataByName(String email, String searchName);

    Page<ProfileMinimizedViewDto> doGenericSearchWithFilters(SearchData searchData, ProfileType profileType, int page, int size, boolean select);

    Page<ProfileMinimizedViewDto> doUserProfileSearchWithFilters(SearchData serialize, ProfileType profileType, int page, int size);

    Page<ProfileMinimizedViewDto> doSearchAndSelectWithFilters(SearchData serialize, ProfileType profileType, int page, int size);

    public List<String> removeCommonWords(List<String> searchStrings);

    Optional<List<DistributionMember>> searchDistributionList(String profileId, String searchString);

    boolean setDefaultSearchLocation(SearchLocation searchLocation);

    Optional<SearchLocation> getDefaultSearchLocation();

    boolean deleteDefaultSearchLocation(String email);

    boolean updateSearchData(String email, SearchData serialize);

}
