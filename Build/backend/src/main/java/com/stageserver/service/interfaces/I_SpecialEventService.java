package com.stageserver.service.interfaces;

import com.stageserver.dto.event.SpecialEventDto;
import com.stageserver.model.event.SpecialEvent;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Optional;

public interface I_SpecialEventService {

    Optional<SpecialEvent> createSpecialEvent(String profileId, String email, SpecialEventDto specialEventDto);

    Optional<SpecialEvent> readSpecialEvent(String specialEventId, String email);

    Optional<SpecialEvent> updateSpecialEvent(String email, SpecialEventDto specialEventDto);

    boolean deleteSpecialEvent(String specialEventId, String email);

    Page<SpecialEvent> getAllSpecialEvents(String profileId, String email, int page, int size);

    boolean checkSpecialEventScheduleAvailability(String profileId, SpecialEventDto specialEventDto);

    boolean scheduleChanged(String specialEventId, SpecialEventDto incomingDto);
}
