package com.stageserver.service.interfaces;

import com.stageserver.dto.event.EventDto;
import com.stageserver.dto.login.UserDto;
import com.stageserver.dto.login.UserInfoDto;
import com.stageserver.dto.profile.ProfileMinimizedViewDto;
import com.stageserver.model.common.ProfileType;
import com.stageserver.model.login.User;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Optional;

public interface I_UserService {
    Optional<User> getUser(String email);

    Optional<UserDto> getUserDto(String email);

    List<User> getUsers();

    boolean addPhoneNumber(String name, String phoneNumber);

    boolean deleteUser(String email);

    boolean confirmPhoneNumber(String name, String code);

    void saveConfirmPhoneNumberToken(User user, String token);

    Optional<User> findUserWithPasswordToken(String token);

    boolean addFavorite(String email, String profileId);

    Page<ProfileMinimizedViewDto> getFavoritesList(ProfileType profileType, String email, int page, int size);

    boolean removeFromFavourites(String email, String profileId);

    Optional<List<User>> searchUsers(String searchString);

    boolean reconfirmPassword(String name, String password);

    boolean updateUserInformation(String email, UserInfoDto infoDto);

    User getProfileOwner(String profileId);

    boolean addFavoriteEvent(String email, String eventId);

    Page<EventDto> getFavoriteEventsList(String email, int page, int size);

    boolean removeFromFavouriteEvent(String email, String eventId);

    boolean addRecentlyVisitedProfile(String email, String profileId);

    boolean blockProfile(String email, String profileId);

    boolean unblockProfile(String email, String profileId);

    void saveCurrentUser(User user);
}
