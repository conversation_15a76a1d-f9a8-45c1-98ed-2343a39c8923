
# logging.level.root=INFO
# logging.level.root=OFF
logging.level.root=ERROR
# logging.level.stageserver=DEBUG
# logging.level.org.springframework.web=INFO

### ç¬¬åæ­¥ï¼

# Neo4j Configuration
spring.neo4j.uri=${NEO4J_URI:bolt://neo4j:7687}
spring.neo4j.authentication.username=${NEO4J_USERNAME:neo4j}
spring.neo4j.authentication.password=${NEO4J_PASSWORD:password}

# AWS S3 Configuration
aws.access.key.id=${AWS_ACCESS_KEY_ID}
aws.secret.access.key=${AWS_SECRET_ACCESS_KEY}
aws.region=${AWS_REGION}
s3.bucket.name=${S3_BUCKET_NAME}

# Server Configuration
server.port=8080
