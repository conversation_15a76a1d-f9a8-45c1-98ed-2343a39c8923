
spring.neo4j.uri=bolt://localhost:7687
spring.neo4j.authentication.username=neo4j
spring.neo4j.authentication.password=K@kapoDev@123
spring.neo4j.ssl.trust.strategy=TRUST_ALL_CERTIFICATES
org.springframework.data.neo4j=DEBUG

springdoc.api-docs.path=/apidocs
spring.profiles.active=prod
spring.messages.basename=lang/messages

json.files.location=./config/json
pdf.files.location=./pdf-files
logging.level.root=INFO
logging.level.kakapo=INFO

#Password Policy
stage-server.password.max-length=20
stage-server.password.min-length=6
stage-server.password.min-special-char=0
stage-server.password.min-uppercase=0

spring.http.multipart.max-file-size=10MB
spring.http.multipart.max-request-size=10MB
spring.http.multipart.enabled=true
spring.http.multipart.location= ../uploads

# Email/SMS Tokens expiration time in minutes
stage-server.token.expiration-time=15

# Two Factor Authentication Prompt
stage-server.two-factor-authentication-prompt=true
stage-server-sms-code-length=6

# Time in minutes
virtual-profile-booking-cancel-time=10

#stage-server-front-end-url=http://************
#stage-server-back-end-url=http://************:8080

stage-server-front-end-url=https://efilmprime.com
stage-server-back-end-url=https://efilmprime.com


# ACT Profile items
stage-server.act.performing-languages=English, French, French(CA),Italian, German, Don't Care

spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=stsgvrgzqjcirdmd
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true


app.jwt-secret=5040841344278d3586387df5e511bbe7504353f3380df26c8011bc6957014453
app.jwt-expiration-milliseconds=108000000
#google.api.key=AIzaSyClhpo7rBycmGYRmb09rGUHfNMaDx2gbVc
google.api.key=AIzaSyCcinvHKlp-sBxSE0Vz1NQFA3msyVQlQuY

spring.security.oauth2.client.registration.google.client-id=769596721332-u6k6pad5l2bmg3rvl7sjg19c6u5unsbo.apps.googleusercontent.com
spring.security.oauth2.client.registration.google.client-secret=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
spring.security.oauth2.client.registration.google.redirect-uri={baseUrl}/login/oauth2/code/{registrationId}
spring.security.oauth2.client.registration.facebook.redirect-uri={baseUrl}/login/oauth2/code/{registrationId}


stripe.payment.secret.key=sk_test_51RDByZPop6RwQuX8akrWeUcjFx1L3rs5Dk08Ds8PaiiYqUo6EGd7vsNiVQkR0gY1dUny5JFKltPFOwrG5IYNYB4200WWU8Fqhu
stripe.webhook.secret=whsec_AJvSmAPPV0oLBu2E3zz8JzEYQkgtE6Tt
stage-server-stripe-payment-amount=1000
stage-server-stripe-payment-currency=CAD
stage-server-stripe-payment-name=StageMinder Payment
stage-server-stripe-payment-quantity=1

server.forward-headers-strategy=native

aws.region=us-east-2
aws.access-key=********************
aws.secret-key=wPMYat7JS1zMBjqK836gNHeluuziQp80vlLCi1Hn
aws.s3.bucket-name=hbp3


spring.security.oauth2.client.registration.facebook.client-id=1003906351841381
spring.security.oauth2.client.registration.facebook.client-secret=********************************
spring.security.oauth2.client.registration.facebook.scope=email,public_profile

