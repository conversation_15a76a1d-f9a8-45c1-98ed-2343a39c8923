
#Messages in successful cases
message-phone-added-success=Phone Number Added successfully
message-email-sent=An email is sent with instructions
message-phone-confirmed-success=Phone Number is successfully confirmed
message-user-registered-success=User is registered successfully
message-user-twofa-enabled=User has two factor authentication enabled
message-user-login-success=User has successfully logged in
message-sms-token-sent=A SMS code has been sent for two factor authentication
message-reset-password-success=Password successfully reset; please use the new password to login
message-data-retrieval-success=Successfully retrieved the data
message-profile-create-success=Profile Created Successfully
message-data-location-create-success=Location Created Successfully
message-profile-data-updated=Profile data updated successfully
message-profile-info-create-success=Act Info added Successfully
message-delete-data-success=Data Deleted Successfully
message-data-add-success=Data added Successfully
message-image-upload-success=File uploaded successfully
message-file-delete-success=File deleted successfully
message-schedule-read-success=Schedule read successfully
message-schedule-create-success=Schedule created successfully
message-location-not-exists=Location does not exist for the profile
message-no-rider-docs-found=No rider documents found
message-password-confirmed=Password confirmed successfully
message-password-not-confirmed=Password not confirmed
message-virtual-profile-claimed-success=Virtual Profile claimed successfully
message-virtual-profile-claim-failed=Virtual Profile claim failed
message-data-update-success=Data updated successfully
message-contract-sent-success=Contract Action executed successfully

#Error Messages in failure cases
error-user-not-authorized-to-update=User is not authorized to update the data
error-unable-to-compute-geo-coordinates=Unable to compute geo-coordinates for the location
error-data-update-failed=Data update failed
error-data-delete-failed=Data delete failed
error-data-read-failed=Data read failed
error-working-hours-not-expected=Working hours only accepted for Venue Profile
error-contact-not-provided=Virtual Contact not provided
error-invalid-profile-type=Invalid Profile Type provided
error-region-not-supported=Region not supported
error-data-add-fail=Unable to add data
error-profile-name-exists=Profile name already exists
error-schedule-read-failed=Schedule read failed
error-schedule-create-failed=Schedule creation failed
error-invalid-language=Invalid language provided
error-invalid-act-role=Invalid Act Role provided
error-invalid-region=Invalid region(Country/State/City) provided
error-invalid-music-genre=Invalid music genre provided
error-invalid-act-type=Invalid Act Type provided
error-invalid-payment-method=Invalid payment method provided
error-invalid-currency=Invalid currency code
error-bandwidth-exceeded=Bandwidth exceeded
error-service-unavailable=Service Unavailable
error-expectation-failed=Expectation Failed
error-internal-server-error=Internal Server Error
error-unauthorized=User is not authorized
error-invalid-phone-number=Phone number is not in valid format
error-not-acceptable=Not acceptable
error-bad-request=Bad Request or incorrect format
error-invalid-sms-code=Invalid or expired SMS code
error-invalid-email-format=Incorrectly formatted email address
error-password-policy-violation=Password does not conform to policy
error-user-already-exists=User with the email already exists
error-unable-to-send-email=Unable to send email
error-not-found=Not Found
error-not-implemented=Not Implemented
error-user-not-twofa-enabled=Two Factor Authentication not enabled for the user
error-user-twofa-verify-failed=Two Factor Authentication Failed
error-invalid-or-expired-token=Invalid or Expired Token
error-forgot-password-token-exists=A forgot password request already exists. Please wait 20 minutes before requesting again
error-verify-email-token-exists=User was requested to verify earlier
error-verify-twofa-token-exists=A Two factor Authentication request still exists. Please wait 20 minutes before requesting again
error-act-location-already-exists=Location already exists for the profile
error-act-info-already-exists=ActInfo already exists for the profile

#Exception messages in failure cases
exception-msg.json=Malformed JSON request
exception-msg.user-exists=User already exists
exception-disabled=User is not enabled
exception-bad-credentials=Bad credentials provided



