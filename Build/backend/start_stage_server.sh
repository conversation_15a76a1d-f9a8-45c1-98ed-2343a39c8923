#!/bin/bash

APP_NAME="StageServer"

echo "To run with debugger enabled, use: $0 --debug"

# Look for the latest matching StageServer-*.jar in the current directory
JAR_NAME=$(ls -t ./${APP_NAME}-*.jar 2>/dev/null | head -n 1)

if [ -z "$JAR_NAME" ]; then
    echo "No JAR file found in current directory matching ${APP_NAME}-*.jar"
    exit 1
fi

echo "🔍 Found JAR: $JAR_NAME"

# Check if debug mode is requested
DEBUG_MODE=false
if [[ "$1" == "--debug" ]]; then
    DEBUG_MODE=true
fi

# Kill existing app process if running
server_pid=$(ps -ef | grep "$APP_NAME" | grep -v grep | awk '{print $2}')
if [ -n "$server_pid" ]; then
    echo "$APP_NAME is already running (PID $server_pid). Killing it..."
    kill -9 "$server_pid"
    sleep 2
else
    echo "$APP_NAME is not currently running."
fi

mkdir -p logs

# Start application
if [ "$DEBUG_MODE" = true ]; then
    echo "Starting $APP_NAME with debug port 5005..."
    nohup java \
      -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005 \
      -jar "$JAR_NAME" > ./logs/"$APP_NAME.log" 2>&1 &
else
    echo "Starting $APP_NAME..."
    nohup java \
      -jar "$JAR_NAME" > ./logs/"$APP_NAME.log" 2>&1 &
fi

echo "$APP_NAME started. Logs available at ./logs/$APP_NAME.log"
