# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production build
.next
out

# Environment files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# Git
.git
.gitignore

# IDE
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Tests
__tests__
*.test.js
*.spec.js

# Documentation
README.md
*.md

# Misc
.eslintrc.json
.prettierrc 