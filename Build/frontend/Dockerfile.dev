# 开发环境专用 Dockerfile
FROM node:18-alpine

WORKDIR /app

# 安装依赖
COPY package.json package-lock.json* ./
RUN npm install

# 复制配置文件
COPY next.config.mjs ./
COPY tailwind.config.js ./
COPY postcss.config.js ./
COPY jsconfig.json ./
COPY dot.env .env

# 复制初始源代码（volumes 会覆盖这些用于热更新）
COPY src ./src
COPY public ./public
COPY messages ./messages

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV NODE_ENV=development
ENV HOSTNAME=0.0.0.0
ENV PORT=3000

# 启动开发服务器
CMD ["npm", "run", "dev"] 