# Stage Minder Frontend

A modern web application built with Next.js for connecting musicians and venues. The platform facilitates booking management and profile creation for both artists and venues.

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn

## Installation

1. Clone the repository
```bash
git clone [repository-url]
cd humming-bird
```

2. Install dependencies
```bash
npm install
# or
yarn install
```

3. Configure environment variables by creating a `.env` file:
```
NEXT_PUBLIC_API_URL=http://*************:8080/api/v1/
NEXT_PUBLIC_SOCKET_URL=http://*************:8080/ws

```

## Development

Run the development server:
```bash
npm run dev
# or
yarn dev
```

The application will be available at [http://localhost:3000](http://localhost:3000)

## Project Structure

```
humming-bird/
├── src/
│   ├── app/              # Next.js 13+ app directory
│   ├── assets/           # Static assets (images, fonts)
│   ├── component/        # Reusable React components
│   ├── config/           # Configuration files
│   ├── context/          # React context providers
│   ├── ui/              # UI components and layouts
│   └── utils/           # Utility functions
├── public/              # Public static files

```

## Key Technologies

- Next.js 13+
- React
- Material-UI (MUI)
- Firebase Authentication
- WebSocket for real-time features
- Redux for state management

## Available Scripts

- `npm run dev` - Run development server
- `npm run build` - Build production bundle
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [Material-UI Documentation](https://mui.com/getting-started/usage/)
- [Firebase Documentation](https://firebase.google.com/docs)
