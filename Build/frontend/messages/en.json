{"specialEvents": {"addSpecialEvent": "Add new event"}, "login": {"login": "Log In", "haveAnAccount": "Don’t have an account?", "signup": "Sign up", "LoginForm": {"Email": "Email", "Password": "Password", "rememberMe": "Remember me", "youAgree": "By signing up to your account you agree with our", "PrivacyPolicy": "Privacy Policy.", "login": "Log In", "ForgotPassword": "Forgot Password?"}}, "signUp": {"register": "Register", "haveAnAccount": "Already have an account?", "logIn": "Log In", "SignupForm": {"Email": "Email", "Password": "Password", "rememberMe": "Remember me", "RepeatPassword": "Repeat Password", "youAgree": "By signing up to your account you agree with our", "PrivacyPolicy": "Privacy Policy.", "SignUp": "Sign Up"}}, "resendEmail": {"verifyYourAccount": "Enter the email address you used to verify your account", "enterEmail": "Enter the email address"}, "emailVerification": {"checkYourEmail": "We’ve sent you a link. Check your email", "resetLink": "Not received your reset link?", "checkSpan": " Check your spam folder or wait 15 minutes before resend.", "backTo": "Back to"}, "passwordRecovery": {"passwordRecovery": "Password Recovery", "enterEmailAddress": "Please enter your email address below and we'll email you instructions to reset it.", "haveAnAccount": "Already have an account?", "logIn": "Log in"}, "resetPassword": {"setNewPassword": "Set a new password", "haveAnAccount": "Already have an account?", "logIn": "Log in"}, "OTP": {"verificationCode": "Enter Verification Code", "OTPForm": {"VerifyOTP": "Verify OTP"}}, "2faAuthentication": {"twoFactor": "Enable two-factor", "authentication": "authentication", "enterPhoneNo": "Enter you phone number below, please:", "setUpLater": "Set Up later", "2faAuthenticationForm": {"VerificationCode": "Get the verification code"}, "2faResendCode": {"ResendCode": "Resend code"}, "2faOTPForm": {"Setup": "Set up"}}, "2faAuthenticationOTP": {"twoFactor": "Enable two-factor", "authentication": "authentication", "6DigitCode": "Enter the 6 digit code you just received", "setUpLater": "Set Up later"}, "notFound": {"LostWay": "Lost Your way ?", "pageNotFound": "Sorry, we can't find that page. You'll find lots to explore on the home page", "home": "Home", "ErrorCode": "Error Code-404"}, "Home": {"EventUniverse": "Unveil the Event Universe: Where Creation Meets Management in Perfect Harmony!", "ArtistVenue": "This site is a Promotion and Administration Tool for Artist and Venues", "LearnMore": "Learn More", "Administer": "Administer your events", "description": "Short description goes here in one paragraph. Enim quis enim, tincidunt.", "SearchFor": "Search for: Acts, Venues, Events, Service Providers and/or Talent", "CreateAutomate": "Create and automate promotion tools", "ViewDetail": "View Detail", "ShowMore": " Show more", "Carousel": {"Events": "Empowering Events, Elevating Experiences –", "Seemlessly": " Seamlessly Manage and Create"}}, "Header": {"UniqueEvents": "Unique event creation platform", "Register/Login": "Register / Log in", "aboutUs": "About Us", "benefits": "Benefits", "pricing": "Pricing", "contactUs": "Contact us"}, "dashboard": {"ThisWeek": "Upcoming Events", "forAct": "(for act, sp, talent profiles, and even Fan)", "continue": "Events In Progress"}, "CreateProfiles": {"ActProfile": "Create the Act profile for future events", "Act": "Act", "Venue": "Venue", "VenueProfile": "Enter the actual address of the private/commercial accommodation that is not linked to a venue in the system.", "ServiceProvider": "Service Provider", "Talent": "Talent", "saveUnpublished": "Save Unpuplished", "createProfile": "Create profile", "ComingSoon": "Coming soon", "WantToCreate": "Which profile do you want to create?"}, "actInfoPerson": {"saveUnpublished": "Save Unpuplished", "actInformation": "Act's information", "ActInfoPersonForm": {"Bio": "Bio", "Social": "Social media links", "YourLink": "your_link"}}, "actInformation": {"saveUnpublished": "Save Unpuplished", "CreateProfile": "Create profile", "ActInformation": "Act’s information", "ActInfoForm": {"NameOfAct": "Name of Act", "NumberOfMembers": "Number of Members", "Authorize": "Who is authorized representer of that act?", "IAm": "I am", "Another": "Another person", "Role": "Role", "ProfileEmail": "Profile Email Address", "EnterEmail": "Enter profile email address", "Performance": "Performance language(s) of the Act", "Communication": "Language(s) for business communication", "Preferred": "Preferred language for communication", "ActInfoIAm": {"UseEmailAddress": "Use my Email Address"}}, "editActInfo": {"whoYouAct": "Who are you in the Act?"}}, "actLocation": {"saveUnpuplished": "Save Unpuplished", "createProfile": "Create profile", "actLocation": "Act’s Location", "ActLocationForm": {"UseLocation": "Use my Location", "Country": "Country", "State": "State", "City": "City", "addressLine": "address line 1", "StreetAddress": "Street Address", "Zip": "Zip/Postal code", "LongDistance": "Can travel long distances", "selectCity": "Select City", "selectState": "Select State", "selectCountry": "Select Country"}}, "profileFooter": {"Back": "Back", "Next": "Next"}, "actType": {"saveUnpuplished": "Save Unpuplished", "createProfile": "Create profile", "actType": "Entertainment Type", "selectOne": "Select one of the following. Note: this can not be changed ones profile is published", "actTypeSavedSuccessfully": "Act type saved successfully", "success": "success"}, "actPreview": {"preview": "Preview", "musicGenre": "Music Genre", "aboutTheAct": "About the act", "locationInfo": {"travelDistance": "Travel distance", "seniorEntertainment": "Senior Entertainment", "standard": "Standard", "charity": "Charity"}}, "musicGenre": {"saveUnpuplished": "Save Unpuplished", "createProfile": "Create profile", "musicGenre": "Music genre", "musicGenreForm": {"errorOccurred": "Unexpected error occurred", "quickSearch": "Enter you music genre for a quick search", "noFound": "No Music music genre list found"}, "musicGenrePreview": {"selectedGenres": "Selected genres", "clearAll": "Clear all", "noMusic": "No Music genre yet , <PERSON><PERSON> on the left panel to select"}}, "actMedia": {"saveUnpuplished": "Save Unpuplished", "createProfile": "Create profile", "actMedia": "Act’s Media", "actMediaForm": {"photoSize": "try to keep the size of the photo under 10 MB.", "uploadingPhotos": "we recommend uploading photos in PNG, GIF or JPEG.", "perfectSize": "perfect size would be 2160x1080 pixels.", "quick": "quick 30 sec", "overviewBand": "up to 1 min overview of the band in action", "photos": "Photos", "drag&Drop": "Click or Drag & Drop", "duploaia": "to upload media files", "cover": "Cover", "toUpload": "to upload", "videoLink": "Video link (optional)", "vimeo": "link to Vimeo or Youtube", "addLink": "Add link", "audioLink": "Audio file (optional)", "soundCloud": "link to Soundcloud"}}, "actPayment": {"saveUnpuplished": "Save Unpuplished", "createProfile": "Create profile", "actPayment": "Act’s Price and Payment", "actPaymentForm": {"typicalPrice": "Typical price", "per": "per", "minimalPrice": "Minimal price", "charity": "Сharity or Cause Event with benefits to Act", "paymentMethod": "Acceptable payment method", "selectCurrency": "Select Currency", "paymentOption": "Select Payment Option"}}, "actReview": {"saveUnpuplished": "Save Unpuplished", "createProfile": "Create profile", "preview": "Preview", "back": "Back", "submit": "Submit", "actCreated": "Act created successfully", "success": "Success", "actReviewCommon": {"reviews": "reviews", "playInternational": "Can play International", "usa": "USA, Mexico, Cuba, Panama,", "more": "and 5 more", "aboutAct": "About the act"}, "actReviewLocation": {"banner": "Gold list banner", "playInternational": "Can play International", "usa": "USA, Mexico, Cuba, Panama,", "more": "and 5 more", "reviews": "reviews", "startedAt": " Started at", "bookNow": "Book Now", "respondHour": "Usually responds in less than an hour", "per": "per", "event": "event", "nextEvent": "Next event:"}}, "profiles": {"profiles": "Profiles", "newProfile": "New profile", "search": "Search your profiles", "noProfile": "No Profile Found", "profileCard": {"profileLocation": "Profile Location", "startedAt": "Started At", "viewProfile": "View profile", "noProfiles": "No Profiles Found , Create One", "writeAReview": "Write a review"}}, "search": {"title": "Search Page", "noSearch": "No Data Found", "weAreSorry": "We are sorry", "specifiedFilter": "Nothing currently found for the specified filters", "saveThisSearch": "Save this search", "searchTerm": "Your search terms and filters will be saved so you can come back to it later", "map": "Map", "list": "List"}, "contracts": {"contractByMe": {"contracts": "Contracts", "keyword": "Search by keyword", "generatedByMe": "Generated by me", "generatedByOther": "Generated by other"}}, "contractsFilter": {"totalRequest": "total requests", "date": "Date", "searchByKeyword": "Search by key word"}, "navbar": {"lookingFor": "Search StageMinder", "looking": "Search"}, "editActCommon": {"save": "Save", "back": "Back", "save&Close": "Save & Close", "editProfile": "Edit Profile", "editAct": "Edit Act’s information", "children": "Suitable for children", "adult": "Suitable for adults only"}, "noteList": {"pleaseNote": "Please Note"}, "editSidebar": {"actInfo": "Act’s Info", "actMedia": "Act’s Media", "actLocation": "Act’s Location", "price&Payment": "Price & Payment", "rider": "Rider", "availability": "Availability schedule", "distribution": "Distribution list", "feedback": "<PERSON><PERSON><PERSON>", "profileDetail": "Profile details", "actBio": "Act’s Bio", "actEntertainmentType": "Act’s Entertainment Type", "actMusicGenre": "Act’s Music genre", "venueInfo": "Venue’s Info", "venueBio": "Venue’s Bio", "venueMedia": "Venue’s Media", "venueLocation": "Venue’s Location"}, "rider": {"addRider": "Add Rider", "view": "View", "actRider": "Act’s rider", "upload": "Upload", "document": "try to keep the size of the document under 200 MB.", "recommandons": "we recommend uploading document in PDF format.", "anyIllegalSubtances": "Any illegal substances are prohibited?", "selectAReason": "Select a reason"}, "distributionList": {"venueAcc": "[My Venue acc]", "userAcc": "[Basic user acc]", "noOne": "No one in the list", "startCreating": "Start creating by adding people", "addList": "Add to the list", "addMember": "Add member to the distribution list", "selectAccount": "Select account", "firstName": "First Name", "enterFirstName": "Enter First name", "lastName": "Last Name", "enterLastName": "Enter Last name", "theUser": "The User", "enterEmail": "<PERSON><PERSON>", "role": "Role", "manager": "Ex: Manager", "distributive": "Distribution Type", "listOfPeople": "A list of people and the type of contract that should be available to them. Please note that users who are not on our system will receive the contractі by email.", "search": "Search by name or email", "contractList": "contact in a list", "selectVersion": "Select Version"}, "availabilityList": {"calendar": "Your Availability Calendar", "unavailableHour": "Choose your unavailable hours", "unavailable": "Unavailable", "editDate": "Edit date & time", "timeZone": "Time zone", "startDate": "Start date", "finishDate": "Finish date", "unavailableCalendar": "This time will be unavailable in your calendar", "deleteTime": "Delete the time", "apply": "Apply", "singleDate": "Single date", "recurringDate": "Recurring date", "weekly": "WEEKLY", "biWeekly": "BI-WEEKLY", "monthly": "MONTHLY"}, "calendarHeader": {"upcomingEvents": "Availability & Upcoming Events", "viewAs": "View as"}, "feedback": {"feedback": "<PERSON><PERSON><PERSON>", "entertainment": "Entertainment Value", "professionalism": "Professionalism", "draw": "Draw", "rate": "Rate", "kateLuschanska": "<PERSON>", "booking": "Booking contract / [Event Date]", "new": "New", "viewContract": "View contract"}, "act": {"follow": "Follow", "reviews": "Reviews", "showMore": "Show more", "showLess": "Show less", "likeThis": "More like this act", "mayLike": "You may like", "recentlyVisited": "Profiles recently visited", "followers": "followers", "gigs/month": "gigs/month", "average": "average", "sortBy": "Sort by"}, "leaveFeedback": {"leaveFeedback": "Leave Feedback", "cancel": "Cancel", "leaveFeedbackForm": {"drawAsExpected": "Draw as expected", "leaveFeedbackTo": "Leave <PERSON><PERSON><PERSON> to", "actName": "Act name", "public": " Public", "visibleProfile": "Will be visible on the performer’s public profile", "feedbackMessage": "Feedback message", "private": "Private", "performer": "Only for performer", "send": "Send"}, "successFeedback": {"done": "Done", "feedbackSent": "Your Feedback has been sent", "successDescription": "V<PERSON>ue will see this on their profile page. You can also see the feedback you have sent in the Feedback section of your Act Profile.", "close": "Close"}}, "blockList": {"unblock": "Unblock", "blockList": "Block list", "unblockDialog": {"unblockAct": "Are you sure you want to unblock {name}?", "blockAct": "Are you sure you want to block {name}?"}}, "accountInformation": {"accountInfo": "Account Information", "accountInfoForm": {"personalData": "Personal data", "emailAddress": "Email address", "principalPhone": "Principal phone number", "alternativePhone": "Alternative phone number", "addressData": "Address data", "address": "Address", "corporateInfo": "Corporate information", "informationAbout": "Information about the corporate I am representing, if have ", "corporationName": "Corporation Name"}}, "settings": {"settings": "Settings", "settingsInfo": {"notifyByEmail": "Notify by email", "notifyBySms": "Notify via SMS", "appNotifications": "In app notifications", "allowNotifications": "Allow notifications", "newBooking": "New booking requests", "newMessages": "New messages", "newFollowers": "New followers", "upcomingEvents": "Upcoming events", "waitingBooking": "Waiting booking requests", "notifications": "Notifications", "howNotify": "How would you like to be notified?", "pushNotifications": "Push-notifications", "deviceSettings": "Need to allow the app to send you notifications in your device settings.", "privacy": "Privacy", "changePassword": "Change password", "account": "Account", "deleteUserAccount": "Here you can delete the personal data of your user account in accordance with GDPR.", "deleteAccount": "Delete my account"}}, "changePassword": {"changeIt": "Please enter your current password below to change it.", "changePasswordForm": {"changePassword": "Change Password", "newPassword": "New Password", "repeatPassword": "Repeat New Password", "resetPassword": "Reset Password"}}, "tokenExpire": {"loggedIn": " Hey, it looks like you're not logged in", "loveToHelp": "We'd love to help you, but we can't save the result if you're not logged in. Sign in or create an account so you don't lose it."}, "verifyVirtualAct": {"successfully": "Virtual Act verified successfully!", "failed": "Virtual Act verification failed"}, "validation": {"countryRequired": "Country is required", "stateRequired": "State is required", "cityRequired": "City is required", "streetAddressRequired": "Street Address is required", "zipCodeRequired": "Zip Code is required"}, "notification": {"notifications": "Notifications", "noNotification": "No notifications available"}, "bookingDetail": {"download": "Download"}, "createBooking": {"bookingRequest": "Booking request", "placementDetails": "Have the event placement details been determined for this booking request?", "eventTakePlace": "Where will the event take place?", "myVenue": "My Venue", "commercialAddress": "Private/Commercial address", "selectVenueProfile": "Select the Venue profile from My Venue profile list", "actualAddress": "Enter the actual address of the private/commercial accommodation that is not linked to a venue in the system.", "selectVenue": "Select your Venue", "setDateAndTime": "Set up date and time", "clickCalendar": "Click on calendar to select date and time range", "purchaserDetails": "Go to purchaser details", "sendToAct": "Send to Act", "goodsAndServices": "Statement of goods and services", "riderDetail": "Go to rider details"}, "requestSummary": {"requestSummary": "Request summary", "who": "Who?", "toDefine": "To define", "when": "When?", "load": "Load", "start": "Start", "duration": "Duration", "fee": "Fee", "contractDetails": "Contract details", "keySummaryPoint": "Here is a summary of some of the key contract points", "soundAndLightening": "Sound and lighting", "nominal": "Reimburse nominal iGig.org fee", "specialProvision": "Also some Special provisions"}, "savedSearch": {"saveThisSearch": "Save this search", "apliedFilter": "Applied filters", "dateFilter": "Keep dynamic date filter", "rollingForward": "Keep the date filter rolling forward", "notifyMeUpdate": "Notify me about updates", "receiveMails": "You will receive emails when search results for this saved search updates.", "viewEvent": "View event", "searchTerm": "Your search terms and filters will be saved so you can come back to it later", "searchName": "Search name", "filterRolling": "Keep the date filter rolling forward", "theSearch": "The search", "alreadyExist": "already exists.", "replace": "Do you want to replace the existing one?", "saveAs": "save as"}, "savedSearchSuccess": {"savedSuccessfully": "Search have been saved successfully!", "fineThisSaved": "You can find this saved search on the", "favoritespage": "Favorites page"}, "searchFilter": {"saveSearch": "Save search", "showList": "Show list", "showMap": "Show map", "location": "Location", "entertainmentType": "Entertainment (type)", "musicGenre": " Music (Genre)", "rating": "Rating", "4AndMore": "4 and more", "3AndMore": "3 and more", "2AndMore": "2 and more", "1AndMore": "1 and more", "withoutRating": "Without rating", "today": "TODAY", "thisWeek": "THIS WEEK", "thisMonth": "NEXT 14 DAYS", "custom": "CUSTOM"}, "searchHeader": {"easyBooking": "Easy booking", "results": "results", "instaBook": "InstaBook", "search": "Search", "highestRateFirst": "Highest rate first", "cheapestRateFirst": "Cheapest rate first", "mostPopularFirst": "Most popular first", "lessPopularFirst": "Less popular first", "event": "Events", "act": "Act", "venue": "Venue"}, "autoLocation": {"addLocation": "City"}, "sidebar": {"dashboard": "Dashboard", "eventCalendar": "Event calendar", "profiles": "Profiles", "messages": "Messages", "calendar": "Calendar", "help": "Help", "logOut": "Logout", "hide": "<PERSON>de"}, "title": {"seeAll": "See all"}, "bookingVenue": {"locationAddressLine": "Location address line", "selected": "Selected", "select": "Select"}, "venueWorkingHour": {"workingHour": "Working hours", "enableWorkingHour": "Enable working hours", "startTime": "Start time", "endTime": "End time", "copyToAll": "Copy to all"}, "profileSuccess": {"success": "Success!", "publish": "Publish", "profileCreated": "Your Profile has been created", "virtualVenueCreated": "Virtual Venue has been created", "virtualActCreated": "Virtual Act has been created"}, "dropdown": {"selectProfile": "Select Profile", "weekly": "Weekly", "selectLanguage": "Select Language", "selectRole": "Select Role", "selectCurrency": "Select Currency", "selectPer": "Select Per", "selectDistributionType": "Select Distribution Type", "selectLocation": "Select Location"}, "venue": {"venueInformation": "Venue's Information", "venueMedia": "Venue's Media", "venueLocation": "Add Venue Location", "venuePayment": "Venue's Price and Payment", "location": "Venue's Location", "editInfo": "Edit <PERSON>ue's Info"}, "favourites": {"acts": "Acts", "events": "Events", "venues": "Venues", "saveSearchs": "Saved Searches", "favourites": "Favourites"}}