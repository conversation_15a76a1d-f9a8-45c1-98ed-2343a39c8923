{"login": {"login": "Se connecter", "haveAnAccount": "Vous n'avez pas de compte ?", "signup": "S'inscrire", "LoginForm": {"Email": "E-mail", "Password": "Mot de passe", "rememberMe": "Souviens-toi de moi", "youAgree": "En vous inscrivant à votre compte, vous acceptez nos", "PrivacyPolicy": "Politique de confidentialité.", "login": "Se connecter", "ForgotPassword": "Mot de passe oublié?"}}, "signUp": {"register": "Registre", "haveAnAccount": "Vous avez déjà un compte?", "logIn": "Se connecter", "SignupForm": {"Email": "E-mail", "Password": "Mot de passe", "rememberMe": "Souviens-toi de moi", "RepeatPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON> le mot de passe", "youAgree": "En vous inscrivant à votre compte, vous acceptez nos", "PrivacyPolicy": "Politique de confidentialité.", "SignUp": "S'inscrire"}}, "resendEmail": {"verifyYourAccount": "Entrez l'adresse e-mail que vous avez utilisée pour vérifier votre compte", "enterEmail": "Entrez l'adresse e-mail"}, "emailVerification": {"checkYourEmail": "Nous vous avons envoyé un lien. Vérifiez votre courrier électronique", "resetLink": "Vous n'avez pas reçu votre lien de réinitialisation ?", "checkSpan": "Vérifiez votre dossier spam ou attendez 15 minutes avant de renvoyer.", "backTo": "Retour à"}, "passwordRecovery": {"passwordRecovery": "Récupération de mot de passe", "enterEmailAddress": "Veuillez entrer votre adresse e-mail ci-dessous et nous vous enverrons par e-mail des instructions pour la réinitialiser.", "haveAnAccount": "Vous avez déjà un compte?", "logIn": "Se connecter"}, "resetPassword": {"setNewPassword": "Définir un nouveau mot de passe", "haveAnAccount": "Vous avez déjà un compte?", "logIn": "Se connecter"}, "OTP": {"verificationCode": "Entrez le code de vérification", "OTPForm": {"VerifyOTP": "Vérifier OTP"}}, "2faAuthentication": {"twoFactor": "Activer deux facteurs", "authentication": "authentification", "enterPhoneNo": "Entrez votre numéro de téléphone ci-dessous, s'il vous plaît :", "setUpLater": "Configurer plus tard", "2faAuthenticationForm": {"VerificationCode": "Obtenez le code de vérification"}, "2faResendCode": {"ResendCode": "Renvoyer le code"}, "2faOTPForm": {"Setup": "Installation"}}, "2faAuthenticationOTP": {"twoFactor": "Activer deux facteurs", "authentication": "authentification", "6DigitCode": "Entrez le code à 6 chiffres que vous venez de recevoir", "setUpLater": "Configurer plus tard"}, "notFound": {"LostWay": "Vous avez perdu votre chemin ?", "pageNotFound": "<PERSON><PERSON><PERSON><PERSON>, nous ne trouvons pas cette page. Vous trouverez beaucoup de choses à explorer sur la page d'accueil", "home": "<PERSON><PERSON>", "ErrorCode": "Code d'erreur-404"}, "Home": {"EventUniverse": "Dévoilez l'univers événementiel : où la création rencontre la gestion dans Parfaite harmonie!", "ArtistVenue": "Ce site est un outil de promotion et d'administration pour les artistes et les salles", "LearnMore": "Apprendre encore plus", "Administer": "Administrez vos événements", "description": "Une brève description est ici en un seul paragraphe. Enim quis enim, tincidunt.", "SearchFor": "Rechercher : Actes, lieux, événements, prestataires de services et/ou talents", "CreateAutomate": "Créer et automatiser des outils de promotion", "ViewDetail": "Voir les détails", "ShowMore": "Montre plus", "Carousel": {"Events": "Des événements stimulants, des expériences rehaussantes –", "Seemlessly": "<PERSON><PERSON><PERSON> et créez en toute transparence"}}, "Header": {"UniqueEvents": "Plateforme unique de création d'événements", "Register/Login": "S'inscrire / Se connecter", "aboutUs": "À propos de nous", "benefits": "Avantages", "pricing": "<PERSON><PERSON><PERSON>", "contactUs": "Contactez-nous"}, "dashboard": {"ThisWeek": "A venir cette semaine", "forAct": "(pour les actes, les sp, les profils de talents et même les fans)", "continue": "Continuer à modifier les événements"}, "CreateProfiles": {"ActProfile": "<PERSON><PERSON><PERSON> le profil Act pour les événements futurs", "Act": "Acte", "Venue": "<PERSON><PERSON>", "VenueProfile": "Saisissez l'adresse réelle du logement privé/commercial qui n'est lié à aucun lieu dans le système.", "ServiceProvider": "Fournisseur de services", "Talent": "Talent", "saveUnpublished": "Enregistrer non publié", "createProfile": "<PERSON><PERSON>er un profil", "ComingSoon": "À venir", "WantToCreate": "Quel profil souhaitez-vous créer ?"}, "actInfoPerson": {"saveUnpublished": "Enregistrer non publié", "actInformation": "Informations sur l'acte", "ActInfoPersonForm": {"Bio": "Biographie", "Social": "Liens vers les réseaux sociaux", "YourLink": "votre lien"}}, "actInformation": {"saveUnpublished": "Enregistrer non publié", "CreateProfile": "<PERSON><PERSON>er un profil", "ActInformation": "Informations sur l'acte", "ActInfoForm": {"NameOfAct": "Nom de l'acte", "NumberOfMembers": "Nombre de membres", "Authorize": "Qui est le représentant autorisé de cet acte ?", "IAm": "Je suis", "Another": "Une autre personne", "Role": "R<PERSON><PERSON>", "ProfileEmail": "Adresse e-mail du profil", "EnterEmail": "Entrez l'adresse e-mail du profil", "Performance": "Langue(s) d'exécution de la Loi", "Communication": "Langue(s) pour la communication commerciale", "Preferred": "Langue préférée pour la communication", "ActInfoIAm": {"UseEmailAddress": "Utiliser mon adresse e-mail"}}, "editActInfo": {"whoYouAct": "Qui es-tu dans la loi ?"}}, "actLocation": {"saveUnpuplished": "Enregistrer non publié", "createProfile": "<PERSON><PERSON>er un profil", "actLocation": "Emplacement de l'acte", "ActLocationForm": {"UseLocation": "Utilise ma location", "Country": "Pays", "State": "État", "City": "Ville", "StreetAddress": "<PERSON><PERSON>e de la rue", "addressLine": "Adresse Ligne 1", "Zip": "Zip / code postal", "LongDistance": "Peut parcourir de longues distances", "selectCity": "Sélectionnez une ville", "selectState": "Sélectionnez l'état", "selectCountry": "Choisissez le pays"}}, "profileFooter": {"Back": "<PERSON><PERSON>", "Next": "Suivante"}, "actType": {"saveUnpuplished": "Enregistrer non publié", "CreateProfile": "<PERSON><PERSON>er un profil", "ActType": "Type de divertissement", "selectOne": "Sélectionnez l'un des éléments suivants. Remarque : cela ne peut pas être modifié si le profil est publié", "actTypeSavedSuccessfully": "Type d'acte enregistré avec succès", "success": "<PERSON><PERSON><PERSON>"}, "actPreview": {"preview": "<PERSON><PERSON><PERSON><PERSON>", "musicGenre": "Genre de musique", "aboutTheAct": "À propos de l'acte", "locationInfo": {"travelDistance": "Distance parcourue", "seniorEntertainment": "Divertissement pour les seniors", "standard": "Standard", "charity": "<PERSON><PERSON><PERSON>"}}, "musicGenre": {"saveUnpuplished": "Enregistrer non publié", "createProfile": "<PERSON><PERSON>er un profil", "musicGenre": "Genre de musique", "musicGenreForm": {"errorOccurred": "Une erreur inattendue s'est produite", "quickSearch": "Entrez votre genre musical pour une recherche rapide", "noFound": "Aucune liste de genres musicaux trouvés"}, "musicGenrePreview": {"selectedGenres": "Genres sélectionnés", "clearAll": "Tout effacer", "noMusic": "Aucun genre musical pour l'instant, cliquez sur le panneau de gauche pour sélectionner"}}, "actMedia": {"saveUnpuplished": "Enregistrer non publié", "createProfile": "<PERSON><PERSON>er un profil", "actMedia": "Médias d’Act", "actMediaForm": {"photoSize": "essayez de garder la taille de la photo en dessous de 10 Mo.", "uploadingPhotos": "nous vous recommandons de télécharger des photos au format PNG, GIF ou JPEG.", "perfectSize": "la taille parfaite serait de 2160 x 1080 pixels.", "quick": "rapide 30 secondes", "overviewBand": "jusqu'à 1 minute d'aperçu du groupe en action", "photos": "Photos", "duploaia": "pour télécharger des fichiers multimédias", "drag&Drop": "<PERSON><PERSON><PERSON> ou glis<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadMedia": "pour télécharger des fichiers multimédias", "cover": "Couverture", "toUpload": "télécharger", "videoLink": "<PERSON><PERSON> v<PERSON> (facultatif)", "vimeo": "lien vers Vimeo ou Youtube", "addLink": "Ajouter un lien", "audioLink": "Fichier audio (facultatif)", "soundCloud": "lien vers Soundcloud"}}, "actPayment": {"saveUnpuplished": "Enregistrer non publié", "createProfile": "<PERSON><PERSON>er un profil", "actPayment": "Prix et paiement de l'acte", "actPaymentForm": {"typicalPrice": "Prix typique", "per": "par", "minimalPrice": "Prix minime", "charity": "Événement caritatif ou caritatif avec des avantages pour agir", "paymentMethod": "Mode de paiement acceptable", "selectCurrency": "Sélectionnez la devise", "paymentOption": "Sélectionnez l'option de paiement"}}, "actReview": {"saveUnpuplished": "Enregistrer non publié", "createProfile": "<PERSON><PERSON>er un profil", "preview": "<PERSON><PERSON><PERSON><PERSON>", "back": "<PERSON><PERSON>", "submit": "So<PERSON><PERSON><PERSON>", "actCreated": "Acte créé avec succès", "success": "Su<PERSON>ès", "actReviewCommon": {"reviews": "Commentaires", "playInternational": "Peut jouer à l'international", "usa": "États-Unis, Mexique, Cuba, Panama,", "more": "et 5 autres", "aboutAct": "À propos de l'acte"}, "actReviewLocation": {"banner": "Bannière de la liste d'or", "playInternational": "Peut jouer à l'international", "usa": "États-Unis, Mexique, Cuba, Panama,", "more": "et 5 autres", "reviews": "Commentaires", "startedAt": " <PERSON><PERSON><PERSON><PERSON>", "bookNow": "Reserve maintenant", "respondHour": "Répond généralement en moins d'une heure", "per": "par", "event": "événement", "nextEvent": "Prochain événement :"}}, "profiles": {"profiles": "Profils", "newProfile": "Nouveau profile", "search": "Recherchez vos profils", "noProfile": "<PERSON><PERSON><PERSON> profil trouvé", "profileCard": {"profileLocation": "Emplacement du profil", "startedAt": "<PERSON><PERSON><PERSON><PERSON>", "viewProfile": "Voir le profil", "noProfiles": "Aucun profil trouvé, créez-en un", "writeAReview": "Écrire une critique"}}, "contracts": {"contractByMe": {"contracts": "Contrats", "keyword": "Recherche par mots-clés", "generatedByMe": "G<PERSON><PERSON><PERSON> par moi", "generatedByOther": "Généré par d'autres"}}, "contractsFilter": {"totalRequest": "demandes totales", "date": "Date", "searchByKeyword": "Recherche par mots-clés"}, "navbar": {"lookingFor": "Search StageMinder", "looking": "Search"}, "editActCommon": {"save": "<PERSON><PERSON><PERSON><PERSON>", "back": "<PERSON><PERSON>", "save&Close": "Sauvegarder et fermer", "editProfile": "Editer le profil", "editAct": "Modifier les informations d'Act", "children": "Convient aux enfants", "adult": "Convient uniquement aux adultes"}, "noteList": {"pleaseNote": "Veuillez noter"}, "editSidebar": {"actInfo": "Informations sur la loi", "actMedia": "Médias d’Act", "actLocation": "Emplacement de l'acte", "price&Payment": "Prix ​​et paiement", "rider": "Cavalière", "availability": "Calendrier de disponibilité", "distribution": "Liste de distribution", "feedback": "Retour", "profileDetail": "<PERSON>é<PERSON> du profil d'Act", "actBio": "Biographie d'Act", "actEntertainmentType": "Type de divertissement de l'acte", "actMusicGenre": "Genre musical de l'acte", "venueInfo": "Informations sur le lieu", "venueBio": "Biographie du lieu", "venueMedia": "<PERSON><PERSON><PERSON><PERSON> du lieu", "venueLocation": "Emplacement du lieu"}, "rider": {"addRider": "Ajouter un cavalier", "view": "Voir", "actRider": "Cavalier d'Act", "upload": "Télécharger", "document": "essayez de maintenir la taille du document en dessous de 200 Mo.", "recommandons": "nous vous recommandons de télécharger le document au format PDF.", "anyIllegalSubtances": "Des substances illégales sont-elles interdites ?", "selectAReason": "Sélectionnez une raison"}, "distributionList": {"venueAcc": "[Mon compte Venue]", "userAcc": "[Accès utilisateur de base]", "noOne": "Personne dans la liste", "startCreating": "Commencez à créer en ajoutant des personnes", "addList": "Ajouter à la liste", "addMember": "Ajouter un membre à la liste de distribution", "selectAccount": "Sélectionnez un compte", "firstName": "Prénom", "enterFirstName": "Entrez votre prénom", "lastName": "Nom de famille", "enterLastName": "Entrer le nom de famille", "theUser": "L'utilisateur", "enterEmail": "Entrez l'e-mail", "role": "R<PERSON><PERSON>", "manager": "Ex : <PERSON><PERSON><PERSON>", "distributive": "Type de distribution", "listOfPeople": "Une liste de personnes et le type de contrat qui devrait être disponible pour eux. Veuillez noter que les utilisateurs qui ne sont pas sur notre système recevront le contrat par email.", "search": "Rechercher par nom ou par e-mail", "contractList": "contact dans une liste", "selectVersion": "Sélectionnez la version"}, "availabilityList": {"calendar": "Votre calendrier de disponibilité", "unavailableHour": "Choisissez vos horaires d'indisponibilité", "unavailable": "Indisponible", "editDate": "Modifier la date et l'heure", "timeZone": "<PERSON><PERSON> ho<PERSON>", "startDate": "Date de début", "finishDate": "Date de fin", "unavailableCalendar": "Cette heure ne sera pas disponible dans votre calendrier", "deleteTime": "Supprimer l'heure", "apply": "Appliquer", "singleDate": "<PERSON><PERSON>-vous unique", "recurringDate": "Date récurrente", "weekly": "HEBDOMADAIRE", "biWeekly": "BIHEBDOMADAIRE", "monthly": "MENSUEL"}, "calendarHeader": {"upcomingEvents": "Disponibilité et événements à venir", "viewAs": "Voir comme"}, "feedback": {"feedback": "Retour", "entertainment": "Valeur de divertissement", "professionalism": "Professionnalisme", "draw": "<PERSON><PERSON><PERSON>", "rate": "<PERSON><PERSON>", "kateLuschanska": "<PERSON>", "booking": "Contrat de réservation / [Date de l'événement]", "new": "Nouvelle", "viewContract": "Voir le contrat"}, "act": {"follow": "Suivre", "reviews": "Commentaires", "showMore": "Montre plus", "showLess": "<PERSON><PERSON> moins", "likeThis": "Plus comme cet acte", "mayLike": "Tu peux aimer", "recentlyVisited": "Profils récemment visités", "followers": "suiveurs", "gigs/month": "concerts/mois", "average": "moyenne", "sortBy": "Trier par"}, "leaveFeedback": {"leaveFeedback": "Laisser les commentaires", "cancel": "Annuler", "leaveFeedbackForm": {"drawAsExpected": "Dessinez comme prévu", "leaveFeedbackTo": "Laissez vos commentaires à", "actName": "Nom de l'acte", "public": " Public", "visibleProfile": "Sera visible sur le profil public de l’artiste", "feedbackMessage": "Message de commentaires", "private": "Priv<PERSON>", "performer": "Uniquement pour l'interprète", "send": "Envoyer"}, "successFeedback": {"done": "Fait", "feedbackSent": "Votre commentaire a été envoyé", "successDescription": "Act verra cela sur sa page de profil. Vous pouvez également voir les commentaires que vous avez envoyés dans la section Commentaires de votre profil de site.", "close": "<PERSON><PERSON><PERSON>"}}, "blockList": {"unblock": "Débloquer", "blockList": "Liste de blocage", "unblockDialog": {"unblockAct": "Êtes-vous sûr de vouloir débloquer Act_name ?", "emailAddress": "Adresse e-mail", "principalPhone": "Numéro de téléphone principal", "alternativePhone": "Numéro de téléphone alternatif", "addressData": "<PERSON><PERSON><PERSON> d'adresse", "address": "<PERSON><PERSON><PERSON>", "corporateInfo": "Information d'entreprise", "informationAbout": "Informations sur l'entreprise que je représente, le cas échéant ", "corporationName": "Nom de la société"}}, "settings": {"settings": "Paramètres", "settingsInfo": {"notifyByEmail": "Notifier par email", "notifyBySms": "Notifier par SMS", "appNotifications": "Notifications dans l'application", "allowNotifications": "Autoriser les notifications", "newBooking": "Nouvelles demandes de réservation", "newMessages": "Nouveaux messages", "newFollowers": "Nouveaux adeptes", "upcomingEvents": "Évènements à venir", "waitingBooking": "Demandes de réservation en attente", "notifications": "Notifications", "howNotify": "Comment souhaitez-vous être averti ?", "pushNotifications": "Notifications push", "deviceSettings": "Vous devez autoriser l'application à vous envoyer des notifications dans les paramètres de votre appareil.", "privacy": "Confidentialité", "changePassword": "Changer le mot de passe", "account": "<PERSON><PERSON><PERSON>", "deleteUserAccount": "<PERSON><PERSON>, vous pouvez supprimer les données personnelles de votre compte utilisateur conformément au RGPD.", "deleteAccount": "Supprimer mon compte"}}, "changePassword": {"changeIt": "Veuillez entrer votre mot de passe actuel ci-dessous pour le modifier.", "changePasswordForm": {"changePassword": "Changer le mot de passe", "newPassword": "nouveau mot de passe", "repeatPassword": "Répété le nouveau mot de passe", "resetPassword": "réinitialiser le mot de passe"}}, "tokenExpire": {"loggedIn": " <PERSON><PERSON>, on dirait que tu n'es pas connecté", "loveToHelp": "Nous serions ravis de vous aider, mais nous ne pouvons pas enregistrer le résultat si vous n'êtes pas connecté. Connectez-vous ou créez un compte pour ne pas le perdre."}, "verifyVirtualAct": {"successfully": "Virtual Act vérifié avec succès !", "failed": "Échec de la vérification de l'acte virtuel"}, "validation": {"countryRequired": "Le pays est requis", "stateRequired": "L'état est requis", "cityRequired": "La ville est obligatoire", "streetAddressRequired": "L'adresse postale est requise", "zipCodeRequired": "Le code postal est requis"}, "notification": {"notifications": "Notifications", "noNotification": "Aucune notification disponible"}, "bookingDetail": {"download": "Télécharger"}, "createBooking": {"bookingRequest": "<PERSON><PERSON>e de réservation", "placementDetails": "Les détails du placement de l'événement ont-ils été déterminés pour cette demande de réservation ?", "eventTakePlace": "Où aura lieu l'événement ?", "myVenue": "Mon lieu", "commercialAddress": "Adresse privée/commerciale", "selectVenueProfile": "Sélectionnez le profil de site dans la liste de profils Mon site", "actualAddress": "Saisissez l'adresse réelle du logement privé/commercial qui n'est lié à aucun lieu dans le système.", "selectVenue": "Sélectionnez votre lieu", "setDateAndTime": "Configurer la date et l'heure", "clickCalendar": "Cliquez sur le calendrier pour sélectionner la date et la plage horaire", "purchaserDetails": "Accédez aux détails de l'acheteur", "sendToAct": "Envoyer pour agir", "goodsAndServices": "Déclaration des biens et services", "riderDetail": "Aller aux détails du coureur"}, "requestSummary": {"requestSummary": "<PERSON>é<PERSON><PERSON><PERSON><PERSON><PERSON>", "who": "OMS?", "toDefine": "Pour définir", "when": "Quand?", "load": "Charger", "start": "Commencer", "duration": "<PERSON><PERSON><PERSON>", "fee": "<PERSON><PERSON>", "contractDetails": "<PERSON>é<PERSON> du contrat", "keySummaryPoint": "Voici un résumé de certains des points clés du contrat", "soundAndLightening": "Son et éclairage", "nominal": "Rembourser les frais nominaux d'iGig.org", "specialProvision": "Aussi quelques dispositions spéciales"}, "savedSearch": {"saveThisSearch": "Enregistrer cette recherche", "apliedFilter": "Filtres appliqués", "dateFilter": "Conserver le filtre de date dynamique", "rollingForward": "Continuer à faire avancer le filtre de date", "notifyMeUpdate": "M'avertir des mises à jour", "receiveMails": "Vous recevrez des e-mails lorsque les résultats de cette recherche enregistrée seront mis à jour.", "viewEvent": "Voir l'événement", "searchTerm": "Vos termes de recherche et filtres seront enregistrés afin que vous puissiez y revenir plus tard.", "searchName": "Rechercher un nom", "filterRolling": "Continuer à faire avancer le filtre de date", "theSearch": "La recherche", "alreadyExist": "existe déjà.", "replace": "Vous souhaitez remplacer l'existant ?", "saveAs": "enregistrer sous"}, "savedSearchSuccess": {"savedSuccessfully": "La recherche a été enregistrée avec succès !", "fineThisSaved": "Vous pouvez retrouver cette recherche enregistrée sur le", "favoritespage": "<PERSON> <PERSON><PERSON>"}, "searchFilter": {"saveSearch": "Enregistrer la recherche", "showList": "Aff<PERSON>r la liste", "showMap": "Afficher la carte", "location": "Emplacement", "entertainmentType": "Divertissement (type)", "musicGenre": "Musique (genre)", "rating": "Notation", "4AndMore": "4 et plus", "3AndMore": "3 et plus", "2AndMore": "2 et plus", "1AndMore": "1 et plus", "withoutRating": "Sans note", "today": "AUJOURD'HUI", "thisWeek": "CETTE SEMAINE", "thisMonth": "CE MOIS", "custom": "COUTUME"}, "searchHeader": {"easyBooking": "Réservation facile", "results": "résultats", "instaBook": "InstaBook", "search": "Recherche", "highestRateFirst": "Le taux le plus élevé en premier", "cheapestRateFirst": "<PERSON><PERSON><PERSON> le moins cher en premier", "mostPopularFirst": "Le plus populaire en premier", "lessPopularFirst": "Moins populaire d’abord", "event": "Événement", "act": "Acte", "venue": "<PERSON><PERSON>"}, "search": {"title": "Page de recherche", "noSearch": "<PERSON><PERSON><PERSON> donn<PERSON> trouvée", "weAreSorry": "Nous sommes désolés", "specifiedFilter": "Rien trouvé actuellement pour les filtres spécifiés", "saveThisSearch": "Enregistrer cette recherche", "searchTerm": "Vos termes de recherche et filtres seront enregistrés afin que vous puissiez y revenir plus tard.", "map": "<PERSON><PERSON>", "list": "Liste"}, "autoLocation": {"addLocation": "la ville"}, "sidebar": {"dashboard": "Tableau de bord", "eventCalendar": "Calendrier des événements", "profiles": "Profiles", "messages": "Messages", "calendar": "<PERSON><PERSON><PERSON>", "help": "Aide", "logOut": "Déconnexion", "hide": "<PERSON><PERSON>"}, "title": {"seeAll": "Voir tout"}, "bookingVenue": {"locationAddressLine": "Ligne d'adresse de localisation", "selected": "<PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "venueWorkingHour": {"workingHour": "Horaires de travail", "enableWorkingHour": "Activer les heures de travail", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "copyToAll": "Copier à tous"}, "profileSuccess": {"success": "Succès!", "publish": "Publier", "profileCreated": "Votre profil a été créé", "virtualVenueCreated": "Un lieu virtuel a été créé", "virtualActCreated": "L'Acte Virtuel a été créé"}, "dropdown": {"selectProfile": "Sélectionnez un profil", "weekly": "Hebdomadaire", "selectLanguage": "Sélectionnez la langue", "selectRole": "Sélectionnez un rôle", "selectCurrency": "Sélectionnez la devise", "selectPer": "Sélectionnez par", "selectDistributionType": "Sélectionnez le type de distribution", "selectLocation": "Sélectionnez un emplacement"}, "venue": {"venueInformation": "Informations sur le lieu", "venueMedia": "<PERSON><PERSON><PERSON><PERSON> du lieu", "venueLocation": "Ajouter l'emplacement du site", "venuePayment": "Prix ​​et paiement du lieu", "location": "Emplacement du lieu", "editInfo": "Modifier les informations du lieu"}, "favourites": {"acts": "Actes", "events": "Événements", "venues": "<PERSON><PERSON>", "saveSearchs": "Recherches enregistrées", "favourites": "<PERSON><PERSON><PERSON>"}}