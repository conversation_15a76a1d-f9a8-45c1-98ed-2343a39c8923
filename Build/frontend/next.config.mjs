import config from "./src/config/index.js";
const { imageConfig } = config;

import createNextIntlPlugin from "next-intl/plugin";
const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
  
  images: {
    remotePatterns: imageConfig.domains,
    domains: imageConfig.domains.map(d => d.hostname),
  },
  
  experimental: {
    serverComponentsExternalPackages: ['undici'],
  },
  
  webpack(config, { isServer }) {
    // 配置externals来避免打包undici
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        "undici": false,
      };
    }
    
    // 将undici设为external
    config.externals = config.externals || [];
    config.externals.push({
      'undici': 'commonjs undici'
    });
    
    config.module.rules.push({
      test: /\.svg$/i,
      use: [
        {
          loader: "@svgr/webpack",
          options: {
            icon: true,
          },
        },
      ],
    });
    
    return config;
  },
};

export default withNextIntl(nextConfig);
