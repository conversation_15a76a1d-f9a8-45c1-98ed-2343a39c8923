{"name": "stage-minder", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "node scripts/start.js", "lint": "next lint", "lint-fix": "next lint --fix", "prepare": "husky install || true", "prettier": "prettier --write ."}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --fix", "eslint", "prettier --write"], "*.json": ["prettier --write"]}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fullcalendar/core": "^6.1.11", "@fullcalendar/daygrid": "^6.1.11", "@fullcalendar/interaction": "^6.1.11", "@fullcalendar/list": "^6.1.11", "@fullcalendar/react": "^6.1.11", "@fullcalendar/rrule": "^6.1.15", "@fullcalendar/timegrid": "^6.1.11", "@hookform/resolvers": "^3.3.4", "@mui/icons-material": "^5.15.15", "@mui/material": "^5.15.15", "@mui/x-date-pickers": "^7.4.0", "@mui/x-date-pickers-pro": "^7.6.1", "@react-google-maps/api": "^2.20.6", "@reduxjs/toolkit": "^2.2.3", "@stomp/stompjs": "^7.0.0", "@svgr/webpack": "^8.1.0", "@uiw/react-color-circle": "^2.3.0", "autosuggest-highlight": "^3.3.4", "axios": "^1.6.8", "classnames": "^2.5.1", "dayjs": "^1.11.11", "firebase": "^10.10.0", "html2canvas": "^1.4.1", "i": "^0.3.7", "jspdf": "^2.5.2", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "moment": "^2.30.1", "next": "14.1.4", "next-intl": "^3.13.0", "notistack": "^3.0.1", "npm": "^10.5.1", "query-string": "^9.0.0", "react": "^18", "react-datepicker": "^6.9.0", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-hook-form": "^7.51.2", "react-hotkeys-hook": "^4.5.0", "react-imask": "^7.6.0", "react-leaflet": "^4.2.1", "react-otp-input": "^3.1.1", "react-photo-view": "^1.2.7", "react-redux": "^9.1.0", "react-slick": "^0.30.2", "rrule": "^2.8.1", "sharp": "^0.33.4", "slick-carousel": "^1.8.1", "socket.io-client": "^4.7.5", "sockjs-client": "^1.6.1", "tailwind-merge": "^2.2.2", "use-debounce": "^10.0.0", "yup": "^1.4.0"}, "devDependencies": {"autoprefixer": "^10.0.1", "eslint": "^8.57.0", "eslint-config-next": "14.2.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^8.0.0", "lint-staged": "^15.2.7", "postcss": "^8", "prettier": "^3.3.2", "tailwindcss": "^3.3.0"}}