"use client";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import BookingDetailsUi from "@/ui/booking-details/booking-details.ui";
import { Close } from "@mui/icons-material";
import { Box, IconButton } from "@mui/material";
import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import useCaptureImage from "@/hooks/usePdfPrint";

import { useRouter, useSearchParams } from "next/navigation";
import {
  modifiedGoodsServices,
  previewContract as previewContractDispatch,
  previewContractWithMessageId,
} from "@/store/slice/booking/booking.slice";
import ContractFooter from "@/ui/contract-footer/contract-footer.ui";
import PaymentStatusModal from "@/component/paymentStatusModal/paymentStatusModal.component";

const BookingDetails = () => {
  //const t = useTranslations("CreateProfiles");
  const p = useTranslations("createBooking");
  const searchParams = useSearchParams();
  const { currentBookingStatus } = useSelector((state) => state.booking);
  const contractId = searchParams.get("contract-id") || currentBookingStatus?.contractId;
  const messageId = searchParams.get("message-id");
  const dispatch = useDispatch();
  // eslint-disable-next-line no-unused-vars
  const [loading, setLoading] = useState(false);
  const { previewContract } = useSelector((state) => state.booking);
  const [modifiedGoodsServicesData, setModifiedGoodsServicesData] = useState();
  const lang = useLocale();
  const canGoBack = typeof window !== "undefined" && window.history.length > 1;
  const router = useRouter();
  const [contractType, setContractType] = useState("");

  useEffect(() => {
    if (contractId) {
      dispatch(modifiedGoodsServices(contractId))
        .unwrap()
        .then((response) => {
          setModifiedGoodsServicesData(response.data.data);
        });
    }
    setContractType(`${previewContract?.bookingParty}${previewContract?.otherParty}`);
  }, []);

  //dispatch(previewContract(currentBookingStatus?.contractId));
  const { downloadPDF } = useCaptureImage({
    allowTaint: true,
    useCORS: true,
    backgroundColor: "rgba(0,0,0,0)",
    removeContainer: true,
  });

  const handleCapture = () => {
    const cardElement = document.querySelector("#bookingPdf");
    downloadPDF(cardElement);
  };

  // Handle the close button click based on the payment context
  const handleClose = () => {
    // Check if the user came from a Stripe payment
    const paymentStatus = searchParams.get("payment_status");
    const sessionId = localStorage.getItem("sessionId");

    // If we detect signs that the user came from Stripe
    if (paymentStatus || sessionId) {
      // Clear any payment related session data
      localStorage.removeItem("sessionId");

      // Navigate directly to contracts page instead of going back
      router.push(`/${lang}/contracts/contracts-by-others`);
    } else {
      // Normal back behavior for other cases
      router.back();
    }
  };

  useEffect(() => {
    if (messageId) {
      setLoading(true);
      dispatch(previewContractWithMessageId({ messageId }))
        .unwrap()
        .then(() => {})
        .finally(() => {
          setLoading(false);
        });
    } else {
      setLoading(true);
      if (contractId) {
        dispatch(previewContractDispatch(contractId))
          .unwrap()
          .then(() => {})
          .finally(() => {
            setLoading(false);
          });
      }
    }
  }, [contractId, messageId]);

  return (
    <Box>
      <ProfileNavbar
        pageNumber="7/7"
        tag={p("bookingRequest")}
        className=" h-[64px] !bg-[--bg-color]"
        isSaveUnPublished={true}
      >
        {" "}
        {canGoBack ? (
          <IconButton onClick={handleClose} className="lg:pr-20">
            <Close className="!text-[--text-color] !text-base" />
          </IconButton>
        ) : previewContract?.contractState !== "CREATED" ? (
          <IconButton
            onClick={() => router.push(`/${lang}/contracts/contracts-by-others`)}
            className="lg:pr-20"
          >
            <Close className="!text-[--text-color] !text-base" />
          </IconButton>
        ) : null}
      </ProfileNavbar>
      <Box
        className="lg:flex gap-x-12 lg:!pl-10 md:!pl-10 !pl-5 !pr-5 lg:!pr-8 !py-24"
        id="bookingPdf"
      >
        <Box className="lg:w-[60%] w-full">
          <BookingDetailsUi
            previewContract={previewContract}
            modifiedGoodsServicesData={modifiedGoodsServicesData}
            contractType={contractType}
          />
        </Box>
        <Box className="lg:w-[40%] w-full pt-8 lg:pt-0">
          {/* <BookingDetailsDescriptionUi previewContract={previewContract} /> */}
        </Box>
      </Box>
      <ContractFooter handleCapture={handleCapture} setLoading={setLoading} loading={loading} />

      {/* Add the PaymentStatusModal */}
      <PaymentStatusModal />
    </Box>
  );
};

export default BookingDetails;
