"use client";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { Box, useMediaQuery, useTheme } from "@mui/material";
import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useRef, useState } from "react";
import DistributionList from "@/assets/svg/act-type.svg/DistributionList.svg";
import { ProfileFooter } from "@/common/profile";
import RequestSummary from "@/common/request-summary/request-summary.common";
import ContractDetailsForm from "@/containers/contract-details/contract-details.form.containers";
import { useForm } from "react-hook-form";
import { goodsServices } from "@/store/slice/booking/booking.slice";
import { showSnackbar } from "@/utils/snackbar.utils";
import { useDispatch, useSelector } from "react-redux";
import { contractValidation } from "@/validation/act/act.validation";
import { yupResolver } from "@hookform/resolvers/yup";

const defaultValues = {
  performersRole: "",
  startDate: "",
  loadingTime: "",
  durationInHours: 0,
  flatRateCurrency: "",
  flatRateAmount: 0,
  flatRatePercentage: 0,
  doorGigEntryFee: 0,
  venueCapacity: 0,
  doorManagedBy: "ACT",
  doorGigPaidBy: "ACT",
  payableTo: "ACT",
  maximumPercentage: 0,
  guaranteedMaximum: 0,
  equipmentProvider: "PERFORMER",
  mealsProvidedByPurchaser: false,
  accommodationProvided: false,
  merchandiseSalesAllowed: false,
  performerMemberOfUnion: false,
  message:
    "We're looking for an incredible performer to elevate the atmosphere every Friday evening at our Venue place.",
  paymentType: "FLAT_RATE",
};

const ContractDetails = () => {
  //const t = useTranslations("CreateProfiles");
  const s = useTranslations("createBooking");
  const p = useTranslations("profileFooter");
  const lang = useLocale();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const { currentBookingStatus, previewContract } = useSelector((state) => state.booking);
  const [submitted, setSubmitted] = useState("");
  const [contractType, setContractType] = useState("");
  const previousOption = useRef("FLAT_RATE");
  const resolver = yupResolver(contractValidation(previewContract, contractType));
  const dispatch = useDispatch();
  const {
    handleSubmit,
    control,
    setValue,
    watch,
    clearErrors,
    formState: { errors },
  } = useForm({
    resolver,
    defaultValues: defaultValues,
    mode: "onChange",
  });

  useEffect(() => {
    setValue("performersRole", previewContract?.goodsAndServices?.performersRole);
    setValue(
      "startDate",
      previewContract?.goodsAndServices?.startDate ?? previewContract?.scheduleTime?.startDate,
    );
    setValue("loadingTime", previewContract?.goodsAndServices?.loadingTime);
    const startDate = new Date(previewContract?.scheduleTime?.startDate);
    const endDate = new Date(previewContract?.scheduleTime?.endDate);
    const diffInMilliseconds = endDate - startDate;
    setValue(
      "durationInHours",
      previewContract?.goodsAndServices?.durationInHours ?? diffInMilliseconds / (1000 * 60 * 60),
    );
    setValue("flatRateCurrency", previewContract?.goodsAndServices?.flatRateCurrency);
    setValue("flatRateAmount", previewContract?.goodsAndServices?.flatRateAmount);
    setValue("flatRatePercentage", previewContract?.goodsAndServices?.flatRatePercentage ?? 0);
    setValue("doorGigEntryFee", previewContract?.goodsAndServices?.doorGigEntryFee);
    setValue("venueCapacity", previewContract?.goodsAndServices?.venueCapacity);
    setValue("doorManagedBy", previewContract?.goodsAndServices?.doorManagedBy ?? "ACT");
    setValue("doorGigPaidBy", previewContract?.goodsAndServices?.doorGigPaidBy);
    setValue("payableTo", previewContract?.goodsAndServices?.payableTo ?? "ACT");
    setValue("maximumPercentage", previewContract?.goodsAndServices?.maximumPercentage);
    setValue("guaranteedMaximum", previewContract?.goodsAndServices?.guaranteedMaximum);
    setValue(
      "equipmentProvider",
      previewContract?.goodsAndServices?.equipmentProvider ?? "PERFORMER",
    );
    setValue(
      "mealsProvidedByPurchaser",
      previewContract?.goodsAndServices?.mealsProvidedByPurchaser,
    );
    setValue("accommodationProvided", previewContract?.goodsAndServices?.accommodationProvided);
    setValue("merchandiseSalesAllowed", previewContract?.goodsAndServices?.merchandiseSalesAllowed);
    setValue("performerMemberOfUnion", previewContract?.goodsAndServices?.performerMemberOfUnion);
    setValue("message", previewContract?.goodsAndServices?.message);
    setValue("paymentType", previewContract?.goodsAndServices?.paymentType ?? "FLAT_RATE");
    setValue(
      "exposureGigCurrency",
      previewContract?.goodsAndServices?.exposureGigCurrency ?? "CAD",
    );
    setContractType(`${previewContract?.bookingParty}${previewContract?.otherParty}`);
    previousOption.current = previewContract?.goodsAndServices?.paymentType ?? "FLAT_RATE";
  }, [previewContract]);

  const updateContracts = (data) => {
    const payload = { ...defaultValues, ...data };
    // if(currentBookingStatus.profileType === "ACT_PROFILE" || currentBookingStatus.profileType === "VIRTUAL_ACT_PROFILE") {
    dispatch(goodsServices({ data: payload, contractId: currentBookingStatus.contractId }))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setSubmitted("booking1");
          showSnackbar(response.data.message, "success");
        }
      })
      .catch((error) => {
        showSnackbar(error, "error");
      });

    //}
  };

  return (
    <Box className="grid grid-cols-2">
      <Box className="lg:col-span-1 col-span-2 h-screen overflow-auto">
        <ProfileNavbar
          pageNumber="4/7"
          tag={s("bookingRequest")}
          className=" h-[64px] lg:!max-w-[49%] !bg-[--bg-color]"
        >
          {" "}
          {/* <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
            {t("saveUnpublished")}
          </Typography>
          <Close className="!text-[--text-color] !text-base" /> */}
        </ProfileNavbar>
        <form onSubmit={handleSubmit(updateContracts)}>
          <Box className="lg:!pl-10 md:!pl-10 !pl-5 !pr-5 lg:!pr-8 !py-24">
            <CreateProfileTitle title={s("goodsAndServices")}>
              <DistributionList className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
            </CreateProfileTitle>
            <Box className="max-w-2xl mt-6">
              <ContractDetailsForm
                control={control}
                setValue={setValue}
                watch={watch}
                errors={errors}
                clearErrors={clearErrors}
                contractType={contractType}
                previousOption={previousOption}
              />
            </Box>
          </Box>
          <ProfileFooter
            buttonName={isSmallScreen ? p("Next") : s("riderDetail")}
            //disabled={changedFields}
            backurl={`/${lang}/venue-riders`}
            //type="button"
            backurlType="router-back"
            footerType={submitted}
            className="lg:!pl-28 lg:!px-2 lg:!max-w-[49%]"
          />
        </form>
      </Box>
      <Box className="h-screen hidden lg:block overflow-auto">
        <RequestSummary contractType={contractType} />
      </Box>
    </Box>
  );
};

export default ContractDetails;
