// @without_layout
// "use client";

// const Layout = ({ children }) => {
//   return <>{children}</>;
// };
// export default Layout;

// // ----

// @layout_with_sidebar

"use client";

import Sidebar from "@/common/sidebar/sidebar.common";
import { Box } from "@mui/material";

const Layout = ({ children }) => {
  return (
    <div>
      <Box className="!hidden lg:!block">
        <Sidebar />
      </Box>

      {/* <Box className="!inline relative lg:!static lg:!hidden">
        <MobileNavbar />
      </Box> */}
      <Box className="ml-[12px] lg:ml-[100px] ">{children}</Box>
    </div>
  );
};
export default Layout;
