"use client";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { Box, useMediaQuery, useTheme } from "@mui/material";
import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useRef, useState } from "react";
import DistributionList from "@/assets/svg/act-type.svg/DistributionList.svg";
import { ProfileFooter } from "@/common/profile";
import RequestSummary from "@/common/request-summary/request-summary.common";
import { useForm } from "react-hook-form";
import { negotiate } from "@/store/slice/booking/booking.slice";
import { showSnackbar } from "@/utils/snackbar.utils";
import { useDispatch, useSelector } from "react-redux";
import { contractValidation } from "@/validation/act/act.validation";
import { yupResolver } from "@hookform/resolvers/yup";
import { useRout<PERSON>, useSearchParams } from "next/navigation";
import NegotiateForm from "@/containers/negotiate/negotiate.form.containers";
import { previewContract as previewContractDispatch } from "@/store/slice/booking/booking.slice";

function isValidISODate(value) {
  // Check if the value is a Date instance
  if (value instanceof Date && !isNaN(value.getTime())) {
    return true;
  }
  // Check if the value is a string in ISO 8601 format
  if (typeof value === "string") {
    const iso8601Regex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z$/;
    return iso8601Regex.test(value) && !isNaN(Date.parse(value));
  }
  return false;
}

const defaultValues = {
  performersRole: "",
  startDate: "",
  loadingTime: "",
  durationInHours: 0,
  flatRateCurrency: "",
  flatRateAmount: 0,
  flatRatePercentage: 0,
  doorGigEntryFee: 0,
  venueCapacity: 0,
  doorManagedBy: "ACT",
  doorGigPaidBy: "ACT",
  payableTo: "ACT",
  maximumPercentage: 0,
  guaranteedMaximum: 0,
  equipmentProvider: "PERFORMER",
  mealsProvidedByPurchaser: false,
  accommodationProvided: false,
  merchandiseSalesAllowed: false,
  performerMemberOfUnion: false,
  message:
    "We're looking for an incredible performer to elevate the atmosphere every Friday evening at our Venue place.",
  paymentType: "FLAT_RATE",
};

const Negotiate = () => {
  //const t = useTranslations("CreateProfiles");
  const s = useTranslations("createBooking");
  const p = useTranslations("profileFooter");
  const lang = useLocale();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const { previewContract } = useSelector((state) => state.booking);
  const [contractType, setContractType] = useState("");
  const previousOption = useRef("FLAT_RATE");
  const resolver = yupResolver(contractValidation(previewContract, contractType));
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();
  const searchParams = useSearchParams();
  const router = useRouter();
  const contractId = searchParams.get("contract-id");

  //const oldContractId = currentBookingStatus?.contractId;
  useEffect(() => {
    if (contractId) {
      setLoading(true);
      dispatch(previewContractDispatch(contractId))
        .unwrap()
        .then(() => {
          setLoading(false);
        })
        .catch(() => {
          router.push(`/${lang}/dashboard`);
        });
    }
  }, [contractId]);
  const {
    handleSubmit,
    control,
    setValue,
    watch,
    clearErrors,
    formState: { errors },
  } = useForm({
    resolver,
    defaultValues: defaultValues,
    mode: "all",
  });

  // useEffect(() => {
  //   setValue("performersRole", previewContract?.goodsAndServices?.performersRole);
  //   setValue(
  //     "startDate",
  //     previewContract?.goodsAndServices?.startDate ?? previewContract?.scheduleTime?.startDate,
  //   );
  //   setValue("loadingTime", previewContract?.goodsAndServices?.loadingTime);
  //   const startDate = new Date(previewContract?.scheduleTime?.startDate);
  //   const endDate = new Date(previewContract?.scheduleTime?.endDate);
  //   const diffInMilliseconds = endDate - startDate;
  //   setValue(
  //     "durationInHours",
  //     previewContract?.goodsAndServices?.durationInHours ?? diffInMilliseconds / (1000 * 60 * 60),
  //   );
  //   setValue("flatRateCurrency", previewContract?.goodsAndServices?.flatRateCurrency);
  //   setValue("flatRateAmount", previewContract?.goodsAndServices?.flatRateAmount);
  //   setValue("flatRatePercentage", previewContract?.goodsAndServices?.flatRatePercentage);
  //   setValue("doorGigEntryFee", previewContract?.goodsAndServices?.doorGigEntryFee);
  //   setValue("venueCapacity", previewContract?.goodsAndServices?.venueCapacity);
  //   setValue("doorManagedBy", previewContract?.goodsAndServices?.doorManagedBy);
  //   setValue("doorGigPaidBy", previewContract?.goodsAndServices?.doorGigPaidBy);
  //   setValue("payableTo", previewContract?.goodsAndServices?.payableTo);
  //   setValue("maximumPercentage", previewContract?.goodsAndServices?.maximumPercentage);
  //   setValue("guaranteedMaximum", previewContract?.goodsAndServices?.guaranteedMaximum);
  //   setValue("equipmentProvider", previewContract?.goodsAndServices?.equipmentProvider);
  //   setValue(
  //     "mealsProvidedByPurchaser",
  //     previewContract?.goodsAndServices?.mealsProvidedByPurchaser,
  //   );
  //   setValue("accommodationProvided", previewContract?.goodsAndServices?.accommodationProvided);
  //   setValue("merchandiseSalesAllowed", previewContract?.goodsAndServices?.merchandiseSalesAllowed);
  //   setValue("performerMemberOfUnion", previewContract?.goodsAndServices?.performerMemberOfUnion);
  //   setValue("message", previewContract?.goodsAndServices?.message);
  //   setValue("paymentType", previewContract?.goodsAndServices?.paymentType);
  //   setValue("exposureGigCurrency", previewContract?.goodsAndServices?.exposureGigCurrency);
  // }, [previewContract]);

  useEffect(() => {
    setValue("performersRole", previewContract?.goodsAndServices?.performersRole);
    setValue(
      "startDate",
      previewContract?.goodsAndServices?.startDate ?? previewContract?.scheduleTime?.startDate,
    );
    setValue("loadingTime", previewContract?.goodsAndServices?.loadingTime);
    const startDate = new Date(previewContract?.scheduleTime?.startDate);
    const endDate = new Date(previewContract?.scheduleTime?.endDate);
    const diffInMilliseconds = endDate - startDate;
    setValue(
      "durationInHours",
      previewContract?.goodsAndServices?.durationInHours ?? diffInMilliseconds / (1000 * 60 * 60),
    );
    setValue("flatRateCurrency", previewContract?.goodsAndServices?.flatRateCurrency);
    setValue("flatRateAmount", previewContract?.goodsAndServices?.flatRateAmount);
    setValue("flatRatePercentage", previewContract?.goodsAndServices?.flatRatePercentage);
    setValue("doorGigEntryFee", previewContract?.goodsAndServices?.doorGigEntryFee);
    setValue("venueCapacity", previewContract?.goodsAndServices?.venueCapacity);
    setValue("doorManagedBy", previewContract?.goodsAndServices?.doorManagedBy ?? "ACT");
    setValue("doorGigPaidBy", previewContract?.goodsAndServices?.doorGigPaidBy);
    setValue("payableTo", previewContract?.goodsAndServices?.payableTo ?? "ACT");
    setValue("maximumPercentage", previewContract?.goodsAndServices?.maximumPercentage);
    setValue("guaranteedMaximum", previewContract?.goodsAndServices?.guaranteedMaximum);
    setValue(
      "equipmentProvider",
      previewContract?.goodsAndServices?.equipmentProvider ?? "PERFORMER",
    );
    setValue(
      "mealsProvidedByPurchaser",
      previewContract?.goodsAndServices?.mealsProvidedByPurchaser,
    );
    setValue("accommodationProvided", previewContract?.goodsAndServices?.accommodationProvided);
    setValue("merchandiseSalesAllowed", previewContract?.goodsAndServices?.merchandiseSalesAllowed);
    setValue("performerMemberOfUnion", previewContract?.goodsAndServices?.performerMemberOfUnion);
    setValue("message", previewContract?.goodsAndServices?.message || null);
    setValue("paymentType", previewContract?.goodsAndServices?.paymentType ?? "FLAT_RATE");
    setValue("exposureGigFee", previewContract?.goodsAndServices?.exposureGigFee ?? 0);
    setValue("exposureGigCurrency", previewContract?.goodsAndServices?.exposureGigCurrency);
    setContractType(`${previewContract?.bookingParty}${previewContract?.otherParty}`);
    previousOption.current = previewContract?.goodsAndServices?.paymentType ?? "FLAT_RATE";
  }, [previewContract]);

  const updateContracts = (data) => {
    const payload = {
      goodsAndServices: {
        ...defaultValues,
        ...data,
      },
    };

    // if(currentBookingStatus.profileType === "ACT_PROFILE" || currentBookingStatus.profileType === "VIRTUAL_ACT_PROFILE") {
    dispatch(negotiate({ data: payload, contractId: contractId }))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          //setSubmitted("booking1");
          router.push(`/${lang}/contracts/contracts-by-others`);
          showSnackbar(response.data.message, "success");
        }
      })
      .catch((error) => {
        showSnackbar(error, "error");
      });

    //}
  };

  const [changedFields, setChangedFields] = useState(false);
  // Store the initial values in a ref when the component mounts
  const initialValuesRef = useRef(previewContract);

  // Watch all fields for changes
  const watchedFields = watch();

  useEffect(() => {
    const initialValues = initialValuesRef.current;

    // Compare the current values to the initial values
    const changedField =
      initialValues?.goodsAndServices &&
      Object.keys(watchedFields).some((key) => {
        const initialValue = initialValues.goodsAndServices[key];
        const currentValue = watchedFields[key];

        // Normalize and compare dates
        if (initialValue && currentValue) {
          const isDate = isValidISODate(initialValue) && isValidISODate(currentValue);

          if (isDate) {
            const normalizedInitialValue =
              initialValue instanceof Date
                ? initialValue.toISOString()
                : new Date(initialValue).toISOString();

            const normalizedCurrentValue =
              currentValue instanceof Date
                ? currentValue.toISOString()
                : new Date(currentValue).toISOString();

            return (
              normalizedInitialValue !== normalizedCurrentValue &&
              !(normalizedInitialValue === "" && normalizedCurrentValue === undefined)
            );
          }
        }
        // console.log("key", key,  typeof initialValue);
        // console.log("key", key, typeof currentValue);
        return initialValue != currentValue && !(initialValue === "" && currentValue === undefined);
        // Default comparison
        // if (key == "durationInHours") {
        //   console.log("durationInHours");
        //   const normalizedInitialValue = Number(initialValue);
        //   const normalizedCurrentValue = Number(currentValue);

        //   console.log("Normalized Initial Value:", normalizedInitialValue);
        //   console.log("Normalized Current Value:", normalizedCurrentValue);
        //   console.log("status",normalizedInitialValue === normalizedCurrentValue)
        //   // Compare normalized values
        //   return normalizedInitialValue !== normalizedCurrentValue;
        // } else {
        //   return initialValue != currentValue && !(initialValue === "" && currentValue === undefined);
        // }
      });
    //console.log("Changed Field:", changedField);
    if (changedField) {
      setChangedFields(true);
    } else {
      setChangedFields(false);
    }
  }, [watchedFields]);

  if (loading) {
    return null;
  }
  return (
    <Box className="grid grid-cols-2">
      <Box className="lg:col-span-1 col-span-2 h-screen overflow-auto">
        <ProfileNavbar
          pageNumber="4/7"
          tag={s("bookingRequest")}
          className=" h-[64px] lg:!max-w-[49%] !bg-[--bg-color]"
        >
          {" "}
          {/* <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
            {t("saveUnpublished")}
          </Typography> */}
          {/* <Close className="!text-[--text-color] !text-base" /> */}
        </ProfileNavbar>
        <form onSubmit={handleSubmit(updateContracts)}>
          <Box className="lg:!pl-10 md:!pl-10 !pl-5 !pr-5 lg:!pr-8 !py-24">
            <CreateProfileTitle title={s("goodsAndServices")}>
              <DistributionList className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
            </CreateProfileTitle>
            <Box className="max-w-2xl mt-6">
              <NegotiateForm
                control={control}
                setValue={setValue}
                watch={watch}
                errors={errors}
                clearErrors={clearErrors}
                contractType={contractType}
              />
            </Box>
          </Box>
          <ProfileFooter
            buttonName={isSmallScreen ? p("Next") : "Review and Submit"}
            disabled={!changedFields}
            backurl={`/${lang}/venue-riders`}
            //type="button"
            backurlType="router-back"
            className="lg:!pl-28 lg:!px-2 lg:!max-w-[54%]"

            //footerType={submitted}
          />
        </form>
      </Box>
      <Box className="h-screen hidden lg:block overflow-auto">
        <RequestSummary contractType={contractType} />
      </Box>
    </Box>
  );
};

export default Negotiate;
