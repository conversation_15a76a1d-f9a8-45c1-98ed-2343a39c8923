"use client";
import { ProfileFooter } from "@/common/profile";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import RequestSummary from "@/common/request-summary/request-summary.common";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { Box, useMediaQuery, useTheme } from "@mui/material";
import { useLocale } from "next-intl";
import React, { useEffect, useState } from "react";
import Rider from "@/assets/svg/act-type.svg/Rider.svg";
import VenueRidersForm from "@/containers/venue-rider-booking/venue-rider-booking.containers";
import { useDispatch, useSelector } from "react-redux";
import { useForm } from "react-hook-form";
import { validateBookingRiders } from "@/validation/act/act.validation";
import { yupResolver } from "@hookform/resolvers/yup";
import { actRiderChanges, venueRiderChanges } from "@/store/slice/booking/booking.slice";
import { showSnackbar } from "@/utils/snackbar.utils";

const VenueRiders = () => {
  //const t = useTranslations("CreateProfiles");
  const lang = useLocale();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const dispatch = useDispatch();
  //const [loading, setLoading] = useState(true);
  const { currentBookingStatus, previewContract } = useSelector((state) => state.booking);
  const [submitted, setSubmitted] = useState("");
  const [riderProfileType, setRiderProfileType] = useState("Venue");

  useEffect(() => {
    // if(currentBookingStatus.profileType === "VENUE_PROFILE" || currentBookingStatus.profileType === "VIRTUAL_VENUE_PROFILE") {
    //   setRiderProfileType("Venue");
    // }

    // if(currentBookingStatus.profileType === "ACT_PROFILE" || currentBookingStatus.profileType === "VIRTUAL_ACT_PROFILE") {
    //   setRiderProfileType("Act");
    // }

    if (currentBookingStatus.previousRoute === "rider-notes") {
      if (
        currentBookingStatus.profileType === "VENUE_PROFILE" ||
        currentBookingStatus.profileType === "VIRTUAL_VENUE_PROFILE"
      ) {
        setRiderProfileType("Venue");
      }

      if (
        currentBookingStatus.profileType === "ACT_PROFILE" ||
        currentBookingStatus.profileType === "VIRTUAL_ACT_PROFILE"
      ) {
        //setRiderProfileType("Act");
      }
    } else {
      if (
        currentBookingStatus.otherProfileType === "VENUE_PROFILE" ||
        currentBookingStatus.otherProfileType === "VIRTUAL_VENUE_PROFILE"
      ) {
        setRiderProfileType("Venue");
      }

      if (
        currentBookingStatus.otherProfileType === "ACT_PROFILE" ||
        currentBookingStatus.otherProfileType === "VIRTUAL_ACT_PROFILE"
      ) {
        //setRiderProfileType("Act");
      }
    }

    // if (
    //   currentBookingStatus.contactType === "userBookAct" &&
    //   (currentBookingStatus.profileType === "ACT_PROFILE" ||
    //     currentBookingStatus.profileType === "VIRTUAL_ACT_PROFILE")
    // ) {
    //   setRiderProfileType("Act");
    // }

    // if (
    //   currentBookingStatus.contactType === "userBookVenue" &&
    //   (currentBookingStatus.profileType === "VENUE_PROFILE" ||
    //     currentBookingStatus.profileType === "VIRTUAL_VENUE_PROFILE")
    // ) {
    //   setRiderProfileType("Venue");
    // }

    // if (
    //   currentBookingStatus.contactType === "actBookVenue" &&
    //   !currentBookingStatus.otherProfileId
    // ) {
    //   setRiderProfileType("Act");
    // }

    // if (
    //   currentBookingStatus.contactType === "actBookVenue" &&
    //   currentBookingStatus.otherProfileId
    // ) {
    //   setRiderProfileType("Venue");
    // }

    // if (
    //   currentBookingStatus.contactType === "venueBookAct" &&
    //   !currentBookingStatus.otherProfileId
    // ) {
    //   setRiderProfileType("Venue");
    // }

    // if (
    //   currentBookingStatus.contactType === "venueBookAct" &&
    //   currentBookingStatus.otherProfileId
    // ) {
    //   setRiderProfileType("Act");
    // }
  }, []);

  const resolver = yupResolver(validateBookingRiders(riderProfileType));

  const getDefaultValues = () => {
    if (riderProfileType === "Act") {
      return {
        //riderUrl: "",
        riderConditions: "",
      };
    } else {
      return {
        parkingConditions: "",
        allowedVisualEffectsList: [],
        //riderUrl: "",
      };
    }
  };

  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors },
  } = useForm({
    resolver,
    defaultValues: getDefaultValues(),
    mode: "all",
  });

  useEffect(() => {
    if (riderProfileType === "Act") {
      //setValue("riderUrl", previewContract?.actRiderChanges?.riderUrl ?? "");
      setValue("riderConditions", previewContract?.actRiderChanges?.riderConditions ?? "");
    } else {
      setValue("parkingConditions", previewContract?.venueRiderChanges?.parkingConditions ?? "");
      setValue(
        "allowedVisualEffectsList",
        previewContract?.venueRiderChanges?.allowedVisualEffectsList ?? [],
      );
      //setValue("riderUrl", previewContract?.venueRiderChanges?.riderUrl ?? "");
    }
  }, [riderProfileType]);

  const updateRiderBooking = (data) => {
    if (riderProfileType === "Act") {
      dispatch(actRiderChanges({ data: data, contractId: currentBookingStatus.contractId }))
        .unwrap()
        .then((response) => {
          if (response.status === 200) {
            setSubmitted("booking1");
            //showSnackbar(response.data.message, "success");
          }
        })
        .catch((error) => {
          showSnackbar(error, "error");
        });
    } else {
      dispatch(venueRiderChanges({ data: data, contractId: currentBookingStatus.contractId }))
        .unwrap()
        .then((response) => {
          if (response.status === 200) {
            setSubmitted("booking1");
            //showSnackbar(response.data.message, "success");
          }
        })
        .catch((error) => {
          showSnackbar(error, "error");
        });
    }
  };
  // if (!loading) {
  //   return <Loader />;
  // }

  return (
    <Box className="grid grid-cols-2">
      <Box className="lg:col-span-1 col-span-2 h-screen overflow-auto">
        <ProfileNavbar
          //pageNumber={`${currentBookingStatus.currentPageCount}/7`}
          pageNumber=""
          tag="Booking request"
          className=" h-[64px] lg:max-w-[49%] !bg-[--bg-color]"
        >
          {" "}
          {/* <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
            {t("saveUnpublished")}
          </Typography>
          <Close className="!text-[--text-color] !text-base" /> */}
        </ProfileNavbar>
        <form onSubmit={handleSubmit(updateRiderBooking)}>
          <Box className="lg:!pl-10 md:!pl-10 !pl-5 !pr-5 lg:!pr-8 !py-24">
            <CreateProfileTitle title={riderProfileType === "Act" ? "Act Rider" : "Venue Rider"}>
              <Rider className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
            </CreateProfileTitle>
            <Box className="max-w-2xl mt-6">
              <VenueRidersForm
                //riderList={riderList}
                //setLoading={setLoading}
                control={control}
                errors={errors}
                riderProfileType={riderProfileType}
                currentBookingStatus={currentBookingStatus}
              />
            </Box>
          </Box>

          <ProfileFooter
            buttonName={isSmallScreen ? "Next" : "Continue"}
            // disabled={selectedItem === null ? true : false}
            backurl={`/${lang}/act-riders`}
            //type="button"
            //onSubmit={handleSubmit(updateRiderBooking, errorRiderBooking)}
            backurlType="router-back"
            footerType={submitted}
            className="lg:!pl-28 lg:!px-2  lg:!max-w-[49%]"
          />
        </form>
      </Box>
      <Box className="h-screen hidden lg:block overflow-auto">
        <RequestSummary />
      </Box>
    </Box>
  );
};

export default VenueRiders;
