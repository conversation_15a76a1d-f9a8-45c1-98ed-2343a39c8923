"use client";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import RequestSummary from "@/common/request-summary/request-summary.common";
import { Box, useMediaQuery, useTheme } from "@mui/material";
import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import VenueProfileSvg from "@/assets/svg/VenueProfile.svg";
import ActProfile from "@/assets/svg/ActProfile.svg";

import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
//import BookingVenue from "@/common/booking-venue/booking-venue.common";
import { ProfileFooter } from "@/common/profile";
import { useDispatch, useSelector } from "react-redux";
import { Loader } from "@/component";
import { getSearchSelectData } from "@/store/slice/common/search.slice";
import ProfileCard from "@/common/profile-card/profile-card.common";
import {
  selectProvideInfo,
  selectVenueInfo,
  setBookingData,
} from "@/store/slice/booking/booking.slice";
import { useRouter } from "next/navigation";

const SelectVenue = () => {
  //const t = useTranslations("CreateProfiles");
  const p = useTranslations("profileFooter");
  const s = useTranslations("createBooking");
  //eslint-disable-next-line
  const [selectedItem, setSelectedItem] = useState();
  const lang = useLocale();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [searchData, setSearchData] = useState([]);
  const dispatch = useDispatch();
  const { currentBookingStatus } = useSelector((state) => state.booking);

  useEffect(() => {
    dispatch(
      getSearchSelectData({
        filter: {},
        profileType:
          currentBookingStatus?.otherProfileType === "ACT_PROFILE" ||
          currentBookingStatus?.otherProfileType === "VIRTUAL_ACT_PROFILE"
            ? "VENUE_PROFILE"
            : "ACT_PROFILE",
      }),
    )
      .unwrap()
      .then((response) => {
        if (response?.data?.content?.length === 0) {
          router.push(`/${lang}/contract-details`);
        }
        setSearchData(response?.data);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
    setSelectedItem(currentBookingStatus?.profileId);
  }, []);

  const profileTypeselecthandler = (profile) => {
    if (profile?.profileType === "ACT_PROFILE" || profile?.profileType === "VIRTUAL_ACT_PROFILE") {
      profile &&
        dispatch(
          selectProvideInfo({
            contractId: currentBookingStatus?.contractId,
            profileId: profile?.profileId,
          }),
        )
          .unwrap()
          .then((res) => {
            if (res.status === 208) {
              const continueBooking = window.confirm(
                "The selected time overlaps with another scheduled event. You can continue or cancel the contract now.",
              );
              if (!continueBooking) {
                router.push(`/${lang}/search?profileType=ACT_PROFILE`);
                return;
              }
            }
            dispatch(
              setBookingData({
                ...currentBookingStatus,
                profileId: profile?.profileId,
                profileType: profile?.profileType,
              }),
            );
          })
          .catch(() => {});
    } else {
      profile &&
        dispatch(
          selectVenueInfo({
            contractId: currentBookingStatus?.contractId,
            profileId: profile?.profileId,
          }),
        )
          .unwrap()
          .then((res) => {
            if (res.status === 208) {
              const continueBooking = window.confirm(
                "The selected time overlaps with another scheduled event. You can continue or cancel the contract now.",
              );
              if (!continueBooking) {
                router.push(`/${lang}/search?profileType=ACT_PROFILE`);
                return;
              }
            }
            dispatch(
              setBookingData({
                ...currentBookingStatus,
                profileId: profile?.profileId,
                profileType: profile?.profileType,
              }),
            );
          })
          .catch(() => {});
    }
  };

  if (loading) {
    return <Loader />;
  }
  return (
    <Box className="grid grid-cols-2">
      <Box className="lg:col-span-1 col-span-2 h-screen overflow-auto">
        <ProfileNavbar
          pageNumber="1/7"
          tag={s("bookingRequest")}
          className=" h-[64px] lg:max-w-[49%] !bg-[--bg-color]"
        >
          {" "}
          {/* <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
            {t("saveUnpublished")}
          </Typography>
          <Close className="!text-[--text-color] !text-base" /> */}
        </ProfileNavbar>
        <Box className="lg:!pl-6 md:!pl-10 !pl-4 !py-24">
          <CreateProfileTitle
            title={
              currentBookingStatus?.otherProfileType === "ACT_PROFILE" ||
              currentBookingStatus?.otherProfileType === "VIRTUAL_ACT_PROFILE"
                ? s("selectVenue")
                : "Select Your Act"
            }
          >
            {(currentBookingStatus?.otherProfileType === "ACT_PROFILE" ||
              currentBookingStatus?.otherProfileType === "VIRTUAL_ACT_PROFILE") && (
              <VenueProfileSvg className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
            )}
            {(currentBookingStatus?.otherProfileType === "VENUE_PROFILE" ||
              currentBookingStatus?.otherProfileType === "VIRTUAL_VENUE_PROFILE") && (
              <ActProfile className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
            )}
          </CreateProfileTitle>
          <Box className="!mt-4 lg:!pb-0 !pr-4 lg:!pr-0">
            {/* <BookingVenue
              profiles={profiles}
              selectedItem={selectedItem}
              onItemClick={handleListItemClick}
            /> */}
            <ProfileCard
              profiles={searchData}
              selectedItem={selectedItem}
              type="select-profile"
              profileTypeselecthandler={profileTypeselecthandler}
              setSelectedItem={setSelectedItem}
            />
          </Box>
        </Box>
        <ProfileFooter
          buttonName={isSmallScreen ? p("Next") : "Go to contract details"}
          disabled={!selectedItem ? true : false}
          backurl={`/${lang}/select-date-and-time`}
          backurlType="router-back"
          type="button"
          footerType="booking"
          className="lg:!pl-28 lg:!px-2  lg:!max-w-[49%]"
        />
      </Box>
      <Box className="h-screen !hidden lg:!block overflow-auto">
        <RequestSummary />
      </Box>
    </Box>
  );
};

export default SelectVenue;
