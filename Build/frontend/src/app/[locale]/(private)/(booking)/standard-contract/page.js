"use client";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { Box, Typography, useMediaQuery, useTheme } from "@mui/material";
import { useLocale } from "next-intl";
import React, { useEffect } from "react";
import Rider from "@/assets/svg/act-type.svg/Rider.svg";
import { ProfileFooter } from "@/common/profile";
import RequestSummary from "@/common/request-summary/request-summary.common";
import { getFinePrint } from "@/store/slice/booking/booking.slice";
import { Loader } from "@/component";
import { useDispatch } from "react-redux";

const StandardContract = () => {
  //const t = useTranslations("CreateProfiles");
  const lang = useLocale();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const [loading, setLoading] = React.useState(true);
  const dispatch = useDispatch();
  const [stnadardContractData, setStandardContractData] = React.useState({
    title: "",
    subTitle: "",
    footer: "",
    content: "",
  });
  useEffect(() => {
    dispatch(getFinePrint())
      .unwrap()
      .then((data) => {
        setStandardContractData(data?.data?.data);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  }, []);
  if (loading) {
    return <Loader />;
  }
  return (
    <Box className="grid grid-cols-2">
      <Box className="lg:col-span-1 col-span-2 h-screen overflow-auto">
        <ProfileNavbar
          pageNumber="7/7"
          tag="Booking request"
          className=" h-[64px] lg:max-w-[49%] !bg-[--bg-color]"
        >
          {" "}
          {/* <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
            {t("saveUnpublished")}
          </Typography>
          <Close className="!text-[--text-color] !text-base" /> */}
        </ProfileNavbar>
        <Box className="lg:!pl-10 md:!pl-10 !pl-5 !pr-5 lg:!pr-8 !py-24">
          <CreateProfileTitle title={stnadardContractData.title}>
            <Rider className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
          </CreateProfileTitle>
          <Box className="max-w-2xl mt-6">
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular mb-6">
              {stnadardContractData.subTitle}
            </Typography>
            <Typography className="text-sm text-[--text-color] CraftworkGrtroteskRegular">
              {stnadardContractData.content}
              {stnadardContractData.footer}
            </Typography>
          </Box>
        </Box>
        <ProfileFooter
          buttonName={isSmallScreen ? "Next" : "Review & submit"}
          // disabled={selectedItem === null ? true : false}
          backurl={`/${lang}/`}
          //type="button"
          backurlType="router-back"
          footerType="booking"
          className="lg:!pl-28 lg:!px-2  lg:!max-w-[49%]"
        />
      </Box>
      <Box className="h-screen hidden lg:block overflow-auto">
        <RequestSummary />
        {/* <PdfView /> */}
      </Box>
    </Box>
  );
};

export default StandardContract;
