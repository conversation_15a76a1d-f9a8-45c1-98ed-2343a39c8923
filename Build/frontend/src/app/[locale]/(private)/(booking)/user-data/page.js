"use client";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { Box, Typography } from "@mui/material";
import React from "react";
import CalenderIcon from "@/assets/svg/CalenderIcon.svg";
import RequestSummary from "@/common/request-summary/request-summary.common";
import AccountInformationForm from "@/containers/account-information-form/account-information-form.containers";
import WarningSvg from "@/assets/svg/Warning.svg";

const UserData = () => {
  //const t = useTranslations("CreateProfiles");
  return (
    <Box className="grid grid-cols-2">
      <Box className="lg:col-span-1 col-span-2 h-screen overflow-auto">
        <ProfileNavbar
          pageNumber="3/7"
          tag="Booking request"
          className=" h-[64px] lg:!max-w-[49%] !bg-[--bg-color]"
        >
          {" "}
          {/* <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
            {t("saveUnpublished")}
          </Typography>
          <Close className="!text-[--text-color] !text-base" /> */}
        </ProfileNavbar>
        <Box className="lg:!pl-10 md:!pl-10 !pl-6 !pr-6 !pt-28">
          <CreateProfileTitle title="User data">
            <CalenderIcon className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
          </CreateProfileTitle>
          <Box className="max-w-xl">
            <Box className="bg-[--footer-bg] w-full p-6 flex items-center gap-3">
              <WarningSvg className="text-2xl" />
              <Typography className="text-sm CraftworkGroteskGX text-[--text-color]">
                Any changes to your information will be updated in your profile.
              </Typography>
            </Box>
            <AccountInformationForm type="user-data" />
          </Box>
        </Box>
      </Box>
      <Box className="h-screen hidden lg:block overflow-auto">
        <RequestSummary />
      </Box>
    </Box>
  );
};

export default UserData;
