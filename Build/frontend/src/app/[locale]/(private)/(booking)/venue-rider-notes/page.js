"use client";
import { Box, useMediaQuery, useTheme } from "@mui/material";
import { useLocale } from "next-intl";
import React, { useEffect, useState } from "react";
import Rider from "@/assets/svg/act-type.svg/Rider.svg";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { ProfileFooter } from "@/common/profile";
import RequestSummary from "@/common/request-summary/request-summary.common";
import ActRidersForm from "@/containers/act-rider-form/act-rider-form.containers";
import { useForm } from "react-hook-form";
import { actRiderNotes, venueRiderNotes } from "@/store/slice/booking/booking.slice";
import { showSnackbar } from "@/utils/snackbar.utils";
import { useDispatch, useSelector } from "react-redux";
import { validateRiderNotes } from "@/validation/act/act.validation";
import { yupResolver } from "@hookform/resolvers/yup";
import { getRiderPdf } from "@/store/slice/act/act.slice";

const VenueActRiders = () => {
  //const t = useTranslations("CreateProfiles");
  const lang = useLocale();
  const theme = useTheme();
  const dispatch = useDispatch();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const { currentBookingStatus, previewContract } = useSelector((state) => state.booking);
  const [submitted, setSubmitted] = useState("");
  const [riderProfileType] = useState("Venue");

  useEffect(() => {
    const pID =
      currentBookingStatus?.contactType === "userBookVenue" ||
      currentBookingStatus?.contactType === "venueBookAct"
        ? currentBookingStatus?.otherProfileId
        : currentBookingStatus?.profileId;
    if (pID) {
      dispatch(getRiderPdf(pID))
        .unwrap()
        .then(() => {
          //(false);
        });
    }
  }, []);
  const { riderList } = useSelector((state) => state.act);

  const resolver = yupResolver(validateRiderNotes);
  const {
    handleSubmit,
    control,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    resolver,
    defaultValues: {
      riderAccepted: false,
      riderRejected: false,
      acceptedWithConditions: false,
      riderRejectionReason: "",
      acceptanceConditions: "",
      fee: "",
      riderUrl: "",
    },
    mode: "all",
  });

  const reason = [
    "Unavailable",
    "Doesn't fit profile",
    "Doesn't fit budget range",
    "Other (specify)",
  ];

  useEffect(() => {
    // if(currentBookingStatus.profileType === "VENUE_PROFILE" || currentBookingStatus.profileType === "VIRTUAL_VENUE_PROFILE") {
    //   setRiderProfileType("Venue");
    // }
    // if(currentBookingStatus.profileType === "ACT_PROFILE" || currentBookingStatus.profileType === "VIRTUAL_ACT_PROFILE") {
    //   setRiderProfileType("Act");
    // }
    // if (currentBookingStatus.previousRoute === "riders") {
    //   if (
    //     currentBookingStatus.profileType === "VENUE_PROFILE" ||
    //     currentBookingStatus.profileType === "VIRTUAL_VENUE_PROFILE"
    //   ) {
    //     setRiderProfileType("Venue");
    //   }
    //   if (
    //     currentBookingStatus.profileType === "ACT_PROFILE" ||
    //     currentBookingStatus.profileType === "VIRTUAL_ACT_PROFILE"
    //   ) {
    //     setRiderProfileType("Act");
    //   }
    // } else {
    //   if (
    //     currentBookingStatus.otherProfileType === "VENUE_PROFILE" ||
    //     currentBookingStatus.otherProfileType === "VIRTUAL_VENUE_PROFILE"
    //   ) {
    //     setRiderProfileType("Venue");
    //   }
    //   if (
    //     currentBookingStatus.otherProfileType === "ACT_PROFILE" ||
    //     currentBookingStatus.otherProfileType === "VIRTUAL_ACT_PROFILE"
    //   ) {
    //     setRiderProfileType("Act");
    //   }
    // }
  }, []);

  // useEffect(() => {
  //   console.log("riderProfileType", riderProfileType);
  //   if (riderProfileType === "Act") {
  //     setValue("acceptanceConditions", previewContract?.actRiderNotes?.acceptanceConditions ?? "");
  //     setValue("acceptedWithConditions", previewContract?.actRiderNotes?.acceptedWithConditions ?? "");
  //     setValue("riderAccepted", previewContract?.actRiderNotes?.riderAccepted ?? false);
  //     setValue("riderRejected", previewContract?.actRiderNotes?.riderRejected ?? false);
  //     setValue("riderRejectionReason", previewContract?.actRiderNotes?.riderRejectionReason ?? "");
  //   } else {
  //     setValue("acceptanceConditions", previewContract?.venueRiderNotes?.acceptanceConditions ?? "");
  //     setValue("acceptedWithConditions", previewContract?.venueRiderNotes?.acceptedWithConditions ?? "");
  //     setValue("riderAccepted", previewContract?.venueRiderNotes?.riderAccepted ?? false);
  //     setValue("riderRejected", previewContract?.venueRiderNotes?.riderRejected ?? false);
  //     setValue("riderRejectionReason", previewContract?.venueRiderNotes?.riderRejectionReason ?? "");
  //   }

  // }, [riderProfileType]);

  // useEffect(() => {
  //   dispatch(getRiderPdf(currentBookingStatus?.profileId))
  //     .unwrap()
  //     .then(() => {
  //       //(false);
  //     });
  // }, []);

  useEffect(() => {
    if (riderProfileType === "Act") {
      if (previewContract.actRiderNotes) {
        setValue("riderAccepted", previewContract.actRiderNotes.riderAccepted);
        setValue("riderRejected", previewContract.actRiderNotes.riderRejected);
        setValue("acceptedWithConditions", previewContract.actRiderNotes.acceptedWithConditions);
        setValue("acceptanceConditions", previewContract.actRiderNotes.acceptanceConditions);
        if (reason.includes(previewContract.actRiderNotes.riderRejectionReason)) {
          setValue("riderRejectionReason", previewContract.actRiderNotes.riderRejectionReason);
        } else {
          setValue("riderRejectionReason", "Other (specify)");
          setValue(
            "riderRejectionReasonMessage",
            previewContract.actRiderNotes.riderRejectionReason,
          );
        }

        if (previewContract.actRiderNotes.riderAccepted) {
          setValue("fee", "Accepted");
        }
        if (previewContract.actRiderNotes.riderRejected) {
          setValue("fee", "Decline or subject to negotiation");
        }
        if (previewContract.actRiderNotes.acceptedWithConditions) {
          setValue("fee", "Accepted with the following exceptions");
        }
      }
    } else {
      if (previewContract?.venueRiderNotes) {
        setValue("riderAccepted", previewContract.venueRiderNotes.riderAccepted);
        setValue("riderUrl", previewContract.venueRiderNotes.riderUrl);
        setValue("riderRejected", previewContract.venueRiderNotes.riderRejected);
        setValue("acceptedWithConditions", previewContract.venueRiderNotes.acceptedWithConditions);
        setValue("acceptanceConditions", previewContract.venueRiderNotes.acceptanceConditions);
        if (reason.includes(previewContract.actRiderNotes?.riderRejectionReason)) {
          setValue("riderRejectionReason", previewContract.actRiderNotes.riderRejectionReason);
        } else {
          setValue("riderRejectionReason", "Other (specify)");
          setValue(
            "riderRejectionReasonMessage",
            previewContract.actRiderNotes?.riderRejectionReason,
          );
        }
        if (previewContract.venueRiderNotes.riderAccepted) {
          setValue("fee", "Accepted");
        }
        if (previewContract.venueRiderNotes.riderRejected) {
          setValue("fee", "Decline or subject to negotiation");
        }
        if (previewContract.venueRiderNotes.acceptedWithConditions) {
          setValue("fee", "Accepted with the following exceptions");
        }
      }
    }
  }, [riderProfileType]);

  const updateRiderNotes = (data) => {
    //riderRejectionReasonMessage
    let rejectionMessage = data.riderRejected ? data.riderRejectionReason : "";
    if (data.riderRejectionReason === "Other (specify)") {
      rejectionMessage = data.riderRejectionReasonMessage;
    }
    const payload = {
      riderAccepted: data.riderAccepted,
      riderRejected: data.riderRejected,
      acceptedWithConditions: data.acceptedWithConditions,
      riderRejectionReason: rejectionMessage,
      acceptanceConditions: data.acceptedWithConditions ? data.acceptanceConditions : "",
      riderUrl: data.riderUrl,
    };

    if (riderProfileType === "Act") {
      dispatch(actRiderNotes({ data: payload, contractId: currentBookingStatus.contractId }))
        .unwrap()
        .then((response) => {
          if (response.status === 200) {
            setSubmitted("booking1");
            //showSnackbar(response.data.message, "success");
          }
        })
        .catch((error) => {
          showSnackbar(error, "error");
        });
    } else {
      dispatch(venueRiderNotes({ data: payload, contractId: currentBookingStatus.contractId }))
        .unwrap()
        .then((response) => {
          if (response.status === 200) {
            setSubmitted("booking1");
            //showSnackbar(response.data.message, "success");
          }
        })
        .catch((error) => {
          showSnackbar(error, "error");
        });
    }
  };

  return (
    <Box className="grid grid-cols-2">
      <Box className="lg:col-span-1 col-span-2 h-screen overflow-auto">
        <ProfileNavbar
          pageNumber="6/7"
          tag="Booking request"
          className=" h-[64px] lg:max-w-[49%] !bg-[--bg-color]"
        >
          {" "}
          {/* <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
            {t("saveUnpublished")}
          </Typography>
          <Close className="!text-[--text-color] !text-base" /> */}
        </ProfileNavbar>
        <form onSubmit={handleSubmit(updateRiderNotes)}>
          <Box className="lg:!pl-10 md:!pl-10 !pl-5 !pr-5 lg:!pr-8 !py-24">
            <CreateProfileTitle
              title={riderProfileType === "Act" ? "Act's rider" : "Venue's rider"}
            >
              <Rider className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
            </CreateProfileTitle>
            <Box className="max-w-2xl mt-6">
              <ActRidersForm
                control={control}
                errors={errors}
                setValue={setValue}
                currentBookingStatus={currentBookingStatus}
                riderProfileType="Venue"
                watch={watch}
                riderList={riderList}
              />
            </Box>
          </Box>
          <ProfileFooter
            buttonName={isSmallScreen ? "Next" : "Next"}
            disabled={
              !(watch("riderAccepted") || watch("riderRejected") || watch("acceptedWithConditions"))
            }
            backurl={`/${lang}/standard-contract`}
            //type="button"
            backurlType="router-back"
            footerType={submitted}
            className="lg:!pl-28 lg:!px-2 lg:!max-w-[49%]"
          />
        </form>
      </Box>
      <Box className="h-screen hidden lg:block overflow-auto">
        <RequestSummary />
        {/* <PdfView /> */}
      </Box>
    </Box>
  );
};

export default VenueActRiders;
