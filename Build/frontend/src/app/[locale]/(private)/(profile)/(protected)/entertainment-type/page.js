"use client";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { Box, FormControlLabel, Radio, RadioGroup, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import UserIcon from "@/assets/svg/UserIcon.svg";
// import classNames from "classnames";
import ActPreview from "@/common/(act)/act-preview/act.preview.common";
import { ProfileFooter } from "@/common/profile";
import { setPreviewData, gerActTypes, createActSkills } from "@/store/slice/act/act.slice";
import { useDispatch, useSelector } from "react-redux";
import { Controller, useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { Loader } from "@/component";
import Image from "next/image";
import { Close, RadioButtonUnchecked } from "@mui/icons-material";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { useLocale, useTranslations } from "next-intl";
import RadioButtonIcon from "@/assets/svg/RadioButton.svg";

const ActType = () => {
  const t = useTranslations("actType");
  const p = useTranslations("profileFooter");
  const lang = useLocale();
  const { showSnackbar } = useSnackbar();
  const { previewData, loading: actLoading, actTypes } = useSelector((state) => state.act);
  const profileId = previewData?.actInfo?.profileId;

  const router = useRouter();
  const { handleSubmit, control, watch } = useForm({
    defaultValues: {
      entertainmentType: previewData?.selectedActType || "Live Music",
    },
  });
  // const { control, watch } = useForm({
  //   defaultValues: {
  //     searchDateType: urlData?.searchDateType || "",
  //     overallRating: urlData?.overallRating || "",
  //   },
  // });
  const dispatch = useDispatch();
  //const [disabled, setDisabled] = useState(!previewData?.selectedActType);
  //const [disabled, setDisabled] = useState(false);
  const [loading, setLoading] = useState(false);
  //const [selectedActType, setSelectedActType] = useState(previewData?.selectedActType);
  const entertainmentType = watch("entertainmentType");
  // const handleSelectedActType = (actType) => {
  //   setSelectedActType(actType.name === selectedActType ? null : actType.name);
  //   setDisabled(false);
  //   dispatch(setPreviewData({ selectedActType: actType.name }));
  // };

  const handleActType = (data) => {
    //setSelectedActType(data.entertainmentType);
    dispatch(setPreviewData({ selectedActType: data.entertainmentType }));
    const actSkillsData = {
      entertainmentType: {
        name: data.entertainmentType,
      },
      musicGenreList: [],
    };
    setLoading(true);
    dispatch(createActSkills({ data: actSkillsData, profileId }))
      .unwrap()
      .then(() => {
        setLoading(false);
        showSnackbar(t("actTypeSavedSuccessfully"), t("success"));
        if (data?.entertainmentType === "Live Music") {
          router.push(`/${lang}/music-genre`);
        } else {
          router.push(`/${lang}/media`);
        }
      })
      .catch((error) => {
        setLoading(false);
        let errorMessage = null;
        if (error.status === 400) {
          errorMessage = error.data;
          router.push(`/${lang}/media`);
        } else {
          errorMessage = error?.data?.message ?? t("musicGenreForm.errorOccurred");
        }
        showSnackbar(errorMessage, "error");
      });
  };

  // const handleActType = () => {
  //   setLoading(true);
  //   setTimeout(() => {
  //     setLoading(false);
  //     showSnackbar(t("actTypeSavedSuccessfully"), t("success"));
  //     if (selectedActType === "Live Music") {
  //       router.push(`/${lang}/music-genre`);
  //     } else {
  //       router.push(`/${lang}/act-media`);
  //     }
  //   }, [100]);
  // };
  useEffect(() => {
    dispatch(gerActTypes());
  }, []);

  if (actLoading) return <Loader />;
  return (
    <form onSubmit={handleSubmit(handleActType)}>
      <Box className="!grid !grid-cols-2">
        <Box className="lg:!col-span-1 !col-span-2">
          <ProfileNavbar
            pageNumber="4/7"
            tag={t("createProfile")}
            className=" h-[64px] lg:right-[50%]"
          >
            {" "}
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
              {t("saveUnpuplished")}
            </Typography>
            <Close className="!text-[--text-color] !text-base" />
          </ProfileNavbar>
          <Box className="lg:!px-12 md:!px-12 !px-4 pt-24 ">
            <CreateProfileTitle title={t("actType")}>
              <UserIcon className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
            </CreateProfileTitle>
            <Box className=" !max-w-[750px] pb-28">
              <Typography className="!text-[--text-color] !text-sm CraftworkGroteskGX !pt-8 !pb-4">
                {t("selectOne")}
              </Typography>

              <Box className="w-full">
                <Controller
                  name="entertainmentType"
                  control={control}
                  render={({ field }) => (
                    <RadioGroup
                      value={field.value}
                      className="border border-[--divider-color] rounded-[4px] p-2 w-full"
                      sx={{
                        display: "inline",
                      }}
                      onChange={(e) => field.onChange(e.target.value)}
                    >
                      {actTypes?.map((actType, index) => (
                        <Box key={index}>
                          <FormControlLabel
                            value={actType.name}
                            control={
                              <Radio
                                icon={
                                  <RadioButtonUnchecked className="!w-6 !h-6 !text-[--text-color]" />
                                }
                                checkedIcon={<RadioButtonIcon className="!w-6 !h-6" />}
                              />
                            }
                            label={
                              <Box htmlFor={`radio-${index}`} className="flex gap-x-2 items-center">
                                <Box>
                                  <Image
                                    src={actType.iconUrl}
                                    alt={actType.name}
                                    width={46}
                                    height={46}
                                  />
                                </Box>
                                <Typography className="text-[--text-color] text-md font-bold CraftworkGroteskRegular">
                                  {actType.name}
                                </Typography>
                              </Box>
                            }
                          />
                          <Box className="flex gap-1 pb-3 pt-1 pl-8">
                            <Typography className="text-[--text-color] CraftworkGroteskRegular text-sm">
                              e.g:{" "}
                              {actType.members.map((member, memberIndex) => (
                                <span key={memberIndex} className="text-[--hide-color]">
                                  {memberIndex > 0 && ", "}
                                  {member.name}
                                </span>
                              ))}
                            </Typography>
                          </Box>
                        </Box>
                      ))}
                    </RadioGroup>
                  )}
                />
              </Box>
            </Box>
          </Box>
          <ProfileFooter
            backurl={`/${lang}/info-person`}
            disabled={entertainmentType ? false : true}
            isPreviewData={previewData?.selectedActType ? true : false}
            loading={loading}
            buttonName={p("Next")}
          />
        </Box>
        {/* Act Preview */}
        <ActPreview />
      </Box>
    </form>
  );
};

export default ActType;
