"use client";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { Box, Typography } from "@mui/material";
import React from "react";

import ActProfile from "@/assets/svg/ActProfile.svg";
import ActInfoPersonForm from "@/containers/act-forms/act-forms-info-person/act-forms.info-person.containers";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import ActPreview from "@/common/(act)/act-preview/act.preview.common";
import { Close } from "@mui/icons-material";
import { useTranslations } from "next-intl";
import { useSelector } from "react-redux";
import VenueProfile from "@/assets/svg/VenueProfile.svg";

const ActInformationPerson = () => {
  const t = useTranslations("actInfoPerson");
  const s = useTranslations("CreateProfiles");
  const p = useTranslations("venue");
  const { previewData } = useSelector((state) => state.act);
  return (
    <Box className="!grid !grid-cols-2">
      <Box className="lg:!col-span-1 !col-span-2">
        <ProfileNavbar pageNumber="3/7" tag="Create profile" className=" h-[64px] lg:right-[50%]">
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
            {t("saveUnpublished")}
          </Typography>
          <Close className="!text-[--text-color] !text-base" />
        </ProfileNavbar>
        <Box className="lg:!px-12 md:!px-12 !px-4 !pt-24 ">
          <CreateProfileTitle
            title={
              previewData?.profile?.option === s("Act")
                ? t("actInformation")
                : p("venueInformation")
            }
          >
            {previewData?.profile?.option === s("Act") ? (
              <ActProfile className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
            ) : (
              <VenueProfile className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
            )}
          </CreateProfileTitle>
          <ActInfoPersonForm />
        </Box>
      </Box>
      {/* Act Preview */}
      <ActPreview />
    </Box>
  );
};

export default ActInformationPerson;
