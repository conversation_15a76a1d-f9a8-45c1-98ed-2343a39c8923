"use client";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import { useLocale } from "next-intl";
import { isTokenExpired, removeLocalStorage } from "@/utils";
import { reset } from "@/store/slice/auth/login.auth.slice";

const Layout = ({ children }) => {
  const router = useRouter();
  const lang = useLocale();
  const dispatch = useDispatch();
  const { token } = useSelector((state) => state.login);

  const { previewData, profileId } = useSelector((state) => state.act);
  const profileIdPreviewData = previewData?.actInfo?.profileId;
  useEffect(() => {
    if (isTokenExpired(token)) {
      removeLocalStorage("access_token");
      router.replace(`/${lang}/login`);
      dispatch(reset());
    } else {
      if (profileId || profileIdPreviewData) {
      } else {
        router.push(`/${lang}/create-profiles`);
      }
    }
  }, []);
  return <>{children}</>;
};
export default Layout;
