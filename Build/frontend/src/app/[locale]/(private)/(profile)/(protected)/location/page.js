"use client";
import { CommonImage } from "@/component";
import { Box, Typography } from "@mui/material";
import React from "react";
import ActLocationPng from "@/assets/png/ActLocationPng.png";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import HomeSvg from "@/assets/svg/HomeSvg.svg";
import ActLocationForm from "@/containers/act-forms/act-location/act-location.containers";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import { Close } from "@mui/icons-material";
import { useTranslations } from "next-intl";
import { useSelector } from "react-redux";

const ActLocation = () => {
  const t = useTranslations("actLocation");
  const s = useTranslations("CreateProfiles");
  const p = useTranslations("venue");
  const { previewData } = useSelector((state) => state.act);
  return (
    <Box className="!grid !grid-cols-2">
      <Box className="lg:!col-span-1 !col-span-2">
        <ProfileNavbar
          pageNumber="2/7"
          tag={t("createProfile")}
          className=" h-[64px] lg:right-[50%]"
        >
          {" "}
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
            {t("saveUnpuplished")}
          </Typography>
          <Close className="!text-[--text-color] !text-base" />
        </ProfileNavbar>
        <Box className="lg:!px-12 md:!px-12 !px-4 pt-24 ">
          <CreateProfileTitle
            title={
              previewData?.profile?.option === s("Act") ? t("actLocation") : p("venueLocation")
            }
          >
            <HomeSvg className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
          </CreateProfileTitle>
          <ActLocationForm />
        </Box>
      </Box>
      <Box className="!hidden lg:!block lg:basis-1/2 ">
        <CommonImage
          src={ActLocationPng}
          alt="image"
          className="w-1/2 lg:!block !hidden object-center h-full fixed"
        />
      </Box>
    </Box>
  );
};

export default ActLocation;
