"use client";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { Box, Typography } from "@mui/material";
import React from "react";
import ActMediaSvg from "@/assets/svg/act-type.svg/ActMediaSvg.svg";
import ActMediaForm from "@/ui/act-media/act-media.ui";
import ActPreview from "@/common/(act)/act-preview/act.preview.common";
import { useTranslations } from "next-intl";
import { Close } from "@mui/icons-material";
import { useSelector } from "react-redux";

const ActMedia = () => {
  const t = useTranslations("actMedia");
  const s = useTranslations("CreateProfiles");
  const p = useTranslations("venue");
  const { previewData } = useSelector((state) => state.act);
  return (
    <Box className="!grid !grid-cols-2">
      <Box className="lg:!col-span-1 !col-span-2">
        <ProfileNavbar
          pageNumber="6/7"
          tag={t("createProfile")}
          className=" h-[64px] lg:right-[50%]"
        >
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
            {t("saveUnpuplished")}
          </Typography>
          <Close className="!text-[--text-color] !text-base" />
        </ProfileNavbar>
        <Box className="lg:!px-12 md:!px-12 !px-4 !pt-24 !pb-28 !max-w-[750px] ">
          <CreateProfileTitle
            title={previewData?.profile?.option === s("Act") ? t("actMedia") : p("venueMedia")}
          >
            <ActMediaSvg className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
          </CreateProfileTitle>
          <ActMediaForm />
        </Box>
      </Box>
      {/** Act Preview */}
      <ActPreview />
    </Box>
  );
};

export default ActMedia;
