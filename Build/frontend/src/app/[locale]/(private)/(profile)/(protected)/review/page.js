"use client";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import { <PERSON><PERSON>, Loader } from "@/component";
import ActReviewSlider from "@/ui/act-review-slider/act-review-slider.ui";
import { Close, East, West } from "@mui/icons-material";
import { Box, Typography, CircularProgress } from "@mui/material";
import Link from "next/link";
import React from "react";
import ActReviewVerticalSlider from "@/ui/act-review-vertical-slider/act-review-vertical-slider.ui";
import ActReviewCommon from "@/common/(act)/act-review/act.review.common";
import { useSelector } from "react-redux";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useTranslations } from "next-intl";
const ActReview = ({ params }) => {
  const t = useTranslations("actReview");
  const [loading, setLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const router = useRouter();
  const { previewData } = useSelector((state) => state.act);
  const profileId = previewData?.actInfo?.profileId;
  const { handleSubmit } = useForm();

  const handleSubmitReview = () => {
    setLoading(true);
    setSubmitLoading(true);
    router.push(`/${params.locale}/${profileId}/virtual-profile-success`);
    // dispatch(publishActProfileInfo({ profileId }))
    //   .unwrap()
    //   .then((response) => {
    //     showSnackbar(response.data.message, "success");
    //     removeLocalStorage("profileId");
    //     removeLocalStorage("previewData");
    //     dispatch(resetPreviewData());
    //     router.push(`/${params.locale}/profiles`);
    //   })
    //   .catch((error) => {
    //     setLoading(false);
    //     showSnackbar(error, "error");
    //   });
  };

  if (submitLoading) {
    return <Loader />;
  }

  return (
    <form onSubmit={handleSubmit(handleSubmitReview)}>
      <Box>
        <ProfileNavbar
          tag={t("createProfile")}
          className=" h-[64px] !fixed !top-0 !right-0 !left-0 !bg-[--bg-color] !z-20"
        >
          {" "}
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
            {t("saveUnpuplished")}
          </Typography>
          <Close className="!text-[--text-color] !text-base" />
        </ProfileNavbar>
        <Box className="!max-w-[88%] !h-full !mt-24 !mx-auto">
          <Typography className="Sora400 !text-[--text-color] !text-xs !mb-6">
            {t("preview")}
          </Typography>
          <Box>
            <Box className="!flex gap-4 !w-full">
              <ActReviewSlider images={previewData?.actMedia?.actPhotos} />
              <ActReviewVerticalSlider images={previewData?.actMedia?.actPhotos} />
            </Box>
          </Box>
          <ActReviewCommon review={previewData} />
        </Box>
        <Box className="!flex !justify-between !fixed !bottom-0 !h-[79px] !right-0 !bg-[--bg-color] !left-0 !border-t !border-t-[--divider-color] !z-20 !py-5 lg:!px-12 md:!px-12 !px-4">
          <Link href={`/${params.locale}/payment`} className="!flex !items-center !gap-x-3">
            <West className="!text-[--text-color]" />
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
              {t("back")}
            </Typography>
          </Link>
          <Button
            className="!bg-[--text-color] !px-4 !py-2"
            sx={{
              border: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
            type="submit"
          >
            {loading ? (
              <CircularProgress size={24} className="!text-black" />
            ) : (
              <>
                <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                  {t("submit")}
                </Typography>
                <East className="!text-[--bg-color] !ml-3" />
              </>
            )}
          </Button>
        </Box>
      </Box>
    </form>
  );
};

export default ActReview;
