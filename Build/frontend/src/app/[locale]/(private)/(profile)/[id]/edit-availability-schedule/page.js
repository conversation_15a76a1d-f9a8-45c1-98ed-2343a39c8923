"use client";
import { Calendar, CommonImage } from "@/component";
import { Box, Typography } from "@mui/material";
import React from "react";
import Unavailable from "@/assets/png/Unavailable.png";
import { useTranslations } from "next-intl";
import SaveBackButtonContainers from "@/containers/edit-act-forms/save-back-button.containers";
import { headerData } from "@/component/calendar/methods/header.data";

const EditActAvailabilitySchedule = ({ params }) => {
  const s = useTranslations("availabilityList");
  const t = useTranslations("calendarHeader");
  const profileId = params.id;

  return (
    <>
      <SaveBackButtonContainers loading={false} />
      <Box className="lg:!ml-[320px] lg:!mt-28 !mt-20 !max-w-full lg:!mx-4 ">
        <Box className="!flex !justify-between !mx-4">
          <Typography className="!text-2xl !text-[--text-color] CraftworkGroteskMedium">
            {s("calendar")}
          </Typography>
          <Box className="!flex !gap-x-8" />
        </Box>
        <Typography className="!text-lg !text-[--text-color] CraftworkGroteskMedium !pt-1 !mx-4">
          {s("unavailableHour")}
        </Typography>
        <Box className="!flex !gap-x-3 !items-center !pt-4 !mx-4">
          <CommonImage src={Unavailable} alt="image" className="!w-[30px] !h-[30px]" />
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
            {s("unavailable")}
          </Typography>
        </Box>
        <Box className="!pb-4">
          <Calendar
            initialView={"timeGridWeek"}
            height={700}
            headerData={headerData}
            title={t("upcomingEvents")}
            selectable={true}
            profileId={profileId}
            editable={true}
          />
        </Box>
      </Box>
    </>
  );
};

export default EditActAvailabilitySchedule;
