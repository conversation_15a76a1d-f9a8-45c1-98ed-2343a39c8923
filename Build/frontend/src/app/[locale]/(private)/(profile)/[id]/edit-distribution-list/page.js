"use client";
import { Loader } from "@/component";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { Box } from "@mui/material";
import React, { useEffect, useState } from "react";
import DistributionList from "@/assets/svg/act-type.svg/DistributionList.svg";
import EditActDistributionListForm from "@/containers/edit-act-forms/edit-act-forms.distribution-list/edit-act-forms.distribution-list";
import { useDispatch } from "react-redux";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { getDistributionList } from "@/store/slice/act/act.slice";
import { useTranslations } from "next-intl";
import SaveBackButtonContainers from "@/containers/edit-act-forms/save-back-button.containers";

const EditActDistributionList = ({ params }) => {
  const s = useTranslations("editSidebar");
  const dispatch = useDispatch();
  const { showSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(true);
  const [fetch, setFetch] = useState(0);
  const [search, setSearch] = React.useState("");
  const profileId = params.id;
  const [distributionList, setDistributionList] = useState([]);
  //eslint-disable-next-line
  const [isSidebar, setIsSidebar] = useState(false);

  useEffect(() => {
    dispatch(getDistributionList({ profileId, search }))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setDistributionList(response.data.data);
          setLoading(false);

          //showSnackbar(response.data.message, "success");
          //router.push(`/${params.locale}/profiles`)
        }
      })
      .catch((error) => {
        setLoading(false);
        showSnackbar(error, "error");
      });
  }, [fetch, search]);

  if (loading) {
    return <Loader />;
  }

  return (
    <>
      <SaveBackButtonContainers loading={loading} />
      <Box className="lg:!max-w-md lg:!mx-auto md:!mx-auto lg:!mt-28 !mt-20 md:!max-w-xl !max-w-full !mx-4 ">
        <CreateProfileTitle title={s("distribution")}>
          <DistributionList className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
        </CreateProfileTitle>
        <EditActDistributionListForm
          profileId={profileId}
          data={distributionList}
          setLoading={setLoading}
          setFetch={setFetch}
          setSearch={setSearch}
          search={search}
        />
      </Box>
    </>
  );
};

export default EditActDistributionList;
