"use client";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import {
  Box,
  FormControlLabel,
  Radio,
  RadioGroup,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { gerActTypes, createActSkills } from "@/store/slice/act/act.slice";
import { useDispatch, useSelector } from "react-redux";
import { Controller, useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { RadioButtonUnchecked } from "@mui/icons-material";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { useLocale, useTranslations } from "next-intl";
import { getActByProfileId } from "@/store/slice/act/act.slice";
import MusicGenreSvg from "@/assets/svg/act-type.svg/MusicGenre.svg";
import RadioButtonIcon from "@/assets/svg/RadioButton.svg";
import SaveBackButtonContainers from "@/containers/edit-act-forms/save-back-button.containers";

const EditActType = ({ params }) => {
  const t = useTranslations("actType");
  const theme = useTheme();
  const lang = useLocale();
  const { showSnackbar } = useSnackbar();
  const profileId = params.id;
  const isMobile = useMediaQuery(theme.breakpoints.down("lg"));
  const { previewData, actTypes, currentProfile } = useSelector((state) => state.act);
  const [selectedActType, setSelectedActType] = useState(previewData?.selectedActType);

  useEffect(() => {
    dispatch(getActByProfileId(profileId))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
        }
      })
      .catch(() => {});
  }, []);

  const router = useRouter();
  const { handleSubmit, control, setValue } = useForm({
    defaultValues: {
      entertainmentType: selectedActType || "",
    },
  });
  const dispatch = useDispatch();
  // eslint-disable-next-line
  const [disabled, setDisabled] = useState(!previewData?.selectedActType);

  const [loading, setLoading] = useState(false);
  // const handleSelectedActType = (actType) => {
  //   setSelectedActType(actType.name === selectedActType ? null : actType.name);
  //   setDisabled(false);
  //   //dispatch(setPreviewData({ selectedActType: actType.name }));
  // };

  useEffect(() => {
    if (currentProfile?.skillsDto?.entertainmentType?.name) {
      setValue("entertainmentType", currentProfile?.skillsDto?.entertainmentType?.name);
      setSelectedActType(currentProfile?.skillsDto?.entertainmentType?.name);
    }
  }, [currentProfile]);

  const handleActType = (data) => {
    const actSkillsData = {
      entertainmentType: {
        name: data?.entertainmentType,
      },
      musicGenreList: [],
    };
    setLoading(true);
    dispatch(createActSkills({ data: actSkillsData, profileId }))
      .unwrap()
      .then(() => {
        setLoading(false);
        showSnackbar(t("actTypeSavedSuccessfully"), t("success"));
        if (data?.entertainmentType === "Live Music") {
          isMobile || currentProfile?.profileStatus === "STATUS_CREATED"
            ? router.push(`/${params.locale}/${params.id}/edit-music-genre`)
            : router.push(`/${params.locale}/profiles`);
        } else {
          isMobile || currentProfile?.profileStatus === "STATUS_CREATED"
            ? router.push(`/${lang}/${params.id}/edit-media`)
            : router.push(`/${lang}/profiles`);
        }
      })
      .catch((error) => {
        setLoading(false);
        let errorMessage = null;
        if (error.status === 400) {
          errorMessage = error.data;
          router.push(`/${lang}/${params.id}/edit-media`);
        } else {
          errorMessage = error?.data?.message ?? t("musicGenreForm.errorOccurred");
        }
        showSnackbar(errorMessage, "error");
      });
  };

  // const handleActType = () => {
  //   setLoading(true);
  //   setTimeout(() => {
  //     setLoading(false);
  //     showSnackbar(t("actTypeSavedSuccessfully"), t("success"));
  //     if (selectedActType === "Live Music") {
  //       router.push(`/${lang}/music-genre`);
  //     } else {
  //       router.push(`/${lang}/act-media`);
  //     }
  //   }, [100]);
  // };

  useEffect(() => {
    dispatch(gerActTypes());
  }, []);

  // if (actLoading) return <Loader />;
  return (
    <form onSubmit={handleSubmit(handleActType)}>
      <SaveBackButtonContainers loading={loading} />
      <Box className="lg:!max-w-xl lg:!mx-auto md:!mx-auto lg:!my-28 !my-20 md:!max-w-xl !max-w-full !mx-4 ">
        <CreateProfileTitle title={t("actType")}>
          <MusicGenreSvg className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
        </CreateProfileTitle>
        <Box className="flex flex-wrap !items-center !gap-4">
          <Box className="w-full">
            <Controller
              name="entertainmentType"
              control={control}
              render={({ field }) => (
                <RadioGroup
                  value={field.value}
                  className="border border-[--divider-color] rounded-[4px] p-2 w-full"
                  sx={{
                    display: "inline",
                  }}
                  onChange={(e) => field.onChange(e.target.value)}
                >
                  {actTypes?.map((actType, index) => (
                    <Box key={index}>
                      <FormControlLabel
                        value={actType.name}
                        control={
                          <Radio
                            icon={
                              <RadioButtonUnchecked className="!w-6 !h-6 !text-[--text-color]" />
                            }
                            checkedIcon={<RadioButtonIcon className="!w-6 !h-6" />}
                          />
                        }
                        label={
                          <Box htmlFor={`radio-${index}`} className="flex gap-x-2 items-center">
                            <Box>
                              <Image
                                src={actType.iconUrl}
                                alt={actType.name}
                                width={46}
                                height={46}
                              />
                            </Box>
                            <Typography className="text-[--text-color] text-md font-bold CraftworkGroteskRegular">
                              {actType.name}
                            </Typography>
                          </Box>
                        }
                      />
                      <Box className="flex gap-1 pb-3 pt-1 pl-8">
                        <Typography className="text-[--text-color] CraftworkGroteskRegular text-sm">
                          e.g:{" "}
                          {actType.members.map((member, memberIndex) => (
                            <span key={memberIndex} className="text-[--hide-color]">
                              {memberIndex > 0 && ", "}
                              {member.name}
                            </span>
                          ))}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </RadioGroup>
              )}
            />
          </Box>
        </Box>
        {/** Act profile form */}
        {/* <EditActGenreForm
          profileId={params.id}
          setValue={setValue}
          control={control}
          watch={watch}
        /> */}
      </Box>
      {/* <Box className="!grid !grid-cols-2">
        <Box className="lg:!col-span-1 !col-span-2">
          <ProfileNavbar
            pageNumber="4/7"
            tag={t("createProfile")}
            className=" h-[64px]"
          >
            {" "}
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
              {t("saveUnpuplished")}
            </Typography>
            <Close className="!text-[--text-color] !text-base" />
          </ProfileNavbar>
          <Box className="lg:!px-12 md:!px-12 !px-4 lg:!pt-16 md:!pt-12 !pt-8 ">
            <CreateProfileTitle title={t("actType")}>
              <UserIcon className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
            </CreateProfileTitle>
            <Box className=" !max-w-[750px] pb-28">
              <Typography className="!text-[--text-color] !text-sm CraftworkGroteskGX !pt-8 !pb-4">
                {t("selectOne")}
              </Typography>
              <Box className="flex flex-wrap !items-center !gap-4">
                {actTypes?.map((actType, index) => (
                  <Box>
                    <Box
                      key={index}
                      className={classNames(
                        "!flex !p-3 !gap-x-3 !border  !rounded-[4px] !cursor-pointer",
                        {
                          "border-[--inprogress-color]":
                            selectedActType === actType.name,
                          "border-[--divider-color]":
                            selectedActType !== actType.name,
                        }
                      )}
                      onClick={() => handleSelectedActType(actType)}
                    >
                      <Box>
                        <Image
                          src={actType.iconUrl}
                          alt={actType.name}
                          width={16}
                          height={16}
                        />
                      </Box>
                      <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular ">
                        {actType.name}
                      </Typography>
                    </Box>
                    <Box className="flex flex-wrap gap-2 pt-2">
                      {actType.members.map((member, memberIndex) => (
                        <Box
                          key={memberIndex}
                          className="border border-[--divider-color] rounded-[2px] p-2"
                        >
                          <Typography className="text-[--text-color] text-sm CraftworkGroteskRegular">
                            {member.name}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </Box>
                ))}
              </Box>
            </Box>
          </Box>
          <ProfileFooter
            backurl={`/${lang}/act-info-person`}
            disabled={disabled}
            isPreviewData={previewData?.selectedActType ? true : false}
            loading={loading}
          />
        </Box>
        Act Preview
        <ActPreview />
      </Box> */}
    </form>
  );
};

export default EditActType;
