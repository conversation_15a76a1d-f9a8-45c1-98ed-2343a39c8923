"use client";
import { Box } from "@mui/material";
import React, { useEffect } from "react";
import Rider from "@/assets/svg/act-type.svg/Rider.svg";
// import { feedbackLinks } from "./data";
import EditActFeedbackReceived from "@/containers/edit-act-forms/edit-act-forms.feedback/edit-act-forms.feedback.received/edit-act-forms.feedback.received.containers";
import { useLocale, useTranslations } from "next-intl";
import SaveBackButtonContainers from "@/containers/edit-act-forms/save-back-button.containers";
import { useDispatch } from "react-redux";
import { listFeedback } from "@/store/slice/common/common.slice";
import ScreenFeedbackViewTitle from "@/common/title/screen-feedback-view.title.common";

const EditActFeedback = ({ params }) => {
  const lang = useLocale();
  const profileId = params.id;
  const [feedbackData, setFeedbacData] = React.useState([]);
  const [feedbackType, setFeedbackType] = React.useState("Received");
  const feedbackLinks = [
    {
      id: 0,
      text: "Received",
      path: `/${lang}/${profileId}/edit-feedback`,
    },
    {
      id: 1,
      text: "Initiated",
      path: `/${lang}/${profileId}/edit-feedback`,
    },
  ];
  const p = useTranslations("feedback");
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(listFeedback(profileId)).then((res) => {
      setFeedbacData(res?.payload?.data?.data);
    });
  }, [profileId]);

  return (
    <>
      <SaveBackButtonContainers loading={false} />
      <Box className="lg:!max-w-2xl lg:!mx-auto md:!mx-auto lg:!mt-28 !mt-20 md:!max-w-xl !mb-4 !max-w-full ">
        <Box>
          <Rider className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px] !mb-4" />
          <ScreenFeedbackViewTitle
            primaryText={p("feedback")}
            links={feedbackLinks}
            setFeedbackType={setFeedbackType}
            feedbackType={feedbackType}
          />
        </Box>
        <EditActFeedbackReceived feedbackData={feedbackData} feedbackType={feedbackType} />
      </Box>
    </>
  );
};

export default EditActFeedback;
