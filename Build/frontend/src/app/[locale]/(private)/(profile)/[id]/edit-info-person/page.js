"use client";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { Box, useTheme, useMediaQuery } from "@mui/material";
import React, { useEffect, useState } from "react";

import ActProfile from "@/assets/svg/ActProfile.svg";
import { usePathname, useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { yupResolver } from "@hookform/resolvers/yup";
import { actInfoPersonValidation } from "@/validation/act/act.validation";
import { useForm } from "react-hook-form";
import EditActInfoPersonContainer from "@/containers/edit-act-forms/edit-info-person/edit-info-person.containers";
import { updateActProfileInfo } from "@/store/slice/act/act.slice";
import { useTranslations } from "next-intl";
import { redirectButtonRoute } from "@/utils";
import SaveBackButtonContainers from "@/containers/edit-act-forms/save-back-button.containers";

const EditActInformationPerson = ({ params }) => {
  const t = useTranslations("editActCommon");
  const s = useTranslations("venue");
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("lg"));

  const router = useRouter();
  const profileId = params.id;
  const dispatch = useDispatch();
  const resolver = yupResolver(actInfoPersonValidation);
  const { showSnackbar } = useSnackbar();
  //eslint-disable-next-line
  const [fetch, setFetch] = useState(1);
  const [loading, setLoading] = useState(false);
  const { currentProfile } = useSelector((state) => state.act);
  const routePath = usePathname();
  const {
    handleSubmit,
    control,
    register,
    watch,
    reset,
    setValue,
    getValues,
    formState: { errors },
  } = useForm({
    resolver,
    defaultValues: {
      bio: currentProfile?.infoDto?.bio || "",
      spotifyLink: "",
      soundCloudLink: "",
      instagramLink: "",
      youtubeLink: "",
      facebookLink: "",
      suitableForAdultsOrChildren: "",
      enableworkinghour: true,
      workingHoursList: [
        // { day: 'MONDAY', startTime: startTime, endTime: endTime },
        // { day: 'TUESDAY', startTime: startTime, endTime: endTime },
        // { day: 'WEDNESDAY', startTime: startTime, endTime: endTime },
        // { day: 'THURSDAY', startTime: startTime, endTime: endTime },
        // { day: 'FRIDAY', startTime: startTime, endTime: endTime },
        // { day: 'SATURDAY', startTime: startTime, endTime: endTime },
        // { day: 'SUNDAY', startTime: startTime, endTime: endTime }
      ],
    },
  });

  // set the default values of the form when get data in currentProfile
  useEffect(() => {
    // use setValue to set the value of the form
    if (currentProfile) {
      setValue("bio", currentProfile?.infoDto?.bio || "");
      setValue(
        "spotifyLink",
        currentProfile?.infoDto?.socialMediaLinks
          ?.find((link) => link.includes("spotify"))
          ?.split("/")
          .pop() || "",
      );
      setValue(
        "soundCloudLink",
        currentProfile?.infoDto?.socialMediaLinks
          ?.find((link) => link.includes("soundcloud"))
          ?.split("/")
          .pop() || "",
      );
      setValue(
        "instagramLink",
        currentProfile?.infoDto?.socialMediaLinks
          ?.find((link) => link.includes("instagram"))
          ?.split("/")

          .pop() || "",
      );
      setValue(
        "youtubeLink",
        currentProfile?.infoDto?.socialMediaLinks
          ?.find((link) => link.includes("youtube"))
          ?.split("/")
          .pop() || "",
      );
      setValue(
        "facebookLink",
        currentProfile?.infoDto?.socialMediaLinks
          ?.find((link) => link.includes("facebook"))
          ?.split("/")
          .pop() || "",
      );
      setValue(
        "suitableForAdultsOrChildren",
        currentProfile?.infoDto?.suitableForAdultsOnly
          ? "Suitable for adults only"
          : "Suitable for children",
      );
      //setValue("enableworkinghour", currentProfile?.infoDto?.weeklyWorkingHours?.workingHoursList?.length > 0 ? true : false);
      //setValue("workingHoursList", currentProfile?.infoDto?.weeklyWorkingHours?.workingHoursList || []);

      // reset({
      //   bio: currentProfile?.infoDto?.bio || "",
      //   spotifyLink:
      //     currentProfile?.infoDto?.socialMediaLinks
      //       ?.find((link) => link.includes("spotify"))
      //       ?.split("/")
      //       .pop() || "",
      //   soundCloudLink:
      //     currentProfile?.infoDto?.socialMediaLinks
      //       ?.find((link) => link.includes("soundcloud"))
      //       ?.split("/")
      //       .pop() || "",
      //   instagramLink:
      //     currentProfile?.infoDto?.socialMediaLinks
      //       ?.find((link) => link.includes("instagram"))
      //       ?.split("/")
      //       .pop() || "",
      //   youtubeLink:
      //     currentProfile?.infoDto?.socialMediaLinks
      //       ?.find((link) => link.includes("youtube"))
      //       ?.split("/")
      //       .pop() || "",
      //   facebookLink:
      //     currentProfile?.infoDto?.socialMediaLinks
      //       ?.find((link) => link.includes("facebook"))
      //       ?.split("/")
      //       .pop() || "",
      //   suitableForAdultsOrChildren: currentProfile?.infoDto?.suitableForAdultsOnly
      //     ? "Suitable for adults only"
      //     : "Suitable for children",
      // });
    }
  }, [currentProfile]);

  const handleActInfo = (data) => {
    const socialMediaLinks = [];
    if (data.spotifyLink) {
      socialMediaLinks.push(`https:/open.spotify.com/${data.spotifyLink}`);
    }
    if (data.soundCloudLink) {
      socialMediaLinks.push(`https:/soundcloud.com/${data.soundCloudLink}`);
    }
    if (data.instagramLink) {
      socialMediaLinks.push(`https:/instagram.com/${data.instagramLink}`);
    }
    if (data.youtubeLink) {
      socialMediaLinks.push(`https:/youtube.com/${data.youtubeLink}`);
    }
    if (data.facebookLink) {
      socialMediaLinks.push(`https:/facebook/com/${data.facebookLink}`);
    }

    const actInfoData = {
      bio: data.bio,
      suitableForAdultsOnly:
        data.suitableForAdultsOrChildren === "Suitable for adults only" ? true : false,
      suitableForChildren:
        data.suitableForAdultsOrChildren === "Suitable for children" ? true : false,
      socialMediaLinks: socialMediaLinks,
      // weeklyWorkingHours: !data.enableworkinghour
      //   ? {}
      //   : {
      //       workingHoursList: data.workingHoursList.map((item) => ({
      //         day: item.day,
      //         startTime: new Date(item.startTime).toISOString(),
      //         endTime: new Date(item.endTime).toISOString(),
      //       })),
      //     },
    };

    if (currentProfile?.profileType === "VENUE_PROFILE" && data.enableworkinghour) {
      actInfoData.weeklyWorkingHours = {
        workingHoursList: data.workingHoursList.map((item) => ({
          day: item.day,
          startTime: new Date(item.startTime).toISOString(),
          endTime: new Date(item.endTime).toISOString(),
        })),
      };
    }

    setLoading(true);
    dispatch(updateActProfileInfo({ profileId, data: actInfoData }))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setLoading(false);
          showSnackbar(response.data.message, "success");
          redirectButtonRoute(
            router,
            routePath,
            "save",
            isMobile,
            currentProfile?.profileType,
            currentProfile?.profileStatus,
          );
        } else if (response.status === 208) {
          setLoading(false);
          showSnackbar(response.data.message, "error");
          redirectButtonRoute(
            router,
            routePath,
            "save",
            isMobile,
            currentProfile?.profileType,
            currentProfile?.profileStatus,
          );
        }
      })
      .catch((error) => {
        setLoading(false);
        showSnackbar(error, "error");
      });
  };

  return (
    <form onSubmit={handleSubmit(handleActInfo)}>
      <SaveBackButtonContainers loading={loading} />
      <Box className="lg:!max-w-[620px] lg:!mx-auto md:!mx-auto lg:!mt-28 !mt-20 md:!max-w-xl !max-w-full !mx-4 ">
        <CreateProfileTitle
          title={
            currentProfile?.profileType === "ACT_PROFILE" ||
            currentProfile?.profileType === "VIRTUAL_ACT_PROFILE"
              ? t("editAct")
              : s("editInfo")
          }
        >
          <ActProfile className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
        </CreateProfileTitle>
        {/** Act profile form */}
        <EditActInfoPersonContainer
          register={register}
          control={control}
          watch={watch}
          reset={reset}
          errors={errors}
          handleSubmit={handleSubmit}
          profileId={profileId}
          fetch={fetch}
          setValue={setValue}
          getValues={getValues}
        />
      </Box>
    </form>
  );
};

export default EditActInformationPerson;
