"use client";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { Box, useMediaQuery, useTheme } from "@mui/material";
import ActProfile from "@/assets/svg/ActProfile.svg";
import React, { useEffect, useState } from "react";
import EditActInformationForm from "@/containers/edit-act-forms/edit-act-forms.information/edit-act-forms.information.containers";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import { actInformationValidation } from "@/validation/act/act.validation";
import { useDispatch, useSelector } from "react-redux";
import { updateProfile, getActByProfileId } from "@/store/slice/act/act.slice";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { usePathname, useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { redirectButtonRoute } from "@/utils/index";
import SaveBackButtonContainers from "@/containers/edit-act-forms/save-back-button.containers";

const EditInformation = ({ params }) => {
  const t = useTranslations("actInformation");
  const s = useTranslations("venue");
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("lg"));
  const router = useRouter();
  const routePath = usePathname();
  const profileId = params.id;
  const { currentUser } = useSelector((state) => state.login);
  const { currentProfile } = useSelector((state) => state.act);
  const dispatch = useDispatch();
  const resolver = yupResolver(actInformationValidation({ previewData: currentProfile }));
  const { showSnackbar } = useSnackbar();
  const [fetch, setFetch] = useState(1);
  const [loading, setLoading] = useState(true);
  const {
    handleSubmit,
    setValue,
    control,
    register,
    watch,
    reset,
    formState: { errors },
  } = useForm({
    resolver,
    defaultValues: {
      profileName: "",
      profileRole: "",
      performanceLanguages: [],
      communicationLanguages: [],
      preferredLanguage: "",
      useMyEmail: false,
      profileEmail: "",
      numMembers: 0,
    },
  });

  useEffect(() => {
    if (currentProfile?.profileType === "VIRTUAL_ACT_PROFILE") {
      setValue("authorizedRepresenter", currentProfile?.virtualContactDto?.contactName);
      setValue("authorizedRepresenterEmail", currentProfile?.virtualContactDto?.contactEmail);
      setValue("authorizedRepresenterPhoneNumber", currentProfile?.virtualContactDto?.contactPhone);
    }
  }, [currentProfile]);

  const updateActInfoProfile = (data) => {
    setLoading(true);
    const actInfoData = {
      profileName: data.profileName,
      profileRole: data.profileRole,
      numMembers: data.numMembers,
      owner: true,
      useMyEmail: data.useMyEmail,
      performanceLanguages: data.performanceLanguages,
      communicationLanguages: data.communicationLanguages,
      preferredLanguage: data.preferredLanguage,
      profileEmail: watch("useMyEmail") ? currentUser?.email : data.profileEmail,
      profileType: data.profileType,
      virtualContactDto: {
        contactName: data?.authorizedRepresenter,
        contactEmail: data?.authorizedRepresenterEmail,
        //contactPhone: `${data.authorizedRepresenterPhoneNumberCountryCode}` + data.authorizedRepresenterPhoneNumber,
        contactPhone: data?.authorizedRepresenterPhoneNumber,
      },
    };
    dispatch(updateProfile({ data: actInfoData, profileId }))
      .unwrap()
      .then((response) => {
        setLoading(false);
        showSnackbar(response.data.message, "success");
        setFetch((prev) => prev + 1);
        redirectButtonRoute(
          router,
          routePath,
          "save",
          isMobile,
          currentProfile?.profileType,
          currentProfile?.profileStatus,
        );
      })
      .catch((error) => {
        setLoading(false);
        showSnackbar(error, "error");
      });
  };

  useEffect(() => {
    dispatch(getActByProfileId(profileId))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setLoading(false);
          reset((prev) => ({ ...prev, ...response.data.data }));
        }
      });
  }, []);

  return (
    <form onSubmit={handleSubmit(updateActInfoProfile)}>
      <SaveBackButtonContainers loading={loading} />

      {/* {((!isFirst && isMobile) || !isMobile) && ( */}
      <Box className="lg:!max-w-md lg:!mx-auto md:!mx-auto lg:!mt-28 !mt-20 md:!max-w-xl !max-w-full !mx-4 ">
        <CreateProfileTitle
          title={
            currentProfile?.profileType === "ACT_PROFILE" ||
            currentProfile?.profileType === "VIRTUAL_ACT_PROFILE"
              ? t("ActInformation")
              : s("venueInformation")
          }
        >
          <ActProfile className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
        </CreateProfileTitle>
        {/** Act profile form */}
        <EditActInformationForm
          register={register}
          control={control}
          watch={watch}
          reset={reset}
          errors={errors}
          handleSubmit={handleSubmit}
          profileId={profileId}
          fetch={fetch}
          setValue={setValue}
        />
      </Box>
      {/* )} */}
    </form>
  );
};

export default EditInformation;
