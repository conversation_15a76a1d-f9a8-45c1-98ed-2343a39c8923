"use client";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { Box } from "@mui/material";
import React, { useEffect, useState } from "react";
import ActLocation from "@/assets/svg/ActLocation.svg";
import { useForm } from "react-hook-form";
import EditActLocationForm from "@/containers/edit-act-forms/edit-act-forms.location/edit-act-forms.location.containers";
import { getActLocation, createActLocation } from "@/store/slice/act/act.slice";
import { yupResolver } from "@hookform/resolvers/yup";
import { actLocationValidation } from "@/validation/act/act.validation";
import { useDispatch, useSelector } from "react-redux";
import { showSnackbar } from "@/utils/snackbar.utils";
import { useRouter } from "next/navigation";
import useIsMobile from "@/hooks/useIsMobile";
import { useTranslations } from "next-intl";
import SaveBackButtonContainers from "@/containers/edit-act-forms/save-back-button.containers";

const EditActLocation = ({ params }) => {
  const t = useTranslations("actLocation");
  const s = useTranslations("venue");
  const profileId = params.id;
  const dispatch = useDispatch();
  const router = useRouter();
  const isMobile = useIsMobile();
  const [loading, setLoading] = useState(false);
  const { currentProfile } = useSelector((state) => state.act);
  const resolver = yupResolver(actLocationValidation);
  const {
    handleSubmit,
    control,
    register,
    watch,
    reset,
    setValue,
    formState: { errors },
  } = useForm({
    resolver,
    defaultValues: {
      state: "",
      country: "",
      city: "",
      streetAddress: "",
      zipCode: "",
      useMyLocation: false,
      canTravelLongDistance: false,
    },
  });

  const handleUpdateLocation = (data) => {
    const actLocationData = {
      country: data.country,
      city: data.city,
      state: data.state,
      streetAddress: data.streetAddress,
      zipCode: data.zipCode,
      // useMyLocation: data.useMyLocation,
      canTravelLongDistance: data.canTravelLongDistance,
    };
    setLoading(true);
    dispatch(createActLocation({ data: actLocationData, profileId }))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setLoading(false);
          showSnackbar(response.data.message, "success");
          if (isMobile) {
            if (currentProfile?.profileType === "VIRTUAL_ACT_PROFILE") {
              router.push(`/${params.locale}/${profileId}/virtual-profile-success`);
            } else {
              router.push(`/${params.locale}/${profileId}/edit-payment`);
            }
          } else {
            if (currentProfile?.profileStatus === "STATUS_CREATED") {
              router.push(`/${params.locale}/${profileId}/edit-payment`);
            } else {
              router.push(`/${params.locale}/profiles`);
            }
          }
        }
      })
      .catch((error) => {
        setLoading(false);
        showSnackbar(error, "error");
      });
  };

  /** get Act Location */
  useEffect(() => {
    dispatch(getActLocation(profileId))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          const actLocation = response.data.data;
          actLocation && reset((prev) => ({ ...prev, ...actLocation }));
        }
      })
      .catch(() => {});
  }, [reset, fetch]);

  return (
    <form onSubmit={handleSubmit(handleUpdateLocation)}>
      <SaveBackButtonContainers loading={loading} />
      <Box className="lg:!max-w-md lg:!mx-auto md:!mx-auto lg:!mt-28 !mt-20 md:!max-w-xl !max-w-full !mx-4 ">
        <CreateProfileTitle
          title={
            currentProfile?.profileType === "ACT_PROFILE" ||
            currentProfile?.profileType === "VIRTUAL_ACT_PROFILE"
              ? t("actLocation")
              : s("location")
          }
        >
          <ActLocation className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
        </CreateProfileTitle>
        {/** Act profile form */}
        <EditActLocationForm
          control={control}
          errors={errors}
          register={register}
          watch={watch}
          setValue={setValue}
        />
      </Box>
    </form>
  );
};

export default EditActLocation;
