"use client";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import React from "react";
import ActMediaSvg from "@/assets/svg/act-type.svg/ActMediaSvg.svg";
import { Box } from "@mui/material";
import EditActMediaForm from "@/containers/edit-act-forms/edit-act-forms.media/edit-act-forms.media.containers";
import { useTranslations } from "next-intl";
import { useSelector } from "react-redux";
import SaveBackButtonContainers from "@/containers/edit-act-forms/save-back-button.containers";

const EditActMedia = ({ params }) => {
  const t = useTranslations("actMedia");
  //const s = useTranslations("editActCommon");
  const p = useTranslations("venue");
  const { currentProfile } = useSelector((state) => state.act);

  return (
    <>
      <SaveBackButtonContainers loading={false} />
      <Box className="lg:!max-w-md lg:!mx-auto md:!mx-auto lg:!mt-28 !mt-20 md:!max-w-xl !max-w-full !mx-4 ">
        <CreateProfileTitle
          title={
            currentProfile?.profileDto?.profileType === "ACT_PROFILE" ||
            currentProfile?.profileDto?.profileType === "VIRTUAL_ACT_PROFILE"
              ? t("actMedia")
              : p("venueMedia")
          }
        >
          <ActMediaSvg className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
        </CreateProfileTitle>
        {/** Act profile form */}
        <EditActMediaForm profileId={params.id} />
      </Box>
    </>
  );
};

export default EditActMedia;
