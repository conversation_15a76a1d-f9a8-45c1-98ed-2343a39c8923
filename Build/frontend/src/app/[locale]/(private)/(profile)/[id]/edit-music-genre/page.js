"use client";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { Box, useTheme, useMediaQuery } from "@mui/material";
import React, { useEffect, useState } from "react";
import MusicGenreSvg from "@/assets/svg/act-type.svg/MusicGenre.svg";
import EditActGenreForm from "@/containers/edit-act-forms/edit-act-forms.genre/edit-act-forms.genre.containers";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import { createActSkills, getActSkills } from "@/store/slice/act/act.slice";
import { useTranslations } from "next-intl";
import SaveBackButtonContainers from "@/containers/edit-act-forms/save-back-button.containers";

const EditMusicGenre = ({ params }) => {
  const s = useTranslations("musicGenre");
  const theme = useTheme();
  const profileId = params.id;
  const isMobile = useMediaQuery(theme.breakpoints.down("lg"));
  const dispatch = useDispatch();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const { showSnackbar } = useSnackbar();
  const { currentProfile } = useSelector((state) => state.act);

  const { handleSubmit, control, setValue, watch } = useForm({
    defaultValues: {
      musicGenre: [],
      entertainmentType: {},
    },
  });

  const handleUpdate = (data) => {
    const musicGenre = data.musicGenre.map((genre) => ({ name: genre.name }));
    const actSkillsData = {
      entertainmentType: { name: data.entertainmentType },
      musicGenreList: musicGenre,
    };

    setLoading(true);
    isMobile || currentProfile?.profileStatus === "STATUS_CREATED"
      ? router.push(`/${params.locale}/${profileId}/edit-media`)
      : router.push(`/${params.locale}/profiles`);
    dispatch(createActSkills({ data: actSkillsData, profileId: params.id }))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setLoading(false);
          showSnackbar(response.data.message, "success");
          isMobile || currentProfile?.profileStatus === "STATUS_CREATED"
            ? router.push(`/${params.locale}/${profileId}/edit-media`)
            : router.push(`/${params.locale}/profiles`);
        }
      })
      .catch((error) => {
        let errorMessage = null;
        setLoading(false);
        errorMessage = error?.data?.message ?? "Unexpected error occurred";

        showSnackbar(errorMessage, "error");
      });
  };

  useEffect(() => {
    dispatch(getActSkills(params.id))
      .unwrap()
      .then((response) => {
        const musicGenreList = response.data.data.musicGenreList;
        const actType = response.data.data.entertainmentType.name;

        setValue("musicGenre", musicGenreList);
        setValue("entertainmentType", actType);
      })
      .catch((error) => {
        return error;
      });
  }, [params.id]);

  return (
    <form onSubmit={handleSubmit(handleUpdate)}>
      <SaveBackButtonContainers loading={loading} />
      <Box className="lg:!max-w-md lg:!mx-auto md:!mx-auto lg:!mt-12 !mt-5 md:!max-w-xl !max-w-full !mx-4 ">
        <CreateProfileTitle title={s("musicGenre")}>
          <MusicGenreSvg className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
        </CreateProfileTitle>
        {/** Act profile form */}
        <EditActGenreForm
          profileId={params.id}
          setValue={setValue}
          control={control}
          watch={watch}
        />
      </Box>
    </form>
  );
};

export default EditMusicGenre;
