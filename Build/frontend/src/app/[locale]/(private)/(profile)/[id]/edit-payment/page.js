"use client";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { Box, useMediaQuery, useTheme } from "@mui/material";
import React, { useState, useEffect } from "react";
import PaymentSvg from "@/assets/svg/act-type.svg/PaymentSvg.svg";
import EditActPaymentForm from "@/containers/edit-act-forms/edit-act-forms.payment/edit-act-forms.payment.containers";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { actPaymentValidation } from "@/validation/act/act.validation";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { useDispatch, useSelector } from "react-redux";
import {
  createActPayment,
  getProfileActPayment,
  NotForRentPayment,
  updateActPayment,
} from "@/store/slice/act/act.slice";
import { usePathname, useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import SaveBackButtonContainers from "@/containers/edit-act-forms/save-back-button.containers";
import { redirectButtonRoute } from "@/utils";

const EditActPayment = ({ params }) => {
  const t = useTranslations("actPayment");
  const s = useTranslations("venue");
  const profileId = params.id;
  const router = useRouter();
  const routePath = usePathname();
  const dispatch = useDispatch();
  const { showSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);
  const { currentProfile, previewData } = useSelector((state) => state.act);
  const resolver = yupResolver(actPaymentValidation({ previewData }));

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const {
    handleSubmit,
    control,
    formState: { errors, defaultValues },
    watch,
    register,
    setValue,
  } = useForm({
    resolver,
    defaultValues: {
      paymentMethod: [],
      typicalPrice: "",
      standardPriceCurrency: "",
      standardPricePer: "",
      charityPrice: "",
      minimalPriceCurrency: "",
      minimalPricePer: "",
      forRentOrNot: currentProfile?.profileType === "VENUE_PROFILE" ? "Not for rent" : "",
    },
  });
  const isPreviewDataEmpty = Object.values(defaultValues).every((value) => {
    if (Array.isArray(value)) {
      return value.length === 0;
    } else {
      return value === "";
    }
  });

  const handleUpdateActPayment = (data) => {
    const actPaymentData = {
      currency: data.standardPriceCurrency,
      typicalPrice: data.typicalPrice,
      minimumPrice: data.charityPrice,
      acceptablePaymentMethods: data.paymentMethod,
      minPriceChargingType: data.minimalPricePer?.toUpperCase(),
      typicalPriceChargingType: data.standardPricePer?.toUpperCase(),
    };

    if (data.forRentOrNot === "For rent") {
      actPaymentData["forRent"] = true;
    }

    setLoading(true);

    if (
      (currentProfile?.profileType === "VENUE_PROFILE" ||
        currentProfile?.profileType === "VIRTUAL_VENUE_PROFILE") &&
      data.forRentOrNot === "Not for rent"
    ) {
      // Extra condition: Dispatch for Not For Rent
      dispatch(
        NotForRentPayment({ data: data.forRentOrNot === "For rent" ? true : false, profileId }),
      )
        .unwrap()
        .then((response) => {
          if (response.status === 200) {
            setLoading(false);
            showSnackbar(response.data.message, "success");
            redirectButtonRoute(
              router,
              routePath,
              "save",
              isMobile,
              currentProfile?.profileType,
              currentProfile?.profileStatus,
            );

            // isMobile || currentProfile?.profileStatus === "STATUS_CREATED"
            //   ? router.push(`/${params.locale}/${params.id}/edit-rider`)
            //   : router.push(`/${params.locale}/profiles`);
          }
        })
        .catch((error) => {
          setLoading(false);
          showSnackbar(error, "error");
        });
    } else {
      // Continue with existing flow for creating or updating payment
      dispatch(
        isPreviewDataEmpty
          ? createActPayment({ data: actPaymentData, profileId })
          : updateActPayment({ data: actPaymentData, profileId }), // Changed to updateActPayment
      )
        .unwrap()
        .then((response) => {
          if (response.status === 200) {
            setLoading(false);
            showSnackbar(response.data.message, "success");
            redirectButtonRoute(
              router,
              routePath,
              "save",
              isMobile,
              currentProfile?.profileType,
              currentProfile?.profileStatus,
            );
          }
        })
        .catch((error) => {
          setLoading(false);
          showSnackbar(error, "error");
        });
    }
  };

  useEffect(() => {
    dispatch(getProfileActPayment(profileId))
      .unwrap()
      .then((response) => {
        const actPayment = response.data.data;
        setValue("paymentMethod", actPayment.acceptablePaymentMethods);
        setValue("typicalPrice", actPayment.typicalPrice);
        setValue("standardPriceCurrency", actPayment.currency);
        setValue("standardPricePer", actPayment.typicalPriceChargingType);
        setValue("charityPrice", actPayment.minimumPrice);
        setValue("minimalPriceCurrency", actPayment.currency);
        setValue("minimalPricePer", actPayment.minPriceChargingType);
        if (
          currentProfile?.profileType === "VENUE_PROFILE" ||
          currentProfile?.profileType === "VIRTUAL_VENUE_PROFILE"
        ) {
          if (actPayment.forRent) {
            setValue("forRentOrNot", "For rent");
          } else {
            setValue("forRentOrNot", "Not for rent");
          }
        }
      })
      .catch(() => {});
  }, []);

  return (
    <form onSubmit={handleSubmit(handleUpdateActPayment)}>
      <SaveBackButtonContainers loading={loading} croute="/edit-payment" />
      <Box className="lg:!max-w-[650px] lg:!mx-auto md:!mx-auto lg:!mt-28 !mt-20 md:!max-w-xl !max-w-full !mx-4 ">
        <CreateProfileTitle
          title={
            currentProfile?.profileType === "ACT_PROFILE" ||
            currentProfile?.profileType === "VIRTUAL_ACT_PROFILE"
              ? t("actPayment")
              : s("venuePayment")
          }
        >
          <PaymentSvg className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
        </CreateProfileTitle>
        {/** Act profile form */}
        <EditActPaymentForm
          register={register}
          control={control}
          errors={errors}
          watch={watch}
          setValue={setValue}
        />
      </Box>
    </form>
  );
};

export default EditActPayment;
