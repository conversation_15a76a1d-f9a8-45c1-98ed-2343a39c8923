"use client";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import React, { useEffect } from "react";
import Rider from "@/assets/svg/act-type.svg/Rider.svg";
import { Box } from "@mui/material";
import { Loader } from "@/component";
import EditActRiderForm from "@/containers/edit-act-forms/edit-act-forms.rider/edit-act-forms.rider.containers";
import { showSnackbar } from "@/utils/snackbar.utils";
import { useDispatch } from "react-redux";
import { uploadRiderPdf, getRiderPdf, deleteRiderPdf } from "@/store/slice/act/act.slice";
import { useTranslations } from "next-intl";
import SaveBackButtonContainers from "@/containers/edit-act-forms/save-back-button.containers";

const EditRider = ({ params }) => {
  const s = useTranslations("editSidebar");
  const profileId = params.id;
  const dispatch = useDispatch();
  const [loading, setLoading] = React.useState(true);

  useEffect(() => {
    dispatch(getRiderPdf(profileId))
      .unwrap()
      .then(() => {
        setLoading(false);
      });
  }, []);

  const handleUploadMedia = (formData) => {
    setLoading(true);
    dispatch(uploadRiderPdf({ file: formData, profileId }))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setLoading(false);
          //setFetch((fetch) => fetch + 1);
          dispatch(getRiderPdf(profileId))
            .unwrap()
            .then(() => {
              setLoading(false);
            });
        }
      })
      .catch((error) => {
        setLoading(false);
        showSnackbar(error, "error");
      });
  };

  const handleDeleteMedia = (file) => {
    setLoading(true);
    dispatch(deleteRiderPdf({ file: file, profileId }))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setLoading(false);
          //setFetch((fetch) => fetch + 1);
          dispatch(getRiderPdf(profileId))
            .unwrap()
            .then(() => {
              setLoading(false);
            });
        }
      })
      .catch((error) => {
        setLoading(false);
        showSnackbar(error, "error");
      });
  };

  if (loading) {
    return <Loader />;
  }

  return (
    <>
      <SaveBackButtonContainers loading={loading} />
      <Box className="lg:!max-w-md lg:!mx-auto md:!mx-auto lg:!mt-28 !mt-20 md:!max-w-xl !max-w-full !mx-4 ">
        <CreateProfileTitle title={s("rider")}>
          <Rider className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
        </CreateProfileTitle>
        {/** Act profile form */}
        <EditActRiderForm
          handleUploadMedia={handleUploadMedia}
          profileId={profileId}
          handleDeleteMedia={handleDeleteMedia}
        />
      </Box>
    </>
  );
};

export default EditRider;
