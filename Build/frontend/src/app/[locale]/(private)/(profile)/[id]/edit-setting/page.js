"use client";

import SaveBackButtonContainers from "@/containers/edit-act-forms/save-back-button.containers";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { Box } from "@mui/material";
import SettingSvg from "@/assets/svg/Setting.svg";
import EditSettingForm from "@/common/edit-setting/edit-setting.common";
import { useTranslations } from "next-intl";
import { useSelector } from "react-redux";

const EditSetting = ({}) => {
  const t = useTranslations("settings");
  const currentUser = useSelector((state) => state.login.currentUser);

  return (
    <>
      <SaveBackButtonContainers loading={false} croute="/edit-setting" />
      <Box className="lg:!max-w-lg lg:!mx-auto md:!mx-auto lg:!mt-28 !mt-20 md:!max-w-xl !max-w-full !mx-4 ">
        <CreateProfileTitle title={t("settings")}>
          <SettingSvg className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
        </CreateProfileTitle>
        <EditSettingForm currentUser={currentUser} />
      </Box>
    </>
  );
};

export default EditSetting;
