import ActProfile from "@/assets/svg/ActProfile.svg";
import MusicGenreSvg from "@/assets/svg/act-type.svg/MusicGenre.svg";
import ActMediaSvg from "@/assets/svg/act-type.svg/ActMediaSvg.svg";
import ActLocation from "@/assets/svg/ActLocation.svg";
import PaymentSvg from "@/assets/svg/act-type.svg/PaymentSvg.svg";
import Rider from "@/assets/svg/act-type.svg/Rider.svg";
import AvailabilitySchedule from "@/assets/svg/act-type.svg/AvailabilitySchedule.svg";
import DistributionList from "@/assets/svg/act-type.svg/DistributionList.svg";
import Star from "@/assets/svg/act-type.svg/Star.svg";
import SettingsSvg from "@/assets/svg/Setting.svg";

export const editSidebarDataAct = [
  {
    id: 0,
    title: "Act’s Info",
    icon: <ActProfile />,
    path: "/edit-information",
  },
  {
    id: 1,
    title: "Act’s Bio",
    icon: <ActProfile />,
    path: "/edit-info-person",
  },
  {
    id: 2,
    title: "Act’s Entertainment Type",
    icon: <ActProfile />,
    path: "/edit-entertainment-type",
  },
  {
    id: 3,
    title: "Act’s Music genre",
    icon: <MusicGenreSvg />,
    path: "/edit-music-genre",
  },
  {
    id: 4,
    title: "Act’s Media",
    icon: <ActMediaSvg />,
    path: "/edit-media",
  },
  {
    id: 5,
    title: "Act’s Location",
    icon: <ActLocation />,
    path: "/edit-location",
  },
  {
    id: 6,
    title: "Price & Payment",
    icon: <PaymentSvg />,
    path: "/edit-payment",
  },
  {
    id: 7,
    title: "Rider",
    icon: <Rider />,
    path: "/edit-rider",
  },
  {
    id: 8,
    title: "Availability schedule",
    icon: <AvailabilitySchedule />,
    path: "/edit-availability-schedule",
  },
  {
    id: 9,
    title: "Distribution list",
    icon: <DistributionList />,
    path: "/edit-distribution-list",
  },
  {
    id: 10,
    title: "Feedback",
    icon: <Star />,
    path: "/edit-feedback",
  },
  {
    id: 11,
    title: "Settings",
    icon: <SettingsSvg />,
    path: "/edit-setting",
  },
];

export const editSidebarDataVenue = [
  {
    id: 0,
    title: "Venue’s Info",
    icon: <ActProfile />,
    path: "/edit-information",
  },
  {
    id: 1,
    title: "Venue’s Bio",
    icon: <ActProfile />,
    path: "/edit-info-person",
  },
  {
    id: 2,
    title: "Venue’s Media",
    icon: <ActMediaSvg />,
    path: "/edit-media",
  },
  {
    id: 3,
    title: "Venue’s Location",
    icon: <ActLocation />,
    path: "/edit-location",
  },
  {
    id: 4,
    title: "Price & Payment",
    icon: <PaymentSvg />,
    path: "/edit-payment",
  },
  {
    id: 5,
    title: "Rider",
    icon: <Rider />,
    path: "/edit-rider",
  },
  {
    id: 6,
    title: "Availability schedule",
    icon: <AvailabilitySchedule />,
    path: "/edit-availability-schedule",
  },
  {
    id: 7,
    title: "Distribution list",
    icon: <DistributionList />,
    path: "/edit-distribution-list",
  },
  {
    id: 8,
    title: "Feedback",
    icon: <Star />,
    path: "/edit-feedback",
  },
  {
    id: 9,
    title: "Settings",
    icon: <SettingsSvg />,
    path: "/edit-setting",
  },
];
