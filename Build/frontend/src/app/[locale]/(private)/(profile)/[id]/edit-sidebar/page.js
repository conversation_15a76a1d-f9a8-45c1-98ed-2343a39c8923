"use client";
import { CommonImage } from "@/component";
import { Box, List, ListItem, ListItemIcon, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { East } from "@mui/icons-material";
// import { editSidebarDataAct, editSidebarDataVenue } from "./edit-sidebar.data.common";
import { usePathname, useRouter } from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
import { useDispatch, useSelector } from "react-redux";
import { getActByProfileId } from "@/store/slice/act/act.slice";
import ActProfile from "@/assets/svg/ActProfile.svg";
import MusicGenreSvg from "@/assets/svg/act-type.svg/MusicGenre.svg";
import ActMediaSvg from "@/assets/svg/act-type.svg/ActMediaSvg.svg";
import ActLocation from "@/assets/svg/ActLocation.svg";
import PaymentSvg from "@/assets/svg/act-type.svg/PaymentSvg.svg";
import Rider from "@/assets/svg/act-type.svg/Rider.svg";
import AvailabilitySchedule from "@/assets/svg/act-type.svg/AvailabilitySchedule.svg";
import DistributionList from "@/assets/svg/act-type.svg/DistributionList.svg";
import Star from "@/assets/svg/act-type.svg/Star.svg";
import SettingsSvg from "@/assets/svg/Setting.svg";

const EditSidebar = () => {
  const t = useTranslations("editSidebar");
  const s = useTranslations("settings");

  const editSidebarDataAct = [
    {
      id: 0,
      title: t("actInfo"),
      icon: <ActProfile />,
      path: "/edit-information",
    },
    {
      id: 1,
      title: t("actBio"),
      icon: <ActProfile />,
      path: "/edit-info-person",
    },
    {
      id: 2,
      title: t("actEntertainmentType"),
      icon: <ActProfile />,
      path: "/edit-entertainment-type",
    },
    {
      id: 3,
      title: t("actMusicGenre"),
      icon: <MusicGenreSvg />,
      path: "/edit-music-genre",
    },
    {
      id: 4,
      title: t("actMedia"),
      icon: <ActMediaSvg />,
      path: "/edit-media",
    },
    {
      id: 5,
      title: t("actLocation"),
      icon: <ActLocation />,
      path: "/edit-location",
    },
    {
      id: 6,
      title: t("price&Payment"),
      icon: <PaymentSvg />,
      path: "/edit-payment",
    },
    {
      id: 7,
      title: t("rider"),
      icon: <Rider />,
      path: "/edit-rider",
    },
    {
      id: 8,
      title: t("availability"),
      icon: <AvailabilitySchedule />,
      path: "/edit-availability-schedule",
    },
    {
      id: 9,
      title: t("distribution"),
      icon: <DistributionList />,
      path: "/edit-distribution-list",
    },
    {
      id: 10,
      title: t("feedback"),
      icon: <Star />,
      path: "/edit-feedback",
    },
    {
      id: 11,
      title: s("settings"),
      icon: <SettingsSvg />,
      path: "/edit-setting",
    },
    {
      id: 12,
      title: "Special Events",
      icon: <SettingsSvg />,
      path: "/special-events",
    },
  ];

  const editSidebarDataVenue = [
    {
      id: 0,
      title: t("venueInfo"),
      icon: <ActProfile />,
      path: "/edit-information",
    },
    {
      id: 1,
      title: t("venueBio"),
      icon: <ActProfile />,
      path: "/edit-info-person",
    },
    {
      id: 2,
      title: t("venueMedia"),
      icon: <ActMediaSvg />,
      path: "/edit-media",
    },
    {
      id: 3,
      title: t("venueLocation"),
      icon: <ActLocation />,
      path: "/edit-location",
    },
    {
      id: 4,
      title: t("price&Payment"),
      icon: <PaymentSvg />,
      path: "/edit-payment",
    },
    {
      id: 5,
      title: t("rider"),
      icon: <Rider />,
      path: "/edit-rider",
    },
    {
      id: 6,
      title: t("availability"),
      icon: <AvailabilitySchedule />,
      path: "/edit-availability-schedule",
    },
    {
      id: 7,
      title: t("distribution"),
      icon: <DistributionList />,
      path: "/edit-distribution-list",
    },
    {
      id: 8,
      title: t("feedback"),
      icon: <Star />,
      path: "/edit-feedback",
    },
    {
      id: 9,
      title: s("settings"),
      icon: <SettingsSvg />,
      path: "/edit-setting",
    },
    {
      id: 10,
      title: "Special Events",
      icon: <SettingsSvg />,
      path: "/special-events",
    },
  ];

  const lang = useLocale();
  const pathName = usePathname();
  const profileId = pathName.split("/")[2];
  const router = useRouter();
  const dispatch = useDispatch();
  const [sideBarData, setSideBarData] = useState(editSidebarDataAct);

  useEffect(() => {
    dispatch(getActByProfileId(profileId))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
        }
      })
      .catch(() => {});
  }, []);

  const { currentProfile } = useSelector((state) => state.act);

  useEffect(() => {
    if (
      currentProfile?.profileType === "VENUE_PROFILE" ||
      currentProfile?.profileType === "VIRTUAL_VENUE_PROFILE"
    ) {
      setSideBarData(editSidebarDataVenue);
    }
    if (
      currentProfile?.profileType === "ACT_PROFILE" ||
      currentProfile?.profileType === "VIRTUAL_ACT_PROFILE"
    ) {
      setSideBarData(editSidebarDataAct);
      if (currentProfile && currentProfile?.skillsDto?.entertainmentType?.name !== "Live Music") {
        setSideBarData((prev) => prev.filter((data) => data.id !== 3));
      }
      if (currentProfile && currentProfile?.profileType === "VIRTUAL_ACT_PROFILE") {
        setSideBarData((prev) => prev.filter((data) => data.id == 0 || data.id == 5));
      }
    }
  }, [currentProfile]);

  return (
    <Box className="!fixed lg:w-[310px] z-[40] w-full !top-[64px] !bg-[--bg-color] !left-0 !bottom-0 lg:!border-r lg:!border-r-[--divider-color] !overflow-auto">
      <Box className=" border-b !p-6 border-b-[--divider-color]">
        {/* <CommonImage src={ProfilePic} alt="image" /> */}
        {currentProfile?.mediaDto?.imageUrls?.length > 0 && (
          <CommonImage
            src={currentProfile?.mediaDto?.imageUrls[0]}
            alt="image"
            width={64}
            height={64}
            className="rounded-full w-14 h-14 object-cover border border-[--divider-color]"
          />
        )}
        <Typography className="text-[--text-color] text-2xl CraftworkGroteskMedium">
          {currentProfile?.profileDto?.profileName}
        </Typography>
        <Typography className="text-[--text-color] text-sm CraftworkGroteskRegular">
          {t("profileDetail")}
        </Typography>
      </Box>
      <List sx={{ paddingTop: 0 }}>
        {sideBarData.map((data) => (
          // <Link key={data.id} href={`/${lang}/${profileId}${data.path}`}

          // >
          <ListItem
            onClick={() => {
              router.push(`/${lang}/${profileId}/${data.path}`);
            }}
            key={data.id}
            className={`flex justify-between items-center cursor-pointer px-4 py-[18px] border-b border-b-[--divider-color] ${
              pathName === data.path ? "!bg-[--footer-bg]" : "!bg-[--bg-color]"
            }`}
          >
            <Box className="flex gap-x-3 items-center">
              <ListItemIcon className="text-2xl" sx={{ minWidth: 0 }}>
                {data.icon}
              </ListItemIcon>
              <Typography className="text-[--text-color] text-sm font-craftWorkRegular">
                {data.title}
              </Typography>
            </Box>
            <East className="text-[--text-color] text-xl" />
          </ListItem>
          // </Link>
        ))}
      </List>
    </Box>
  );
};

export default EditSidebar;
