"use client";
import { Button, CommonImage } from "@/component";
import { Box, CssBaseline, TextField, Typography } from "@mui/material";
import React from "react";
import FeedbackImage from "@/assets/png/FeedbackImage.png";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import { Close, East } from "@mui/icons-material";
import LeaveFeedbackSvg from "@/assets/svg/LeaveFeedbackSvg.svg";
import DistributionImage from "@/assets/png/DistributionImage.png";
import PublicSvg from "@/assets/svg/PublicSvg.svg";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

const LeaveFeedback = () => {
  const t = useTranslations("leaveFeedback");
  const router = useRouter();

  const feedbackHandler = () => {
    router.push(`/${params.locale}/profiles`);
  };

  return (
    <Box className="!flex !flex-row !w-full !min-h-screen">
      <CssBaseline />
      <Box className="lg:basis-1/2 w-full relative">
        <ProfileNavbar
          onClick={feedbackHandler}
          pageNumber="3/7"
          tag={t("leaveFeedback")}
          className=" !h-[64px] !bg-[--bg-color] !fixed !top-0 !left-0 lg:!right-[50%] !right-0"
        >
          {" "}
          <Typography className="!text-[--text-color] !normal-case !text-sm CraftworkGroteskHeavy !underline">
            {t("cancel")}
          </Typography>
          <Close className="!text-[--text-color] !text-base" />
        </ProfileNavbar>
        <Box className="!px-5 !pt-32 !max-w-2xl">
          <LeaveFeedbackSvg className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
          <Box className="!flex !justify-between !items-center">
            <Typography className="!text-[--text-color] lg:!text-2xl !text-lg CraftworkGroteskMedium !py-4">
              Reply on the feedback
            </Typography>
            <Box className="!flex !gap-x-2 !items-center">
              <CommonImage src={DistributionImage} alt="image" />
              <Typography className="!text-sm !text-[--text-color] CraftworkGroteskHeavy">
                [who left the feedback]
              </Typography>
            </Box>
          </Box>
          <Box className="!border !border-[--divider-color] !bg-[--footer-bg] !rounded-[4px] !w-full !p-5">
            <Box className="!flex !gap-x-2 !items-center">
              <PublicSvg className="!text-base" />
              <Typography className="!text-sm !text-[--text-color] CraftworkGroteskRegular">
                {t("public")}
              </Typography>
            </Box>
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular !py-4">
              The Sonic Thunder&apos;s electrifying performance had everyone on their feet, making
              them a must-book band for any event.
            </Typography>
            <Typography className="!text-[--hide-color] !text-sm CraftworkGroteskRegular">
              an hour ago
            </Typography>
          </Box>
          <Typography className="!text-[--text-color] !text-lg CraftworkGroteskMedium !pt-5">
            Reply
          </Typography>
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular !py-3">
            This reply would be visible on your profile page
          </Typography>
          <TextField
            type="text"
            size="small"
            multiline
            rows={5}
            placeholder="Feedback message"
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& .MuiOutlinedInput-root": {
                color: "var(--text-color)",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            className="!border !w-full !h-[180px] !py-1 CraftworkGroteskRegular !border-[--text-color] rounded-[2px] !mb-24"
          />
        </Box>
        <Box className="!border-t !border-t-[--divider-color] !fixed !left-0 !right-0 lg:!right-[50%] !bottom-0 !bg-[--bg-color] !flex !justify-end">
          <Button
            className="!bg-[--text-color] !flex !gap-x-4 !py-2 !my-4 !mr-8"
            sx={{
              border: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
          >
            <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
              Send
            </Typography>
            <East className="!text-xl !text-[--bg-color]" />
          </Button>
        </Box>
      </Box>
      <Box className="!hidden lg:!block lg:basis-1/2">
        <CommonImage
          src={FeedbackImage}
          alt="feedback-image"
          className="w-1/2 lg:!block !hidden overflow-hidden object-center h-full fixed"
        />
      </Box>
    </Box>
  );
};

export default LeaveFeedback;
