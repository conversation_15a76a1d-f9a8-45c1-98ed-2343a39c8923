"use client";
import React, { useEffect } from "react";
import { useDispatch } from "react-redux";
import { Loader } from "@/component";
import PdfViewComponent from "@/component/pdf/pdf-view.component";
import { viewPdf } from "@/store/slice/act/act.slice";
import { useRouter, useSearchParams } from "next/navigation";

const PdfView = ({ params }) => {
  const profileId = params.id;
  const [pdfUrl, setPdfUrl] = React.useState("");
  const dispatch = useDispatch();
  // eslint-disable-next-line
  const [loading, setLoading] = React.useState(false);
  const router = useRouter();
  // get query params from url in next/navigation
  const [searchParams] = useSearchParams();

  useEffect(() => {
    dispatch(viewPdf({ profileId, fileName: searchParams[1] }))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          let pdfUrlResponse = response.data;

          // pdf url is as blob and assign to iframe
          const file = new Blob([pdfUrlResponse], { type: "application/pdf" });
          pdfUrlResponse = URL.createObjectURL(file);
          setPdfUrl(pdfUrlResponse);
        }
      })
      .catch(() => {
        router.push(`/${params.locale}/profiles`);
      });
  }, []);

  if (loading) {
    return <Loader />;
  }
  return (
    <>
      <PdfViewComponent pdfUrl={pdfUrl} />
    </>
  );
};

export default PdfView;
