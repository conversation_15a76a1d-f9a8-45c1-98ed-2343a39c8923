"use client";
import { Box, Drawer, IconButton } from "@mui/material";

import { useState } from "react";
import { Close, Menu } from "@mui/icons-material";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import EditSidebar from "../edit-sidebar/page";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";

const SpecialEventsLayout = () => {
  const [isSidebar, setIsSidebar] = useState(false);

  const toggleSidebar = () => setIsSidebar(!isSidebar);
  const router = useRouter();
  const lang = useLocale();
  return (
    <>
      {/* Desktop Navbar */}
      <Box className="!hidden lg:!inline">
        <ProfileNavbar className="h-[64px] !fixed !top-0 !right-0 !left-0 !bg-[--bg-color] !z-20">
          <IconButton
            onClick={() => router.push(`${lang}/profiles`)}
            className="lg:pr-12  !rounded-full !p-2"
          >
            <Close className="!text-[--text-color] !text-base w-6 h-6" />
          </IconButton>
        </ProfileNavbar>
      </Box>

      {/* Mobile Header */}
      <Box className="!flex !justify-between lg:!hidden fixed right-0 left-0 z-20 bg-[--bg-color] top-0 !p-4">
        <IconButton onClick={toggleSidebar}>
          <Menu className="text-2xl text-[--text-color]" />
        </IconButton>
        <IconButton onClick={() => router.back()} className="lg:pr-20  !rounded-full !p-2">
          <Close className="!text-[--text-color] !text-base w-6 h-6" />
        </IconButton>
      </Box>

      {/* Desktop Sidebar */}
      <Box className="lg:block hidden">
        <EditSidebar />
      </Box>

      {/* Mobile Sidebar Drawer */}
      <Drawer
        open={isSidebar}
        anchor="left"
        onClose={toggleSidebar}
        sx={{
          "& .MuiDrawer-paper": {
            marginTop: "72px",
            width: "100%",
          },
        }}
      >
        <EditSidebar />
      </Drawer>
    </>
  );
};

export default SpecialEventsLayout;
