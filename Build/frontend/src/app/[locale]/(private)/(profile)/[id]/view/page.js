"use client";
import Navbar from "@/common/navbar/navbar.common";
import Sidebar from "@/common/sidebar/sidebar.common";
import ActReviewSlider from "@/ui/act-review-slider/act-review-slider.ui";
import { Box, IconButton, Typography } from "@mui/material";
import React, { useEffect } from "react";
import { useDispatch } from "react-redux";
import ActDetail from "@/ui/act-detail/act-detail.ui";
import { Loader } from "@/component";
import MobileFooter from "@/common/footer/mobile.footer.common";
import { getActByProfileId } from "@/store/slice/act/act.slice";
import { Close, KeyboardArrowLeft } from "@mui/icons-material";
import Link from "next/link";
import { useLocale, useTranslations } from "next-intl";
import { resetBookingData } from "@/store/slice/booking/booking.slice";
import { useRouter } from "next/navigation";

const Act = ({ params }) => {
  const t = useTranslations("rider");
  //const s = useTranslations("act");
  const lang = useLocale();
  const profileId = params.id || "";
  const dispatch = useDispatch();
  const [actDataById, setActDataById] = React.useState({});
  const [loading, setLoading] = React.useState(true);
  const router = useRouter();
  const canGoBack = typeof window !== "undefined" && window.history.length > 1;

  /** get Act Data */
  useEffect(() => {
    dispatch(resetBookingData());
    if (profileId) {
      dispatch(getActByProfileId(profileId))
        .unwrap()
        .then((response) => {
          if (response.status === 200) {
            setActDataById(response.data.data);
            setLoading(false);
            //reset((prev) => ({ ...prev, ...actLocation }));
          }
        })
        .catch(() => {});
    }
  }, []);

  if (loading) {
    return <Loader />;
  }
  return (
    <>
      <Box className="fixed lg:block hidden !z-30 !left-0 !right-0 !top-0">
        <Navbar />
      </Box>
      <Box className=" lg:hidden flex justify-between pt-4 pr-4">
        <Box className="flex items-center">
          <Link href={`/${lang}/profiles`}>
            <IconButton>
              <KeyboardArrowLeft className="!text-[--text-color] !text-lg" />
            </IconButton>
          </Link>
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
            {t("view")}
          </Typography>
        </Box>
        {/* <Box className="flex gap-2 items-center">
          <IconButton sx={{ padding: 0 }}>
            <LikeIcon className="!text-xl" />
          </IconButton>
          <IconButton sx={{ padding: 0 }}>
            <ShareIcon className="text-2xl" />
          </IconButton>
          <Button
            sx={{
              border: 0,
              padding: 0,
              "&.MuiButtonBase-root": {
                color: "transparent !important",
              },
            }}
            className="flex gap-1 !normal-case py-0 "
          >
            <Typography className="text-sm text-[--text-color] underline CraftworkGroteskHeavy">
              {s("follow")}
            </Typography>
            <AddCalender className="text-xl" />
          </Button>
        </Box> */}
      </Box>
      <Box className="!hidden lg:!block">
        <Sidebar />
      </Box>
      <Box className=" h-full lg:mt-24 mt-5">
        <Box className="lg:!pl-[120px] !pl-4">
          <Box className="flex justify-between items-start">
            <Typography className="Sora400 !text-[--text-color] !text-xs !mb-6">
              {t("view")} / <span>{actDataById?.profileDto?.profileName}</span>
            </Typography>
            {canGoBack && (
              <IconButton onClick={() => router.back()} className="lg:pr-20">
                <Close className="!text-[--text-color] !text-base" />
              </IconButton>
            )}
          </Box>

          <Box className="w-full">
            <Box className="!flex !gap-4 lg:!w-[98%] !w-[96%]">
              <ActReviewSlider images={actDataById?.mediaDto?.imageUrls} />
            </Box>
          </Box>
          <ActDetail data={actDataById} profileId={profileId} />
        </Box>
        <Box className="lg:pl-24 pl-0 lg:pb-0 pb-6">
          <MobileFooter className="my-16 px-8 py-6 bg-[--bg-color]" />
        </Box>
      </Box>
    </>
  );
};

export default Act;
