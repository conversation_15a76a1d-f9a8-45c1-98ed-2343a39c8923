"use client";
import { Clear, East } from "@mui/icons-material";
import { Box, Button, Typography } from "@mui/material";
import Link from "next/link";
import VirtualActPng from "@/assets/png/VirtualActPng.png";
import ActPng from "@/assets/png/act-success.png";
import VirtualVenuetPng from "@/assets/png/virtual-venue-success.png";

import React, { useEffect } from "react";
import VirtualAct from "@/assets/svg/VirtualAct.svg";
import { CommonImage, Loader } from "@/component";
import { useLocale, useTranslations } from "next-intl";
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { publishActProfileInfo, resetPreviewData } from "@/store/slice/act/act.slice";
import { usePathname, useRouter } from "next/navigation";
import { getActByProfileId } from "@/store/slice/act/act.slice";
import { removeLocalStorage } from "@/utils";

const VirtualProfileSuccess = () => {
  const t = useTranslations("leaveFeedback");
  const p = useTranslations("profileSuccess");
  const lang = useLocale();
  const { showSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const router = useRouter();
  const pathName = usePathname();
  const profileId = pathName.split("/")[2];

  useEffect(() => {
    dispatch(getActByProfileId(profileId))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setLoading(false);
        }
      })
      .catch(() => {
        setLoading(false);
      });
  }, []);

  const { currentProfile } = useSelector((state) => state.act);

  let message = "";
  let scrImage = VirtualActPng;
  if (currentProfile?.profileType === "ACT_PROFILE") {
    message = p("profileCreated");
    scrImage = ActPng;
  } else if (currentProfile?.profileType === "VENUE_PROFILE") {
    scrImage = ActPng;
    message = p("profileCreated");
  } else if (currentProfile?.profileType === "VIRTUAL_VENUE_PROFILE") {
    scrImage = VirtualVenuetPng;
    message = p("virtualVenueCreated");
  } else {
    scrImage = ActPng;
    message = p("virtualActCreated");
  }

  // eslint-disable-next-line
  const [loading, setLoading] = React.useState(true);
  const handlePublishHandler = () => {
    setLoading(true);
    dispatch(publishActProfileInfo({ profileId }))
      .unwrap()
      .then(() => {
        removeLocalStorage("profileId");
        removeLocalStorage("previewData");
        dispatch(resetPreviewData());
        router.push(`/${lang}/profiles`);
        setLoading(false);
        //(isMobile && currentProfile?.profileStatus !== 'STATUS_PUBLISHED')?router.push(`/${params.locale}/${profileId}/edit-info-person`):router.push(`/${params.locale}/profiles`);
      })
      .catch((error) => {
        setLoading(false);
        showSnackbar(error, "error");
      });
  };

  if (loading) {
    return <Loader />;
  }

  return (
    <Box className="lg:!flex !flex-row !w-full !min-h-screen">
      <Box className="lg:basis-1/2 w-full ">
        {/* <ProfileNavbar
          tag="Create Profile"
          pageNumber="2/4"
          className=" h-[64px] !fixed !top-0 lg:!right-[50%] !right-0 !left-0  !bg-[--bg-color] !z-20"
        /> */}
        <Box className="max-w-3xl lg:pt-60 pt-5 lg:px-10 px-5">
          <CommonImage
            src={scrImage}
            alt="virtual-act-success"
            className="lg:w-1/2 w-full lg:hidden rounded-[1px] mb-10 object-center lg:h-full h-[240px]"
          />
          <VirtualAct className="w-12 h-12" />
          <Typography className="text-[--text-color] text-2xl font-craftWorkMedium pt-5">
            {p("success")}
            <br />
            {message}
          </Typography>
          {/* <Typography className="text-[--text-sm text-[--text-color] CraftworkGroteskGX pt-10">
            You can review it in the{" "}
            <Link href={`/${lang}/`}>
              <span className="underline">Act page</span>
            </Link>
            .
          </Typography> */}
        </Box>
        <Box className="border-t border-t-[--divider-color] fixed right-0 lg:right-[50%] left-0 z-20 bottom-0 bg-[--bg-color] px-8 h-[70px] items-center flex justify-between">
          <Link href={`/${lang}/profiles`}>
            <Button className="!normal-case flex gap-x-2">
              <Typography className="text-[--text-color] underline text-sm CraftworkGroteskHeavy">
                {t("cancel")}
              </Typography>
              <Clear className="text-xl text-[--text-color]" />
            </Button>
          </Link>
          <Button
            onClick={handlePublishHandler}
            className="!bg-[--text-color] flex gap-1 !normal-case"
          >
            {/* {loading ? (
              <CircularProgress size={24} className="!text-black" />
            ) : (
              <>
                <Typography className="text-[--bg-color] text-sm CraftworkGroteskHeavy">
                  Publish
                </Typography>
                <East className="text-[--bg-color]" />
              </>
            )} */}

            <Typography className="text-[--bg-color] text-sm CraftworkGroteskHeavy">
              {p("publish")}
            </Typography>
            <East className="text-[--bg-color]" />
          </Button>
        </Box>
      </Box>
      <Box className="lg:!block hidden lg:basis-1/2">
        <CommonImage
          src={VirtualActPng}
          alt="virtual-act-success"
          className="lg:w-1/2 w-full px-5 lg:px-0 object-center h-full lg:fixed"
        />
      </Box>
    </Box>
  );
};

export default VirtualProfileSuccess;
