"use client";
import { CommonImage } from "@/component";
import React from "react";
import ActInfoPng from "@/assets/png/ActInfoPng.png";
import { Box, Typography } from "@mui/material";
import ActProfile from "@/assets/svg/ActProfile.svg";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import ActInfoForm from "@/containers/act-forms/act-forms.info/act-forms.info.containers";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import { Close } from "@mui/icons-material";
import { useTranslations } from "next-intl";
import { useSelector } from "react-redux";
import VenueProfile from "@/assets/svg/VenueProfile.svg";
const ActInformation = () => {
  const t = useTranslations("actInformation");
  const s = useTranslations("CreateProfiles");
  const p = useTranslations("venue");
  const { previewData } = useSelector((state) => state.act);
  return (
    <Box className="!grid !grid-cols-2">
      <Box className="lg:!col-span-1 !col-span-2">
        <ProfileNavbar
          pageNumber="1/7"
          tag={t("CreateProfile")}
          className=" h-[64px] lg:right-[50%]"
        >
          {" "}
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
            {t("saveUnpublished")}
          </Typography>
          <Close className="!text-[--text-color] !text-base" />
        </ProfileNavbar>
        <Box className="lg:!px-12 md:!px-12 !px-4 !pt-24 ">
          <CreateProfileTitle
            title={
              previewData?.profile?.option === s("Act")
                ? t("ActInformation")
                : p("venueInformation")
            }
          >
            {previewData?.profile?.option === s("Act") ? (
              <ActProfile className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
            ) : (
              <VenueProfile className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
            )}
          </CreateProfileTitle>
          {/** Act profile form */}
          <ActInfoForm />
        </Box>
      </Box>
      <Box className="!hidden lg:!block lg:basis-1/2 ">
        <CommonImage
          src={ActInfoPng}
          alt="image"
          className="w-1/2 lg:!block !hidden object-center h-full fixed"
          priority
        />
      </Box>
    </Box>
  );
};

export default ActInformation;
