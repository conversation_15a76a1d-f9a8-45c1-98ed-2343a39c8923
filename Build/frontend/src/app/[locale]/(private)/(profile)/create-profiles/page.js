"use client";
import { Button, CommonImage } from "@/component";
import {
  Box,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  useMediaQuery,
  Typography,
  useTheme,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import CreateProfilesPng from "@/assets/png/CreateProfilesPng.png";
import { Close, East, West } from "@mui/icons-material";
import ListOfProfiles from "@/assets/svg/ListOfProfiles.svg";
import RadioButtonUncheckedIcon from "@mui/icons-material/RadioButtonUnchecked";
import ActProfileSvg from "@/assets/svg/ActProfile.svg";
import VenueProfileSvg from "@/assets/svg/VenueProfile.svg";
import classNames from "classnames";
import RadioButtonIcon from "@/assets/svg/RadioButton.svg";
import Link from "next/link";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import { useSelector, useDispatch } from "react-redux";
import { setPreviewData } from "@/store/slice/act/act.slice";
import { useLocale, useTranslations } from "next-intl";
import { getLocalStorage, removeLocalStorage } from "@/utils";
import { resetPreviewData } from "@/store/slice/act/act.slice";

const CreateProfiles = () => {
  const t = useTranslations("CreateProfiles");
  const s = useTranslations("profileFooter");
  const lang = useLocale();
  const dispatch = useDispatch();
  const { previewData } = useSelector((state) => state.act);
  const [selectedItem, setSelectedItem] = useState(
    (getLocalStorage("profileId") && previewData?.profile) || null,
  );

  useEffect(() => {
    // if (!getLocalStorage("profileId")) {
    removeLocalStorage("profileId");
    removeLocalStorage("previewData");
    setSelectedItem(null);
    dispatch(resetPreviewData());
    // }
  }, []);
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));

  const options = [
    {
      id: 0,
      option: t("Act"),
      description: t("ActProfile"),
      icon: <ActProfileSvg className="lg:!w-12 lg:!h-12 !w-[25px] !h-[25px]" />,
      path: "/act-information",
      isDisabled: false,
    },
    {
      id: 1,
      option: t("Venue"),
      description: t("VenueProfile"),
      icon: <VenueProfileSvg className="lg:!w-12 lg:!h-12  !w-[25px] !h-[25px]" />,
      path: "/act-information",
      isDisabled: false,
    },
    {
      id: 2,
      option: t("ServiceProvider"),
      description: t("VenueProfile"),
      isDisabled: true,
    },
    {
      id: 3,
      option: t("Talent"),
      description: t("VenueProfile"),
      isDisabled: true,
    },
  ];

  const handleListItemClick = (item) => {
    if (!item.isDisabled) {
      setSelectedItem(item.id === selectedItem ? null : item);
      dispatch(setPreviewData({ profile: item }));
    }
  };

  return (
    <Box className="!grid !grid-cols-2">
      <Box className="lg:!col-span-1 !col-span-2">
        <ProfileNavbar
          pageNumber="1/5"
          tag={t("createProfile")}
          className=" h-[64px] lg:right-[50%] !bg-[--bg-color]"
        >
          {" "}
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
            {t("saveUnpublished")}
          </Typography>
          <Close className="!text-[--text-color] !text-base" />
        </ProfileNavbar>
        <Box className="lg:!px-12 md:!px-12 !px-4 !pt-24">
          <CreateProfileTitle title={t("WantToCreate")}>
            <ListOfProfiles className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
          </CreateProfileTitle>
          <List sx={{ padding: 0 }} className="lg:!mt-12 md:!mt-12 !mb-28">
            {options.map((data) => (
              <Link key={data.id} href={data}>
                <ListItem
                  disabled={data.isDisabled}
                  className={classNames(
                    "!border !rounded-[4px] !max-w-[750px] !mb-4 !flex !justify-between lg:!p-6 md:!p-6 !p-4",
                    {
                      "cursor-not-allowed": data.isDisabled,
                      "cursor-pointer": !data.isDisabled,
                      "border-[--inprogress-color]": selectedItem?.id === data.id,
                      "border-[--divider-color]": selectedItem?.id !== data.id,
                    },
                  )}
                  onClick={() => handleListItemClick(data)}
                >
                  <Box className="!flex !gap-x-4">
                    <ListItemIcon sx={{ minWidth: "1px" }}>
                      {selectedItem?.id === data.id ? (
                        <RadioButtonIcon className="!w-6 !h-6" />
                      ) : (
                        <RadioButtonUncheckedIcon className=" !text-[--text-color]" />
                      )}
                    </ListItemIcon>
                    <Box>
                      <Typography className="!text-[--text-color] !text-lg CraftworkGroteskMedium">
                        {data.option}
                      </Typography>
                      <Typography className="!text-[--text-color] !break-words !text-sm CraftworkGroteskRegular !pt-2">
                        {isSmallScreen
                          ? "Create the Act profile for future events"
                          : data.description}
                      </Typography>
                    </Box>
                  </Box>
                  {data.isDisabled === false ? (
                    <IconButton sx={{ padding: 0 }} className="!mb-[18px] !w-12 !h-12">
                      {data?.icon}
                    </IconButton>
                  ) : (
                    <Button
                      sx={{ border: 0, minWidth: 120 }}
                      disabled
                      className="bg-[--divider-color] !break-words !mb-10 !text-[--inprogress-color]  !text-sm CraftworkGrotestRegular !border !border-[--divider-color] !rounded-full !normal-case"
                    >
                      {t("ComingSoon")}
                    </Button>
                  )}
                </ListItem>
              </Link>
            ))}
          </List>
        </Box>
        <Box className="!flex lg:!justify-end !justify-between !fixed !bottom-0 !h-[79px] lg:!right-[50%] !right-0 !bg-[--bg-color] !left-0 !border-t !border-t-[--divider-color] !z-20 !py-5 lg:!px-12 md:!px-12 !px-4">
          <Link href="/create-profiles" className="!flex !items-center lg:!hidden !gap-x-3">
            <West className="!text-[--text-color]" />
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
              {s("Back")}
            </Typography>
          </Link>
          <Link
            href={selectedItem ? `/${lang}${selectedItem.path}` : ""}
            style={{ pointerEvents: selectedItem ? "auto" : "none" }}
          >
            <Button
              disabled={selectedItem === null ? true : false}
              className={classNames("!px-4 !py-2", {
                "!bg-[--disabled-color]": !selectedItem,
                "!bg-[--text-color]": selectedItem,
              })}
              sx={{
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
            >
              <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                {s("Next")}
              </Typography>
              <East className="!text-[--bg-color] !ml-3" />
            </Button>
          </Link>
        </Box>
      </Box>
      <Box className="!hidden lg:!block lg:basis-1/2 ">
        <CommonImage
          src={CreateProfilesPng}
          alt="image"
          className="w-1/2 lg:!block !hidden object-center h-full fixed"
        />
      </Box>
    </Box>
  );
};

export default CreateProfiles;
