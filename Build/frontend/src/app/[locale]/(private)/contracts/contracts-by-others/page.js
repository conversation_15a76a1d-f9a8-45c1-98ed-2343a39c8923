"use client";
import Navbar from "@/common/navbar/navbar.common";
import Sidebar from "@/common/sidebar/sidebar.common";
import ScreenViewTitle from "@/common/title/screen-view.title.common";
import { Box } from "@mui/material";
import React from "react";
import { useLocale, useTranslations } from "next-intl";
import ContractsCard from "@/common/contracts-card/contracts-card.common";
import { useDispatch } from "react-redux";
import { otherContracts } from "@/store/slice/booking/booking.slice";
import { useEffect, useState } from "react";
import { Loader } from "@/component";
import MobileNavbar from "@/common/navbar/mobile-navbar.common";
import MobileViewFilter from "@/common/mobile-view-filter/mobile-view-filter.common";
import usePaginate from "@/hooks/usePaginate";

const ContractByOthers = () => {
  const lang = useLocale();
  const t = useTranslations("contracts.contractByMe");
  const [loading, setLoading] = useState(true);
  const dispatch = useDispatch();
  const links = [
    { id: 0, path: `/${lang}/contracts/contracts-by-me`, text: "Generated by me" },
    {
      id: 1,
      path: `/${lang}/contracts/contracts-by-others`,
      text: "Generated by others",
    },
  ];
  const [search, setSearch] = useState("");
  const [contractByMeData, setContractByMeData] = useState([]);
  const { pageNo } = usePaginate();
  const page = pageNo - 1 || 0;
  const size = 6;

  useEffect(() => {
    dispatch(otherContracts({ page, size }))
      .unwrap()
      .then((response) => {
        setContractByMeData(response?.data?.data);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  }, [page]);

  if (loading) {
    return <Loader />;
  }
  return (
    <>
      <Box className="lg:!fixed lg:!z-30 !left-0 !right-0 !top-0">
        <Navbar />
      </Box>
      {/* <MobileViewTitle title={t("contracts")} /> */}
      <Box className="!flex lg:!px-0 !px-4">
        <Box className="!hidden lg:!block">
          <Sidebar />
        </Box>
        <Box className="!inline lg:!hidden">
          <MobileNavbar />
        </Box>
        <Box className="lg:!pl-32 !pl-0 lg:!px-10 lg:!pt-24 !w-full">
          <ScreenViewTitle primaryText={t("contracts")} links={links} />
          <Box className="lg:!py-0 !py-4">
            <MobileViewFilter
              value={search}
              onChange={(event) => setSearch(event.target.value)}
              placeholder={t("keyword")}
            />
          </Box>
          <Box className="!py-6 !hidden lg:!inline">
            {/* <ContractsFilter eventRequest="1024" /> */}
          </Box>
          <ContractsCard contracts={contractByMeData} />
        </Box>
      </Box>
    </>
  );
};

export default ContractByOthers;
