import ProfileImage from "@/assets/png/Image.png";

export const eventData = [
  {
    id: 0,
    eventName: "Event name",
    date: "Oct 18, 2023   | 7PM, EST",
    venue: "Venue Name",
    location: "Location address",
    src: ProfileImage,
    profileName: "Profile (MY act, talent, sp)",
    isTicket: false,
  },
  {
    id: 1,
    eventName: "Event name",
    date: "Oct 18, 2023   | 7PM, EST",
    venue: "Venue Name",
    location: "Location address",
    src: ProfileImage,
    profileName: "Profile (MY act, talent, sp)",
    isTicket: false,
  },
  {
    id: 2,
    eventName: "Event name",
    date: "Oct 18, 2023   | 7PM, EST",
    venue: "Venue Name",
    location: "Location address",
    src: ProfileImage,
    profileName: "Profile (MY act, talent, sp)",
    isTicket: true,
  },
  {
    id: 3,
    eventName: "Event name",
    date: "Oct 18, 2023   | 7PM, EST",
    venue: "Venue Name",
    location: "Location address",
    src: ProfileImage,
    profileName: "Profile (MY act, talent, sp)",
    isTicket: false,
  },
  {
    id: 4,
    eventName: "Event name",
    date: "Oct 18, 2023   | 7PM, EST",
    venue: "Venue Name",
    location: "Location address",
    src: ProfileImage,
    profileName: "Profile (MY act, talent, sp)",
    isTicket: false,
  },
  {
    id: 5,
    eventName: "Event name",
    date: "Oct 18, 2023   | 7PM, EST",
    venue: "Venue Name",
    location: "Location address",
    src: ProfileImage,
    profileName: "Profile (MY act, talent, sp)",
    isTicket: false,
  },
  {
    id: 6,
    eventName: "Event name",
    date: "Oct 18, 2023   | 7PM, EST",
    venue: "Venue Name",
    location: "Location address",
    src: ProfileImage,
    profileName: "Profile (MY act, talent, sp)",
    isTicket: true,
  },
  {
    id: 7,
    eventName: "Event name",
    date: "Oct 18, 2023   | 7PM, EST",
    venue: "Venue Name",
    location: "Location address",
    src: ProfileImage,
    profileName: "Profile (MY act, talent, sp)",
    isTicket: false,
  },
  {
    id: 8,
    eventName: "Event name",
    date: "Oct 18, 2023   | 7PM, EST",
    venue: "Venue Name",
    location: "Location address",
    src: ProfileImage,
    profileName: "Profile (MY act, talent, sp)",
    isTicket: false,
  },
  {
    id: 9,
    eventName: "Event name",
    date: "Oct 18, 2023   | 7PM, EST",
    venue: "Venue Name",
    location: "Location address",
    src: ProfileImage,
    profileName: "Profile (MY act, talent, sp)",
    isTicket: false,
  },
];

export const editinEventData = [
  {
    id: 0,
    month: "Oct",
    date: "18",
    name: "Event name",
    day: "Tueaday Oct 18, 2023 at 7PM, EST",
    progress: "In Progress",
    taskDone: "4/6",
    value: "67",
  },
  {
    id: 1,
    month: "Oct",
    date: "18",
    name: "Event name",
    day: "Tueaday Oct 18, 2023 at 7PM, EST",
    progress: "In Progress",
    taskDone: "4/6",
    value: "67",
  },
  {
    id: 2,
    month: "Oct",
    date: "18",
    name: "Event name",
    day: "Tuesday Oct 18, 2023 at 7PM  | repeat every Friday",
    progress: "In Progress",
    taskDone: "4/6",
    value: "67",
  },
  {
    id: 3,
    month: "Oct",
    date: "18",
    name: "Event name",
    day: "Tueaday Oct 18, 2023 at 7PM, EST",
    progress: "In Progress",
    taskDone: "4/6",
    value: "67",
  },
  {
    id: 4,
    month: "Oct",
    date: "18",
    name: "Event name",
    day: "Tuesday Oct 18, 2023 at 7PM, EST",
    progress: "In Progress",
    taskDone: "4/6",
    value: "67",
  },
  {
    id: 5,
    month: "Oct",
    date: "18",
    name: "Event name",
    day: "Tueaday Oct 18, 2023 at 7PM, EST",
    progress: "In Progress",
    taskDone: "4/6",
    value: "67",
  },
  {
    id: 6,
    month: "Oct",
    date: "18",
    name: "Event name",
    day: "Tueaday Oct 18, 2023 at 7PM, EST",
    progress: "In Progress",
    taskDone: "4/6",
    value: "67",
  },
];
