"use client";
import Navbar from "@/common/navbar/navbar.common";
import Sidebar from "@/common/sidebar/sidebar.common";
import EventCard from "@/ui/event-card/event-card.ui";
import { Typography, Box } from "@mui/material";
import { useEffect, useState } from "react";
import EditingEventsCard from "@/ui/editing-events-card/editing-events-card.ui";
import MobileNavbar from "@/common/navbar/mobile-navbar.common";
// import MobileViewTitle from "@/common/title/mobile-view.title.common";
import { useTranslations } from "next-intl";
import { useDispatch } from "react-redux";
import { eventList } from "@/store/slice/booking/booking.slice";

const Dashboard = () => {
  const t = useTranslations("dashboard");
  //const events = ["Events in progress", "Eventa done", "Events pending"];

  const page = 0;
  const size = 1000;
  const [publishedEvent, setPublishedEvent] = useState([]);
  const dispatch = useDispatch();
  const [editEventData, setEditEventData] = useState([]);

  useEffect(() => {
    const fetchEvents = async (status, setState) => {
      try {
        const response = await dispatch(eventList({ status, page, size })).unwrap();
        setState(response?.data?.data);
      } catch (error) {
        //eslint-disable-next-line
        console.log(error);
      }
    };

    fetchEvents("STATUS_PUBLISHED", setPublishedEvent);
    fetchEvents("STATUS_UNPUBLISHED", setEditEventData);
  }, []);

  // const handleLogout = () => {
  //   dispatch(logout())
  //     .unwrap()
  //     .then(() => {
  //       showSnackbar("Logout successfully", "success");
  //       router.replace(`/${lang}`);
  //     })
  //     .catch((error) => {
  //       showSnackbar(error, "error");
  //     });
  // };
  return (
    <Box className="!flex !flex-col">
      {/* <Button
        className="!bg-[--text-color] !m-6 !py-3"
        onClick={handleLogout}
        sx={{
          minWidth: 100,
          maxWidth: 0,
          border: 0,
          "&.MuiButtonBase-root": {
            color: "white !important",
          },
        }}
      >
        <Typography className="!normal-case CraftworkGroteskHeavy !w-full !text-sm !leading-[15.4px] !text-[--bg-color]">
          Log out
        </Typography>
      </Button> */}
      {/* <Link href="/change-password" className="!mx-6">
        <Button
          className="!bg-[--text-color] !py-3"
          sx={{
            border: 0,
            "&.MuiButtonBase-root": {
              color: "white !important",
            },
          }}
        >
          <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
            Change password
          </Typography>
        </Button>
      </Link> */}

      {/* <Link href="/create-profiles" className="!mx-6 mt-3">
        <Button
          className="!bg-[--text-color] !py-3"
          sx={{
            border: 0,
            "&.MuiButtonBase-root": {
              color: "white !important",
            },
          }}
        >
          <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
            Create Profile
          </Typography>
        </Button>
      </Link> */}
      <Box className="lg:!fixed lg:!z-30 !left-0 !right-0 !top-0">
        <Navbar />
      </Box>
      {/* <MobileViewTitle title="Search" /> */}
      <Box className="!flex !pb-24 lg:!pb-0">
        <Box className="!hidden lg:!block">
          <Sidebar />
        </Box>
        <Box className="!inline lg:!hidden">
          <MobileNavbar />
        </Box>
        {/* <Box className="lg:!pl-36 !pl-4 lg:!pt-24 !w-full"> */}
        <Box className="lg:!pl-44 lg:!pr-8 sm:px-8 !pl-4 lg:!pt-24 !w-full">
          <Box className="!flex !pr-8">
            {publishedEvent?.content?.length > 0 && (
              <Typography className="!text-[--text-color] !text-2xl CraftworkGroteskMedium">
                {t("ThisWeek")}
              </Typography>
            )}
            {/* {publishedEvent?.content?.length > 0 && (
              <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                {publishedEvent?.content?.length}
              </Typography>
            )} */}
          </Box>
          <EventCard eventData={publishedEvent?.content} />
          <Box className="lg:!flex lg:!justify-between !py-5 !pr-8">
            {editEventData?.content?.length > 0 && (
              <Typography className="!text-[--text-color] !text-2xl CraftworkGroteskMedium">
                {t("continue")}
              </Typography>
            )}
            {/* <Dropdown
              options={events}
              onSelect={handleSelectEvent}
              selectedValue={selectedEvent}
              title="Events in progress"
              className="!text-[--text-color]"
            /> */}
          </Box>
          <EditingEventsCard editEventData={editEventData} />
        </Box>
      </Box>
    </Box>
  );
};
export default Dashboard;
