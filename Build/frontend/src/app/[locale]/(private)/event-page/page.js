"use client";
import LogoComponent from "@/common/logo-component/logo-component.common";
import MobileNavbar from "@/common/navbar/mobile-navbar.common";
import Navbar from "@/common/navbar/navbar.common";
import Sidebar from "@/common/sidebar/sidebar.common";
import { Button } from "@/component";
import { Add, Clear, Filter, SouthEast } from "@mui/icons-material";
import { Box, IconButton, InputAdornment, TextField, Typography } from "@mui/material";
import OutlinedUser from "@/assets/svg/OutlinedUser.svg";
import { useLocale } from "next-intl";
import Link from "next/link";
import React, { useState } from "react";
import OutlinedSearch from "@/assets/svg/OutlinedSearch.svg";
// import EventCard from "@/ui/event-card/event-card.ui";
import MobileViewFilter from "@/common/mobile-view-filter/mobile-view-filter.common";
import EventCard from "@/common/profile-card/event-card.common ";

const EventPaage = () => {
  const lang = useLocale();
  //eslint-disable-next-line
  const [search, setSearch] = useState("");
  const [keyword, setKeyword] = useState("");

  return (
    <div>
      <Box className="!fixed lg:!block !hidden !z-30 !left-0 !right-0 !top-0">
        <Navbar />
      </Box>
      <Box className="!flex">
        <Box className="!hidden lg:!block">
          <Sidebar />
        </Box>
        <Box className="!inline lg:!hidden">
          <MobileNavbar />
        </Box>
        <Box className="lg:!pl-32 !pl-0 lg:!px-10 lg:!pt-24 !pt-5 !w-full">
          <Box className="!flex !px-4 items-center lg:justify-end justify-between">
            <Box className="ml-4 mb-2 lg:hidden inline">
              <Link href={`/${lang}`}>
                {/* <Logo className="!w-[70px] !h-6" /> */}
                <LogoComponent />
              </Link>
            </Box>
            <Box className="lg:!hidden !flex !items-center !gap-x-3">
              <Link href={`/${lang}/create-profiles`}>
                <Button className="!flex !items-center !gap-x-2 !py-2" sx={{ minWidth: 0 }}>
                  <Typography className="!normal-case !underline CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--text-color]">
                    {/* {t("newProfile")} */} Add Event
                  </Typography>
                  <SouthEast className=" !text-[--text-color] !text-2xl" />
                </Button>
              </Link>
              <Link href={`/${lang}/user/account-information`}>
                <OutlinedUser className="!w-6 !h-6 !cursor-pointer" />{" "}
              </Link>
            </Box>
            <Box className="!hidden lg:!flex !gap-x-3">
              <TextField
                size="small"
                placeholder="search"
                className="Sora500 !text-[--text-color] !border !border-[--text-color] !rounded-[4px]"
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end" style={{ cursor: "pointer" }}>
                      {search && (
                        <Clear
                          className="!text-[--text-color] !text-base"
                          onClick={() => {
                            startSearchNow.current = true;
                            setKeyword("");
                          }}
                        />
                      )}
                    </InputAdornment>
                  ),
                  startAdornment: (
                    <InputAdornment position="start" style={{ cursor: "pointer" }}>
                      <OutlinedSearch className="!text-2xl" />
                    </InputAdornment>
                  ),
                }}
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                    fontFamily: "var(--craftWorkRegular)",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  border: 0,
                }}
                value={search}
                onChange={(event) => {
                  startSearchNow.current = true;
                  setKeyword(event.target.value);
                }}
              />
              <Link href={`/${lang}/create-profiles`}>
                <Button className="!bg-[--text-color] !gap-x-4 !py-2" sx={{ minWidth: 0 }}>
                  <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                    Add Event
                  </Typography>
                  <Add className="!text-[--bg-color] !text-2xl" />
                </Button>
              </Link>
            </Box>
          </Box>
          <Box className="flex lg:hidden gap-4 justify-between px-6">
            <TextField
              size="small"
              placeholder="search"
              className="Sora500 !text-[--text-color] !border !border-[--text-color] !rounded-[4px]"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end" style={{ cursor: "pointer" }}>
                    {search && (
                      <Clear
                        className="!text-[--text-color] !text-base"
                        onClick={() => {
                          startSearchNow.current = true;
                          setKeyword("");
                        }}
                      />
                    )}
                  </InputAdornment>
                ),
                startAdornment: (
                  <InputAdornment position="start" style={{ cursor: "pointer" }}>
                    <OutlinedSearch className="!text-2xl" />
                  </InputAdornment>
                ),
              }}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              value={keyword || search}
              onChange={(event) => {
                startSearchNow.current = true;
                setKeyword(event.target.value);
              }}
            />
            <IconButton className="lg:hidden">
              <Filter className="!text-2xl" />
            </IconButton>
          </Box>
          <Box className="!px-4">
            <MobileViewFilter
              value={keyword || search}
              onChange={(event) => {
                startSearchNow.current = true;
                setKeyword(event.target.value);
              }}
              placeholder="Search your profiles"
            />
          </Box>
          <Typography className="!text-[--text-color] pl-4 pt-4 !text-2xl CraftworkGroteskMedium">
            Events
          </Typography>
          <Box className="!mt-4 !pb-20 lg:!pb-0 !px-4 lg:!px-0">
            <EventCard />
          </Box>
        </Box>
      </Box>
    </div>
  );
};

export default EventPaage;
