"use client";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import { Box, IconButton, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import ContractSidebar from "../contract-sidebar/page";
import { <PERSON><PERSON>, Loader } from "@/component";
import { Close, SouthEast } from "@mui/icons-material";
import ActInfoCard from "@/common/act-info-card/act-info-card.common";
import { useParams, useRouter } from "next/navigation";
import { getActEventInfo } from "@/store/slice/booking/booking.slice";
import { useDispatch } from "react-redux";
import { useLocale } from "next-intl";
import { getLocalStorage } from "@/utils";

const ActInfo = () => {
  const dispatch = useDispatch();
  const [actInfoData, setActInfoData] = useState(null);
  const [loading, setLoading] = useState(true);
  const params = useParams();
  const eventId = params?.id;
  const router = useRouter();
  const lang = useLocale();
  const [contractData, setContractData] = useState(null);

  useEffect(() => {
    if (eventId) {
      dispatch(getActEventInfo(eventId))
        .unwrap()
        .then((response) => {
          if (response.status === 200) {
            setActInfoData(response.data.data);
            setContractData({
              contractId:
                [response?.data?.data?.primaryContractId] ?? response?.data?.data?.actContractIds,
              contractStatus: "",
            });
          }
        })
        .catch(() => {})
        .finally(() => setLoading(false));
    } else {
      setLoading(false);
    }
  }, [eventId, dispatch]); // Ensure `dispatch` is included as a dependency

  return (
    <div className="">
      <ProfileNavbar
        isSaveUnPublished={true}
        tag="Edit Event"
        className=" h-[64px] !fixed !top-0 !right-0 !left-0  !bg-[--bg-color] !z-20"
      />
      {/* <MobileViewTitle title={t("contracts")} /> */}
      <Box className="!flex lg:!px-0 !px-4">
        <Box className="!hidden lg:!block">
          <ContractSidebar />
        </Box>
        <Box className="lg:!w-full lg:!mx-auto md:!mx-auto lg:!mt-28 !mt-20 md:!max-w-xl !max-w-full !mx-4 ">
          {loading ? (
            <Loader />
          ) : (
            <>
              <Box className="fixed lg:top-24 top-5 max-sm:z-20 right-5 lg:right-12">
                <IconButton
                  onClick={() => {
                    if (getLocalStorage("redirect") === "dashboard") {
                      router.push(`/${lang}/dashboard`);
                    } else {
                      router.push(`/${lang}/contracts/contracts-by-me`);
                    }
                  }}
                >
                  <Close className="text-[--text-color] text-base" />
                </IconButton>
              </Box>
              {/* <MainInfoCard /> */}
              <ActInfoCard data={actInfoData} type="Act" contractData={contractData} />
            </>
          )}
        </Box>
      </Box>
      <Box className="flex justify-end relative pr-12 py-8">
        <Button
          className="flex fixed right-10 bottom-8 gap-2 !bg-[--text-color] !normal-case"
          onClick={() => router.push(`/${lang}/event/${eventId}/publish`)}
        >
          <Typography className="text-[--bg-color] text-sm font-craftWorkHeavy">Save</Typography>
          <SouthEast className="text-[--bg-color] size-5" />
        </Button>
      </Box>
    </div>
  );
};

export default ActInfo;
