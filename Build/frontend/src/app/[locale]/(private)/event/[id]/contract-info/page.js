"use client";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import { Box, IconButton, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import ContractSidebar from "../contract-sidebar/page";
import { <PERSON><PERSON>, Loader } from "@/component";
import { Close, SouthEast } from "@mui/icons-material";
import ContractInfoCard from "@/common/contract-info-card/contract-info-card.common";
import ContractInfoDilog from "@/common/contract-info-dialog/contract-info-dialog.common";
import { useParams, useRouter } from "next/navigation";
import { getContractInfo } from "@/store/slice/booking/booking.slice";
import { useDispatch } from "react-redux";
import { useLocale } from "next-intl";
import { getLocalStorage } from "@/utils";

const ContractInfo = () => {
  const [open, setOpen] = useState(false);
  const toggleSortDrawer = (newOpen) => {
    setOpen(newOpen);
  };
  //eslint-disable-next-line
  const [fetch, setFetch] = useState(0);
  const params = useParams();
  const eventId = params?.id;
  const dispatch = useDispatch();
  const [contractByMeData, setContractByMeData] = useState([]);
  const lang = useLocale();
  const router = useRouter();
  const [actContractDetailsList, setActContractDetailsList] = useState([]);

  const [loading, setLoading] = React.useState(true);

  useEffect(() => {
    if (eventId || fetch)
      dispatch(getContractInfo(eventId))
        .unwrap()
        .then((response) => {
          if (response.status === 200) {
            setActContractDetailsList(response.data.data.actContractDetailsList ?? []);
            setContractByMeData(
              response?.data?.data?.actContractDetailsList
                ? [
                    ...response.data.data.actContractDetailsList, // Spread existing array elements
                    response.data.data.primeContractDetails, // Add primeContractDetails
                  ]
                : [response.data.data.primeContractDetails], // If actContractDetailsList is null or undefined, start with primeContractDetails
            );
            setLoading(false);
          }
        })
        .catch(() => {
          setLoading(false);
          //showSnackbar(error, "error");
        });
  }, [eventId, fetch]);
  if (loading) {
    return <Loader />;
  }
  return (
    <div className="">
      <ProfileNavbar
        isSaveUnPublished={true}
        tag="Edit Event"
        className=" h-[64px] !fixed !top-0 !right-0 !left-0  !bg-[--bg-color] !z-20"
      />
      {/* <MobileViewTitle title={t("contracts")} /> */}
      <Box className="!flex lg:!px-0 !px-4">
        <Box className="!hidden lg:!block">
          <ContractSidebar />
        </Box>
        <Box className="lg:!w-full lg:!mx-auto md:!mx-auto lg:!mt-28 !mt-20 md:!max-w-[750px] !max-w-full !mx-4 ">
          <Box className="fixed lg:top-24 top-5 max-sm:z-20 right-5 lg:right-12">
            <IconButton
              onClick={() => {
                if (getLocalStorage("redirect") === "dashboard") {
                  router.push(`/${lang}/dashboard`);
                } else {
                  router.push(`/${lang}/contracts/contracts-by-me`);
                }
              }}
            >
              <Close className="text-[--text-color] text-base" />
            </IconButton>
          </Box>
          {/* <MainInfoCard /> */}
          <Button
            onClick={() => toggleSortDrawer(true)}
            className="!bg-[--text-color] !normal-case py-2"
          >
            <Typography className="text-sm text-[--bg-color] font-craftWorkHeavy">
              Add Contract
            </Typography>
          </Button>
          <ContractInfoCard contracts={contractByMeData} />
        </Box>
        <ContractInfoDilog
          open={open}
          handleClose={() => toggleSortDrawer(false)}
          eventId={eventId}
          actContractDetailsList={actContractDetailsList || []}
          setFetch={setFetch}
        />
      </Box>
      <Box className="flex justify-end relative pr-12 py-8">
        <Button
          className="flex fixed right-10 bottom-8 gap-2 !bg-[--text-color] !normal-case"
          onClick={() => router.push(`/${lang}/event/${eventId}/venue-info`)}
        >
          <Typography className="text-[--bg-color] text-sm font-craftWorkHeavy">Save</Typography>
          <SouthEast className="text-[--bg-color] size-5" />
        </Button>
      </Box>
    </div>
  );
};

export default ContractInfo;
