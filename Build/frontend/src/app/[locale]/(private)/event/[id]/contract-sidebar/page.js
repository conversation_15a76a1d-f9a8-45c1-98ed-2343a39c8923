"use client";
import { East } from "@mui/icons-material";
import { Box, List, ListItem, Typography } from "@mui/material";
import { useLocale } from "next-intl";
import { useParams, usePathname } from "next/navigation";
import React, { useEffect } from "react";
import CalendarIcon from "@/assets/svg/CalenderIcon.svg";
import { getCurrentEvent, setEventInfo } from "@/store/slice/booking/booking.slice";
import { useDispatch, useSelector } from "react-redux";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import Link from "next/link";
dayjs.extend(utc);
dayjs.extend(timezone);

const ContractSidebar = () => {
  //const [eventInfo, setEventInfo] = useState("");

  //const events = ["InProgress", "New", "Cancelled"];
  // const handleSelectEvent = (value) => {
  //   setSelectedEvent(value);
  // };
  const lang = useLocale();
  const pathName = usePathname();
  // const router = useRouter();
  const params = useParams();
  const eventId = params?.id;
  const dispatch = useDispatch();
  const eventInfo = useSelector((state) => state.booking.eventInfo);
  const formatDateTime = (dateString) => {
    const date = dayjs(dateString).tz("America/Toronto"); // Replace with your desired timezone
    return date.format("MMM D, YYYY | h A z");
  };

  useEffect(() => {
    //if (eventInfo !== null && params?.id) {
    dispatch(getCurrentEvent(params?.id))
      .unwrap()
      .then((response) => {
        setEventInfo(response?.data?.data);
      })
      .catch(() => {});
    //}
  }, [params?.id]);

  const sideBarData = [
    {
      id: 0,
      title: "Main info",
      path: "main-info",
    },
    {
      id: 1,
      title: "Media",
      path: "edit-media",
    },
    {
      id: 2,
      title: "Promotional Materials",
      path: "promotional-materials",
    },
    {
      id: 3,
      title: "Contracts info",
      path: "contract-info",
    },
    {
      id: 4,
      title: "Venue info",
      path: "venue-info",
    },
    {
      id: 5,
      title: "Acts info",
      path: "act-info",
    },
    {
      id: 6,
      title: "Publish",
      path: "publish",
    },
  ];

  if (!eventInfo) {
    return null;
  }
  return (
    <Box className="!fixed z-10 lg:w-[310px] w-full !top-[64px] !bg-[--bg-color] !left-0 !bottom-0 lg:!border-r lg:!border-r-[--divider-color] !overflow-auto">
      <Box className=" border-b !p-6 border-b-[--divider-color]">
        {/* <CommonImage src={ProfilePic} alt="image" /> */}
        {/* {currentProfile?.mediaDto?.imageUrls?.length > 0 && (
        <CommonImage
          src={currentProfile?.mediaDto?.imageUrls[0]}
          alt="image"
          width={64}
          height={64}
          className="rounded-full w-14 h-14 object-cover border border-[--divider-color]"
        />
      )} */}
        {/* <Typography className="text-[--text-color] text-2xl CraftworkGroteskMedium">
        {currentProfile?.profileDto?.profileName}
      </Typography> */}
        {/* <Dropdown
          options={events}
          onSelect={handleSelectEvent}
          selectedValue={selectedEvent}
          title="InProgress"
          className="!text-[--text-color]"
        /> */}
        <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy">
          {eventInfo?.eventName}
        </Typography>
        <Box className="flex gap-3 items-center py-3">
          <CalendarIcon className="size-6" />
          <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
            {formatDateTime(eventInfo?.scheduleTime?.startDate)}
          </Typography>
        </Box>
        <Box />
      </Box>
      <List sx={{ paddingTop: 0 }}>
        {sideBarData.map((data) => (
          <Link key={data.id} href={`/${lang}/event/${eventId}/${data.path}`}>
            <ListItem
              // onClick={() => {
              //   router.push(`/${lang}/event/${eventId}/${data.path}`);
              // }}
              className={`flex justify-between items-center cursor-pointer px-4 py-[18px] border-b border-b-[--divider-color] ${
                pathName.includes(data.path) ? "!bg-[--footer-bg]" : "!bg-[--bg-color]"
              }`}
            >
              <Box className="flex gap-x-3 items-center">
                {/* <ListItemIcon className="text-2xl" sx={{ minWidth: 0 }}>
              {data.icon}
            </ListItemIcon> */}
                <Typography className="text-[--text-color] text-sm font-craftWorkRegular">
                  {data.title}
                </Typography>
              </Box>
              <East className="text-[--text-color] text-xl" />
            </ListItem>
          </Link>
        ))}
      </List>
    </Box>
  );
};

export default ContractSidebar;
