"use client";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import React, { useEffect, useState } from "react";
import ActMediaSvg from "@/assets/svg/act-type.svg/ActMediaSvg.svg";
import { Box, IconButton, Typography } from "@mui/material";
// import SaveBackButtonContainers from "@/containers/edit-act-forms/save-back-button.containers";
import EditEventMediaForm from "@/containers/edit-act-forms/edit-act-forms.media/edit-event-forms.media.containers";
import { useParams, useRouter } from "next/navigation";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import ContractSidebar from "../contract-sidebar/page";
import { Button, Loader } from "@/component";
import { Close, SouthEast } from "@mui/icons-material";
import { useLocale } from "next-intl";
import { getLocalStorage } from "@/utils";
import { useDispatch } from "react-redux";
import { getCurrentEvent } from "@/store/slice/booking/booking.slice";

const EditActMedia = () => {
  const params = useParams();
  const eventId = params?.id;
  const lang = useLocale();
  const router = useRouter();
  const [loading, setLoading] = React.useState(true);
  const [fetch, setFetch] = useState(0);
  const dispatch = useDispatch();
  const [actPhotos, setActPhotos] = useState([]);
  /** get the act media */

  useEffect(() => {
    if (eventId || fetch)
      dispatch(getCurrentEvent(eventId))
        .unwrap()
        .then((response) => {
          if (response.status === 200) {
            const imageUrl = response.data.data.eventMediaInfo?.imageUrls;
            setActPhotos(imageUrl);
            setLoading(false);
          }
        })
        .catch(() => {
          //showSnackbar(error, "error");
        });
  }, [eventId, fetch]);

  if (loading) {
    return <Loader />;
  }
  return (
    <>
      {/* <SaveBackButtonContainers loading={false} /> */}
      <ProfileNavbar
        isSaveUnPublished={true}
        tag="Edit Event"
        className=" h-[64px] !fixed !top-0 !right-0 !left-0  !bg-[--bg-color] !z-20"
      />
      {/* <MobileViewTitle title={t("contracts")} /> */}
      <Box className="!flex lg:!px-0 !px-4">
        <Box className="!hidden lg:!block">
          <ContractSidebar />
        </Box>
        <Box className="lg:!max-w-md lg:!mx-auto md:!mx-auto lg:!mt-28 !mt-20 md:!max-w-xl !max-w-full !mx-4 ">
          <Box className="fixed lg:top-24 top-5 max-sm:z-20 right-5 lg:right-12">
            <IconButton
              onClick={() => {
                if (getLocalStorage("redirect") === "dashboard") {
                  router.push(`/${lang}/dashboard`);
                } else {
                  router.push(`/${lang}/contracts/contracts-by-me`);
                }
              }}
            >
              <Close className="text-[--text-color] text-base" />
            </IconButton>
          </Box>
          <CreateProfileTitle title="Event Media">
            <ActMediaSvg className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
          </CreateProfileTitle>
          {/** Act profile form */}
          <EditEventMediaForm
            profileId={params.id}
            setFetch={setFetch}
            fetch={fetch}
            actPhotos={actPhotos}
          />
        </Box>
      </Box>
      <Box
        className="flex justify-end relative pr-12 py-8"
        onClick={() => router.push(`/${lang}/event/${eventId}/promotional-materials`)}
      >
        <Button className="flex fixed right-10 bottom-8 gap-2 !bg-[--text-color] !normal-case">
          <Typography className="text-[--bg-color] text-sm font-craftWorkHeavy">Save</Typography>
          <SouthEast className="text-[--bg-color] size-5" />
        </Button>
      </Box>
    </>
  );
};

export default EditActMedia;
