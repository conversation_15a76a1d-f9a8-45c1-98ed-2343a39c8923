"use client";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import { Box, IconButton } from "@mui/material";
import React from "react";
import ContractSidebar from "../contract-sidebar/page";
import MainInfoCard from "@/common/main-info-card/main-info-card.common";
import { useSelector } from "react-redux";
import { useRouter, useSearchParams } from "next/navigation";
import { Close } from "@mui/icons-material";
import { useLocale } from "next-intl";
import { getLocalStorage, setLocalStorage } from "@/utils";
import { notFound } from "next/navigation";

const MainInfo = () => {
  //const [eventInfo, setEventInfo] = useState([]);
  const router = useRouter();
  const lang = useLocale();
  const searchParams = useSearchParams();
  const redirect = searchParams.get("redirect");
  if (redirect) {
    setLocalStorage("redirect", redirect);
  }
  const eventInfo = useSelector((state) => state?.booking?.eventInfo);
  // useEffect(() => {
  //   dispatch(getCurrentEvent(params?.id))
  //     .unwrap()
  //     .then((response) => {
  //       setEventInfo(response?.data?.data);
  //       setLoading(false);
  //     })
  //     .catch(() => {
  //       setLoading(false);
  //     });

  //   setLocalStorage("redirect", redirect);
  // }, []);

  // if (!eventInfo) {
  //   return <Loader />;
  // }

  if (eventInfo?.editable === false) {
    notFound();
  }
  return (
    <div>
      <ProfileNavbar
        isSaveUnPublished={true}
        tag="Edit Event"
        className=" h-[64px] !fixed !top-0 !right-0 !left-0  !bg-[--bg-color] !z-20"
      />
      {/* <MobileViewTitle title={t("contracts")} /> */}
      <Box className="!flex w-full lg:!px-0 ">
        <Box className="!hidden lg:!block">
          <ContractSidebar />
        </Box>
        <Box className="lg:!max-w-xl lg:!mx-auto md:!mx-auto lg:!mt-28 !mt-20 md:!max-w-xl !max-w-full !mx-4 ">
          <Box className="fixed lg:top-24 top-5 max-sm:z-20 right-5 lg:right-12">
            <IconButton
              onClick={() => {
                if (getLocalStorage("redirect") === "dashboard") {
                  router.push(`/${lang}/dashboard`);
                } else {
                  router.push(`/${lang}/contracts/contracts-by-me`);
                }
              }}
            >
              <Close className="text-[--text-color] text-base" />
            </IconButton>
          </Box>
          <MainInfoCard eventInfo={eventInfo} />
        </Box>
      </Box>
    </div>
  );
};

export default MainInfo;
