"use client";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import { Box, Button, IconButton, Typography } from "@mui/material";
import React, { useEffect } from "react";
import ContractSidebar from "../contract-sidebar/page";
import { Close, SouthEast } from "@mui/icons-material";
import PromotionalMaterialsDetail from "@/common/promotional-materials/promotional-materials.common";
import { useParams, useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { getLocalStorage } from "@/utils";
import { Loader } from "@/component";

const PromotionalMaterials = () => {
  const router = useRouter();
  const lang = useLocale();
  const params = useParams();
  const eventId = params?.id;
  const [loading, setLoading] = React.useState(true);
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return <Loader />;
  }

  return (
    <div>
      <ProfileNavbar
        isSaveUnPublished={true}
        tag="Edit Event"
        className=" h-[64px] !fixed !top-0 !right-0 !left-0  !bg-[--bg-color] !z-20"
      />
      {/* <MobileViewTitle title={t("contracts")} /> */}
      <Box className="!flex w-full lg:!px-0 !px-4">
        <Box className="!hidden lg:!block">
          <ContractSidebar />
        </Box>
        <Box className="lg:!max-w-xl lg:!mx-auto md:!mx-auto lg:!mt-28 !mt-20 md:!max-w-xl !max-w-full !mx-4 ">
          <Box className="fixed lg:top-24 top-5 max-sm:z-20 right-5 lg:right-12">
            <IconButton
              onClick={() => {
                if (getLocalStorage("redirect") === "dashboard") {
                  router.push(`/${lang}/dashboard`);
                } else {
                  router.push(`/${lang}/contracts/contracts-by-me`);
                }
              }}
            >
              <Close className="text-[--text-color] text-base" />
            </IconButton>
          </Box>
          {/* <MainInfoCard eventInfo={eventInfo} /> */}
          <PromotionalMaterialsDetail />
          <Box className="flex justify-end relative pr-12 py-8">
            <Button
              className="flex fixed right-10 bottom-8 gap-2 !bg-[--text-color] !normal-case"
              onClick={() => router.push(`/${lang}/event/${eventId}/contract-info`)}
            >
              <Typography className="text-[--bg-color] text-sm font-craftWorkHeavy">
                Save
              </Typography>
              <SouthEast className="text-[--bg-color] size-5" />
            </Button>
          </Box>
        </Box>
      </Box>
    </div>
  );
};

export default PromotionalMaterials;
