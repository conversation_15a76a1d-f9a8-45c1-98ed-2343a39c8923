"use client";
import React from "react";
import { useDispatch } from "react-redux";
import { Box, IconButton, Typography } from "@mui/material";
import { Button } from "@/component";
import { Close, SouthEast } from "@mui/icons-material";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import ContractSidebar from "../contract-sidebar/page";
import { useParams, useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { publishEvent } from "@/store/slice/booking/booking.slice";
import { getLocalStorage } from "@/utils";

const Publish = () => {
  const dispatch = useDispatch();
  const params = useParams();
  const lang = useLocale();
  const router = useRouter();

  const handlePublish = () => {
    dispatch(publishEvent(params?.id))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          // Redirect to dashboard on successful publish
          //router.push(`/${lang}/dashboard`);
          if (getLocalStorage("redirect") === "dashboard") {
            router.push(`/${lang}/dashboard`);
          } else {
            router.push(`/${lang}/contracts/contracts-by-me`);
          }
        }
      })
      .catch(() => {
        // Optionally show an error message here
      });
  };

  return (
    <div>
      <ProfileNavbar
        isSaveUnPublished={true}
        tag="Edit Event"
        className=" h-[64px] !fixed !top-0 !right-0 !left-0  !bg-[--bg-color] !z-20"
      />
      <Box className="!flex lg:!px-0 !px-4">
        <Box className="!hidden lg:!block">
          <ContractSidebar />
        </Box>
      </Box>
      <Box className="h-screen flex flex-col justify-center items-center">
        <Box className="fixed lg:top-24 top-5 right-5 max-sm:z-20 lg:right-12">
          <IconButton onClick={() => router.push(`/${lang}/contracts/contracts-by-me`)}>
            <Close className="text-[--text-color] text-base" />
          </IconButton>
        </Box>
        <Button
          className="flex gap-2 !bg-[--text-color] py-3 px-6 !normal-case"
          onClick={handlePublish}
        >
          <Typography className="text-[--bg-color] text-sm font-craftWorkHeavy">Publish</Typography>
          <SouthEast className="text-[--bg-color] size-5" />
        </Button>
      </Box>
    </div>
  );
};

export default Publish;
