"use client";
import Navbar from "@/common/navbar/navbar.common";
import { Box, IconButton, Typography } from "@mui/material";
import { useLocale, useTranslations } from "next-intl";
import { useParams, useRouter } from "next/navigation";
import React, { useEffect } from "react";
import { Close, KeyboardArrowLeft } from "@mui/icons-material";
import { Loader } from "@/component";
import Sidebar from "@/common/sidebar/sidebar.common";
// import ActReviewSlider from '@/ui/act-review-slider/act-review-slider.ui';
import MobileFooter from "@/common/footer/mobile.footer.common";
// import ActDetail from '@/ui/act-detail/act-detail.ui';
import MediaImage from "@/assets/png/MediaImage.png";
import MediaImage1 from "@/assets/png/MediaImage1.png";
import MediaImage2 from "@/assets/png/MediaImage2.png";
import MediaImage3 from "@/assets/png/MediaImage3.png";
import EvnetDetailPage from "@/common/event-detail-page/evnet-detail-page.common";
import { getEventsById } from "@/store/slice/booking/booking.slice";
import { useDispatch } from "react-redux";

const ViewEvent = () => {
  const favouritesImages = [MediaImage, MediaImage1, MediaImage2, MediaImage3];
  const t = useTranslations("rider");
  //const s = useTranslations("act");
  const lang = useLocale();
  const [actDataById, setActDataById] = React.useState({});
  const [loading, setLoading] = React.useState(true);
  const router = useRouter();
  const canGoBack = typeof window !== "undefined" && window.history.length > 1;
  const params = useParams();

  const profileId = params?.id;
  const dispatch = useDispatch();
  useEffect(() => {
    if (profileId) {
      dispatch(getEventsById(profileId))
        .unwrap()
        .then((response) => {
          if (response.status === 200) {
            setActDataById(response.data.data);
            setLoading(false);
          }
        })
        .catch(() => {
          router.push(`/${lang}/search?profileType=EVENT_PROFILE&distance=0`);
        });
    }
  }, []);

  if (loading) {
    return <Loader />;
  }
  return (
    <>
      <Box className="fixed lg:block hidden !z-30 !left-0 !right-0 !top-0">
        <Navbar />
      </Box>
      <Box className=" lg:hidden flex justify-between pt-4 pr-4">
        <Box className="flex items-center">
          {canGoBack && (
            <IconButton onClick={() => router.back()}>
              <KeyboardArrowLeft className="!text-[--text-color] !text-lg" />
            </IconButton>
          )}

          {/* <Link href={`/${lang}/profiles`}>
            <IconButton>
              <KeyboardArrowLeft className="!text-[--text-color] !text-lg" />
            </IconButton>
          </Link> */}
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
            {t("view")}
          </Typography>
        </Box>
        <Box className="flex gap-2 items-center">
          {/* <IconButton sx={{ padding: 0 }}>
            <LikeIcon className="!text-xl" />
          </IconButton> */}
          {/* <IconButton sx={{ padding: 0 }}>
            <ShareIcon className="text-2xl" />
          </IconButton> */}
          {/* <Button
            sx={{
              border: 0,
              padding: 0,
              "&.MuiButtonBase-root": {
                color: "transparent !important",
              },
            }}
            className="flex gap-1 !normal-case py-0 "
          >
            <Typography className="text-sm text-[--text-color] underline CraftworkGroteskHeavy">
              {s("follow")}
            </Typography>
            <AddCalender className="text-xl" />
          </Button> */}
        </Box>
      </Box>
      <Box className="!hidden lg:!block">
        <Sidebar />
      </Box>
      <Box className=" h-full lg:mt-24 mt-5">
        <Box className="lg:!pl-[120px] !pl-4">
          <Box className="flex justify-between items-start">
            <Typography className="Sora400 !text-[--text-color] !text-xs !mb-6">
              {t("view")} / <span>Event</span>
            </Typography>
            {canGoBack && (
              <IconButton onClick={() => router.back()} className="lg:pr-20">
                <Close className="!text-[--text-color] !text-base" />
              </IconButton>
            )}
          </Box>

          <Box className="w-full">
            <EvnetDetailPage favouritesImages={favouritesImages} data={actDataById} />
          </Box>

          {/* <ActDetail data={actDataById} profileId={profileId} /> */}
        </Box>
        <Box className="lg:pl-24 pl-0 lg:pb-0 pb-6">
          <MobileFooter className="my-16 px-8 py-6 bg-[--bg-color]" />
        </Box>
      </Box>
    </>
  );
};

export default ViewEvent;
