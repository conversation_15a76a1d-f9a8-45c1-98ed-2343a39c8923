"use client";
import Navbar from "@/common/navbar/navbar.common";
import Sidebar from "@/common/sidebar/sidebar.common";
import ScreenViewTitle from "@/common/title/screen-view.title.common";
import { West } from "@mui/icons-material";
import { Box, Typography } from "@mui/material";
import React, { useEffect } from "react";
import LikeIcon from "@/assets/svg/LikeIcon.svg";
import { Button, Loader } from "@/component";
import Link from "next/link";
import { useLocale, useTranslations } from "next-intl";
import ProfileCard from "@/common/profile-card/profile-card.common";
import { getFavProfile } from "@/store/slice/common/common.slice";
import { useDispatch, useSelector } from "react-redux";
import usePaginate from "@/hooks/usePaginate";

const FavoruitesAct = () => {
  const t = useTranslations("favourites");
  const lang = useLocale();
  const dispatch = useDispatch();
  const { pageNo } = usePaginate();

  const page = pageNo - 1 || 0;
  const size = 12;
  const pageParams = { page, size };
  useEffect(() => {
    dispatch(getFavProfile({ profileType: "ACT_PROFILE", pageParams }));
  }, [page]);

  const { favouritesData, loading: loading } = useSelector((state) => state.common);

  const links = [
    { id: 0, path: `/${lang}/favourites/acts`, text: t("acts") },
    {
      id: 1,
      path: `/${lang}/favourites/venues`,
      text: t("venues"),
    },
    {
      id: 4,
      path: `/${lang}/favourites/saved-searches`,
      text: t("saveSearchs"),
    },
  ];

  if (loading) {
    return <Loader />;
  }

  return (
    <>
      <Box className="!fixed lg:!block !hidden !z-30 !left-0 !right-0 !top-0">
        <Navbar />
      </Box>
      <Box className=" !block lg:!hidden !px-4 !pt-5">
        <Link href={`/${lang}/dashboard`}>
          <Button className="!flex !gap-2 !cursor-pointer !normal-case !pb-8">
            <West className="!text-2xl !text-[--text-color]" />
            <Typography className="!text-sm !text-[--text-color] font-craftWorkHeavy">
              Back
            </Typography>
          </Button>
        </Link>
        <LikeIcon className="!text-2xl" />
        <Typography className="!text-[--text-color] !text-lg font-craftWorkMedium !pt-2">
          {t("favourites")}
        </Typography>
      </Box>
      <Box className="!flex">
        <Box className="!hidden lg:!block">
          <Sidebar />
        </Box>
        <Box className="lg:!pl-32 !pl-0 lg:!px-10 lg:!pt-24 !pt-0 !w-full">
          <ScreenViewTitle primaryText={t("favourites")} links={links} />
          <Box className="!gap-6 !pb-5 lg:!mt-16 !mt-4 !px-4 lg:!px-0">
            {/* <FavroutiesCard favouritesData={favouritesData} /> */}
            <ProfileCard profiles={favouritesData} type="fav" />
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default FavoruitesAct;
