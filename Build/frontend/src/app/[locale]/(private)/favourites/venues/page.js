"use client";
import Navbar from "@/common/navbar/navbar.common";
import Sidebar from "@/common/sidebar/sidebar.common";
import ScreenViewTitle from "@/common/title/screen-view.title.common";
import { Box, Typography } from "@mui/material";
import React, { useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON>, Loader } from "@/component";
import { West } from "@mui/icons-material";
import LikeIcon from "@/assets/svg/LikeIcon.svg";
import { useLocale, useTranslations } from "next-intl";
import { useDispatch, useSelector } from "react-redux";
import ProfileCard from "@/common/profile-card/profile-card.common";
import { getFavProfile } from "@/store/slice/common/common.slice";
import usePaginate from "@/hooks/usePaginate";

const FavouritesVenue = () => {
  const t = useTranslations("favourites");
  const lang = useLocale();
  const dispatch = useDispatch();
  const { pageNo } = usePaginate();

  const page = pageNo - 1 || 0;
  const size = 12;
  const pageParams = { page, size };
  useEffect(() => {
    dispatch(getFavProfile({ profileType: "VENUE_PROFILE", pageParams }));
  }, [page]);

  const { favouritesData, loading: loading } = useSelector((state) => state.common);

  const links = [
    { id: 0, path: `/${lang}/favourites/acts`, text: t("acts") },
    {
      id: 1,
      path: `/${lang}/favourites/venues`,
      text: t("venues"),
    },
    {
      id: 4,
      path: `/${lang}/favourites/saved-searches`,
      text: t("saveSearchs"),
    },
  ];

  if (loading) {
    return <Loader />;
  }

  return (
    <>
      <Box className="!fixed lg:!block !hidden !z-30 !left-0 !right-0 !top-0">
        <Navbar />
      </Box>
      <Box className=" !block lg:!hidden !px-4 !pt-5">
        <Link href={`/${lang}/dashboard`}>
          <Button className="!flex !gap-2 !cursor-pointer !normal-case !pb-8">
            <West className="!text-2xl !text-[--text-color]" />
            <Typography className="!text-sm !text-[--text-color] font-craftWorkHeavy">
              Back
            </Typography>
          </Button>
        </Link>
        <LikeIcon className="!text-2xl" />
        <Typography className="!text-[--text-color] !text-lg font-craftWorkMedium !pt-2">
          {t("favourites")}
        </Typography>
      </Box>
      <Box className="!flex">
        <Box className="!hidden lg:!block">
          <Sidebar />
        </Box>
        <Box className="lg:!pl-32 !pl-0 lg:!px-10 lg:!pt-24 !pt-0 !w-full">
          <ScreenViewTitle primaryText={t("favourites")} links={links} />
          <Box className="!mt-4 !pb-5 !px-4 lg:!px-0">
            <ProfileCard profiles={favouritesData} type="fav" />
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default FavouritesVenue;
