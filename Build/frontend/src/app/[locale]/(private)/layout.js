"use client";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";
import { isTokenExpired, removeLocalStorage } from "@/utils";
import { useLocale } from "next-intl";
import { Loader } from "@/component";
import { reset } from "@/store/slice/auth/login.auth.slice";
import { notificationList, setInstantMessage } from "@/store/slice/common/instantMessage.slice";

import useStompClient from "@/hooks/useStompClient";

const Layout = ({ children }) => {
  const router = useRouter();
  const lang = useLocale();
  const { token, currentUser } = useSelector((state) => state.login);

  const [loading, setLoading] = useState(true);
  const dispatch = useDispatch();
  const stompClient = useStompClient();
  const username = currentUser?.email;

  useEffect(() => {
    if (isTokenExpired(token)) {
      removeLocalStorage("access_token");
      router.replace(`/${lang}/login`);
      dispatch(reset());
    } else {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    dispatch(notificationList());
  }, []);

  useEffect(() => {
    if (stompClient && stompClient.connected) {
      stompClient.subscribe(`/user/${username}/queue/messages`, (message) => {
        const parsedMessage = JSON.parse(message.body);
        //eslint-disable-next-line no-console
        //console.log("Received message:", parsedMessage);
        dispatch(setInstantMessage(parsedMessage));
      });
    }
  }, [username, stompClient]);

  // const handleSendMessage = () => {
  //   if (stompClient && stompClient.connected) {
  //     const msg = {
  //       sender: username,
  //       receiver: receiver,
  //       content: JSON.stringify(message),
  //       timestamp: new Date().toISOString(),
  //       isSeen: false,
  //     };

  //     const headers = {
  //       Authorization: "Bearer " + getLocalStorage("access_token"),
  //     };

  //     stompClient.send("/app/send/message", headers, JSON.stringify(msg));
  //   }
  //   setMessage("");
  // };

  if (loading) return <Loader />;

  return <>{children}</>;
};
export default Layout;
