"use client";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import React from "react";
import AccountSvg from "@/assets/svg/Account.svg";
import { Box } from "@mui/material";
import AccountInformationForm from "@/containers/account-information-form/account-information-form.containers";
import { useTranslations } from "next-intl";

const AccountInformation = () => {
  const t = useTranslations("accountInformation");
  return (
    <>
      <Box className="max-w-2xl lg:ml-[40%] mx-auto lg:!px-12 md:!px-12 !px-4 lg:!pt-44 !pt-16 ">
        <CreateProfileTitle title={t("accountInfo")}>
          <AccountSvg className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
        </CreateProfileTitle>
        <AccountInformationForm type="account" />
      </Box>
    </>
  );
};

export default AccountInformation;
