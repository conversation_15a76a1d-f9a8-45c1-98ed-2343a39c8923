"use client";
import { Button, CommonImage, Loader } from "@/component";
import { Box, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import BlockListSvg from "@/assets/svg/BlockList.svg";
import { SouthEast } from "@mui/icons-material";
import ActImage from "@/assets/png/ActImage.png";
import UnblockDialog from "@/common/unblock-dialog/unblock-dialog.common";
import { useTranslations } from "next-intl";
import { useDispatch } from "react-redux";
import { getBlockedUsers } from "@/store/slice/act/act.slice";

const BlockList = () => {
  const t = useTranslations("blockList");
  const [open, setOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [blockedUsers, setBlockedUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const dispatch = useDispatch();
  const handleClickOpen = (user) => {
    setSelectedUser(user);
    setOpen(true);
  };

  useEffect(() => {
    dispatch(getBlockedUsers())
      .unwrap()
      .then((res) => {
        setBlockedUsers(res?.data);
        setLoading(false);
      });
  }, [dispatch]);

  const handleClose = () => {
    setSelectedUser(null);
    setOpen(false);
  };

  const handleUnblockSuccess = (profileId) => {
    setBlockedUsers((prevUsers) => ({
      ...prevUsers,
      blockedProfilesInfoList: (prevUsers?.blockedProfilesInfoList || []).filter(
        (user) => user.profileId !== profileId,
      ),
    }));
    handleClose();
  };

  if (loading) return <Loader />;
  return (
    <>
      <Box className="max-w-2xl lg:ml-[40%] mx-auto lg:!px-12 md:!px-12 !px-4 lg:!pt-44 !pt-16 ">
        <CreateProfileTitle title={t("blockList")}>
          <BlockListSvg className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
        </CreateProfileTitle>
        {blockedUsers?.blockedProfilesInfoList?.map((user) => (
          <Box
            key={user.profileId}
            className="bg-[--footer-bg] flex justify-between w-full items-center px-5 py-3 mb-3 border border-[--divider-color] rounded-[4px]"
          >
            <Box className="flex gap-x-5 items-center">
              <CommonImage
                src={user.imageUrls?.[0] || ActImage}
                alt={user.profileName}
                className="w-10 h-10 rounded-full object-cover"
                width={64}
                height={64}
              />
              <Typography className="text-[--text-color] text-sm font-craftWorkRegular">
                {user.profileName}
              </Typography>
            </Box>
            <Button
              className="!normal-case"
              onClick={() => handleClickOpen(user)}
              sx={{
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "transparent !important",
                },
              }}
            >
              <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy underline">
                {t("unblock")}
              </Typography>
              <SouthEast className="text-[--text-color]" />
            </Button>
          </Box>
        ))}
        <UnblockDialog
          open={open}
          handleClose={handleClose}
          type="unblock"
          profileId={selectedUser?.profileId}
          actData={{
            image: selectedUser?.imageUrls?.[0],
            name: selectedUser?.profileName,
          }}
          onSuccess={handleUnblockSuccess}
        />
      </Box>
    </>
  );
};

export default BlockList;
