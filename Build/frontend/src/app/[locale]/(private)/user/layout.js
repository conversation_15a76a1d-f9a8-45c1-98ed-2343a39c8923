"use client";
import MobileNavbar from "@/common/navbar/mobile-navbar.common";
import Navbar from "@/common/navbar/navbar.common";
import Sidebar from "@/common/sidebar/sidebar.common";
import { CommonImage } from "@/component";
import { Box, Drawer, IconButton, Typography } from "@mui/material";
import React, { useState } from "react";
import BlockListImage from "@/assets/png/BlockListImage.png";
import AccountSidebar from "@/common/account-sidebar/account-sidebar.common";
import { Menu, West } from "@mui/icons-material";
import Link from "next/link";
import { useLocale } from "next-intl";

const Layout = ({ children }) => {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const lang = useLocale();

  const handleDrawerClose = () => {
    setDrawerOpen(false);
  };
  return (
    <>
      <Box className="!fixed lg:!block !hidden z-30 !left-0 !right-0 !top-0">
        <Navbar />
      </Box>
      <Box>
        <Box className="lg:!hidden !inline ">
          <IconButton onClick={() => setDrawerOpen(true)} className="pt-4 pl-4">
            <Menu className="text-[--text-color] w-8 h-8" />
          </IconButton>
        </Box>
      </Box>
      <Drawer
        anchor="bottom"
        open={drawerOpen}
        onClose={handleDrawerClose}
        transitionDuration={{ enter: 300, exit: 500 }}
        className="lg:hidden inline"
        sx={{
          "& .MuiPaper-root": {
            width: "100%",
            height: "98%",
            backgroundColor: "var(--bg-color)",
            transition: "transform 0.8s ease-in-out",
          },
        }}
      >
        <Link href={`/${lang}/profiles`} className="mt-8 mx-4 flex gap-2">
          <West className="text-[--text-color] text-lg" />
          <Typography className="underline text-sm font-craftWorkHeavy text-[--text-color]">
            My profile
          </Typography>
        </Link>
        <CommonImage src={BlockListImage} alt="block-list" className="w-full h-[80px] mt-4" />
        <Box onClick={handleDrawerClose}>
          <AccountSidebar />
        </Box>
      </Drawer>
      <Box className="!flex lg:!pt-16 !pt-4 lg:!pb-0">
        <Box className="!hidden lg:!block">
          <Sidebar />
        </Box>
        <Box className="!inline lg:!hidden">
          <MobileNavbar />
        </Box>
        <CommonImage
          src={BlockListImage}
          alt="block-list"
          className="w-full pl-24 fixed z-10 left-0 right-0 h-[140px] lg:inline hidden"
        />
      </Box>
      <Box className="!hidden lg:!block">
        <AccountSidebar />
      </Box>
      {children}
    </>
  );
};
export default Layout;
