import CreateProfileTitle from "@/ui/create-profile-title/create-profile-title.ui";
import { Box } from "@mui/material";
import SettingSvg from "@/assets/svg/Setting.svg";
import React from "react";
import SettingInfo from "@/common/settings/settings.common";

const Settings = () => {
  return (
    <>
      <Box className="max-w-2xl lg:ml-[40%] mx-auto lg:!px-12 md:!px-12 !px-4 lg:!pt-44 !pt-16 ">
        <CreateProfileTitle title="Settings">
          <SettingSvg className="lg:!w-[37px] lg:!h-[37px] md:!w-[37px] md:!h-[37px] !w-[25px] !h-[25px]" />
        </CreateProfileTitle>
        <SettingInfo />
      </Box>
    </>
  );
};

export default Settings;
