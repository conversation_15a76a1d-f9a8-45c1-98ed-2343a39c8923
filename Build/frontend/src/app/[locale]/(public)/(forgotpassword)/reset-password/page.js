import React from "react";
import { Box, Typography } from "@mui/material";
import PasswordRecoveryPng from "@/assets/png/PasswordRecovery.png";
import CommonImage from "@/component/image/image.component";
import Link from "next/link";
import DesktopFooter from "@/common/footer/desktop.footer.common";
import MobileFooter from "@/common/footer/mobile.footer.common";
import ResetPasswordForm from "@/containers/forgot-password/reset-password.container";
import { useTranslations } from "next-intl";
import LogoComponent from "@/common/logo-component/logo-component.common";

const ResetPassword = ({ params }) => {
  const t = useTranslations("resetPassword");

  return (
    <Box className="!flex !flex-row !w-full !min-h-screen">
      <Box className="!hidden lg:!block lg:basis-1/2 overflow-hidden">
        <CommonImage
          src={PasswordRecoveryPng}
          alt="email-verification-image"
          className="w-1/2 lg:!block !hidden overflow-hidden object-center !h-full fixed"
        />
      </Box>
      <Box className="lg:basis-1/2 w-full md:px-8 px-4 relative flex-grow flex flex-col justify-between">
        <Box className="lg:top-6 lg:left-12 py-6 flex lg:justify-normal justify-center">
          <Link href={`/${params.locale}`}>
            {/* <Logo className="!w-[70px] !h-6" /> */}
            <LogoComponent />
          </Link>
        </Box>
        <Box className="!max-w-[350px] !m-auto lg:!mt-44 !mt-16">
          <Typography className="!text-[--text-color] CraftworkGroteskMedium !mb-6 !text-2xl !text-center">
            {t("setNewPassword")}
          </Typography>
          {/** Reset Password Form  */}
          <ResetPasswordForm />
          <Box className="my-5 lg:my-8">
            <Typography className="text-center text-sm leading-[15.4px] mb-16 CraftworkGroteskMedium text-[--text-color]">
              {t("haveAnAccount")}
              <Link href={`${params.locale}/login`} className="CraftworkGroteskHeavy">
                {t("logIn")}
              </Link>
            </Typography>
          </Box>
        </Box>
        <DesktopFooter className="!mb-6 !mx-auto !hidden md:!inline lg:!inline" />
        <MobileFooter className="!-mx-4 lg:!hidden md:!hidden !inline bg-[--footer-bg]" />
      </Box>
    </Box>
  );
};

export default ResetPassword;
