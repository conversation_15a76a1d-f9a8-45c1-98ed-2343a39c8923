"use client";
import React from "react";
import { Box, Typography } from "@mui/material";
import Authentication2FA from "@/assets/png/Authentication2FA.png";
import { CommonImage } from "@/component";
import Link from "next/link";
import DesktopFooter from "@/common/footer/desktop.footer.common";
import TwoFaOtpForm from "@/containers/2FA/2fa-otp.container";
import TwoFAResendCode from "@/containers/2FA/2Fa-resend-code.container";
import { useTranslations } from "next-intl";
import LogoComponent from "@/common/logo-component/logo-component.common";
import { useSelector } from "react-redux";

const Authentication2FAOTP = ({ params }) => {
  const t = useTranslations("2faAuthenticationOTP");
  const { token } = useSelector((state) => state.login);

  return (
    <Box className="!flex !flex-row !w-full !min-h-screen">
      <Box className="!hidden lg:!block lg:basis-1/2 overflow-hidden">
        <CommonImage
          src={Authentication2FA}
          alt="2fa-image"
          className="w-1/2 lg:!block !hidden overflow-hidden object-center h-full fixed"
        />
      </Box>
      <Box className=" lg:basis-1/2 !w-full md:!px-8 px-4 relative flex-grow">
        <Box className="lg:absolute lg:top-6 lg:!left-12 !py-2 !flex lg:justify-normal !justify-center">
          <Link href={token ? `/${params.locale}/search` : `/${params.locale}`}>
            {/* <Logo className="!w-[70px] !h-6" /> */}
            <LogoComponent />
          </Link>
        </Box>
        <Box className="!max-w-[350px] !m-auto lg:!mt-[20vh] !mt-20">
          <Typography className="!text-[--text-color] CraftworkGroteskMedium !mb-6 !text-2xl !text-center">
            {t("twoFactor")}
            <br /> {t("authentication")}
          </Typography>
          <Typography className="!text-[--text-color] Sora300 !mb-6 !text-sm !text-center">
            {t("6DigitCode")}
          </Typography>
          <TwoFaOtpForm />
          <Box>
            <TwoFAResendCode />
            <Link
              href={`/${params.locale}/dashboard`}
              replace
              className="!hidden lg:!inline !left-0 !right-0 !mx-auto"
            >
              <Typography className="!underline CraftworkGroteskHeavy !text-[--text-color] !text-sm !text-center !my-16">
                Set Up later
              </Typography>
            </Link>
          </Box>

          <DesktopFooter className="!absolute !bottom-8 !left-0 !right-0 !mx-auto" />
        </Box>
      </Box>
    </Box>
  );
};

export default Authentication2FAOTP;
