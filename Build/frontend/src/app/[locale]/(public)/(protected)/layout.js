"use client";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { useEffect } from "react";
import { getLocalStorage } from "@/utils";
import { useLocale } from "next-intl";

const Layout = ({ children }) => {
  const router = useRouter();
  const lang = useLocale();
  const { token, data } = useSelector((state) => state.login);
  useEffect(() => {
    if (!token) {
      if (data?.twoFaEnabled) {
        return;
      } else if (
        (!token && data?.twoFaEnabled && getLocalStorage("access_token") === undefined) ||
        token === undefined
      ) {
        // If token is not present and twoFaEnabled is true, return children
        return;
      } else if (getLocalStorage("access_token") === null) {
        router.push(`/${lang}/login`);
      }
      // Redirect to login page if token is not present or 2FA is enabled
      router.push(`/${lang}/login`);
    }
  }, [token, data, router]);

  return <>{children}</>;
};

export default Layout;
