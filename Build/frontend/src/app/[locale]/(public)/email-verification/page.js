"use client";
import React from "react";
import { Box, Typography } from "@mui/material";
import EmailVerificationPng from "@/assets/png/EmailVerification.png";
import Link from "next/link";
import CommonImage from "@/component/image/image.component";
import DesktopFooter from "@/common/footer/desktop.footer.common";
import ResendRegisterEmail from "@/containers/register/resend-register-email";
import LogoComponent from "@/common/logo-component/logo-component.common";

const EmailVerification = ({ params }) => {
  return (
    <Box className="!flex !flex-row !w-full !min-h-screen">
      <Box className="!hidden lg:!block lg:basis-1/2 overflow-hidden">
        <CommonImage
          src={EmailVerificationPng}
          alt="email-verification-image"
          className="w-1/2 lg:!block !hidden overflow-hidden object-center h-full fixed"
        />
      </Box>
      <Box className=" lg:basis-1/2 !w-full md:!px-8 px-4 relative flex-grow">
        <Box className="lg:absolute lg:top-6 lg:!left-12 !py-2 !flex lg:justify-normal !justify-center">
          <Link href={`/${params.locale}`}>
            {/* <Logo className="!w-[70px] !h-6" /> */}
            <LogoComponent />
          </Link>
        </Box>
        <Box className="!max-w-md !mx-auto !mt-44">
          <Typography className="!text-[--text-color] CraftworkGroteskMedium !mb-6 !text-2xl !text-center">
            We’ve sent you a link. Check your email
          </Typography>
          <Typography className="!text-[--text-color] Sora300 !mb-6 !text-sm !text-center">
            Not received your reset link?
            <br />
            Check your spam folder or wait 15 minutes before resend.
          </Typography>
          <ResendRegisterEmail />
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular !text-center !my-12">
            Back to{" "}
            <Link href="/" className="Sora500">
              Main
            </Link>
          </Typography>
          <DesktopFooter className="!absolute !bottom-8 !left-0 !right-0 !mx-auto" />
        </Box>
      </Box>
    </Box>
  );
};

export default EmailVerification;
