"use client";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import { setInstantMessage } from "@/store/slice/common/instantMessage.slice";
import useStompClient from "@/hooks/useStompClient";

const Layout = ({ children }) => {
  const { token, currentUser } = useSelector((state) => state.login);
  const stompClient = useStompClient();
  const username = currentUser?.email;

  // const router = useRouter();
  // get the route and check current route is public or private
  const dispatch = useDispatch();

  // useEffect(() => {
  //   if (token) {
  //     dispatch(notificationList());
  //   }
  // }, []);

  useEffect(() => {
    if (token && stompClient && stompClient.connected) {
      stompClient.subscribe(`/user/${username}/queue/messages`, (message) => {
        const parsedMessage = JSON.parse(message.body);
        //eslint-disable-next-line no-console
        //console.log("Received message:", parsedMessage);
        dispatch(setInstantMessage(parsedMessage));
      });
    }
  }, [username, stompClient]);

  return <>{children}</>;
};
export default Layout;
