"use client";
import { Box, CssBaseline, Typography } from "@mui/material";
import LoginImage from "@/assets/png/Login.png";
import MuiDivider from "@/common/divider/divider.common";
import DesktopFooter from "@/common/footer/desktop.footer.common";
import { CommonImage } from "@/component";
import Link from "next/link";
import LoginForm from "@/containers/login/login.container";
import GoogleLogin from "@/containers/login/google.login.container";
import FacebookLogin from "@/containers/login/facebook.login.container";
import { useLocale, useTranslations } from "next-intl";
import LogoComponent from "@/common/logo-component/logo-component.common";
import { useDispatch, useSelector } from "react-redux";
import { getCurrentUserEmail, loginWithOAuth2 } from "@/store/slice/auth/login.auth.slice";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";
import { setLocalStorage } from "@/utils";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";

const LoginPage = () => {
  const t = useTranslations("login");
  const dispatch = useDispatch();
  const searchParams = useSearchParams();
  const lang = useLocale();
  const status = searchParams.get("status");
  const { showSnackbar } = useSnackbar();
  const router = useRouter();
  const { token } = useSelector((state) => state.login);

  useEffect(() => {
    if (token) {
      router.push(`/${lang}/dashboard`);
      return;
    }

    // Check if the URL has ?status=success
    if (status === "success") {
      dispatch(loginWithOAuth2())
        .unwrap()
        .then((user) => {
          if (user.status === "success") {
            dispatch(getCurrentUserEmail());
            setLocalStorage("access_token", user.data.accessToken);
            //showSnackbar(user.data.message, "success");
            user.data.promptForTwofaSetup && router.push(`/${lang}/2fa-authentication`);
            !user.data.promptForTwofaSetup && router.push(`/${lang}/dashboard`);
          } else if (user.status === 202 && user.data.twoFaEnabled) {
            //setLocalStorage("access_token", user.data.data.accessToken);
            //showSnackbar(user.data.message, "success");
            router.push(`/${lang}/otp`);
          } else if (user.status === 208) {
            setLocalStorage("access_token", null);
            //showSnackbar(user.data, "error");
            router.push(`/${lang}/otp`);
          }
        })
        .catch((error) => {
          showSnackbar(error, "error");
        });
    }
  }, [status, token]);

  return (
    <Box className="!flex !flex-row !w-full !min-h-screen">
      <CssBaseline />
      <Box className="!hidden lg:!block lg:basis-1/2">
        <CommonImage
          src={LoginImage}
          alt="login-image"
          className="w-1/2 lg:!block !hidden overflow-hidden object-center h-full fixed"
        />
      </Box>
      <Box className="lg:basis-1/2 w-full md:px-8 px-4 relative flex-grow flex flex-col justify-between">
        <Box className="lg:top-6 lg:left-12 !py-6 flex lg:justify-normal justify-center">
          <Link href={`/${lang}`}>
            {/* <Logo className="!w-[70px] !h-6" /> */}
            <LogoComponent />
          </Link>
        </Box>
        <Box className="max-w-[350px] m-auto lg:mt-16 mt-8 flex-grow">
          <Typography className="text-[--text-color] mb-6 CraftworkGroteskMedium text-2xl text-center">
            {t("login")}
          </Typography>
          <LoginForm />
          <MuiDivider />
          <Box className="flex justify-center gap-x-4 py-4">
            {/* facebook login */}
            <FacebookLogin />
            {/* google login */}
            <GoogleLogin />
          </Box>
          <Box className="my-5 xl:my-8">
            <Typography className="text-center CraftworkGroteskMedium text-sm leading-[15.4px] text-[--text-color]">
              {t("haveAnAccount")}
              <Link href={`/${lang}/signup`} className="CraftworkGroteskHeavy">
                {t("signup")}
              </Link>
            </Typography>
          </Box>
        </Box>
        <DesktopFooter className="mx-auto !mb-6" />
      </Box>
    </Box>
  );
};

export default LoginPage;
