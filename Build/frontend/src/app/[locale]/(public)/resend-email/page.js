import React from "react";
import { Box, Typography } from "@mui/material";
import Authentication2FAPPng from "@/assets/png/Authentication2FA.png";
import { CommonImage } from "@/component";
import DesktopFooter from "@/common/footer/desktop.footer.common";
import ResendEmailForm from "@/containers/resend-email/resend-email.container";
import Link from "next/link";
import { useTranslations } from "next-intl";
import LogoComponent from "@/common/logo-component/logo-component.common";

const ResendEmail = ({ params }) => {
  const t = useTranslations("resendEmail");
  return (
    <Box className="!flex !flex-row !w-full !min-h-screen  !m-auto">
      <Box className="!hidden lg:!block lg:basis-1/2 overflow-hidden">
        <CommonImage
          src={Authentication2FAPPng}
          alt="2fa-image"
          className="w-1/2 lg:!block !hidden overflow-hidden object-center h-full fixed"
        />
      </Box>
      <Box className=" lg:basis-1/2 !w-full md:!px-8 px-4 relative flex-grow">
        <Box className="lg:absolute lg:top-6 lg:!left-12 !py-2 !flex lg:justify-normal !justify-center">
          <Link href={`/${params.locale}`}>
            {/* <Logo className="!w-[70px] !h-6" /> */}
            <LogoComponent />
          </Link>
        </Box>
        <Box className="!max-w-[350px] !m-auto lg:!mt-44 !mt-20">
          <Typography className="!text-[--text-color] Sora300 !mb-6 !text-sm !text-center">
            {t("verifyYourAccount")}
          </Typography>
          {/** 2 fa PhoneNumber screen */}
          <ResendEmailForm />
          <DesktopFooter className="!absolute !bottom-8 !left-0 !right-0 !mx-auto" />
        </Box>
      </Box>
    </Box>
  );
};

export default ResendEmail;
