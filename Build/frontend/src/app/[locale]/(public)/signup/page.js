import { Box, Typography } from "@mui/material";
import Signup from "@/assets/png/Signup.png";

import MuiDivider from "@/common/divider/divider.common";
import { CommonImage } from "@/component";
import DesktopFooter from "@/common/footer/desktop.footer.common";
import Link from "next/link";
import RegisterForm from "@/containers/register/register.container";
import seo from "@/seo";
import { useTranslations } from "next-intl";
import LogoComponent from "@/common/logo-component/logo-component.common";
export async function generateMetadata() {
  return {
    title: seo.signup.title,
    description: seo.signup.description,
    keywords: seo.signup.keywords,
  };
}
const Register = ({ params }) => {
  const t = useTranslations("signUp");

  return (
    <Box className="flex flex-row w-full min-h-screen">
      <Box className="hidden lg:block lg:basis-1/2 overflow-hidden">
        <CommonImage
          src={Signup}
          alt="signup-image"
          className="w-1/2 lg:block hidden overflow-hidden object-center h-full fixed"
        />
      </Box>
      <Box className="lg:basis-1/2 w-full md:px-8 px-4 relative flex-grow flex flex-col justify-between">
        <Box className="lg:top-6 lg:left-12 py-6 flex lg:justify-normal justify-center">
          <Link href={`/${params.locale}`}>
            {/* <Logo className="!w-[70px] !h-6" /> */}
            <LogoComponent />
          </Link>
        </Box>
        <Box className="max-w-[350px] m-auto lg:mt-16 mt-8 flex-grow">
          <Typography className="text-[--text-color] CraftworkGroteskMedium mb-6 text-2xl text-center">
            {t("register")}
          </Typography>
          <RegisterForm />

          <MuiDivider />
          {/* <Box className="flex justify-center gap-x-4 py-4">
            <FacebookLogin />
            <GoogleLogin />
          </Box> */}
          <Box className="my-5 lg:my-8">
            <Typography className="text-center text-sm leading-[15.4px] mb-16 CraftworkGroteskMedium text-[--text-color]">
              {t("haveAnAccount")}
              <Link href={`/${params.locale}/login`} className="CraftworkGroteskHeavy">
                {t("logIn")}
              </Link>
            </Typography>
          </Box>
        </Box>
        <DesktopFooter className="mx-auto !mb-6" />
      </Box>
    </Box>
  );
};
export default Register;
