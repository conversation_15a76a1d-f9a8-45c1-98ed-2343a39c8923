import ReduxProvider from "@/context/redux/redux.provider";
import { SnackbarProvider } from "@/context/snackbar/snackbar.provider";
import { Suspense } from "react";
import AppProvider from "@/context/app/app.provider";
import seo from "@/seo";
import TokenExpiredProvider from "@/context/token-expired/token.expired.provider";
import { NextIntlClientProvider, useMessages } from "next-intl";
import PhotoViewProvider from "@/providers/PhotoViewProvider";

import "@/styles/globals.css";

export async function generateMetadata() {
  return {
    title: seo.home.title,
    description: seo.home.description,
    keywords: seo.home.keywords,
    publisher: seo.home.publisher,
    authors: seo.home.author,
    robots: seo.home.robots,
    openGraph: {
      title: seo.home.openGraph.title,
      dessciption: seo.home.openGraph.description,
      url: seo.home.openGraph.url,
      type: seo.home.openGraph.type,
      siteName: seo.home.openGraph.siteName,
      // authors:seo.home.openGraph.authors,
    },
  };
}
export default function RootLayout({ children, params: { locale } }) {
  const messages = useMessages();
  return (
    <html lang={locale}>
      <body suppressHydrationWarning={true}>
        <SnackbarProvider>
          <ReduxProvider>
            <TokenExpiredProvider>
              <NextIntlClientProvider locale={locale} messages={messages}>
                <AppProvider>
                  <Suspense>
                    <PhotoViewProvider>{children}</PhotoViewProvider>
                  </Suspense>
                </AppProvider>
              </NextIntlClientProvider>
            </TokenExpiredProvider>
          </ReduxProvider>
        </SnackbarProvider>
      </body>
    </html>
  );
}
