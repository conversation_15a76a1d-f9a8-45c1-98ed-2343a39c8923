"use client";
import { Container, Typography, Box, Button } from "@mui/material";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { useRouter } from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
const NotFound = () => {
  const t = useTranslations("notFound");
  const lang = useLocale();
  const router = useRouter();

  const handleClick = () => {
    router.push(`/${lang}`);
  };
  return (
    <>
      <Container className="text-[--text-color]">
        <Box className="flex flex-col justify-center">
          <Typography className="!text-[50px] text-center !mt-36 CraftworkGroteskSemiBold" as="h1">
            {t("LostWay")}
          </Typography>
          <Typography className="text-center !mt-8 CraftworkGroteskRegular">
            {t("pageNotFound")}
          </Typography>
          <Box className="!flex flex-col justify-center items-center">
            <Button
              className="!bg-[--text-color] !flex !gap-x-4 !p-4 !mt-5"
              onClick={handleClick}
              sx={{
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
            >
              <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                {t("home")}
              </Typography>
              <ArrowSouthEast />
            </Button>
            <Typography className="!mt-20 !text-[30px] CraftworkGroteskMedium">
              {t("ErrorCode")}
            </Typography>
          </Box>
        </Box>
      </Container>
    </>
  );
};
export default NotFound;
