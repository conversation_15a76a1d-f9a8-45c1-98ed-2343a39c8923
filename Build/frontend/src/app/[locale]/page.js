"use client";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import Carousel from "@/common/carousel/carousel.common";
import Banner from "@/assets/png/banner-1.png";
import { Box, Divider, Typography, useMediaQuery, useTheme } from "@mui/material";
import { CommonImage, Button } from "@/component";
import Background from "@/assets/png/Background.png";
import Vector2 from "@/assets/svg/Vector2.svg";
import Vector1 from "@/assets/svg/Vector-1.svg";
import Vector3 from "@/assets/svg/Vector3.svg";
import MobileFooter from "@/common/footer/mobile.footer.common";
import DesktopFooter from "@/common/footer/desktop.footer.common";
import Header from "@/common/header/header.common";
import { HomeHeaderSidebar } from "@/ui";
import { useEffect, useState, useMemo } from "react";
import { useTranslations } from "next-intl";

const Home = () => {
  const t = useTranslations("Home");
  const images = useMemo(() => [<PERSON>, <PERSON>, <PERSON>], []);
  // const popularbands = [
  //   {
  //     id: 0,
  //     src: BandImage1,
  //     alt: "image",
  //     category: "Category",
  //   },
  //   {
  //     id: 1,
  //     src: BandImage2,
  //     alt: "image",
  //     category: "Category",
  //   },
  //   {
  //     id: 2,
  //     src: BandImage3,
  //     alt: "image",
  //     category: "Category",
  //   },
  //   {
  //     id: 3,
  //     src: BandImage4,
  //     alt: "image",
  //     category: "Category",
  //   },
  // ];

  // const trendingActs = [
  //   {
  //     id: 0,
  //     src: ActImage,
  //     alt: "image",
  //     category: "Singer",
  //     price: "$36.99",
  //   },
  //   {
  //     id: 1,
  //     src: ActImage1,
  //     alt: "image",
  //     category: "DJ",
  //     price: "$36.99",
  //   },
  //   {
  //     id: 2,
  //     src: ActImage2,
  //     alt: "image",
  //     category: "Vocalist",
  //     price: "$36.99",
  //   },
  //   {
  //     id: 3,
  //     src: ActImage3,
  //     alt: "image",
  //     category: "DJ",
  //     price: "$36.99",
  //   },
  // ];
  const theme = useTheme();
  const isScreenLarge = useMediaQuery(theme.breakpoints.up("lg"));

  const [isLargeScreen, setIsLargeScreen] = useState(isScreenLarge ?? true);

  useEffect(() => {
    setIsLargeScreen(isScreenLarge);
  }, [isScreenLarge]);

  return (
    <>
      <Box className="!fixed !z-30 !left-0 !right-0 !top-0">
        {isLargeScreen ? <Header /> : <HomeHeaderSidebar />}
      </Box>
      <Box className="xl:!mx-32 lg:!mx-24 !mx-8 lg:!my-32 !my-24">
        <Carousel images={images} />
        <Box className=" !relative lg:!pt-40 !pt-20">
          <Typography className="lg:!text-8xl md:!text-6xl !text-4xl !text-[--text-color] CraftworkGroteskMedium lg:!leading-[117.6px]">
            {t("EventUniverse")}
          </Typography>
          <Box className="!flex lg:!flex-row md:!flex-row !gap-4 !flex-col !justify-between !my-4">
            <Typography className="!text-[--text-color] !text-center !text-lg CraftworkGroteskGX">
              {t("ArtistVenue")}
            </Typography>
            <Button className="!bg-[--text-color] !gap-x-4 !my-6 !py-3" sx={{ minWidth: 0 }}>
              <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                {t("LearnMore")}
              </Typography>
              <ArrowSouthEast alt="arrow" />
            </Button>
          </Box>
          <Box className="!absolute !-top-8 !-right-32 !hidden lg:!inline">
            <CommonImage src={Background} alt="background" />
          </Box>
        </Box>
        <Box className="!grid lg:!grid-cols-3 md:!grid-cols-2 !grid-cols-1 lg:!gap-20 !gap-8 !my-32">
          <Box className="!flex !flex-col !items-center">
            <Vector1 className="!w-[122px] !h-[129px]" />
            <Typography className="!text-sm !text-center !text-[--text-color] CraftworkGroteskHeavy !pb-1">
              {t("Administer")}
            </Typography>
            <Typography className="!text-sm !text-center  !text-[--text-color] CraftworkGroteskGX">
              {t("description")}
            </Typography>
          </Box>
          <Box className="!flex !flex-col !items-center">
            <Vector3 className="!w-[122px] !h-[129px]" />
            <Typography className="!text-sm !text-center !text-[--text-color] CraftworkGroteskHeavy !pb-1">
              {t("CreateAutomate")}
            </Typography>
            <Typography className="!text-sm !text-center !text-[--text-color] CraftworkGroteskGX">
              {t("description")}
            </Typography>
          </Box>
          <Box className="!flex !flex-col !items-center">
            <Vector2 className="!w-[122px] !h-[129px]" />
            <Typography className="!text-sm !text-center !text-[--text-color] CraftworkGroteskHeavy !pb-1">
              {t("SearchFor")}
            </Typography>
            <Typography className="!text-sm !text-center !text-[--text-color] CraftworkGroteskGX">
              {t("description")}
            </Typography>
          </Box>
        </Box>
        <Divider sx={{ borderTop: "thin solid rgba(76, 78, 79, 0.5)" }} />
        {/* <Box className="!pb-16">
          <Title titleName="Popular Bands" />
          <Box className="!grid lg:!grid-cols-4 md:!grid-cols-2 !grid-cols-1 lg:!gap-20 !gap-8 !mt-8">
            {popularbands.map((band, index) => (
              <Box
                key={index}
                sx={{ boxShadow: 0, border: 0, borderRadius: 0 }}
                className="!bg-[--bg-color] !border !border-[--divider-color]"
              >
                <Box className="!flex !flex-col !items-center">
                  <CommonImage className="!min-w-full" src={band.src} alt={band.alt} />
                  <Typography className="!text-center !text-sm CraftworkGroteskRegular !text-[--text-color] !my-4">
                    {band.category}
                  </Typography>
                  <Button
                    sx={{
                      border: 0,
                      "&.MuiButtonBase-root": {
                        color: "white !important",
                      },
                    }}
                    className="!border !border-[--divider-color] !rounded-[4px] !flex !gap-x-3 !mb-4 !py-2"
                  >
                    <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--text-color]">
                      {t("ViewDetail")}
                    </Typography>{" "}
                    <SouthEast className="!text-[--text-color]" />
                  </Button>
                </Box>
              </Box>
            ))}
          </Box>
        </Box>
        <Divider sx={{ borderTop: "thin solid rgba(76, 78, 79, 0.5)" }} />
        <Box>
          <Title titleName="Trending acts" />
          <Box className="!grid lg:!grid-cols-4 md:!grid-cols-2 !grid-cols-1 lg:!gap-20 !gap-8 !w-full !mt-8">
            {trendingActs.map((act, index) => (
              <Box key={index}>
                <CommonImage src={act.src} alt={act.alt} className="!min-w-full" />
                <Box className="!flex !justify-between !my-2">
                  <Box className="">
                    <Typography className=" !text-sm !text-[--text-color] CraftworkGroteskRegular">
                      {act.category}
                    </Typography>
                    <Typography className=" !text-sm !text-[--text-color] CraftworkGroteskHeavy">
                      {act.price}
                    </Typography>
                  </Box>
                  <LikeIcon />
                </Box>
              </Box>
            ))}
          </Box>
          <Box className="!flex !justify-center !my-12">
            <Button
              sx={{
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
              className="!border !border-[--divider-color] !rounded-[4px] !flex !gap-x-3 !mb-4 !py-2"
            >
              <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--text-color]">
                {t("ShowMore")}
              </Typography>{" "}
              <SouthEast className="!text-[--text-color]" />
            </Button>
          </Box>
        </Box> */}
      </Box>
      <Box className="!bg-[--footer-bg]">
        <MobileFooter className="!px-12 lg:!px-20 !py-12 bg-[--footer-bg]" />
        <Divider sx={{ borderTop: "thin solid rgba(76, 78, 79, 0.5)" }} className="lg:!mx-20" />
        <DesktopFooter className="!py-12" />
      </Box>
    </>
  );
};
export default Home;
