/* #### Generated By: http://www.cufonfonts.com #### */

/* Craftwork Grotesk Font */

@font-face {
  font-family: "CraftworkGroteskRegular";
  font-style: normal;
  font-weight: normal;
  src: url("./craft-work//CraftworkGrotesk-Regular.ttf");
}

.CraftworkGroteskRegular {
  font-family: var(--craftWorkRegular) !important;
}

@font-face {
  font-family: "CraftworkGroteskGX";
  font-style: normal;
  font-weight: normal;
  src: url("./craft-work/CraftworkGroteskGX.ttf");
}
.CraftworkGroteskGX {
  font-family: var(--craftWorkGX) !important;
}

@font-face {
  font-family: "CraftworkGroteskMedium";
  font-style: normal;
  font-weight: normal;
  src: url("./craft-work/CraftworkGrotesk-Medium.ttf");
}

.CraftworkGroteskMedium {
  font-family: var(--craftWorkMedium) !important;
}

@font-face {
  font-family: "CraftworkGroteskSemiBold";
  font-style: normal;
  font-weight: normal;
  src: url("./craft-work/CraftworkGrotesk-SemiBold.ttf");
}

.CraftworkGroteskSemiBold {
  font-family: var(--craftWorkSemiBold) !important;
}

@font-face {
  font-family: "CraftworkGroteskBold";
  font-style: normal;
  font-weight: normal;
  src: url("./craft-work/CraftworkGrotesk-Bold.ttf");
}

.CraftworkGroteskBold {
  font-family: var(--craftWorkBold) !important;
}

@font-face {
  font-family: "CraftworkGroteskHeavy";
  font-style: normal;
  font-weight: normal;
  src: url("./craft-work/CraftworkGrotesk-Heavy.ttf");
}

.CraftworkGroteskHeavy {
  font-family: var(--craftWorkHeavy) !important;
}

@font-face {
  font-family: "Sora100";
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url(./sora/Sora-Thin.ttf);
}

/* Sora Font */

.Sora100 {
  font-family: var(--sora-thin) !important;
}
@font-face {
  font-family: "Sora200";
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url(./sora/Sora-ExtraLight.ttf);
}

.Sora200 {
  font-family: var(--sora-extralight) !important;
}
@font-face {
  font-family: "Sora300";
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(./sora/Sora-Light.ttf);
}

.Sora300 {
  font-family: var(--sora-light) !important;
}
@font-face {
  font-family: "Sora400";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(./sora/Sora-Regular.ttf);
}

.Sora400 {
  font-family: var(--sora-regular) !important;
}
@font-face {
  font-family: "Sora500";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(./sora/Sora-Medium.ttf);
}

.Sora500 {
  font-family: var(--sora-medium) !important;
}
@font-face {
  font-family: "Sora600";
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(./sora/Sora-SemiBold.ttf);
}

.Sora600 {
  font-family: var(--sora-semibold) !important;
}
@font-face {
  font-family: "Sora700";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(./sora/Sora-Bold.ttf);
}

.Sora700 {
  font-family: var(--sora-bold) !important;
}
@font-face {
  font-family: "Sora800";
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url(./sora/Sora-ExtraBold.ttf);
}

.Sora800 {
  font-family: var(--sora-extrabold) !important;
}

/* Poppins Font */

@font-face {
  font-family: "Poppins100";
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url(../fonts/poppins/Poppins-Thin.ttf);
}

.Poppins100 {
  font-family: var(--pop-thin) !important;
}
@font-face {
  font-family: "Poppins200";
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url(../fonts/poppins/Poppins-ExtraLight.ttf);
}

.Poppins200 {
  font-family: var(--pop-extraLight) !important;
}

@font-face {
  font-family: "Poppins300";
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(../fonts/poppins/Poppins-Light.ttf);
}

.Poppins300 {
  font-family: var(--pop-light) !important;
}

@font-face {
  font-family: "Poppins400";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(../fonts/poppins/Poppins-Regular.ttf);
}

.Poppins400 {
  font-family: var(--pop-regular) !important;
}

@font-face {
  font-family: "Poppins500";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(../fonts/poppins/Poppins-Medium.ttf);
}

.Poppins500 {
  font-family: var(--pop-medium) !important;
}

@font-face {
  font-family: "Poppins600";
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(../fonts/poppins/Poppins-SemiBold.ttf);
}

.Poppins600 {
  font-family: var(--pop-semibold) !important;
}

@font-face {
  font-family: "Poppins700";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(../fonts/poppins/Poppins-Bold.ttf);
}

.Poppins700 {
  font-family: var(--pop-bold) !important;
}

@font-face {
  font-family: "Poppins800";
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url(../fonts/poppins/Poppins-ExtraBold.ttf);
}

.Poppins800 {
  font-family: var(--pop-extraBold) !important;
}

@font-face {
  font-family: "Poppins900";
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url(../fonts/poppins/Poppins-Black.ttf);
}

.Poppins900 {
  font-family: var(--pop-black) !important;
}
