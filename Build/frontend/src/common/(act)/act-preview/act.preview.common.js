"use client";
import { Box, Typography } from "@mui/material";
import Vector4 from "@/assets/svg/Vector4.svg";
import Vector5 from "@/assets/svg/Vector5.svg";
import Vector6 from "@/assets/svg/Vector6.svg";
import Vector7 from "@/assets/svg/Vector7.svg";
import LocationInfo from "@/common/location-info/location-info.common";
import { useSelector } from "react-redux";
import { transformText } from "@/utils";
import { CommonImage } from "@/component";
import { useTranslations } from "next-intl";
const ActPreview = () => {
  const t = useTranslations("actPreview");
  const { previewData } = useSelector((state) => state.act);

  return (
    <Box className="!hidden !bg-[--inprogress-color] !min-h-screen lg:!block lg:basis-1/2 !px-14 !pt-20">
      <Typography className="!text-[--bg-color] !text-2xl CraftworkGroteskMedium !pb-8">
        {t("preview")}
      </Typography>
      <Box className="!flex !gap-x-4 !w-full">
        {previewData?.actMedia?.actPhotos?.length > 0 ? (
          <>
            {previewData?.actMedia?.actPhotos?.[0] && (
              <Box className="!w-full !h-full !flex !items-center object-cover !justify-center !bg-[--image-bg] !rounded-[4px]">
                <CommonImage
                  src={previewData?.actMedia?.actPhotos?.[0]}
                  alt="Preview media"
                  height={1}
                  width={1}
                  layout="responsive"
                  className="!w-full !h-[360px]"
                />
              </Box>
            )}
            <Box className="!h-[17vw] !flex !flex-wrap !flex-col !gap-3">
              {previewData?.actMedia?.actPhotos?.slice(1).map((images, index) => (
                <>
                  <Box key={index}>
                    <Box className="!max-w-[70px] h-full !rounded-[4px]">
                      <CommonImage
                        src={images}
                        alt="Preview media"
                        height={1}
                        width={1}
                        layout="responsive"
                        className="!w-[10vw] !h-[3.5vw]"
                      />
                    </Box>
                  </Box>
                </>
              ))}
            </Box>
          </>
        ) : (
          <>
            <Box className="!w-full !h-full !flex !items-center !justify-center !py-20 !bg-[--image-bg] !rounded-[4px]">
              <Vector4 className="!w-[155px] !h-[155px]" />
            </Box>
            <Box className="!flex !flex-col !gap-y-3">
              <Box className="!max-w-[70px] !h-full !flex !items-center !justify-center !bg-[--image-bg] !p-2 !rounded-[4px]">
                <Vector5 className="!w-[47px] !h-[54px]" />
              </Box>
              <Box className="!w-full !h-full !flex !items-center !justify-center !bg-[--image-bg] !p-2 !rounded-[4px]">
                <Vector6 className="!w-[47px] !h-[54px]" />
              </Box>
              <Box className="!w-full !h-full !flex !items-center !justify-center !bg-[--image-bg] !p-2 !rounded-[4px]">
                <Vector4 className="!w-[47px] !h-[54px]" />
              </Box>
              <Box className="!w-full !h-full !flex !items-center !justify-center !bg-[--image-bg] !p-2 !rounded-[4px]">
                <Vector7 className="!w-[47px] !h-[54px]" />
              </Box>
            </Box>
          </>
        )}
      </Box>
      <Box className="!flex !gap-x-5 !pt-8">
        <Box className="!w-[68%]">
          <Typography className="!text-[--bg-color] !text-2xl CraftworkGroteskMedium ">
            {previewData?.profile?.option === "Act"
              ? previewData?.actInfo?.profileName ?? "Act’s name"
              : previewData?.actInfo?.profileName ?? "Venue's name"}
          </Typography>
          <Typography className="!text-[--bg-color] !text-sm CraftworkGroteskRegular !pt-3">
            {previewData?.profile?.option === "Act"
              ? previewData?.selectedActType ?? "Type of Act"
              : previewData?.selectedActType ?? "Type of Venue"}
          </Typography>
          <Box className="!flex !flex-wrap !gap-x-4 !mb-8">
            {previewData?.musicGenre?.length > 0 ? (
              previewData?.musicGenre?.map((genre, index) => (
                <>
                  <Typography
                    key={index}
                    className="!text-[--bg-color] !bg-[--image-bg] !text-sm CraftworkGroteskRegular !rounded-[4px] !p-2 !mt-3 capitalize"
                  >
                    {transformText(genre.name)}
                  </Typography>
                  {genre.members.length > 0 &&
                    genre.members.map((member, index) => (
                      <Typography
                        key={index}
                        className="!text-[--bg-color] !bg-[--image-bg] !text-sm CraftworkGroteskRegular !rounded-[4px] !p-2 !mt-3 capitalize"
                      >
                        {transformText(member)}
                      </Typography>
                    ))}
                </>
              ))
            ) : (
              <Typography className="!text-[--bg-color] !bg-[--image-bg] !text-sm CraftworkGroteskRegular !rounded-[4px] !p-2 !mt-3">
                {t("musicGenre")}
              </Typography>
            )}
          </Box>
          <Typography className="!text-[--bg-color] !text-lg CraftworkGroteskMedium !pb-4 ">
            {previewData?.profile?.option === "Act" ? t("aboutTheAct") : "About the Venue"}
          </Typography>
          <Typography
            className="!text-[--bg-color] break-words !text-sm CraftworkGroteskRegular !pb-3"
            // as="pre"
          >
            {previewData?.actInfoSocial?.bio}
          </Typography>
        </Box>
        <LocationInfo className="!w-[50%] !max-h-[340px]" />
      </Box>
    </Box>
  );
};
export default ActPreview;
