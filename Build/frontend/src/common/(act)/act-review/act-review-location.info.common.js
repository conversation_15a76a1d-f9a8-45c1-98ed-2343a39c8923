"use client";
import { Box, Typography } from "@mui/material";
import React from "react";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import { Button } from "@/component";
import Email from "@/assets/svg/act-type.svg/Email.svg";
import Rating from "@/component/rating/rating.components";
import { generateLocationString } from "@/utils";
import GigIcon from "@/assets/svg/GigIcon.svg";
import AddUser from "@/assets/svg/AddUser.svg";
import DollarIcon from "@/assets/svg/DollarIcon.svg";
import SocialMediaLinks from "./social-media-links";
import { useTranslations } from "next-intl";
import BookingButtonComponent from "@/component/button/booking-button.component";

const ActReviewLocation = ({ data, type }) => {
  const t = useTranslations("actReview.actReviewLocation");
  const s = useTranslations("act");

  const handleEmailClick = () => {
    window.location.href = `mailto:${data?.profileDto?.profileEmail}`;
  };

  return (
    <Box className="!border !border-[--text-color] lg:!w-[325px] !w-full !min-h-[350px] !rounded-[4px] !p-5 !mt-5">
      {type === "actLocation" && (
        <>
          <Box className="!flex !flex-wrap !gap-3 !items-center !my-2">
            <Rating value={data?.profileRatingDto?.overallRating} readOnly />
            {/* <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
              {data?.profileRatingDto?.overallRating}
            </Typography> */}
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
              ({data?.profileRatingDto?.numberOfRatings} {t("reviews")})
            </Typography>
          </Box>
          <Box className="!flex !items-center !gap-3">
            <LocationSvg className="!w-6 !h-6" />
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
              {generateLocationString(data?.locationDto).length > 30
                ? generateLocationString(data?.locationDto).substring(0, 30) + "..."
                : generateLocationString(data?.locationDto)}
            </Typography>
          </Box>
          {/* <Box className="!flex !items-center !gap-3 py-2">
            <Globe className="!w-6 !h-6 !stroke-white" />
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
              {t("playInternational")}:1
            </Typography>
          </Box> */}
        </>
      )}
      {/* <Box className="!flex !justify-between">
        <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
          {t("banner")}
        </Typography>
        <Box className="!flex !gap-3">
          <FilledStar className="!w-5 !h-5" />
          <Award className="!w-5 !h-5" />
        </Box>
      </Box> */}

      {type === "actLocation" && (
        <>
          <Box className="grid grid-cols-3 !gap-x-2 !py-5">
            {(data?.profileType === "ACT_PROFILE" ||
              data?.profileType === "VIRTUAL_ACT_PROFILE") && (
              <>
                <Box className="!border !p-2 !border-[--light-border-color] !rounded-[2px]">
                  <GigIcon className="text-xl" />
                  <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                    {data?.profileDto?.gigsPerMonth} {s("gigs/month")}
                  </Typography>
                </Box>
                <Box className="!border !p-2 !border-[--light-border-color] !rounded-[2px]">
                  <AddUser className="text-xl" />
                  <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                    {data?.profileDto?.numberOfFollowers} {s("followers")}
                  </Typography>
                </Box>
                <Box className="!border !w-full !p-2 !border-[--light-border-color] !rounded-[2px]">
                  <DollarIcon className="text-xl" />
                  <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                    {s("average")} $ {data?.profileDto?.averageGigsPrice} k /gig*
                  </Typography>
                </Box>
              </>
            )}

            {(data?.profileType === "VENUE_PROFILE" ||
              data?.profileType === "VIRTUAL_VENUE_PROFILE") && (
              <>
                <Box className="!border !p-2 !border-[--light-border-color] !rounded-[2px]">
                  <GigIcon className="text-xl" />
                  <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                    {data?.profileDto?.numberOfBookingsPerMonth} bookings / month
                  </Typography>
                </Box>
                <Box className="!border !p-2 !border-[--light-border-color] !rounded-[2px]">
                  <AddUser className="text-xl" />
                  <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                    {data?.profileDto?.numberOfFollowers} {s("followers")}
                  </Typography>
                </Box>
                <Box className="!border !w-full !p-2 !border-[--light-border-color] !rounded-[2px]">
                  <DollarIcon className="text-xl" />
                  <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                    {s("average")} $ {data?.profileDto?.averageBookingPrice} /bookings*
                  </Typography>
                </Box>
              </>
            )}
          </Box>
        </>
      )}

      {(type === "actReviewLocation" || type === "actLocationSmallScreen") && (
        <>
          <Box className="!my-4 !flex !justify-between">
            <Box className="!flex !items-center !gap-3">
              <LocationSvg className="!w-6 !h-6" />
              <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                {generateLocationString(data?.actLocation || data?.locationDto).length > 30
                  ? generateLocationString(data?.actLocation || data?.locationDto).substring(
                      0,
                      30,
                    ) + "..."
                  : generateLocationString(data?.actLocation || data?.locationDto)}
              </Typography>
            </Box>
            {/* {type === "actReviewLocation" && (
              <IconButton onClick={() => router.push("/location")}>
                <EditIcon className="!w-6 !h-6" />
              </IconButton>
            )} */}
          </Box>
          {/* <Box className="!flex !items-center !gap-3">
            <Globe className="!w-6 !h-6 !stroke-white" />
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
              {t("playInternational")}:
            </Typography>
          </Box>
          <Box className="!flex !items-center !mt-1 !gap-3">
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
              {t("usa")} <span className="!underline">{t("more")}</span>
            </Typography>
          </Box> */}

          <Box className="!flex !flex-wrap !gap-3 !items-center !my-4">
            <Rating value={0} readOnly />
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
              0
            </Typography>
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
              (0 {t("reviews")})
            </Typography>
          </Box>
          {type === "actReviewLocation" && (
            <Box className="!flex !justify-between items-center">
              <Box>
                <Typography className="!text-sm CraftworkGroteskRegular !text-[--hide-color]">
                  {t("startedAt")}
                </Typography>
                <Typography className="!text-sm CraftworkGroteskHeavy !text-[--text-color]">
                  {`$ ${
                    data?.actPayment?.charityPrice
                  } / per ${data?.actPayment?.minimalPricePer.toLowerCase()}`}
                </Typography>
              </Box>
              {/* <Box>
                <IconButton onClick={() => router.push("/payment")}>
                  <EditIcon className="!w-6 !h-6" />
                </IconButton>
              </Box> */}
            </Box>
          )}
          {type === "actReviewLocation" && (
            <Box className="!flex !justify-between items-center">
              <Box>
                <Typography className="!text-sm CraftworkGroteskRegular !text-[--hide-color]">
                  {t("startedAt")}
                </Typography>
                <Typography className="!text-sm CraftworkGroteskHeavy !text-[--text-color]">
                  {`$ ${
                    data?.actPayment?.typicalPrice
                  } / per ${data?.actPayment?.standardPricePer.toLowerCase()}`}
                </Typography>
              </Box>
              {/* <Box>
                <IconButton onClick={() => router.push("/payment")}>
                  <EditIcon className="!w-6 !h-6" />
                </IconButton>
              </Box> */}
            </Box>
          )}
          {type === "actLocationSmallScreen" && (
            <Box className="!my-4 !flex !justify-between !items-center">
              <Box>
                <Typography className="!text-sm CraftworkGroteskRegular !text-[--hide-color]">
                  {t("startedAt")}
                </Typography>
                <Typography className="!text-sm CraftworkGroteskHeavy !text-[--text-color]">
                  {`$ ${data?.paymentsDto?.minimumPrice ?? 0} / per ${
                    data?.paymentsDto?.minPriceChargingType ?? "event"
                  }`}
                </Typography>
              </Box>
              <Box className="!flex !gap-x-3">
                <SocialMediaLinks socials={data?.infoDto?.socialMediaLinks} />
              </Box>
            </Box>
          )}
        </>
      )}
      {type === "actLocation" && (
        <Box className="!flex !gap-x-5 pb-5">
          {/* <RespondClock /> */}
          {/* <Typography className="!text-[--hide-color] !text-sm CraftworkGroteskRegular ">
            {t("respondHour")}
          </Typography> */}
        </Box>
      )}
      <Box className="!flex !gap-3">
        {type !== "actReviewLocation" && (
          <Button
            sx={{
              border: 0,
              minWidth: "2px",
              "&.MuiButtonBase-root": {
                color: "transparent !important",
              },
            }}
            className="!border !border-[--text-color] !rounded-[4px] !mb-4 !py-1"
            onClick={handleEmailClick}
          >
            <Email className="!w-7 !h-7" />
          </Button>
        )}
        {!data?.ownProfile && type !== "actReviewLocation" && (
          <BookingButtonComponent
            profileId={data?.profileDto?.profileId}
            profileType={data?.profileDto?.profileType}
            className="w-full mb-4"
          />
        )}
      </Box>
      {(type === "actReviewLocation" || type === "actLocationSmallScreen") && (
        <Box className="!flex !gap-x-5">
          {/* <RespondClock />
          <Typography className="!text-[--hide-color] !text-sm CraftworkGroteskRegular ">
            {t("respondHour")}
          </Typography> */}
        </Box>
      )}
      {type === "actLocation" && (
        <Box className="!flex !gap-x-3 justify-center">
          <SocialMediaLinks socials={data?.infoDto?.socialMediaLinks} />
        </Box>
      )}
    </Box>
  );
};

export default ActReviewLocation;
