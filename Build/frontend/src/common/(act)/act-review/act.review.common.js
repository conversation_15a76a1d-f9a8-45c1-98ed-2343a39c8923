"use client";
import { Box, Typography, IconButton } from "@mui/material";
import React from "react";
import EditIcon from "@/assets/svg/EditIcon.svg";
import ActReviewLocation from "./act-review-location.info.common";
import { generateLocationString, transformText } from "@/utils";
import { useRouter } from "next/navigation";
import Rating from "@/component/rating/rating.components";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import { useTranslations } from "next-intl";
import { useSelector } from "react-redux";

const ActReviewCommon = ({ review }) => {
  const t = useTranslations("actReview.actReviewCommon");
  const router = useRouter();
  const { previewData } = useSelector((state) => state.act);
  return (
    <Box className="lg:!flex lg:!justify-between !min-h-full !pb-28">
      <Box className="!pr-5 w-[78%]">
        <Box className="!flex !justify-between !items-center">
          <Typography className="!text-[--text-color] !text-4xl CraftworkGroteskHeavy !leading-[43.3px] !pt-5">
            {review?.actInfo?.profileName}
          </Typography>
          <IconButton onClick={() => router.push("/act-information")}>
            <EditIcon className="!w-6 !h-6 !cursor-pointer" />
          </IconButton>
        </Box>
        <Box className="lg:!hidden !flex !flex-wrap !gap-3 !items-center !my-4">
          <Rating value={4} readOnly />
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
            4.0
          </Typography>
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
            (17 {t("reviews")})
          </Typography>
        </Box>
        <Box className="!my-4 lg:!hidden !flex !justify-between">
          <Box className="!flex !items-center !gap-3">
            <LocationSvg className="!w-6 !h-6" />
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
              {generateLocationString(review?.actLocation).length > 30
                ? generateLocationString(review?.actLocation).substring(0, 30) + "..."
                : generateLocationString(review?.actLocation)}
            </Typography>
          </Box>
          <IconButton onClick={() => router.push("/location")}>
            <EditIcon className="!w-6 !h-6" />
          </IconButton>
        </Box>
        {/* <Box className="lg:!hidden !flex !items-center !gap-3">
          <Globe className="!w-6 !h-6 !stroke-white" />
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
            {t("playInternational")}:
          </Typography>
        </Box>
        <Box className="lg:!hidden !flex !items-center !mt-1 !gap-3">
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
            {t("usa")}
            <span className="!underline">{t("more")}</span>
          </Typography>
        </Box> */}
        <Box className="!flex !flex-wrap !gap-2 !py-4">
          {review?.musicGenre?.map((genre, index) => (
            <>
              <Box key={index} className="!bg-[--divider-color] !px-3 !rounded-[4px] !py-[6px]">
                <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                  {transformText(genre.name)}
                </Typography>
              </Box>
              {genre.members.length > 0 &&
                genre.members.map((member, index) => (
                  <Box key={index} className="!bg-[--divider-color] !px-3 !rounded-[4px] !py-[6px]">
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                      {transformText(member)}
                    </Typography>
                  </Box>
                ))}
            </>
          ))}
        </Box>
        <Typography className="!text-[--text-color] !text-2xl CraftworkGroteskMedium !leading-[28.8px]">
          {previewData?.profile?.option === "Act" ? t("aboutAct") : "About the Venue"}
        </Typography>
        <Typography
          className="!text-[--text-color] !break-words !text-sm CraftworkGroteskRegular !pb-3"
          // as="pre"
        >
          {review?.actInfoSocial?.bio}
        </Typography>
      </Box>
      <ActReviewLocation data={review} type="actReviewLocation" />
    </Box>
  );
};

export default ActReviewCommon;
