import FacebookOutlinedIcon from "@mui/icons-material/FacebookOutlined";
import InstagramIcon from "@mui/icons-material/Instagram";
import YouTubeIcon from "@mui/icons-material/YouTube";
import Link from "next/link";
import SoundCloud from "@/assets/svg/SoundCloud.svg";
import Spotify from "@/assets/svg/Spotify.svg";

const SocialMediaLinks = ({ socials }) => {
  return (
    <>
      {socials &&
        socials.length > 0 &&
        socials.map((item, index) => {
          if (item.includes("facebook")) {
            return (
              <Link href={item} key={index} target="_blank">
                <FacebookOutlinedIcon className="!w-6 !h-6 !text-[--text-color]" />
              </Link>
            );
          }
          if (item.includes("instagram")) {
            return (
              <Link href={item} key={index} target="_blank">
                <InstagramIcon className="!w-6 !h-6 !text-[--text-color]" />
              </Link>
            );
          }
          if (item.includes("youtube")) {
            return (
              <Link href={item} key={index} target="_blank">
                <YouTubeIcon className="!w-6 !h-6 !text-[--text-color]" />
              </Link>
            );
          }
          if (item.includes("spotify")) {
            return (
              <Link href={item} key={index} target="_blank">
                <Spotify className="!w-6 !h-6 !text-[--text-color]" />
              </Link>
            );
          }
          if (item.includes("soundcloud")) {
            return (
              <Link href={item} key={index} target="_blank">
                <SoundCloud className="!w-6 !h-6 !text-[--text-color]" />
              </Link>
            );
          }
        })}
    </>
  );
};

export default SocialMediaLinks;
