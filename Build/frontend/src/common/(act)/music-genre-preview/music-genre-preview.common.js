import { Close, Clear } from "@mui/icons-material";
import { Box, Typography } from "@mui/material";
import { useSelector, useDispatch } from "react-redux";
import { transformText } from "@/utils";
import { setPreviewData } from "@/store/slice/act/act.slice";
import { useTranslations } from "next-intl";
const MusicGenerePreview = () => {
  const t = useTranslations("musicGenre");
  const dispatch = useDispatch();
  const { previewData } = useSelector((state) => state.act);
  return (
    <Box className="!hidden !bg-[--inprogress-color] !min-h-screen lg:!block lg:basis-1/2 !px-14 !pt-20">
      <Box className="!flex !items-center !justify-between">
        <Typography className="!text-[--bg-color] !text-2xl CraftworkGroteskMedium">
          {t("musicGenrePreview.selectedGenres")} ({previewData?.musicGenre?.length ?? 0})
        </Typography>
        {previewData?.musicGenre?.length > 0 && (
          <Box
            className="!flex !cursor-pointer !gap-x-1"
            onClick={() => dispatch(setPreviewData({ musicGenre: [] }))}
          >
            <Typography className="!text-[--bg-color] !text-sm CraftworkGroteskHeavy !underline">
              {t("musicGenrePreview.clearAll")}
            </Typography>
            <Close className="!text-[--bg-color] !text-base font-black" />
          </Box>
        )}
      </Box>
      <Box className="!flex !flex-wrap !gap-3 !my-8">
        {previewData?.musicGenre?.length > 0 ? (
          previewData?.musicGenre?.map((data, index) => (
            <Box key={index} className="!flex flex-wrap !gap-1 ">
              <Box
                key={index}
                className="!flex !gap-x-1 !items-center !bg-[--image-bg] !py-2 !px-3 !rounded-[4px] capitalize"
              >
                <Typography className="!text-[--bg-color] !text-sm CraftworkGroteskRegular">
                  {transformText(data.name)}
                </Typography>
                <Clear className="!text-sm" />
              </Box>
              {data.members.length > 0 &&
                data.members.map((member, index) => (
                  <Box
                    key={index}
                    className="!flex !gap-x-1 !items-center !bg-[--image-bg] !py-2 !px-3 !rounded-[4px] capitalize"
                  >
                    <Typography className="!text-[--bg-color] !text-sm CraftworkGroteskRegular">
                      {transformText(member)}
                    </Typography>
                    <Clear className="!text-sm" />
                  </Box>
                ))}
            </Box>
          ))
        ) : (
          <Typography className="!text-sm CraftworkGroteskRegular">
            {t("musicGenrePreview.noMusic")}
          </Typography>
        )}
      </Box>
    </Box>
  );
};
export default MusicGenerePreview;
