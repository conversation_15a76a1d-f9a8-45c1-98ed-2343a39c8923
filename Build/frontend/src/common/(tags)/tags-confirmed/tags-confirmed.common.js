import { Box, Typography } from "@mui/material";
import React from "react";
import Confirmed from "@/assets/svg/Confirmed.svg";

const ConfirmedTag = ({ label = "Confirmed" }) => {
  return (
    <Box className="!flex !gap-x-1 !items-center !bg-[--confirmed-color] !p-1 !rounded-[2px]">
      <Confirmed className="!w-[14px] !h-[14px]" />
      <Typography className="!text-[--text-color] !text-sm CraftworkGrotesk-SemiBold !leading-[15.4px]">
        {label}
      </Typography>
    </Box>
  );
};

export default ConfirmedTag;
