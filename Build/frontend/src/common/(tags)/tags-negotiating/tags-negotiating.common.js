import { Box, Typography } from "@mui/material";
import React from "react";
import InProgressIcon from "@/assets/svg/InProgressIcon.svg";

const NegotiatingTag = () => {
  return (
    <Box className="!flex !gap-x-1 !items-center !bg-[--inprogress-color] !p-1 !rounded-[2px]">
      <InProgressIcon className="!w-[12px] !h-[12px]" />
      <Typography className="!text-[--text-color] !text-sm CraftworkGroteskSemiBold !leading-[15.4px]">
        Negotiating
      </Typography>
    </Box>
  );
};

export default NegotiatingTag;
