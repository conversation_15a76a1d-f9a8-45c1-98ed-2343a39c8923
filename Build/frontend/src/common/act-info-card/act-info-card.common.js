"use client";
import { Button, CommonImage } from "@/component";
import { Box, Typography } from "@mui/material";
import React from "react";
import Slider from "react-slick";
import Rating from "@/component/rating/rating.components";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import DollarIcon from "@/assets/svg/DollarIcon.svg";
import { SouthEast } from "@mui/icons-material";
import { generateLocationString } from "@/utils";
import MediaImage from "@/assets/png/MediaImage.png";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import ContractIcon from "@/assets/svg/ContractIcon.svg";
import ConfirmedTag from "../(tags)/tags-confirmed/tags-confirmed.common";
import Link from "next/link";
import ViewSvg from "@/assets/svg/ViewSvg.svg";

const ActInfoCard = ({ data, type, contractData }) => {
  const datas = [data?.primaryActProfileDetails] ?? data?.actProfileDetailsList;

  const lang = useLocale();
  const settings = {
    className: "act-info-card-slider slider-class",
    dots: true,
    infinite: false,
    slidesToShow: 1,
    slidesToScroll: 1,
    prevArrow: <></>,
    nextArrow: <></>,
  };
  const router = useRouter();
  return (
    <div>
      <Typography className="text-2xl font-craftWorkMedium text-[--text-color]">{type}</Typography>
      {datas?.map((profileData, index) => {
        const images = profileData?.mediaDto?.imageUrls || [];
        return (
          <Box
            key={index}
            className="bg-[--footer-bg] border border-[--divider-color] md:flex rounded-[5px] my-6"
          >
            <Slider {...settings} className="max-w-[200px] sm:items-center">
              {images.length > 0 ? (
                images.map((image, imgIndex) => (
                  <CommonImage
                    key={imgIndex}
                    className="w-[200px] h-[200px] object-fill"
                    src={image}
                    alt={`Profile_image_${index}_${imgIndex}`}
                    width={200}
                    height={200}
                  />
                ))
              ) : (
                <CommonImage
                  className="w-[200px] h-[200px] object-fill"
                  src={MediaImage}
                  alt={`profile_image_default_${index}`}
                  width={200}
                  height={200}
                />
              )}
            </Slider>
            <Box className="p-3 w-full">
              <Box className="flex justify-between items-center">
                <Typography className="text-[--text-color] font-craftWorkGX text-2xl">
                  {profileData?.profileDto?.profileName}
                </Typography>
              </Box>
              <Box className="!flex !flex-wrap !gap-3 !items-center !my-2">
                <Rating value={data?.profileRatingDto?.overallRating} readOnly />
                {/* <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                  4.0
                </Typography> */}
                <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                  ({data?.profileRatingDto?.numberOfRatings} reviews)
                </Typography>
              </Box>
              <Box className="!flex !items-center !gap-3">
                <LocationSvg className="!w-6 !h-6" />
                <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                  {generateLocationString(profileData?.locationDto).length > 30
                    ? generateLocationString(profileData?.locationDto).substring(0, 30) + "..."
                    : generateLocationString(profileData?.locationDto)}
                </Typography>
              </Box>
              {/* <Box className="!flex !items-center !gap-3 my-2">
                <FeetIcon className="!w-6 !h-6" />
                <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                  1500 sq feet
                </Typography>
              </Box> */}
              <Box className="md:!flex !items-center justify-between">
                <Box className="!flex !items-center !gap-3">
                  <DollarIcon className="!w-6 !h-6" />
                  <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                    {profileData?.paymentsDto?.currency} {profileData?.paymentsDto?.minimumPrice}
                  </Typography>
                </Box>
                <Button
                  className="!bg-[--text-color] max-sm:mt-3 capitalize !text-[--bg-color] !rounded-[5px] !py-2 !px-4"
                  onClick={() => router.push(`/${lang}/${profileData?.profileDto?.profileId}/view`)}
                >
                  <Typography className="!text-sm font-craftWorkHeavy">View Profile</Typography>
                  <SouthEast className="!text-[--bg-color] !size-5" />
                </Button>
              </Box>
            </Box>
          </Box>
        );
      })}
      <Box className="bg-[--footer-bg] justify-between border gap-2 flex-wrap border-[--divider-color] px-3 py-4 flex rounded-[5px] my-6">
        <Box className="flex gap-2 cursor-default items-center">
          <ContractIcon className="w-6 h-6" />
          <Typography className="text-[--text-color] font-craftWorkRegular text-sm">
            Contract details
          </Typography>
        </Box>
        <Box className="flex gap-4 flex-wrap items-center">
          <Box className="cursor-default">
            <ConfirmedTag />
          </Box>
          <Link
            href={`/${lang}/booking-details?contract-id=${contractData?.contractId?.[0]}`}
            className="flex gap-2 items-center"
          >
            <Typography className="text-[--text-color] underline font-craftWorkHeavy text-sm">
              View Contract
            </Typography>
            <ViewSvg className="w-5 h-5" />
          </Link>
        </Box>
      </Box>
      {/* <Box className="bg-[--footer-bg] justify-between border border-[--divider-color] px-3 py-4 flex flex-wrap rounded-[5px] my-6">
        <Box className="flex gap-2 items-center">
          <PaymentIcon className="w-6 h-6" />
          <Typography className="text-[--text-color] font-craftWorkRegular text-sm">
            Payments
          </Typography>
        </Box>
        <Box className="flex gap-4 items-center">
          <Typography className="text-sm text-[--text-color] font-craftWorkHeavy">$ 3k</Typography>
          <ConfirmedTag />
          <Link href="#" className="flex gap-1 items-center">
            <Typography className="text-[--text-color] underline font-craftWorkHeavy text-sm">
              Make a deposit
            </Typography>
            <SouthEast className="text-[--text-color] w-5 h-5" />
          </Link>
        </Box>
      </Box> */}
      {/* <Button>
        <Typography className="text-sm normal-case CraftworkGroteskRegular text-[--warn-color]">
          Invoice func?
        </Typography>
      </Button> */}
    </div>
  );
};

export default ActInfoCard;
