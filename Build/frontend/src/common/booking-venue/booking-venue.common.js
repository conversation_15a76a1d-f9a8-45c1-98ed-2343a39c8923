"use client";
import { Box, Typography } from "@mui/material";
import React from "react";
import CardCarousel from "../profile-card/profile-card.carousel.common";
import Rating from "@/component/rating/rating.components";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import { Button } from "@/component";
import { SouthEast } from "@mui/icons-material";
import Slider from "react-slick";
import classNames from "classnames";
import { useTranslations } from "next-intl";

const BookingVenue = ({ profiles, selectedItem, onItemClick }) => {
  const t = useTranslations("bookingVenue");
  const settings = {
    className: "slide-classes",
    dots: false,
    infinite: true,
    speed: 1000,
    slidesToShow: 2,
    slidesToScroll: 1,
    swipeToSlide: true,
    autoplay: false,
    autoplaySpeed: 10000,
    prevArrow: <></>,
    nextArrow: <></>,
    responsive: [
      {
        breakpoint: 1224,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 2,
        },
      },
      {
        breakpoint: 800,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2,
          initialSlide: 2,
        },
      },
      {
        breakpoint: 520,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  return (
    <Box className="w-[99%]">
      <Slider {...settings}>
        {profiles?.map((profile, index) => (
          <Box
            key={index}
            className={classNames("border mb-4 rounded-[4px] bg-[--footer-bg] cursor-pointer", {
              "border-[--inprogress-color]": selectedItem?.id === profile.id,
              "border-[--divider-color]": selectedItem?.id !== profile.id,
            })}
          >
            <CardCarousel images={profile.profileImageUrls} className="slider-class" />
            <Box className="px-4 py-5">
              <Typography className="text-[--text-color] text-2xl font-craftWorkGX">
                {profile.profileName?.length > 12
                  ? profile.profileName.substring(0, 12) + "..."
                  : profile.profileName}
              </Typography>
              <Box className="flex flex-wrap gap-3 items-center my-4">
                <Rating value={profile.popularityStars} readOnly />
                <Typography className="text-[--text-color] text-sm CraftworkGroteskRegular">
                  {profile.popularityStars}
                </Typography>
                <Typography className="text-[--text-color] text-sm CraftworkGroteskRegular">
                  {`(${profile.numberOfReviews} reviews)`}
                </Typography>
              </Box>
              <Box className="flex gap-2 items-center">
                <LocationSvg className="text-2xl" />
                <Typography className="text-[--text-color] text-sm CraftworkGroteskRegular">
                  {t("lookingAddressLine")}
                </Typography>
              </Box>
              <Button
                disabled={selectedItem?.id === profile.id}
                className={classNames("!normal-case flex gap-2 rounded-[4px] py-3 w-full mt-3", {
                  "!bg-[--text-color]": selectedItem?.id !== profile.id,
                  "!bg-[--disabled-color]": selectedItem?.id === profile.id,
                })}
                onClick={() => onItemClick(profile)}
              >
                <Typography className="text-[--bg-color] text-sm CraftworkGroteskHeavy">
                  {selectedItem?.id === profile.id ? t("selected") : t("select")}
                </Typography>
                <SouthEast className="text-[--bg-color] text-lg" />
              </Button>
            </Box>
          </Box>
        ))}
      </Slider>
    </Box>
  );
};

export default BookingVenue;
