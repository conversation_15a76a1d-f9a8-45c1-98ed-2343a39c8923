// "use client";
// import React, { useState, useEffect } from "react";
// import { Box, Typography } from "@mui/material";
// import Slider from "react-slick";
// import "slick-carousel/slick/slick.css";
// import "slick-carousel/slick/slick-theme.css";
// import { CommonImage } from "@/component";
// import VectorSvg from "@/assets/svg/Vector.svg";
// import Vector1Svg from "@/assets/svg/Vector-1.svg";
// import HomeSearch from "@/common/search/home/<USER>";
// import { useTranslations } from "next-intl";

// const Carousel = ({ images }) => {
//   // eslint-disable-next-line
//   const [count, setCount] = useState(0);
//   const t = useTranslations("Home");

//   useEffect(() => {
//     const targetCount = 1000;
//     const animationDuration = 4000;
//     const intervalDuration = 10;
//     const steps = animationDuration / intervalDuration;
//     const increment = Math.ceil(targetCount / steps);

//     let currentCount = 0;
//     let step = 0;

//     const intervalId = setInterval(() => {
//       currentCount += increment;
//       step++;

//       if (currentCount >= targetCount || step >= steps) {
//         clearInterval(intervalId);
//         currentCount = targetCount;
//       }

//       setCount(currentCount);
//     }, intervalDuration);

//     return () => {
//       clearInterval(intervalId);
//     };
//   }, []);

//   const settings = {
//     className: "slider variable-width",
//     dots: true,
//     infinite: true,
//     speed: 1000,
//     slidesToShow: 1,
//     slidesToScroll: 1,
//     adaptiveHeight: true,
//     swipeToSlide: true,
//     autoplay: true,
//     autoplaySpeed: 10000,
//     prevArrow: <></>,
//     nextArrow: <></>,
//   };

//   return (
//     <>
//       <Box className=" object-cover hidden lg:inline">
//         <Slider {...settings} className="slider-home">
//           {images?.map((image, index) => (
//             <Box className="!relative " key={index}>
//               <CommonImage
//                 className="object-cover !w-full  !rounded-[4px] !border !border-[--text-color]"
//                 key={index}
//                 src={image}
//                 alt={`Slide ${index}`}
//               />
//               <VectorSvg className="!absolute !right-20 !top-32 !w-[50px] !h-[48px]" />
//               <Vector1Svg className="!absolute !bottom-36 !left-44 !w-20 !h-[88px]" />
//               <Box className="!absolute !left-0 !right-0 !top-1/4 !m-auto flex flex-col items-center">
//                 <Typography className=" !text-[--text-color] CraftworkGroteskMedium !text-center !text-[40px] !leading-[48px]">
//                   {t("Carousel.Events")} <br />
//                   {t("Carousel.Seemlessly")}
//                 </Typography>
//               </Box>
//             </Box>
//           ))}
//         </Slider>
//       </Box>
//       <Box className="lg:!absolute !left-0 !right-0 lg:!top-1/2 !m-auto flex flex-col items-center">
//         {/* <HomeSearch /> */}
//         <HomeSearch
//           showDatePicker={false}
//           showSearchInput={false}
//           showProfileTypeDropdown={false}
//         />
//       </Box>
//     </>
//   );
// };
// export default Carousel;

//memoized carousel to avoid multiple re-renders
"use client";
import React, { memo, useMemo } from "react";
import { Box, Typography } from "@mui/material";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { CommonImage } from "@/component";
import VectorSvg from "@/assets/svg/Vector.svg";
import Vector1Svg from "@/assets/svg/Vector-1.svg";
import HomeSearch from "@/common/search/home/<USER>";
import { useTranslations } from "next-intl";

// Memoize static components
const MemoVector = memo(VectorSvg);
const MemoVector1 = memo(Vector1Svg);
const MemoCommonImage = memo(CommonImage);
const MemoHomeSearch = memo(HomeSearch);

const Carousel = ({ images }) => {
  const t = useTranslations("Home");

  // Memoize settings to prevent recreating on every render
  const settings = useMemo(
    () => ({
      className: "slider variable-width",
      dots: true,
      infinite: true,
      speed: 1000,
      slidesToShow: 1,
      slidesToScroll: 1,
      adaptiveHeight: true,
      swipeToSlide: true,
      autoplay: true,
      autoplaySpeed: 10000,
      prevArrow: <></>,
      nextArrow: <></>,
    }),
    [],
  );

  // Memoize slide content
  const slides = useMemo(
    () =>
      images?.map((image, index) => (
        <Box className="!relative" key={`slide-${image.src}-${index}`}>
          <MemoCommonImage
            className="object-cover !w-full !rounded-[4px] !border !border-[--text-color]"
            src={image}
            alt={`Slide ${index}`}
          />
          <MemoVector className="!absolute !right-20 !top-32 !w-[50px] !h-[48px]" />
          <MemoVector1 className="!absolute !bottom-36 !left-44 !w-20 !h-[88px]" />
          <Box className="!absolute !left-0 !right-0 !top-1/4 !m-auto flex flex-col items-center">
            <Typography className="!text-[--text-color] CraftworkGroteskMedium !text-center !text-[40px] !leading-[48px]">
              {t("Carousel.Events")} <br />
              {t("Carousel.Seemlessly")}
            </Typography>
          </Box>
        </Box>
      )),
    [images, t],
  );

  return (
    <>
      <Box className="object-cover hidden lg:inline">
        <Slider {...settings} className="slider-home">
          {slides}
        </Slider>
      </Box>
      <Box className="lg:!absolute !left-0 !right-0 lg:!top-1/2 !m-auto flex flex-col items-center">
        <MemoHomeSearch
          showDatePicker={false}
          showSearchInput={false}
          showProfileTypeDropdown={false}
        />
      </Box>
    </>
  );
};

export default memo(Carousel, (prevProps, nextProps) => {
  // Only re-render if images array changes
  return prevProps.images === nextProps.images;
});
