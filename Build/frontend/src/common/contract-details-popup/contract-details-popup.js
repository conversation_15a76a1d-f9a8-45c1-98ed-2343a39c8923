import { Button } from "@/component";
import {
  addGsMsgTemplates,
  gsMsgTemplates,
  putGsMsgTemplates,
} from "@/store/slice/booking/booking.slice";
import { Clear, SouthEast } from "@mui/icons-material";
import {
  Box,
  Dialog,
  DialogContent,
  Drawer,
  InputLabel,
  TextField,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import React, { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

const ContractDetailsPopup = ({
  open,
  handleClose,
  message,
  setPrevMess,
  selectedMessage,
  setSelectedMessage,
  type,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const dispatch = useDispatch();
  const {
    handleSubmit,
    control,
    setValue,
    formState: {},
  } = useForm({
    //resolver,
    defaultValues: {
      name: "",
      message: message ?? "",
    },
    mode: "all",
  });

  useEffect(() => {
    if (message) {
      setValue("message", message);
      setValue("name", selectedMessage?.name);
    }
  }, [message, selectedMessage]);
  const updateContractsDetails = (data) => {
    if (type === "save") {
      dispatch(putGsMsgTemplates(data))
        .unwrap()
        .then(() => {
          dispatch(gsMsgTemplates())
            .unwrap()
            .then((res) => {
              setPrevMess(res.data.data);
              setSelectedMessage(null);
            });
          handleClose();
        })
        .catch(() => {});
    } else {
      dispatch(addGsMsgTemplates(data))
        .unwrap()
        .then(() => {
          dispatch(gsMsgTemplates())
            .unwrap()
            .then((res) => {
              setPrevMess(res.data.data);
            });
          handleClose();
        })
        .catch(() => {});
    }
  };

  const content = (
    <DialogContent className="!max-w-xl !bg-[--footer-bg] p-6">
      <Box className="!w-full">
        <InputLabel className="text-sm w-full text-[--text-color] py-2 CraftworkGroteskRegular">
          Name
        </InputLabel>
        <Controller
          name="name"
          control={control}
          render={({ field }) => (
            <TextField
              type="text"
              size="small"
              {...field}
              placeholder="Name"
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& .MuiOutlinedInput-root": {
                  color: "var(--text-color)",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              className="!border !w-full !py-1 CraftworkGroteskRegular !border-[--text-color] rounded-[2px] !mb-3"
            />
          )}
        />
      </Box>
      <Box className="!w-full">
        <InputLabel className="text-sm w-full text-[--text-color] py-2 CraftworkGroteskRegular">
          message
        </InputLabel>
        <Controller
          name="message"
          control={control}
          render={({ field }) => (
            <TextField
              type="text"
              size="small"
              multiline
              disabled
              rows={8}
              {...field}
              placeholder="Message"
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& .MuiOutlinedInput-root": {
                  color: "var(--text-color)",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              className="!border !w-full !h-[150px] !py-1 CraftworkGroteskRegular !border-[--text-color] rounded-[2px] !mb-3"
            />
          )}
        />
      </Box>
      <Box
        className={` ${isMobile ? "fixed flex justify-between items-center px-4 py-3 left-0 right-0 bottom-0 border-[1px] border-[--divider-color]" : "flex justify-between"}`}
      >
        <Button
          className=" !flex !gap-x-2 items-center !mt-5"
          onClick={handleClose}
          sx={{
            border: 0,
            "&.MuiButtonBase-root": {
              color: "white !important",
            },
          }}
        >
          <Typography className="!normal-case CraftworkGroteskHeavy text-center !underline !text-sm !leading-[15.4px] !text-[--text-color]">
            Close
          </Typography>
          <Clear className="text-[--text-color] text-xl" />
        </Button>

        <Button
          className="!bg-[--text-color] !flex !gap-x-1 !px-4 items-center !mt-5"
          sx={{
            border: 0,
            "&.MuiButtonBase-root": {
              color: "white !important",
            },
          }}
          onClick={handleSubmit(updateContractsDetails)}
        >
          <Typography className="!normal-case CraftworkGroteskHeavy text-center !text-sm !leading-[15.4px] !text-[--bg-color]">
            Save
          </Typography>
          <SouthEast className="text-[--bg-color] text-xl" />
        </Button>
      </Box>
    </DialogContent>
  );
  return (
    <>
      {isMobile ? (
        <Drawer
          anchor="bottom"
          open={open}
          onClose={handleClose}
          sx={{
            "& .MuiPaper-root": {
              height: "95%",
              backgroundColor: "var(--bg-color)",
            },
          }}
        >
          {content}
        </Drawer>
      ) : (
        <Dialog
          open={open}
          maxWidth={false}
          sx={{ "& .MuiBackdrop-root": { backgroundColor: "#181B1BE5" } }}
        >
          {content}
        </Dialog>
      )}
    </>
  );
};

export default ContractDetailsPopup;
