import { Box, Typography } from "@mui/material";
import React from "react";
import { CheckBox, CommonImage } from "@/component";
import dayjs from "dayjs";
import ConfirmedTag from "@/common/(tags)/tags-confirmed/tags-confirmed.common";
import DeclinedTag from "@/common/(tags)/tags-declined/tags-declined.common";
import Paginate from "@/containers/paginate/paginate.container";

const ContractInfoCard = ({ contracts, type, selectedContracts, setSelectedContracts }) => {
  const handleCheckboxChange = (contract) => {
    setSelectedContracts((prevSelected) => {
      if (prevSelected.some((item) => item.contractId === contract.contractId)) {
        // Remove contract if already selected
        return prevSelected.filter((item) => item.contractId !== contract.contractId);
      }
      // Add contract if not selected
      return [...prevSelected, contract];
    });
  };

  return (
    <Box className="lg:!pt-6 overflow-auto lg:!max-h-[600px]">
      {contracts.length > 0 &&
        contracts?.map(
          (data) =>
            data && (
              <Box
                key={data.id}
                className="lg:!min-h-[66px] !h-fit p-2 !mb-4 !cursor-default !border !flex flex-wrap lg:!justify-between gap-2 !items-center !px-3 !rounded-[4px] !border-[--divider-color] !bg-[--footer-bg]"
              >
                {type === "select" && (
                  <CheckBox
                    sx={{ color: "#EFEFEF", marginRight: "5px" }}
                    checked={selectedContracts.some((item) => item.contractId === data.contractId)} // Check if contract is selected
                    onChange={() => handleCheckboxChange(data)}
                  />
                )}
                <Box className="!flex !gap-x-3 items-center">
                  {data.bookingParty === "VENUE" && data?.venueProfileImageUrls?.[0] && (
                    <CommonImage
                      src={data?.venueProfileImageUrls?.[0]}
                      alt="image"
                      width={60}
                      height={60}
                      className="rounded-full w-10 h-10 object-cover"
                    />
                  )}
                  {data.bookingParty === "ACT" && data?.actProfileImageUrls?.[0] && (
                    <CommonImage
                      src={data?.actProfileImageUrls?.[0]}
                      alt="image"
                      width={60}
                      height={60}
                      className="rounded-full w-10 h-10 object-cover"
                    />
                  )}

                  {data.bookingParty === "VENUE" && (
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskBold">
                      {`[ ${data.venueProfileName}]`}
                    </Typography>
                  )}
                  {data.bookingParty === "USER" && (
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskBold">
                      {`[ ${data?.purchaserInfo?.firstName} ${data?.purchaserInfo?.lastName}]`}
                    </Typography>
                  )}
                  {data.bookingParty === "ACT" && (
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskBold">
                      {`[ ${data.actProfileName}]`}
                    </Typography>
                  )}

                  {/* <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                            x
                          </Typography> */}
                  {data.otherParty === "VENUE" && data?.venueProfileImageUrls?.[0] && (
                    <CommonImage
                      src={data?.venueProfileImageUrls?.[0]}
                      alt="image"
                      width={60}
                      height={60}
                      className="rounded-full w-10 h-10 object-cover"
                    />
                  )}
                  {data.otherParty === "ACT" && data?.actProfileImageUrls?.[0] && (
                    <CommonImage
                      src={data?.actProfileImageUrls?.[0]}
                      alt="image"
                      width={60}
                      height={60}
                      className="rounded-full w-10 h-10 object-cover"
                    />
                  )}
                  {data.otherParty === "VENUE" && (
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskBold">
                      {`[ ${data.venueProfileName}]`}
                    </Typography>
                  )}
                  {data.otherParty === "USER" && (
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskBold">
                      {`[ ${data?.purchaserInfo?.firstName} ${data?.purchaserInfo?.lastName}]`}
                    </Typography>
                  )}
                  {data.otherParty === "ACT" && (
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskBold">
                      {`[ ${data.actProfileName}]`}
                    </Typography>
                  )}
                  <Box>{data?.contractTag}</Box>
                </Box>

                <Box className="!flex lg:gap-x-3 items-center">
                  <Box className="!flex !gap-x-2 items-center">
                    <Typography className="!text-[--text-color] w-24 !text-sm CraftworkGroteskRegular">
                      {dayjs(data?.scheduleTime?.startDate).format("MMM DD, YYYY")}
                    </Typography>
                  </Box>
                  <Typography className="!pl-4 !text-[--text-color] !text-sm CraftworkGroteskRegular">
                    {data?.preformancePrice}
                  </Typography>
                  {data?.contractState === "CONFIRMED" && <ConfirmedTag />}
                  {data?.contractState === "DECLINED" && <DeclinedTag name={"DECLINED"} />}
                  {/* Handle other states similarly */}
                </Box>
              </Box>
            ),
        )}

      <Box className="flex justify-center lg:mb-0 mb-20">
        {contracts?.totalPages > 1 && (
          <Paginate totalRecords={contracts?.totalElements} perPageRecord={contracts.size} />
        )}
      </Box>
    </Box>
  );
};

export default ContractInfoCard;
