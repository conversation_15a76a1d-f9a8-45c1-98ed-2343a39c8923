import { But<PERSON> } from "@/component";
import {
  <PERSON>,
  Dialog,
  DialogContent,
  Drawer,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { Clear } from "@mui/icons-material";
import { useDispatch } from "react-redux";
import { addContractInfo, getContractList } from "@/store/slice/booking/booking.slice";
import ContractInfoCard from "../contract-info-card/contract-info-card.common";

const ContractInfoDilog = ({ open, handleClose, eventId, actContractDetailsList, setFetch }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const [contractInfoData, setContractInfoData] = useState([]);
  const dispatch = useDispatch();
  const [selectedContracts, setSelectedContracts] = useState([]);

  // Synchronize selectedContracts with actContractDetailsList when dialog opens
  useEffect(() => {
    if (open) {
      setSelectedContracts(actContractDetailsList || []); // Default to an empty array if undefined
    }
  }, [open, actContractDetailsList]);

  useEffect(() => {
    dispatch(getContractList(eventId))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setContractInfoData(response.data.data.content);
        }
      })
      .catch(() => {
        //showSnackbar(error, "error");
      });
  }, []);

  const handleSaveContract = () => {
    dispatch(
      addContractInfo({
        eventId,
        data: {
          //primeContractId: "string",
          // actContractIdList: [
          //   ...(actContractDetailsList || []), // Spread actContractDetailsList if it exists
          //   ...(selectedContracts || []), // Spread selectedContracts if it exists
          // ],
          actContractIdList: selectedContracts.map((item) => item.contractId),
        },
      }),
    )
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setFetch((prev) => prev + 1);
          handleClose();
        }
      })
      .catch(() => {
        //showSnackbar(error, "error");
      });
  };

  const content = (
    <DialogContent className="!max-w-3xl !bg-[--footer-bg] p-6">
      <ContractInfoCard
        contracts={contractInfoData}
        type="select"
        selectedContracts={selectedContracts}
        setSelectedContracts={setSelectedContracts}
      />
      <Box
        className={` ${isMobile ? "fixed flex justify-between items-center px-4 py-3 left-0 right-0 bottom-0 border-[1px] border-[--divider-color]" : "flex justify-between"}`}
      >
        <Button
          className=" !gap-x-2 !py-3"
          sx={{
            minWidth: 0,
            "&.MuiButtonBase-root": {
              color: "white !important",
            },
          }}
          onClick={handleClose}
        >
          <Typography className="!normal-case CraftworkGroteskHeavy !underline !text-sm !leading-[15.4px] !text-[--text-color]">
            Cancel
          </Typography>
          <Clear className="!text-lg !text-[--text-color]" />
        </Button>
        <Button
          className=" !p-3 !flex !gap-x-2 !bg-[--text-color] !rounded-[4px]"
          sx={{
            minWidth: 0,
            border: 0,
            "&.MuiButtonBase-root": {
              color: "white !important",
            },
          }}
          onClick={handleSaveContract}
        >
          <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
            Save
          </Typography>
          <ArrowSouthEast />
        </Button>
      </Box>
    </DialogContent>
  );
  return (
    <>
      {isMobile ? (
        <Drawer
          anchor="bottom"
          open={open}
          onClose={handleClose}
          sx={{
            "& .MuiPaper-root": {
              height: "95%",
              backgroundColor: "var(--bg-color)",
            },
          }}
        >
          {content}
        </Drawer>
      ) : (
        <Dialog
          open={open}
          maxWidth={false}
          sx={{ "& .MuiBackdrop-root": { backgroundColor: "#181B1BE5" } }}
        >
          {content}
        </Dialog>
      )}
    </>
  );
};

export default ContractInfoDilog;
