"use client";
import { But<PERSON> } from "@/component";
import { KeyboardArrowDownOutlined } from "@mui/icons-material";
import { Box, Menu, MenuItem } from "@mui/material";
import React, { useState } from "react";
import { data } from "./countrycode-dropdown.data.common";

const CountryCodeDropdown = ({ selectedValue, onSelect }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [search, setSearch] = useState("");
  const filteredData = data.filter((code) =>
    code.country.toLowerCase().includes(search.toLowerCase()),
  );

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setSearch("");
  };

  const handleMenuItemClick = (value) => {
    onSelect(value);
    handleClose();
  };

  return (
    <Box className="">
      <Button
        onClick={handleClick}
        className="!border-[1px] !border-solid !border-[--text-color] !bg-[--footer-color] !text-[--text-color] !rounded-[4px] CraftworkGroteskMedium md:!text-base !text-[14px] !text-base !normal-case !py-3 !px-2 !justify-between"
      >
        {selectedValue}
        <KeyboardArrowDownOutlined className="text-[--text-color] !w-4 !h-4" />
      </Button>
      <Menu
        anchorEl={anchorEl}
        keepMounted
        open={Boolean(anchorEl)}
        onClose={handleClose}
        className=" !shadow-none"
        sx={{
          "& .MuiMenu-list": {
            paddingTop: 0,
          },
        }}
        PaperProps={{
          style: {
            boxShadow: "none",
            borderRadius: "4px",
            maxHeight: "300px",
            overflowY: "auto",
          },
        }}
      >
        {filteredData.map((code) => (
          <MenuItem
            key={code.country}
            className="Poppins400 md:!text-base !text-sm !py-3 gap-4 !text-[--text-color] !bg-[--footer-bg] md:!text-[19px] !text-[14px]"
            onClick={() => handleMenuItemClick(code.code)}
          >
            ({code.code})
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
};

export default CountryCodeDropdown;
