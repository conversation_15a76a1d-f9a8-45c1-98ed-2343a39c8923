import { Divider } from "@mui/material";
import React from "react";

const MuiDivider = () => {
  return (
    <>
      <Divider
        className="!text-[--text-color]"
        sx={{
          "&::after": {
            borderTop: "thin solid rgba(76, 78, 79, 0.5)",
          },
          "&::before": {
            borderTop: "thin solid rgba(76, 78, 79, 0.5)",
          },
        }}
      >
        or
      </Divider>
    </>
  );
};

export default MuiDivider;
