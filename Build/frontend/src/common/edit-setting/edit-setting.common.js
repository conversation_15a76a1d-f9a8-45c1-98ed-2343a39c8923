import { Box, Divider, Typography } from "@mui/material";
import React from "react";
//import AvatarPng from "@/assets/png/Avatar.png";
import { Button } from "@/component";

const EditSettingForm = ({ currentUser }) => {
  return (
    <Box className="pt-6">
      <Typography className="text-lg text-[--text-color] CraftworkGroteskMedium">
        Ownership
      </Typography>
      <Box className="lg:flex md:flex justify-between items-center py-2">
        <Box className="flex gap-1 items-center">
          {/* <CommonImage src={AvatarPng} alt="avatar" className="rounded-full w-6 h-6" /> */}
          <Typography className="text-sm CraftworkGroteskRegular text-[--text-color]">
            {currentUser?.firstName} {currentUser?.lastName}
          </Typography>
          <Typography className="text-sm text-[--hide-color] CraftworkGroteskRegular">
            {currentUser?.email}
          </Typography>
        </Box>
        <Button
          disabled
          className="flex !normal-case"
          sx={{
            border: 0,
            "&.MuiButtonBase-root": {
              color: "transparent !important",
            },
          }}
        >
          <Typography className="text-xs text-[--text-color] underline CraftworkGroteskHeavy">
            Transfer ownership
          </Typography>
          {/* <KeyboardArrowRight className="text-lg text-[--text-color]" /> */}
        </Button>
      </Box>
      <Divider sx={{ borderTop: "thin solid rgba(76, 78, 79, 0.5)" }} className="pt-4" />
      {/* <Box className="pt-12">
        <Typography className="text-lg text-[--text-color] CraftworkGroteskMedium">
          Account
        </Typography>
        <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular py-3">
          Here you can delete the personal data of your user account in accordance with GDPR.{" "}
        </Typography>
        <Button
          className="flex !normal-case"
          sx={{
            border: 0,
            padding: 0,
            "&.MuiButtonBase-root": {
              color: "transparent !important",
            },
          }}
        >
          <Typography className="text-xs text-[--text-color] underline CraftworkGroteskHeavy">
            Delete the Harmony Hall account
          </Typography>
          <Clear className="text-lg text-[--text-color]" />
        </Button>
      </Box> */}
    </Box>
  );
};

export default EditSettingForm;
