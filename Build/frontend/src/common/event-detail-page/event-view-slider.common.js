"use client";
import { East, West } from "@mui/icons-material";
import React, { useRef, useState } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css"; // Added missing imports
import "slick-carousel/slick/slick-theme.css"; // Added missing imports
import MediaImage from "@/assets/png/MediaImage.png";

const EventViewSlider = ({ images }) => {
  const sliderRef = useRef(null);
  const [currentSlide, setCurrentSlide] = useState(0);

  const settings = {
    dots: false,
    infinite: images?.length > 1,
    afterChange: (current) => setCurrentSlide(current),
    prevArrow: <West />,
    nextArrow: <East />,
  };

  return (
    <div className="slider-container">
      <div className="custom-paging">
        <ul className="vertical-slider">
          {images?.length > 0 ? (
            images.map((slide, index) => (
              <li
                key={index}
                className={index === currentSlide ? "active" : ""}
                onClick={() => sliderRef.current.slickGoTo(index)}
              >
                <img src={slide} alt={`Thumbnail ${index + 1}`} />
              </li>
            ))
          ) : (
            <li className="active">
              <img src={MediaImage.src} alt="Default thumbnail" />
            </li>
          )}
        </ul>
      </div>

      <Slider ref={sliderRef} {...settings} className="slick-act-slides">
        {images?.length > 0 ? (
          images.map((slide, index) => (
            <div key={index} className="bg-[--divider-color]">
              <img src={slide} alt={`Slide ${index + 1}`} className="object-contain" />
            </div>
          ))
        ) : (
          <div className="bg-[--divider-color]">
            <img src={MediaImage.src} alt="Default slide" className="object-contain" />
          </div>
        )}
      </Slider>
    </div>
  );
};

export default EventViewSlider;
