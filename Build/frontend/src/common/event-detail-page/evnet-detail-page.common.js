"use client";
import { Box, Typography, useMediaQuery, useTheme } from "@mui/material";
import { useLocale } from "next-intl";
import React from "react";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import { FacebookOutlined, Instagram, YouTube } from "@mui/icons-material";
import SoundCloud from "@/assets/svg/SoundCloud.svg";
import Spotify from "@/assets/svg/Spotify.svg";
import { generateLocationString } from "@/utils";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { useRouter } from "next/navigation";
import EventDetailSlider from "@/ui/act-detail/event-detail.slider";
import { Button, CommonImage } from "@/component";
import CalenderIcon from "@/assets/svg/CalenderIcon.svg";
import EventViewSlider from "./event-view-slider.common";
import Avatar from "@/assets/png/Avatar.png";

dayjs.extend(utc);
dayjs.extend(timezone);

const EventDetailPage = ({ data }) => {
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const formatDateTime = (dateString) => {
    const timeZone = "America/Toronto";
    const date = dayjs(dateString).tz(timeZone);
    return `${date.format("MMM D, YYYY")} | ${date.format("hA")}`;
  };
  const formatTime = (dateString) => {
    const timeZone = "America/Toronto";
    const date = dayjs(dateString).tz(timeZone);
    return `${date.format("MMM D, YYYY")} | ${date.format("hA")}, EST`;
  };
  const router = useRouter();
  const locale = useLocale();

  const socialMediaUrls = data?.eventMainInfo?.socialMediaUrls || [];
  const soundCloudUrl = socialMediaUrls.find((url) => url.includes("soundcloud.com"));
  const spotifyUrl = socialMediaUrls.find((url) => url.includes("spotify.com"));
  const instagramUrl = socialMediaUrls.find((url) => url.includes("instagram.com"));
  const youtubeUrl = socialMediaUrls.find((url) => url.includes("youtube.com"));
  const facebookUrl = socialMediaUrls.find((url) => url.includes("facebook.com"));

  return (
    <Box>
      {/* Full Width EventViewSlider */}
      <Box className="w-full">
        <EventViewSlider images={data?.eventMediaInfo?.imageUrls} />
      </Box>

      {/* Content Grid */}
      <Box className="grid grid-cols-12 gap-4 mt-4">
        {/* Left Column */}
        <Box className="col-span-12 lg:col-span-7 pr-5">
          <Box className="lg:flex gap-8 pb-5">
            <Box className="pt-6 w-full">
              <Box className="lg:flex lg:justify-between pb-8">
                <Typography className="text-[--text-color] text-2xl CraftworkGroteskMedium">
                  About the Event
                </Typography>
              </Box>
              {isSmallScreen ? (
                <Box className="flex gap-2">
                  <Typography className="!text-sm text-[--text-color] break-words CraftworkGroteskRegular">
                    {data?.eventMainInfo?.aboutEvent}
                  </Typography>
                </Box>
              ) : (
                <Typography className="!text-sm text-[--text-color] break-words CraftworkGroteskRegular">
                  {data?.eventMainInfo?.aboutEvent}
                </Typography>
              )}
            </Box>
          </Box>

          <Box className="mt-8">
            <Typography className="text-[--text-color] text-2xl CraftworkGroteskMedium mb-4">
              Venue
            </Typography>
            <Box className="bg-[--footer-bg] rounded-[4px] border border-[--divider-color] p-2 flex justify-between items-center">
              <Box className="flex gap-4 items-center">
                <CommonImage
                  src={data?.venueImageUrls?.[0] ?? Avatar}
                  alt="avatar"
                  className="size-10"
                  width="10"
                  height="10"
                />
                <Box>
                  <Typography className="text-sm font-craftWorkHeavy text-[--text-color]">
                    {data?.venueName}
                  </Typography>
                </Box>
              </Box>
              <Box>
                <Button
                  className="!bg-[--text-color] !normal-case"
                  onClick={() => router.push(`/${locale}/${data?.venueProfileId}/view`)}
                >
                  <Typography className="text-sm font-craftWorkHeavy text-[--bg-color] px-4 py-1">
                    View
                  </Typography>
                </Button>
              </Box>
            </Box>
          </Box>

          {data?.actProfileInfoList?.[0] && (
            <Box className="mt-8">
              <Typography className="text-[--text-color] text-2xl CraftworkGroteskMedium mb-4">
                Act
              </Typography>
              <Box className="bg-[--footer-bg] rounded-[4px] border border-[--divider-color] p-2 flex justify-between items-center">
                <Box className="flex gap-4 items-center">
                  <CommonImage
                    src={data?.actProfileInfoList[0]?.actImageUrls?.[0] ?? Avatar}
                    alt="avatar"
                    className="size-10"
                    width="10"
                    height="10"
                  />
                  <Box>
                    <Typography className="text-sm font-craftWorkHeavy text-[--text-color]">
                      {data.actProfileInfoList[0].actName}
                    </Typography>
                  </Box>
                </Box>
                <Box>
                  <Button
                    className="!bg-[--text-color] !normal-case"
                    onClick={() =>
                      router.push(`/${locale}/${data.actProfileInfoList[0].actProfileId}/view`)
                    }
                  >
                    <Typography className="text-sm font-craftWorkHeavy text-[--bg-color] px-4 py-1">
                      View
                    </Typography>
                  </Button>
                </Box>
              </Box>
            </Box>
          )}

          <Box className="pt-12">
            <Box className="flex gap-2 items-center pt-2">
              <LocationSvg className="text-2xl" />
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                {generateLocationString(data?.venueLocation).length > 32
                  ? generateLocationString(data?.venueLocation).substring(0, 32) + "..."
                  : generateLocationString(data?.venueLocation)}
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Right Column */}
        <Box className="col-span-12 lg:col-span-5 relative z-10 !bg-[--bg-color] pl-5">
          <Box className="flex gap-4 items-center">
            <Typography className="lg:text-[32px] text-base font-craftWorkHeavy text-[--text-color]">
              {data?.eventMainInfo?.eventName}
            </Typography>
          </Box>
          <Box className="flex gap-2 items-center pt-6">
            <CalenderIcon className="text-2xl" />
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              {`${formatDateTime(data?.scheduleTime?.startDate)} -${formatTime(
                data?.scheduleTime?.endDate,
              )}`}
            </Typography>
          </Box>
          <Box className="flex gap-2 items-start py-2">
            <LocationSvg className="text-2xl" />
            <Box>
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                {data?.venueName}
              </Typography>
              <Typography className="text-sm text-[--hide-color] CraftworkGroteskRegular">
                {generateLocationString(data?.venueLocation).length > 32
                  ? generateLocationString(data?.venueLocation).substring(0, 32) + "..."
                  : generateLocationString(data?.venueLocation)}
              </Typography>
            </Box>
          </Box>
          <div className="h-[1px] w-full bg-[--text-color] my-12" />
          <Box className="flex justify-between items-center mr-8">
            {(facebookUrl || instagramUrl || spotifyUrl || soundCloudUrl || youtubeUrl) && (
              <Typography className="text-sm font-craftWorkRegular text-[--text-color]">
                Social Media
              </Typography>
            )}
            <Box className="flex gap-2 items-center">
              {facebookUrl && <FacebookOutlined className="text-[--text-color] size-6" />}
              {instagramUrl && <Instagram className="text-[--text-color] size-6" />}
              {spotifyUrl && <SoundCloud className="text-[--text-color] size-6" />}
              {youtubeUrl && <YouTube className="text-[--text-color] size-6" />}
              {spotifyUrl && <Spotify className="text-[--text-color] size-6" />}
            </Box>
          </Box>
        </Box>
      </Box>

      {/* More Like This Section */}
      <Typography className="text-[--text-color] text-2xl CraftworkGroteskMedium mt-6">
        More like this Event
      </Typography>
      <Box className="py-80 relative">
        <EventDetailSlider datas={data?.similarEvents} />
      </Box>
    </Box>
  );
};

export default EventDetailPage;
