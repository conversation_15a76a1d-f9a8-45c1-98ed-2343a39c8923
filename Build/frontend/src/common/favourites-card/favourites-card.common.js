"use client";
import React from "react";
import CardCarousel from "../profile-card/profile-card.carousel.common";
import { Box, Typography } from "@mui/material";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import AddCalender from "@/assets/svg/AddCalender.svg";
import FilledHeart from "@/assets/svg/FilledHeart.svg";
import RespondClock from "@/assets/svg/RespondClock.svg";
import { Button } from "@/component";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import StarIcon from "@/assets/svg/act-type.svg/Star.svg";
import { FacebookOutlined, Instagram, YouTube } from "@mui/icons-material";

const FavroutiesCard = ({ data }) => {
  return (
    <>
      <Box
        key={data.id}
        className="!border !border-[--divider-color] !max-w-full md:!max-w-xs lg:!max-w-sm !rounded-[4px] !bg-[--footer-bg]"
      >
        <Box className="relative">
          <CardCarousel images={data?.favouritesImages} className="favourites-slider" />
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskSemiBold !absolute !left-3 !top-3 !bg-[--profile-type] !rounded-[2px] !px-[6px] !py-[2px]">
            {data?.favouritesTag}
          </Typography>
          <Box className="!absolute lg:!hidden flex !right-4 !top-4 !gap-x-2">
            <AddCalender className="!text-2xl" />
            <FilledHeart className="!text-2xl" />
          </Box>
        </Box>
        <Box className="!px-4">
          <Box className="!pb-5">
            <Box className="!flex !justify-between !items-center">
              <Typography className="!text-[--text-color] !text-2xl font-craftWorkGX">
                {data.actName}
              </Typography>
              <Box className="lg:!flex !hidden !gap-x-2">
                <AddCalender className="!text-2xl" />
                <FilledHeart className="!text-2xl" />
              </Box>
              <Box className="!flex lg:!hidden !gap-x-2 !items-center">
                <StarIcon className="!fill-white !text-2xl" />
                <Typography className="!text-[--text-color] !text-sm font-craftWorkRegular">
                  4.0 (1800)
                </Typography>
              </Box>
            </Box>
            <Typography className="!text-[--text-color] !text-sm font-craftWorkRegular">
              {data?.actDescription}
            </Typography>
          </Box>
          <Box className="lg:!flex !hidden !gap-x-3 !pb-5">
            <Box className="!border !w-full !p-[10px] !border-[--light-border-color] !rounded-[2px]">
              <Typography className="!text-left !text-[--text-color] font-craftWorkRegular">
                {data.followers}
                <br /> followers
              </Typography>
            </Box>
            <Box className="!border !w-full !p-[10px] !border-[--light-border-color] !rounded-[2px]">
              <Typography className="!text-left !text-sm !text-[--text-color] !font-craftWorkRegular">
                average
                <br /> {data.averagePrice}
              </Typography>
            </Box>
          </Box>
          <Box className="!flex !gap-x-3">
            <LocationSvg className="!text-2xl" />
            <Typography className="!text-[--text-color] !text-sm  font-craftWorkRegular">
              {data.location}
            </Typography>
          </Box>
          <Box className="!flex !justify-between lg:!hidden items-center !py-2">
            <Box>
              <Typography className="!text-sm CraftworkGroteskRegular !text-[--hide-color]">
                Started at
              </Typography>
              <Typography className="!text-sm CraftworkGroteskHeavy !text-[--text-color]">
                $ 3k / per event
              </Typography>
            </Box>
            <Box className="!flex !gap-2">
              <FacebookOutlined className="!text-2xl !text-[--text-color]" />
              <Instagram className="!text-2xl !text-[--text-color]" />
              <YouTube className="!text-2xl !text-[--text-color]" />
            </Box>
          </Box>
          {/* <Box className="!flex !gap-x-3 !py-3">
            <AddCalender className="!text-2xl" />
            <Typography className="!text-[--text-color] !text-sm  font-craftWorkRegular">
              Next event: {data.nextEvent}
            </Typography>
          </Box> */}
          <Box className="!flex !gap-x-3 ">
            <RespondClock className="!text-lg" />
            <Typography className="!text-[--hide-color] !text-sm  font-craftWorkRegular">
              Usually responds in less than an hour
            </Typography>
          </Box>
          <Box className="!flex !gap-2 !my-4">
            <Button
              className="!border-[2px] !px-2 !border-[--text-color] !rounded-[4px]"
              sx={{
                minWidth: 0,
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
            >
              {data.button}
            </Button>
            <Button
              className="!bg-[--text-color] !w-full !gap-x-4 !py-3"
              sx={{
                minWidth: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
            >
              <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                View profile
              </Typography>
              <ArrowSouthEast alt="arrow" />
            </Button>
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default FavroutiesCard;
