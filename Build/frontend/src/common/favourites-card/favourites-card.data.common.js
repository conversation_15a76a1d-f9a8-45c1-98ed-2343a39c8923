import MediaImage from "@/assets/png/MediaImage.png";
import MediaImage1 from "@/assets/png/MediaImage1.png";
import MediaImage2 from "@/assets/png/MediaImage2.png";
import MediaImage3 from "@/assets/png/MediaImage3.png";
import Play from "@/assets/svg/Play.svg";

export const favouritesData = [
  {
    id: 0,
    favouritesImages: [MediaImage, MediaImage1, MediaImage2, MediaImage3],
    favouritesTag: "Act",
    actName: "Act name",
    actDescription: "Soul, Folk, Rock, Rhythm, Blues, ...",
    followers: "23k",
    averagePrice: "$ 3.2 k /gig*",
    location: "Location address",
    nextEvent: "27 Jun",
    button: <Play className="!text-2xl" />,
  },
  {
    id: 1,
    favouritesImages: [MediaImage, MediaImage1, MediaImage2, MediaImage3],
    favouritesTag: "Act",
    actName: "Act name",
    actDescription: "Soul, Folk, Rock, Rhythm, Blues, ...",
    followers: "23k",
    averagePrice: "$ 3.2 k /gig*",
    location: "Location address",
    nextEvent: "27 Jun",
    button: <Play className="!text-2xl" />,
  },
  {
    id: 2,
    favouritesImages: [MediaImage, MediaImage1, MediaImage2, MediaImage3],
    favouritesTag: "Act",
    actName: "Act name",
    actDescription: "Soul, Folk, Rock, Rhythm, Blues, ...",
    followers: "23k",
    averagePrice: "$ 3.2 k /gig*",
    location: "Location address",
    nextEvent: "27 Jun",
    button: <Play className="!text-2xl" />,
  },
  {
    id: 3,
    favouritesImages: [MediaImage, MediaImage1, MediaImage2, MediaImage3],
    favouritesTag: "Act",
    actName: "Act name",
    actDescription: "Soul, Folk, Rock, Rhythm, Blues, ...",
    followers: "23k",
    averagePrice: "$ 3.2 k /gig*",
    location: "Location address",
    nextEvent: "27 Jun",
    button: <Play className="!text-2xl" />,
  },
  {
    id: 4,
    favouritesImages: [MediaImage, MediaImage1, MediaImage2, MediaImage3],
    favouritesTag: "Act",
    actName: "Act name",
    actDescription: "Soul, Folk, Rock, Rhythm, Blues, ...",
    followers: "23k",
    averagePrice: "$ 3.2 k /gig*",
    location: "Location address",
    nextEvent: "27 Jun",
    button: <Play className="!text-2xl" />,
  },
];
