import { Box, Typography } from "@mui/material";
import React from "react";
import CardCarousel from "../profile-card/profile-card.carousel.common";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import AddCalender from "@/assets/svg/AddCalender.svg";
import FilledHeart from "@/assets/svg/FilledHeart.svg";
import { Button, CommonImage } from "@/component";
import CalenderIcon from "@/assets/svg/CalenderIcon.svg";
import Ticket from "@/assets/svg/Ticket.svg";
import OutlinedTicket from "@/assets/svg/act-type.svg/OutlinedTicket.svg";

const FavouritesEventCard = ({ favouritesData }) => {
  return (
    <>
      {favouritesData?.map((data) => (
        <Box
          key={data.id}
          className="!border !border-[--divider-color] !max-w-full md:!max-w-xs lg:!max-w-sm !rounded-[4px] !bg-[--footer-bg]"
        >
          <Box className="!relative">
            <CardCarousel images={data.eventImages} className="favourites-slider" />
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskSemiBold !absolute !left-3 !top-3 !bg-[--profile-type] !rounded-[2px] !px-[6px] !py-[2px]">
              {data.favouritesTag}
            </Typography>
            <Box className="!absolute lg:!hidden flex !right-4 !top-4 !gap-x-2">
              <AddCalender className="!text-2xl" />
              <FilledHeart className="!text-2xl" />
            </Box>
          </Box>
          <Box className="!px-4">
            <Box className="!pb-5">
              <Box className="!flex !justify-between !items-center">
                <Typography className="!text-[--text-color] !text-2xl font-craftWorkGX">
                  {data.eventName}
                </Typography>
                <Box className="!flex lg:!hidden !gap-x-2">
                  <AddCalender className="!text-2xl" />
                  <FilledHeart className="!text-2xl" />
                </Box>
              </Box>
            </Box>
            <Box className="!flex !gap-x-3">
              <CalenderIcon className="!text-2xl" />
              <Typography className="!text-[--text-color] !text-sm  font-craftWorkRegular">
                {data.eventTime}
              </Typography>
            </Box>
            <Box className="!flex !gap-x-3 !py-3">
              <LocationSvg className="!text-2xl" />
              <Typography className="!text-[--text-color] !text-sm  font-craftWorkRegular">
                {data.eventLocation}
              </Typography>
            </Box>
            <Box className="!flex !gap-x-3  !pb-8">
              <CommonImage src={data.imageSrc} alt="venue-image" />
              <Typography className="!text-[--text-color] !text-sm  font-craftWorkRegular">
                {data.venueName}
              </Typography>
            </Box>
            <Box className="!flex !gap-x-3 !py-3">
              <Ticket className="!text-2xl" />
              <Typography className="!text-[--text-color] !text-sm  font-craftWorkRegular">
                {data.ticketPrice}
              </Typography>
            </Box>
            <Button
              className="!bg-[--text-color] !w-full !gap-x-4 !py-3 !mb-4"
              sx={{
                minWidth: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
            >
              <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                Buy a ticket
              </Typography>
              <OutlinedTicket className="!text-lg" />
            </Button>
          </Box>
        </Box>
      ))}
    </>
  );
};

export default FavouritesEventCard;
