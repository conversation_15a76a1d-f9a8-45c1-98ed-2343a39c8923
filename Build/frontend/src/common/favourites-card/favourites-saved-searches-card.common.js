"use client";
import { Box, Chip, Typography } from "@mui/material";
import React from "react";
import SavedData from "@/assets/svg/SavedData.svg";
import DeleteSvg from "@/assets/svg/act-type.svg/DeleteSvg.svg";
import { Button } from "@/component";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import FavouritesVenuePopup from "../favourites-venue-popup/favourites-venue-popup.common";
import FavouritesVenueDeletePopup from "../favourites-venue-popup/favourites-venue-delete-popup.common";
import { getSavedSearches, deleteSavedSearches } from "@/store/slice/common/search.slice";
import { useDispatch } from "react-redux";
import { showSnackbar } from "@/utils/snackbar.utils";

const FavouritesSavedSearchesCard = ({ favouritesSavedSearchesData }) => {
  const dispatch = useDispatch();
  const [open, setOpen] = React.useState(false);
  const [currData, setCurrData] = React.useState({});

  const handleClickOpen = (data, searchCriteria) => {
    setCurrData({ data, searchCriteria });
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const [deleteOpen, setDeleteOpen] = React.useState(false);

  const handleClickDeleteOpen = (data) => {
    setCurrData(data);
    setDeleteOpen(true);
  };

  const handleDeleteClose = () => {
    setDeleteOpen(false);
  };

  const onDeleteSavedSearch = (data) => {
    dispatch(deleteSavedSearches(data.searchName))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          dispatch(getSavedSearches())
            .unwrap()
            .then(() => {
              setDeleteOpen(false);
              showSnackbar("Saved search deleted successfully", "success");
            });
        }
      })
      .catch((error) => {
        showSnackbar(error, "error");
      });
  };

  const setSavedSearchData = (filter) => {
    const searchCriteria = [];

    if (filter.searchStrings && filter.searchStrings.length > 0) {
      searchCriteria.push(...filter.searchStrings.filter(Boolean)); // Filtering out empty strings
    }

    if (filter?.searchFilter?.searchLocation) {
      const { cityName, stateName, countryName } = filter.searchFilter.searchLocation;
      if (cityName || stateName || countryName) {
        searchCriteria.push("Location");
      }
      // if (cityName) searchCriteria.push("City: " + cityName);
      // if (stateName) searchCriteria.push("Location: " + stateName);
      // if (countryName) searchCriteria.push("Location: " + countryName);
    }

    if (filter?.searchFilter?.searchLocation?.distance) {
      searchCriteria.push("Distance");
    }

    if (filter?.searchFilter?.entertainmentTypesList) {
      filter?.searchFilter?.entertainmentTypesList.forEach((et) => {
        searchCriteria.push(et.name);
      });
    }
    if (filter?.searchFilter?.musicGenreList) {
      filter?.searchFilter?.musicGenreList.forEach((mg) => {
        searchCriteria.push(mg.name);
      });
    }

    const displayedCriteria = searchCriteria.slice(0, 2);
    const remainingCount = searchCriteria.length - displayedCriteria.length;

    return { displayedCriteria, remainingCount, searchCriteria };
  };

  return (
    <>
      {favouritesSavedSearchesData.map((option) => {
        const { displayedCriteria, remainingCount, searchCriteria } = setSavedSearchData(option);

        return (
          <Box
            key={option.id}
            className="!border !border-[--divider-color] !px-6 lg:!pt-0 !pt-4 !w-full !bg-[--footer-bg]"
          >
            <Box className="lg:!flex lg:!justify-between md:!flex md:!justify-between !items-center">
              <Box className="!flex !gap-x-3 !items-center">
                <SavedData className="!text-2xl" />
                <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                  {option?.searchName}
                </Typography>
              </Box>
              <Box className="lg:flex lg:gap-x-3 lg:py-0 py-2 items-center">
                <Typography className="text-[--text-color] text-sm CraftworkGroteskRegular !text-left">
                  Applied filters3
                </Typography>
                <Box className="flex flex-wrap gap-3 lg:py-0 py-4 items-center">
                  {displayedCriteria.map((data, index) => (
                    <Chip
                      key={index}
                      label={data}
                      sx={{ backgroundColor: "var(--text-color)" }}
                      className="text-sm CraftworkGroteskSemiBold"
                    />
                  ))}
                  {remainingCount > 0 && (
                    <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy underline">
                      and {remainingCount} more
                    </Typography>
                  )}
                </Box>
              </Box>
              <Box className="!flex !gap-2 !my-4">
                <Button
                  className="!border-[2px] !px-2 !border-[--text-color] !rounded-[4px]"
                  sx={{
                    minWidth: 0,
                    border: 0,
                    "&.MuiButtonBase-root": {
                      color: "white !important",
                    },
                  }}
                  onClick={() => handleClickDeleteOpen(option)}
                >
                  <DeleteSvg className="!text-2xl" />
                </Button>
                <Button
                  className="!bg-[--text-color] !w-full !gap-x-4 !py-3"
                  sx={{
                    minWidth: 0,
                    "&.MuiButtonBase-root": {
                      color: "white !important",
                    },
                  }}
                  onClick={() => {
                    handleClickOpen(option, searchCriteria);
                  }}
                >
                  <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                    Launch
                  </Typography>
                  <ArrowSouthEast alt="arrow" />
                </Button>
              </Box>
            </Box>
          </Box>
        );
      })}
      <FavouritesVenuePopup
        handleClose={handleClose}
        open={open}
        data={currData.data}
        searchCriteria={currData.searchCriteria}
      />
      <FavouritesVenueDeletePopup
        handleClose={handleDeleteClose}
        open={deleteOpen}
        data={currData}
        onDeleteSavedSearch={onDeleteSavedSearch}
      />
    </>
  );
};

export default FavouritesSavedSearchesCard;
