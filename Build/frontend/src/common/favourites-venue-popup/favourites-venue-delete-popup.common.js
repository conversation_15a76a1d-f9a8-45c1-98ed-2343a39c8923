import { Button } from "@/component";
import { Clear } from "@mui/icons-material";
import { Box, Chip, Dialog, DialogContent, Typography } from "@mui/material";
import React from "react";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";

const FavouritesVenueDeletePopup = ({ handleClose, open, data, onDeleteSavedSearch }) => {
  return (
    <Dialog
      open={open}
      maxWidth={false}
      sx={{ "& .MuiBackdrop-root": { backgroundColor: "#181B1BE5" } }}
    >
      <DialogContent className=" !max-w-lg !bg-[--footer-bg] !border-[1px] !px-10 !pt-10 !border-[--text-color]">
        <Typography className="!text-[--text-color] !text-2xl CraftworkGroteskHeavy !pb-2">
          Are you sure you want to delete saved search?
        </Typography>
        <Box className="flex !flex-wrap !gap-3 lg:!py-0 !py-4 !items-center">
          {data?.searchFilters?.map((data) => (
            <Chip
              key={data}
              label={data}
              sx={{ backgroundColor: "var(--text-color)" }}
              className="text-sm CraftworkGroteskSemiBold"
            />
          ))}
        </Box>
        <Box className="!flex !gap-2 !justify-end !my-4 ">
          <Button
            className=" !gap-x-2 !py-3"
            sx={{
              minWidth: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
            onClick={handleClose}
          >
            <Typography className="!normal-case CraftworkGroteskHeavy !underline !text-sm !leading-[15.4px] !text-[--text-color]">
              Cancel
            </Typography>
            <Clear className="!text-lg !text-[--text-color]" />
          </Button>
          <Button
            className=" !px-2  lg:!w-1/2 !w-full !flex !gap-x-2 !bg-[--text-color] !rounded-[4px]"
            sx={{
              minWidth: 0,
              border: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
            onClick={() => onDeleteSavedSearch(data)}
          >
            <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
              Delete
            </Typography>
            <ArrowSouthEast />
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default FavouritesVenueDeletePopup;
