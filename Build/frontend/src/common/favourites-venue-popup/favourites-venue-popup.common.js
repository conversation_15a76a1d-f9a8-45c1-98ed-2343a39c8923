"use client";
import { Box, Chip, Dialog, DialogContent, Typography } from "@mui/material";
import React from "react";
import SavedSearchPopupIcon from "@/assets/svg/SavedSearchPopupIcon.svg";
import { Clear } from "@mui/icons-material";
import { Button } from "@/component";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { useLocale } from "next-intl";
import { useRouter } from "next/navigation";
import { filterToURL } from "@/utils";

const FavouritesVenuePopup = ({ handleClose, open, data, searchCriteria }) => {
  const lang = useLocale();
  const router = useRouter();

  return (
    <Dialog
      open={open}
      maxWidth={false}
      sx={{ "& .MuiBackdrop-root": { backgroundColor: "#181B1BE5" } }}
    >
      <DialogContent className="!flex !gap-x-6 !max-w-2xl !bg-[--footer-bg] !border-[1px] lg:!px-10 lg:!py-10 !px-6 !py-6 !border-[--text-color]">
        <Box className="lg:!w-12 lg:!h-12 !w-8 !h-8">
          <SavedSearchPopupIcon className="!text-5xl" />
        </Box>
        <Box>
          <Typography className="!text-[--text-color] !text-lg CraftworkGroteskMedium !pb-2">
            {data?.searchName}
          </Typography>
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular !py-2">
            Applied filters
          </Typography>
          <Box className="flex !flex-wrap !gap-3 lg:!py-0 !py-4 !items-center">
            {searchCriteria?.map((data) => (
              <Chip
                key={data}
                label={data}
                sx={{ backgroundColor: "var(--text-color)" }}
                className="text-sm CraftworkGroteskSemiBold"
              />
            ))}
          </Box>
          <Box className="!flex !gap-2 !my-4 lg:!pb-0 !pb-6 ">
            <Button
              className="!bg-[--text-color] !w-full !gap-x-4 !py-3"
              sx={{
                minWidth: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
              onClick={() => {
                filterToURL(router, lang, data);
              }}
            >
              <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                Launch
              </Typography>
              <ArrowSouthEast alt="arrow" />
            </Button>
            {/* <Button
                            className="!border-[2px] !px-2 !border-[--text-color] !rounded-[4px]"
                            sx={{
                                minWidth: 0,
                                border: 0,
                                "&.MuiButtonBase-root": {
                                    color: "white !important",
                                },
                            }}
                        >
                            <DeleteSvg className="!text-2xl" />
                        </Button> */}
          </Box>
        </Box>
        <Clear className="!text-lg !text-[--text-color] !cursor-pointer" onClick={handleClose} />
      </DialogContent>
    </Dialog>
  );
};

export default FavouritesVenuePopup;
