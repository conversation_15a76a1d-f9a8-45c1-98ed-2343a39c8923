import React from "react";
import { Box, Typography } from "@mui/material";
import FacebookSvg from "@/assets/svg/FacebookSvg.svg";
import Instagram from "@/assets/svg/Instagram.svg";
import Youtube from "@/assets/svg/Youtube.svg";
import Link from "next/link";
import Logo from "@/assets/svg/LOGO.svg";
import Button from "@/component/button/button.component";
import { useLocale } from "next-intl";

const MobileFooter = ({ className }) => {
  const data = ["General", "Contacts"];
  const lang = useLocale();
  const list = [
    { id: 0, text: "About Us", path: "/" },
    { id: 1, text: "Phone / Email", path: "/" },
    { id: 2, text: "Blog", path: "/" },
    { id: 3, text: "Address / Country", path: "/" },
  ];

  return (
    <Box
      className={` ${className} !flex !flex-col !border-t !border-t-[--divider-color] lg:!flex-row !justify-between`}
    >
      <Box>
        <Link href={`/${lang}`}>
          <Logo className="!w-[83px] !h-6" />
        </Link>
        <Box className="!flex !gap-x-4 !py-4">
          <Button
            className="!bg-[--text-color] !w-[44px] !h-[44px] !rounded-full !p-0"
            sx={{ minWidth: 0 }}
          >
            <FacebookSvg className="!w-[14.67px] !h-[14.67px]" />
          </Button>
          <Button
            className="!bg-[--text-color] !w-[44px] !h-[44px] !rounded-full !p-0"
            sx={{ minWidth: 0 }}
          >
            <Instagram className="!w-[14.67px] !h-[14.67px]" />
          </Button>
          <Button
            className="!bg-[--text-color] !w-[44px] !h-[44px] !rounded-full !p-0"
            sx={{ minWidth: 0 }}
          >
            <Youtube className="!w-[14.67px] !h-[14.67px]" />
          </Button>
        </Box>
      </Box>
      <Box className="!grid !grid-cols-2">
        {data.map((item, index) => (
          <Typography
            key={index}
            className="!break-words !pb-2 !text-[--text-color] CraftworkGroteskGX !text-lg"
          >
            {item}
          </Typography>
        ))}
        {list.map((item, index) => (
          // <Link href={item.path} key={item.id}>
          <Typography
            className="!break-words !pb-2 !text-[--text-color] CraftworkGroteskGX !text-sm cursor-pointer"
            key={index}
          >
            {item.text}
          </Typography>
          // </Link>
        ))}
      </Box>
    </Box>
  );
};

export default MobileFooter;
