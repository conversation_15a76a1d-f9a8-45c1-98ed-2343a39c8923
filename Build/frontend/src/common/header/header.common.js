"use client";
import React, { useState, useTransition } from "react";
import { Box, Divider, Typography } from "@mui/material";
import Link from "next/link";
import Button from "@/component/button/button.component";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { Dropdown } from "@/component";
import { useSelector } from "react-redux";
import OutlinedUser from "@/assets/svg/OutlinedUser.svg";
import { useRouter } from "next/navigation";
import { getLocalStorage, setLocalStorage } from "@/utils";
import { useLocale, useTranslations } from "next-intl";
import { locales } from "../../navigation";
import LogoComponent from "../logo-component/logo-component.common";

const Header = () => {
  const lang = useLocale();
  const t = useTranslations("Header");
  const s = useTranslations("dropdown");
  const languageOptions = locales.map((locale) => locale.toUpperCase());
  const { token } = useSelector((state) => state.login);
  const [selectedLanguage, setSelectedLanguage] = useState(getLocalStorage("lang") ?? lang);
  // eslint-disable-next-line
  const [isPending, startTransition] = useTransition();
  const router = useRouter();
  const handleSelectLanguage = (value) => {
    startTransition(() => {
      router.replace(`/${value}`);
    });
    setLocalStorage("lang", value);
    setSelectedLanguage(value);
  };

  const list = [
    { id: 0, text: t("aboutUs"), path: `/${lang}` },
    { id: 1, text: t("benefits"), path: `/${lang}` },
    { id: 2, text: t("pricing"), path: `/${lang}` },
    { id: 3, text: t("contactUs"), path: `/${lang}` },
  ];

  return (
    <Box className="!flex !h-[72px] !items-center !justify-between !px-10 !bg-[--bg-color] !border-b !border-b-[--divider-color]">
      <Box className="!flex items-center !gap-x-3">
        <Link href={token ? `/${lang}/search?profileType=ACT_PROFILE` : `/${lang}`}>
          {/* <Logo className="!w-[70px] !h-6" /> */}
          <LogoComponent />
        </Link>
        <Typography className="!text-[--text-color] !text-lg CraftworkGroteskGX">
          {t("UniqueEvents")}
        </Typography>
      </Box>
      <Box className="!flex !gap-x-5">
        {list.map((data, index) => (
          // <Link key={data.id} href={data.path}>
          <Typography
            className="!text-[--text-color] !text-sm !leading-[15.4px] CraftworkGroteskGX cursor-pointer"
            key={index}
          >
            {data.text}
          </Typography>
          // </Link>
        ))}
      </Box>
      <Box className="!flex !items-center !gap-2">
        {/* <Box className="!flex">
          <LocationSvg className="!w-6 !h-6 !mt-2" />
          <Dropdown
            options={cityOptions}
            onSelect={handleSelectCity}
            selectedValue={selectedCity}
            title="Select Location"
            className="!text-[--text-color]"
          />
        </Box> */}
        <Box className="!flex !items-center !gap2">
          <Dropdown
            options={languageOptions}
            onSelect={handleSelectLanguage}
            selectedValue={selectedLanguage}
            title={s("selectLanguage")}
            className="!text-[--text-color] !uppercase"
          />
        </Box>
        <Divider
          orientation="vertical"
          sx={{ border: "thin solid rgba(76, 78, 79, 0.5)" }}
          className="!mx-5 !h-10"
        />
        {!token || token === null ? (
          <Link href={`/${lang}/login`}>
            <Button className="!bg-[--text-color] !flex !gap-x-2 !px-2 !py-3" sx={{ px: 0 }}>
              <Typography className="!normal-case !hidden lg:!inline md:!inline CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                {t("Register/Login")}
              </Typography>
              <ArrowSouthEast alt="arrow" />
            </Button>
          </Link>
        ) : (
          <Link href={`/${lang}/dashboard`}>
            {" "}
            <OutlinedUser className="!w-6 !h-6 !cursor-pointer" />{" "}
          </Link>
        )}
      </Box>
    </Box>
  );
};

export default Header;
