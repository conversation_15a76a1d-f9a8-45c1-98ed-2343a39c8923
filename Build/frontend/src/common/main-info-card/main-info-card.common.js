"use client";
import { CommonImage, Loader } from "@/component";
import React, { useEffect, useState } from "react";
import MainInfoImage from "@/assets/png/MainInfoImage.png";
import { Box, Button, InputAdornment, TextField, Typography, IconButton } from "@mui/material";
import { useForm, useWatch } from "react-hook-form";
import { Close, SouthEast, Add } from "@mui/icons-material";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { useDispatch } from "react-redux";
import * as Yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { addMainInfo, getMainInfo } from "@/store/slice/booking/booking.slice";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { LocalizationProvider } from "@mui/x-date-pickers";
import CalendarIcon from "@/assets/svg/CalenderIcon.svg";
import SocialLink from "@/assets/png/Social-link.png";
import FacebookOutlinedIcon from "@mui/icons-material/FacebookOutlined";
import { Instagram, YouTube } from "@mui/icons-material";
import SoundCloudIcon from "@/assets/svg/SoundCloud.svg";
import { EVENT_CONSTANTS } from "@/validation/auth/constants";

import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
dayjs.extend(duration);

const validationSchema = Yup.object().shape({
  eventName: Yup.string()
    .required("Event name is required")
    .max(
      EVENT_CONSTANTS.EVENT_NAME.MAX_LENGTH,
      `Event name must not exceed ${EVENT_CONSTANTS.EVENT_NAME.MAX_LENGTH} characters`,
    ),
  tags: Yup.array().of(Yup.string().optional()),
  aboutEvent: Yup.string()
    .required("About Event is required")
    .max(
      EVENT_CONSTANTS.ABOUT_EVENT.MAX_LENGTH,
      `About Event must not exceed ${EVENT_CONSTANTS.ABOUT_EVENT.MAX_LENGTH} characters`,
    ),
  spotifyLink: Yup.string().max(
    EVENT_CONSTANTS.SOCIAL_LINKS.MAX_LENGTH,
    `Link must not exceed ${EVENT_CONSTANTS.SOCIAL_LINKS.MAX_LENGTH} characters`,
  ),
  soundCloudLink: Yup.string().max(
    EVENT_CONSTANTS.SOCIAL_LINKS.MAX_LENGTH,
    `Link must not exceed ${EVENT_CONSTANTS.SOCIAL_LINKS.MAX_LENGTH} characters`,
  ),
  instagramLink: Yup.string().max(
    EVENT_CONSTANTS.SOCIAL_LINKS.MAX_LENGTH,
    `Link must not exceed ${EVENT_CONSTANTS.SOCIAL_LINKS.MAX_LENGTH} characters`,
  ),
  youtubeLink: Yup.string().max(
    EVENT_CONSTANTS.SOCIAL_LINKS.MAX_LENGTH,
    `Link must not exceed ${EVENT_CONSTANTS.SOCIAL_LINKS.MAX_LENGTH} characters`,
  ),
  facebookLink: Yup.string().max(
    EVENT_CONSTANTS.SOCIAL_LINKS.MAX_LENGTH,
    `Link must not exceed ${EVENT_CONSTANTS.SOCIAL_LINKS.MAX_LENGTH} characters`,
  ),
});

const MainInfoCard = ({ eventInfo }) => {
  const [tagInput, setTagInput] = useState("");
  const [loading, setLoading] = useState(true);
  const {
    register,
    handleSubmit,
    control,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      eventName: eventInfo?.eventName || "",
      tags: [],
      aboutEvent: "",
      spotifyLink: "",
      soundCloudLink: "",
      instagramLink: "",
      youtubeLink: "",
      facebookLink: "",
    },
    mode: "onChange",
  });
  const { showSnackbar } = useSnackbar();
  const router = useRouter();
  const lang = useLocale();

  useEffect(() => {
    if (eventInfo?.eventId) {
      dispatch(getMainInfo(eventInfo?.eventId))
        .unwrap()
        .then((response) => {
          const socialMediaUrls = response?.data?.data?.socialMediaUrls || [];
          const soundCloudUrl = socialMediaUrls.find((url) => url.includes("soundcloud.com"));
          const spotifyUrl = socialMediaUrls.find((url) => url.includes("spotify.com"));
          const instagramUrl = socialMediaUrls.find((url) => url.includes("instagram.com"));
          const youtubeUrl = socialMediaUrls.find((url) => url.includes("youtube.com"));
          const facebookUrl = socialMediaUrls.find((url) => url.includes("facebook.com"));
          const spotifyLink = spotifyUrl ? spotifyUrl.split("/").pop() : "";
          const instagramLink = instagramUrl ? instagramUrl.split("/").pop() : "";
          const youtubeLink = youtubeUrl ? youtubeUrl.split("/").pop() : "";
          const facebookLink = facebookUrl ? facebookUrl.split("/").pop() : "";
          const soundCloudLink = soundCloudUrl ? soundCloudUrl.split("/").pop() : "";

          setValue("tags", response?.data?.data?.tags);
          setValue("aboutEvent", response?.data?.data?.aboutEvent);
          setValue("eventName", response?.data?.data?.eventName);
          setValue("spotifyLink", spotifyLink);
          setValue("soundCloudLink", soundCloudLink);
          setValue("instagramLink", instagramLink);
          setValue("youtubeLink", youtubeLink);
          setValue("facebookLink", facebookLink);
          setLoading(false);
        })
        .catch(() => {
          setLoading(false);
        });
    }
  }, [eventInfo?.eventId]);

  const actData = useWatch({ control });
  useEffect(() => {
    if (actData.actPhotos?.length > 0) {
      dispatch(setPreviewData({ actMedia: actData }));
    }
  }, [actData]);

  const dispatch = useDispatch();

  const handleSubmitSchedule = (data) => {
    const socialMediaLinks = [];
    if (data.spotifyLink) {
      socialMediaLinks.push(`https:/open.spotify.com/${data.spotifyLink}`);
    }
    if (data.soundCloudLink) {
      socialMediaLinks.push(`https:/soundcloud.com/${data.soundCloudLink}`);
    }
    if (data.instagramLink) {
      socialMediaLinks.push(`https:/instagram.com/${data.instagramLink}`);
    }
    if (data.youtubeLink) {
      socialMediaLinks.push(`https:/youtube.com/${data.youtubeLink}`);
    }
    if (data.facebookLink) {
      socialMediaLinks.push(`https:/facebook.com/${data.facebookLink}`);
    }
    data.socialMediaUrls = socialMediaLinks;
    delete data.spotifyLink;
    delete data.soundCloudLink;
    delete data.instagramLink;
    delete data.youtubeLink;
    delete data.facebookLink;

    dispatch(addMainInfo({ eventId: eventInfo?.eventId, data }))
      .unwrap()
      .then(() => {
        router.push(`/${lang}/event/${eventInfo?.eventId}/edit-media`);
      })
      .catch((error) => {
        showSnackbar(error, "error");
      });
  };

  const tags = watch("tags") ?? [];

  // Handle tag input change
  const handleTagInputChange = (e) => {
    setTagInput(e.target.value);
  };

  // Handle Enter key press
  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      addTag();
      e.preventDefault();
    }
  };

  //  tag function that works for both button click and Enter key
  const addTag = () => {
    if (tagInput && !tags?.includes(tagInput)) {
      setValue("tags", [...tags, tagInput]);
      setTagInput("");
    }
  };

  const handleDeleteTag = (tagToRemove) => {
    setValue(
      "tags",
      tags.filter((tag) => tag !== tagToRemove),
    );
  };

  const { startDate, endDate, timeZone } = eventInfo?.scheduleTime || {};
  const start = dayjs(startDate);
  const end = dayjs(endDate);
  const durationInHours = dayjs.duration(end.diff(start)).asHours().toFixed(2);
  if (loading) {
    return <Loader />;
  }
  return (
    <Box className="">
      <form onSubmit={handleSubmit(handleSubmitSchedule)}>
        <Box className="flex gap-3 items-center">
          <CommonImage src={MainInfoImage} alt="info-image" className="w-[40px] h-[40px]" />
          <Typography className="text-2xl CraftworkGroteskMedium text-[--text-color]">
            Main info
          </Typography>
        </Box>
        <Box className="pt-4 pl-14">
          <Typography className="text-sm CraftworkGroteskRegular text-[--text-color]">
            Craft an event name that captivates: Your event&apos;s name is the key to sparking
            excitement and curiosity among attendees. Choose it wisely to set the stage for an
            unforgettable experience.
          </Typography>
          <TextField
            type="text"
            size="small"
            placeholder="Event Title"
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            inputProps={{
              maxLength: EVENT_CONSTANTS.EVENT_NAME.MAX_LENGTH,
            }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& .MuiOutlinedInput-root": {
                color: "var(--text-color)",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            name="eventName"
            className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !my-4"
            {...register("eventName")}
          />
          {errors.eventName && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.eventName.message}
            </Typography>
          )}
          <Typography className="text-sm text-[--text-color] CraftworkRegular">
            Create tags that will describe your event to help people find it
          </Typography>

          {/* Modified Tag Input with Add Button */}
          <Box className="flex w-full gap-2 items-center">
            <TextField
              type="text"
              size="small"
              placeholder="Tags"
              value={tagInput}
              onChange={handleTagInputChange}
              onKeyDown={handleKeyDown}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                },
                "& .MuiOutlinedInput-root": {
                  color: "var(--text-color)",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
                flexGrow: 1,
              }}
              className="!border !w-full !py-1 CraftworkRegular !border-white rounded-[2px] !mt-4 mb-2"
            />
            <IconButton
              onClick={addTag}
              className="!bg-[--text-color] !mt-4 !mb-2"
              size="small"
              aria-label="add tag"
            >
              <Add className="text-[--bg-color]" />
            </IconButton>
          </Box>

          <Box className="flex gap-3 flex-wrap">
            {tags?.map((data) => (
              <Box key={data} className="bg-[--footer-bg] rounded-[2px] flex gap-2 py-1 px-2">
                <Typography className="text-sm font-craftWorkRegular text-[--text-color]">
                  {data}
                </Typography>
                <Close
                  className="text-[--text-color] size-4"
                  onClick={() => handleDeleteTag(data)}
                />
              </Box>
            ))}
          </Box>
          {errors.tags && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.tags.message}
            </Typography>
          )}
        </Box>
        <Box className="py-6 pl-14">
          <Typography className="text-2xl CraftworkGroteskMedium text-[--text-color]">
            About the Event
          </Typography>
          <TextField
            type="text"
            size="small"
            multiline
            rows={5}
            placeholder="About the Event"
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            inputProps={{
              maxLength: EVENT_CONSTANTS.ABOUT_EVENT.MAX_LENGTH,
            }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& .MuiOutlinedInput-root": {
                color: "var(--text-color)",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            name="aboutEvent"
            className="!border !w-full !h-[140px] !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !mt-4"
            {...register("aboutEvent")}
          />
          {errors.aboutEvent && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.aboutEvent.message}
            </Typography>
          )}
        </Box>

        <Box className="flex gap-3 items-center">
          <CommonImage src={SocialLink} alt="info-image" className="w-[40px] h-[40px]" />
          <Typography className="text-2xl CraftworkGroteskMedium text-[--text-color]">
            Social media links
          </Typography>
        </Box>
        <Box className="pl-14">
          <TextField
            type="text"
            size="small"
            placeholder="Your link"
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start" style={{ cursor: "pointer", marginRight: 0 }}>
                  <SoundCloudIcon className="!w-6 !h-6 !mr-4" />
                  <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                    https:/soundcloud.com/
                  </Typography>
                </InputAdornment>
              ),
            }}
            inputProps={{
              maxLength: EVENT_CONSTANTS.SOCIAL_LINKS.MAX_LENGTH,
            }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& input": {
                color: "#EFEFEF",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            name="soundCloudLink"
            className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !my-4"
            {...register("soundCloudLink")}
          />
          <TextField
            type="text"
            size="small"
            placeholder="Your link"
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start" style={{ cursor: "pointer", marginRight: 0 }}>
                  <Instagram className="text-[--text-color] !w-6 !h-6 !mr-4" />
                  <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                    https:/instagram/
                  </Typography>
                </InputAdornment>
              ),
            }}
            inputProps={{
              maxLength: EVENT_CONSTANTS.SOCIAL_LINKS.MAX_LENGTH,
            }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& input": {
                color: "#EFEFEF",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            name="instagramLink"
            className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !my-4"
            {...register("instagramLink")}
          />
          <TextField
            type="text"
            size="small"
            placeholder="Your link"
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start" style={{ cursor: "pointer", marginRight: 0 }}>
                  <YouTube className="text-[--text-color] !w-6 !h-6 !mr-4" />
                  <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                    https:/youtube/
                  </Typography>
                </InputAdornment>
              ),
            }}
            inputProps={{
              maxLength: EVENT_CONSTANTS.SOCIAL_LINKS.MAX_LENGTH,
            }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& input": {
                color: "#EFEFEF",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            name="youtubeLink"
            className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !my-4"
            {...register("youtubeLink")}
          />
          <TextField
            type="text"
            size="small"
            placeholder="Your link"
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start" style={{ cursor: "pointer", marginRight: 0 }}>
                  <FacebookOutlinedIcon className="text-[--text-color] !w-6 !h-6 !mr-4" />
                  <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                    https:/facebook/
                  </Typography>
                </InputAdornment>
              ),
            }}
            inputProps={{
              maxLength: EVENT_CONSTANTS.SOCIAL_LINKS.MAX_LENGTH,
            }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& input": {
                color: "#EFEFEF",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            name="facebookLink"
            className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !my-4"
            {...register("facebookLink")}
          />
        </Box>

        <Box className="flex gap-3 items-center">
          <CalendarIcon className="w-[40px] h-[40px]" />
          <Typography className="text-2xl CraftworkGroteskMedium text-[--text-color]">
            Date and time
          </Typography>
        </Box>
        <Box className="pl-14 pt-4">
          <Typography className="text-sm font-craftWorkRegular text-[--text-color]">
            Select the perfect date and time so attendees mark their calendars, eagerly anticipating
            your event.{" "}
          </Typography>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Typography className="text-sm font-craftWorkRegular text-[--text-color]">
              Tell the users when the event starts
            </Typography>
            <Box className="py-2">
              (
              <Box className="py-2">
                {/* Time Zone */}
                <Box className="w-full py-3">
                  <Typography className="text-sm text-[--text-color] py-2 CraftworkGroteskGX">
                    Time Zone
                  </Typography>
                  <TextField
                    value={timeZone}
                    InputProps={{
                      readOnly: true,
                    }}
                    className="w-full !text-[--text-color] border border-[--divider-color]"
                    sx={{
                      "& input::placeholder": {
                        color: "#EFEFEF",
                        border: 0,
                      },
                      "& .MuiOutlinedInput-root": {
                        color: "var(--text-color)",
                        fontFamily: "var(--craftWorkRegular)",
                      },
                      "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderWidth: 0,
                      },
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderWidth: 0,
                      },
                      border: 0,
                    }}
                  />
                </Box>

                {/* Start Date */}
                <Box className="w-full py-3">
                  <Typography className="text-sm pb-2 text-[--text-color] CraftworkGroteskGX">
                    Start Date
                  </Typography>
                  <TextField
                    value={dayjs(startDate).format("YYYY-MM-DD HH:mm:ss")}
                    InputProps={{
                      readOnly: true,
                    }}
                    className="border w-full !text-[--text-color]  border-[--divider-color]"
                    sx={{
                      "& input::placeholder": {
                        color: "#EFEFEF",
                        border: 0,
                      },
                      "& .MuiOutlinedInput-root": {
                        color: "var(--text-color)",
                        fontFamily: "var(--craftWorkRegular)",
                      },
                      "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderWidth: 0,
                      },
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderWidth: 0,
                      },
                      border: 0,
                    }}
                  />
                </Box>

                {/* End Date */}
                <Box className="w-full py-3">
                  <Typography className="text-sm pb-2 text-[--text-color] CraftworkGroteskGX">
                    End Date
                  </Typography>
                  <TextField
                    value={dayjs(endDate).format("YYYY-MM-DD HH:mm:ss")}
                    InputProps={{
                      readOnly: true,
                    }}
                    className="border w-full !text-[--text-color]  border-[--divider-color]"
                    sx={{
                      "& input::placeholder": {
                        color: "#EFEFEF",
                        border: 0,
                      },
                      "& .MuiOutlinedInput-root": {
                        color: "var(--text-color)",
                        fontFamily: "var(--craftWorkRegular)",
                      },
                      "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderWidth: 0,
                      },
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderWidth: 0,
                      },
                      border: 0,
                    }}
                  />
                </Box>

                {/* Duration */}
                <Box className="w-full">
                  <Typography className="text-sm pb-2 text-[--text-color] CraftworkGroteskGX">
                    Duration
                  </Typography>
                  <TextField
                    value={`${durationInHours} hours`}
                    InputProps={{
                      readOnly: true,
                    }}
                    className="!w-full mt-4 CraftworkGroteskRegular border border-[--divider-color] rounded-[2px]"
                    sx={{
                      "& input::placeholder": {
                        color: "#EFEFEF",
                        border: 0,
                      },
                      "& .MuiOutlinedInput-root": {
                        color: "var(--text-color)",
                        fontFamily: "var(--craftWorkRegular)",
                      },
                      "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderWidth: 0,
                      },
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderWidth: 0,
                      },
                      border: 0,
                    }}
                  />
                </Box>
              </Box>
            </Box>
          </LocalizationProvider>
        </Box>

        <Box className="flex justify-end relative pr-12 py-8">
          <Button
            className="flex fixed right-10 bottom-8 gap-2 !bg-[--text-color] !normal-case"
            type="submit"
          >
            <Typography className="text-[--bg-color] text-sm font-craftWorkHeavy">Save</Typography>
            <SouthEast className="text-[--bg-color] size-5" />
          </Button>
        </Box>
      </form>
    </Box>
  );
};

export default MainInfoCard;
