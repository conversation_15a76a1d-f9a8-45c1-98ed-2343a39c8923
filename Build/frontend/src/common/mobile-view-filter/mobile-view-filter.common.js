import { Box, IconButton } from "@mui/material";
import React from "react";
import Filter from "@/assets/svg/Filter.svg";
import Searchbar from "../searchbar/searchbar.common";

const MobileViewFilter = ({ handleOpen }) => {
  return (
    <Box className=" !flex !gap-x-4 !items-center lg:!hidden !w-full">
      <Searchbar placeholder="Search for profiles" />
      <IconButton onClick={handleOpen}>
        <Filter className="!text-2xl" />
      </IconButton>
    </Box>
  );
};

export default MobileViewFilter;
