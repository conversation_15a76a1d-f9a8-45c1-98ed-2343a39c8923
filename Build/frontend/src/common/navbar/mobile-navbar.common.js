"use client";
import { Box, IconButton } from "@mui/material";
import React from "react";
import Profiles from "@/assets/svg/Profiles.svg";
import CalenderIcon from "@/assets/svg/CalenderIcon.svg";
import SearchIcon from "@/assets/svg/SearchIcon.svg";

import Contracts from "@/assets/svg/Contracts.svg";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { useLocale } from "next-intl";
import DashboardIcon from "@/assets/svg/Dashboard.svg";
import styles from "./navbar.module.css";

const MobileNavbar = () => {
  const lang = useLocale();
  const pathname = usePathname();
  const navbarMenu = [
    {
      id: 0,
      icon: <CalenderIcon />,
      path: `/${lang}/events`,
    },
    {
      id: 1,
      icon: <DashboardIcon />,
      path: `/${lang}/dashboard`,
    },
    {
      id: 2,
      icon: (
        <span className={styles.whiteStroke}>
          <SearchIcon />
        </span>
      ),
      path: `/${lang}/search?profileType=ACT_PROFILE`,
    },
    {
      id: 3,
      icon: <Contracts />,
      path: `/${lang}/contracts/contracts-by-me`,
      isBadge: true,
    },
    {
      id: 4,
      icon: <Profiles />,
      path: `/${lang}/profiles`,
    },
  ];

  // Check if the current path starts with the menu path
  const isActiveRoute = (menuPath) => {
    // For search, we need to check if pathname starts with search path
    if (menuPath.includes("/search")) {
      return pathname.startsWith(`/${lang}/search`);
    }
    return pathname === menuPath;
  };

  return (
    <Box className="!fixed !pb-4 !flex !justify-between !bottom-0 !left-0 !right-0 !h-[87px] !bg-[--bg-color] !border-t !border-[--divider-color] !z-20">
      {navbarMenu.map((menu) => (
        <Link href={menu.path} key={menu.id}>
          <Box
            className={`hover:bg-[--footer-bg] !p-4 ${
              isActiveRoute(menu.path) ? "bg-[--footer-bg] rounded-[4px]" : ""
            }`}
          >
            <IconButton
              className={`${
                menu.path === `/${lang}/dashboard` && pathname === menu.path
                  ? "!text-[--text-color]"
                  : "!text-black"
              } ${menu.id === 2 ? "!bg-none !p-2" : ""}`}
            >
              <div className="text-white flex items-center justify-center"> {menu.icon}</div>
            </IconButton>
          </Box>
        </Link>
      ))}
    </Box>
  );
};

export default MobileNavbar;
