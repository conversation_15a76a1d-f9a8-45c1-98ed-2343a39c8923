import { Box, IconButton, Typography } from "@mui/material";
import React, { useState } from "react";
import NewBookingSvg from "@/assets/svg/NewBooking.svg";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { useDispatch } from "react-redux";
import { messageSeen, recievedAck } from "@/store/slice/booking/booking.slice";
import { Clear } from "@mui/icons-material";
import { deleteNotification, notificationList } from "@/store/slice/common/instantMessage.slice";
// import AvatarPng from "@/assets/png/Avatar.png";
// import AvatarImage from "@/assets/png/AvatarImage.png";
// import { SouthEast } from "@mui/icons-material";
import CancelledBookingSvg from "@/assets/svg/CancelledBooking.svg";
import ConfirmedBookingSvg from "@/assets/svg/ConfirmedBooking.svg";
// import TicketSvg from "@/assets/svg/Ticket.svg";
import NegotiatingBookingSvg from "@/assets/svg/NegotiatingBooking.svg";
import OutlinedMessageSvg from "@/assets/svg/OutlinedMessage.svg";
import ViewFeedbackDialog from "@/component/view-feedback-dialog/view-feedback-dialog.component";

const NotificationCard = ({ type }) => {
  //const images = [AvatarPng, AvatarImage];
  const router = useRouter();
  const lang = useLocale();
  const dispatch = useDispatch();
  const handleDeleteNotification = () => {
    dispatch(deleteNotification(type?.messageId))
      .unwrap()
      .then(() => {
        dispatch(notificationList());
      });
  };
  const [open, setOpen] = useState(false);
  const [feedbackData, setFeedbackData] = useState({});
  const handleClose = () => {
    setOpen(false);
  };
  const handleViewFeedback = (type) => {
    setOpen(true);
    setFeedbackData(type);
  };
  return (
    <>
      <Box className="border border-[--divider-color] w-full p-4 rounded-[6px]">
        <Box className="flex justify-between pb-2 items-center">
          <Typography className="text-base CraftworkGroteskMedium text-[--text-color]">
            {type?.messageType}
          </Typography>
          {/* <Box className="p-1 bg-[--text-color] rounded-[2px]">
            <NewBookingSvg className="text-2xl" />
          </Box> */}
        </Box>

        {type?.content?.messageType === "BOOKING_REQUEST" &&
          (type?.content?.contractContentDto?.contractState === "RECEIVED" ||
            type?.content?.contractContentDto?.contractState === "SENT" ||
            type?.content?.contractContentDto?.contractState === "CREATED") && (
            <Box className="flex justify-between pb-2 items-center">
              <Typography className="text-base CraftworkGroteskMedium text-[--text-color]">
                Booking request
              </Typography>

              <Box className="p-1 bg-[--text-color] rounded-[2px] cursor-pointer">
                <NewBookingSvg
                  className="text-2xl"
                  onClick={() => {
                    if (!type?.seen) {
                      if (type?.content?.contractContentDto?.contractState === "RECEIVED") {
                        dispatch(recievedAck(type?.content?.contractContentDto?.contractId));
                      }

                      dispatch(messageSeen(type?.messageId));
                    }
                    router.push(
                      `/${lang}/booking-details?contract-id=${type?.content?.contractContentDto?.contractId}&message-id=${type?.messageId}`,
                    );
                  }}
                />
              </Box>
              <IconButton onClick={handleDeleteNotification}>
                <Clear className="text-[--text-color] text-lg" />
              </IconButton>
            </Box>
          )}
        {type?.content?.messageType === "GENERIC_MESSAGE" && (
          <Box className="flex justify-between pb-2 items-center">
            <Typography className="text-base CraftworkGroteskMedium text-[--text-color]">
              Message
            </Typography>
            <Box className="p-1 bg-[--text-color] rounded-[2px] cursor-pointer">
              <OutlinedMessageSvg
                className="text-2xl"
                onClick={() => {
                  if (!type?.seen) {
                    dispatch(messageSeen(type?.messageId))
                      .unwrap()
                      .then(() => {
                        //dispatch(notificationList());
                      });
                  }
                }}
              />
            </Box>
            <IconButton onClick={handleDeleteNotification}>
              <Clear className="text-[--text-color] text-lg" />
            </IconButton>
          </Box>
        )}
        {type?.content?.messageType === "FEEDBACK_MESSAGE" && (
          <Box className="flex justify-between pb-2 items-center">
            <Typography className="text-base CraftworkGroteskMedium text-[--text-color]">
              Feedback Message
            </Typography>
            <Box className="p-1 bg-[--text-color] rounded-[2px] cursor-pointer">
              <OutlinedMessageSvg
                className="text-2xl"
                onClick={() => {
                  if (!type?.seen) {
                    dispatch(messageSeen(type?.messageId))
                      .unwrap()
                      .then(() => {
                        //dispatch(notificationList());
                      });
                  }
                  handleViewFeedback(type);
                }}
              />
            </Box>
            <IconButton onClick={handleDeleteNotification}>
              <Clear className="text-[--text-color] text-lg" />
            </IconButton>
          </Box>
        )}
        {type?.content?.messageType === "BOOKING_REQUEST" &&
          type?.content?.contractContentDto?.contractState === "CONFIRMED" && (
            <Box className="flex justify-between pb-2 items-center">
              <Typography className="text-base CraftworkGroteskMedium text-[--text-color]">
                Booking request
              </Typography>
              <Box className="p-1 bg-[--confirmed-color] rounded-[2px] cursor-pointer">
                <ConfirmedBookingSvg
                  className="text-2xl"
                  onClick={() => {
                    if (!type?.seen) {
                      dispatch(recievedAck(type?.content?.contractContentDto?.contractId));
                      dispatch(messageSeen(type?.messageId));
                    }
                    router.push(
                      `/${lang}/booking-details?contract-id=${type?.content?.contractContentDto?.contractId}&message-id=${type?.messageId}`,
                    );
                  }}
                />
              </Box>
              <IconButton onClick={handleDeleteNotification}>
                <Clear className="text-[--text-color] text-lg" />
              </IconButton>
            </Box>
          )}

        {type?.content?.messageType === "BOOKING_REQUEST" &&
          (type?.content?.contractContentDto?.contractState === "DECLINED" ||
            type?.content?.contractContentDto?.contractState === "CANCELLED") && (
            <Box className="flex justify-between pb-2 items-center">
              <Typography className="text-base CraftworkGroteskMedium text-[--text-color]">
                Booking request
              </Typography>
              <Box className="p-1 bg-[--delete-color] rounded-[2px] cursor-pointer">
                <CancelledBookingSvg
                  className="text-2xl"
                  onClick={() => {
                    if (!type?.seen) {
                      dispatch(recievedAck(type?.content?.contractContentDto?.contractId));
                      dispatch(messageSeen(type?.messageId));
                    }
                    router.push(
                      `/${lang}/booking-details?contract-id=${type?.content?.contractContentDto?.contractId}&message-id=${type?.messageId}`,
                    );
                  }}
                />
              </Box>
              <IconButton onClick={handleDeleteNotification}>
                <Clear className="text-[--text-color] text-lg" />
              </IconButton>
            </Box>
          )}
        {type?.content?.messageType === "BOOKING_REQUEST" &&
          type?.content?.contractContentDto?.contractState === "NEGOTIATING" && (
            <Box className="flex justify-between pb-2 items-center">
              <Typography className="text-base CraftworkGroteskMedium text-[--text-color]">
                Booking request
              </Typography>
              <Box className="p-1 bg-[--inprogress-color] rounded-[2px] cursor-pointer">
                <NegotiatingBookingSvg
                  className="text-2xl"
                  onClick={() => {
                    if (!type?.seen) {
                      dispatch(recievedAck(type?.content?.contractContentDto?.contractId));
                      dispatch(messageSeen(type?.messageId));
                    }
                    router.push(
                      `/${lang}/booking-details?contract-id=${type?.content?.contractContentDto?.contractId}&message-id=${type?.messageId}`,
                    );
                  }}
                />
              </Box>
              <IconButton onClick={handleDeleteNotification}>
                <Clear className="text-[--text-color] text-lg" />
              </IconButton>
            </Box>
          )}

        {/* {type === "Ticket" && (
        <Box className="flex justify-between pb-2 items-center">
          <Typography className="text-lg CraftworkGroteskMedium text-[--text-color]">
            Don&#900;t miss the event!
          </Typography>
          <Box className="p-1 bg-[--text-color] rounded-[2px]">
            <TicketSvg className="text-2xl" />
          </Box>
        </Box>
      )}
      
      */}
        <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
          {/* {type?.content} */}
          {type?.content?.messageType === "GENERIC_MESSAGE" &&
            `${type?.content?.genericMsgContentDto?.sendingUserName} sent a message to ${type?.content?.genericMsgContentDto?.receivingUserName}`}

          {type?.content?.messageType === "BOOKING_REQUEST" &&
            type?.content?.contractContentDto?.actionString &&
            `${type?.content?.contractContentDto?.actionString}`}

          {type?.content?.messageType === "FEEDBACK_MESSAGE" &&
            type?.content?.feedbackMsgDto?.actionString &&
            `${type?.content?.feedbackMsgDto?.actionString}`}

          {/* {type?.content?.messageType === "BOOKING_REQUEST" &&
          (type?.content?.contractContentDto?.contractState === "SENT" ||
            type?.content?.contractContentDto?.contractState === "CREATED") &&
          `${
            type?.content?.contractContentDto?.bookingParty === "VENUE"
              ? type?.content?.contractContentDto?.venueProfileName
              : type?.content?.contractContentDto?.bookingParty === "USER"
                ? type?.content?.contractContentDto?.userName
                : type?.content?.contractContentDto?.actProfileName
          } sent a request with 
            ${
              type?.content?.contractContentDto?.otherParty === "VENUE"
                ? type?.content?.contractContentDto?.venueProfileName
                : type?.content?.contractContentDto?.otherParty === "USER"
                  ? type?.content?.contractContentDto?.userName
                  : type?.content?.contractContentDto?.actProfileName
            }
            `}

        {type?.content?.messageType === "BOOKING_REQUEST" &&
          type?.content?.contractContentDto?.contractState === "RECEIVED" &&
          `${
            type?.content?.contractContentDto?.otherParty === "VENUE"
              ? type?.content?.contractContentDto?.venueProfileName
              : type?.content?.contractContentDto?.otherParty === "USER"
                ? type?.content?.contractContentDto?.userName
                : type?.content?.contractContentDto?.actProfileName
          } initiated a request with  
            ${
              type?.content?.contractContentDto?.bookingParty === "VENUE"
                ? type?.content?.contractContentDto?.venueProfileName
                : type?.content?.contractContentDto?.bookingParty === "USER"
                  ? type?.content?.contractContentDto?.userName
                  : type?.content?.contractContentDto?.actProfileName
            }
            `}

        {type?.content?.messageType === "BOOKING_REQUEST" &&
          type?.content?.contractContentDto?.contractState === "CONFIRMED" &&
          `${
            type?.content?.contractContentDto?.bookingParty === "VENUE"
              ? type?.content?.contractContentDto?.venueProfileName
              : type?.content?.contractContentDto?.bookingParty === "USER"
                ? type?.content?.contractContentDto?.userName
                : type?.content?.contractContentDto?.actProfileName
          } confirmed the request with
            ${
              type?.content?.contractContentDto?.otherParty === "VENUE"
                ? type?.content?.contractContentDto?.venueProfileName
                : type?.content?.contractContentDto?.otherParty === "USER"
                  ? type?.content?.contractContentDto?.userName
                  : type?.content?.contractContentDto?.actProfileName
            }
            `}

        {type?.content?.messageType === "BOOKING_REQUEST" &&
          type?.content?.contractContentDto?.contractState === "DECLINED" &&
          `${
            type?.content?.contractContentDto?.bookingParty === "VENUE"
              ? type?.content?.contractContentDto?.venueProfileName
              : type?.content?.contractContentDto?.bookingParty === "USER"
                ? type?.content?.contractContentDto?.userName
                : type?.content?.contractContentDto?.actProfileName
          } declined the request with 
            ${
              type?.content?.contractContentDto?.otherParty === "VENUE"
                ? type?.content?.contractContentDto?.venueProfileName
                : type?.content?.contractContentDto?.otherParty === "USER"
                  ? type?.content?.contractContentDto?.userName
                  : type?.content?.contractContentDto?.actProfileName
            }
            `}

        {type?.content?.messageType === "BOOKING_REQUEST" &&
          type?.content?.contractContentDto?.contractState === "CANCELLED" &&
          `${
            type?.content?.contractContentDto?.bookingParty === "VENUE"
              ? type?.content?.contractContentDto?.venueProfileName
              : type?.content?.contractContentDto?.bookingParty === "USER"
                ? type?.content?.contractContentDto?.userName
                : type?.content?.contractContentDto?.actProfileName
          } cancelled the request with 
            ${
              type?.content?.contractContentDto?.otherParty === "VENUE"
                ? type?.content?.contractContentDto?.venueProfileName
                : type?.content?.contractContentDto?.otherParty === "USER"
                  ? type?.content?.contractContentDto?.userName
                  : type?.content?.contractContentDto?.actProfileName
            }
            `}

        {type?.content?.messageType === "BOOKING_REQUEST" &&
          type?.content?.contractContentDto?.contractState === "NEGOTIATING" &&
          `${
            type?.content?.contractContentDto?.bookingParty === "VENUE"
              ? type?.content?.contractContentDto?.venueProfileName
              : type?.content?.contractContentDto?.bookingParty === "USER"
                ? type?.content?.contractContentDto?.userName
                : type?.content?.contractContentDto?.actProfileName
          } is negotiating a request with 
            ${
              type?.content?.contractContentDto?.otherParty === "VENUE"
                ? type?.content?.contractContentDto?.venueProfileName
                : type?.content?.contractContentDto?.otherParty === "USER"
                  ? type?.content?.contractContentDto?.userName
                  : type?.content?.contractContentDto?.actProfileName
            }
            `} */}
        </Typography>

        {/* {(type === "Cancel Booking" ||
        type === "New Booking" ||
        type === "Confirmed Booking" ||
        type === "Message") && (
        <Typography className="text-sm text-[--text-color] CraftworkGroteskBold">
          &#91;Some_Venue&#x5B; <span className="CraftworkGroteskRegular">sent a request to</span>{" "}
          &#91;My_Act_profile&#x5B;
        </Typography>
      )}
      {type === "Ticket" && (
        <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
          The event you bought tickets for is tomorrow, don&#900;t miss it!
        </Typography>
      )} */}
        {/* <Box className="flex justify-between items-center pt-2">
        <AvatarGroup max={2}>
          {images.map((image, index) => (
            <Avatar key={index} src={image} alt="image" className="h-6 w-6" />
          ))}
        </AvatarGroup>
        <IconButton sx={{ padding: 0 }}>
          <SouthEast className="text-2xl text-[--text-color]" />
        </IconButton>
      </Box> */}
      </Box>
      <ViewFeedbackDialog open={open} handleClose={handleClose} feedbackData={feedbackData} />
    </>
  );
};

export default NotificationCard;
