"use client";
import { Clear } from "@mui/icons-material";
import { Box, Dialog, DialogContent, IconButton, Typography } from "@mui/material";
import React, { useState } from "react";
import NotificationPopupSvg from "@/assets/svg/NotificationPopup.svg";
import NotificationCard from "../notification-card/notification-card.common";
import { useSelector } from "react-redux";
import { useTranslations } from "next-intl";

const NotificationPopup = ({ open, handleClose }) => {
  const t = useTranslations("notification");
  const { loading, instantMessage } = useSelector((state) => state.instantMessage);
  const [clickedNotifications, setClickedNotifications] = useState({});

  const handleNotificationClick = (index) => {
    setClickedNotifications((prev) => ({ ...prev, [index]: true }));
  };

  return (
    !loading && (
      <Box>
        <Dialog
          sx={{
            "& .MuiBackdrop-root": {
              backgroundColor: "transparent",
            },
            "& .MuiDialog-container": {
              justifyContent: "end",
              marginTop: "30px",
              maxHeight: "620px",
            },
            "& .MuiDialog-paper": {
              margin: "22px",
              width: 340,
              height: 610,
              backgroundColor: "var(--footer-bg)",
              border: "1px solid var(--divider-color)",
            },
          }}
          open={open}
          onClose={handleClose}
        >
          <DialogContent>
            <Box className="flex justify-between items-center">
              <Typography className="text-[--text-color] text-sm CraftworkGroteskRegular">
                {t("notifications")}{" "}
                {instantMessage.filter((item) => !item.dismissed).length > 0 &&
                  `(${instantMessage.filter((item) => !item.dismissed).length})`}
              </Typography>
              <IconButton onClick={handleClose}>
                <Clear className="text-[--text-color] text-lg" />
              </IconButton>
            </Box>
            {instantMessage.filter((item) => !item.dismissed).length > 0 ? (
              <>
                {instantMessage.map((data, index) =>
                  !data.dismissed ? (
                    <Box
                      key={index}
                      className={`pb-3 ${!clickedNotifications[index] ? "bg-[--footer-bg]" : ""}`}
                      onClick={() => handleNotificationClick(index)}
                    >
                      <NotificationCard type={data} />
                    </Box>
                  ) : null,
                )}
              </>
            ) : (
              <Box className="flex flex-col pt-9 items-center">
                <NotificationPopupSvg className="!text-5xl" />
                <Typography className="text-[#7A7D8B] text-sm font-craftWorkRegular">
                  {t("noNotification")}
                </Typography>
              </Box>
            )}
          </DialogContent>
        </Dialog>
      </Box>
    )
  );
};

export default NotificationPopup;
