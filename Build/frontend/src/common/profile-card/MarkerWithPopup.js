import React, { useMemo } from "react";
import { Marker, Popup } from "react-leaflet";
import { Icon } from "leaflet";
import { useMapInstance } from "./MapProvider";
import MarkerImage from "@/assets/png/Marker.png";

const customIcon = new Icon({
  iconUrl: MarkerImage?.src,
  iconSize: [50, 50],
  iconAnchor: [12, 25],
  popupAnchor: [0, -25],
});

const MarkerWithPopup = ({ marker, markers }) => {
  const map = useMapInstance();

  // Calculate offset for overlapping markers
  const position = useMemo(() => {
    const overlappingMarkers = markers.filter((m) => m.lat === marker.lat && m.lng === marker.lng);

    if (overlappingMarkers.length > 1) {
      // Find current marker's index in overlapping group
      const overlapIndex = overlappingMarkers.findIndex(
        (m) => m.properties.name === marker.properties.name,
      );

      // Add small offset based on index (0.0001 degrees ≈ 11 meters)
      const offset = 0.0001 * overlapIndex;
      return [parseFloat(marker.lat) + offset, parseFloat(marker.lng) + offset];
    }

    return [marker.lat, marker.lng];
  }, [marker, markers]);

  const handleMarkerClick = () => {
    map.setView(position);
  };

  return (
    <Marker position={position} icon={customIcon} eventHandlers={{ click: handleMarkerClick }}>
      <Popup closeButton={false} className="m-0 w-72 leaflet-popup-custom">
        <div className="flex flex-col gap-2">
          <img
            src={marker?.properties?.url}
            className="w-full h-[7rem]"
            alt={marker?.properties?.name}
          />
          <h1 className="text-[1rem] CraftworkGroteskSemiBold">{marker?.properties?.name}</h1>
          <p className="text-[0.8rem] border-b-2 CraftworkGroteskRegular">
            {marker?.properties?.discription}
          </p>
          <p className="text-[0.8rem] CraftworkGroteskRegular">{marker?.properties?.Address}</p>
          {/* <a href={`tel:${marker.properties.Phone}`} className="text-[0.8rem]">
            {marker.properties.Phone}
          </a> */}
        </div>
      </Popup>
    </Marker>
  );
};

export default MarkerWithPopup;
