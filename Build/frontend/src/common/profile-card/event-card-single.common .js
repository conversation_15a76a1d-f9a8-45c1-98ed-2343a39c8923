import { Box, Typography } from "@mui/material";
import React from "react";
import { Button, CommonImage } from "@/component";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import CardCarousel from "./profile-card.carousel.common";
import { generateLocationString } from "@/utils";
import { useLocale } from "next-intl";
import CalenderIcon from "@/assets/svg/CalenderIcon.svg";
import { useSelector } from "react-redux";
import { useAppContext } from "@/context/token-expired/token.expired.context";
import { useRouter } from "next/navigation";
import Avatar from "@/assets/png/Avatar.png";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { SouthEast } from "@mui/icons-material";
dayjs.extend(utc);
dayjs.extend(timezone);

// import {
//   addFavouriteByProfileID,
//   removeFavouriteByProfileID,
// } from "@/store/slice/common/favourite.slice";

const EventSingleCard = ({ profile }) => {
  // const p = useTranslations("actReview.actReviewLocation");
  const lang = useLocale();
  const { token } = useSelector((state) => state.login);
  const { setIsTokenExpired } = useAppContext();
  const router = useRouter();

  const clickHandler = (profileId) => {
    if (!token) {
      setIsTokenExpired(true);
      return;
    }

    router.push(`/${lang}/event/${profileId}/view`);
  };

  const formatDateTime = (dateString) => {
    const timeZone = "America/Toronto"; // Set the timezone
    const date = dayjs(dateString).tz(timeZone);

    return `${date.format("MMM D, YYYY")} | ${date.format("hA")}, EST`;
  };

  return (
    <Box
      className="!border !border-[--divider-color] !w-[90vw] md:!max-w-xs lg:!w-[330px] !mb-4 !rounded-[4px] !bg-[--footer-bg]"
      // className={classNames(
      //   "border mb-4 !w-full md:!max-w-xs lg:!w-[330px] rounded-[4px] bg-[--footer-bg] cursor-pointer",
      //   {
      //     "border-[--inprogress-color]": selectedItem === profile.profileId,
      //     "border-[--divider-color]": selectedItem !== index,
      //   },
      // )}
    >
      <Box className="!relative">
        <CardCarousel
          images={profile?.eventMediaInfo?.imageUrls}
          className="slider-class"
          profileId={profile.eventId}
        />
        <Typography className="!text-[--text-color] !text-sm CraftworkGroteskSemiBold !absolute !left-3 !top-3 !bg-[--profile-type] !rounded-[2px] !px-[6px] !py-[2px]">
          {profile.eventName}
        </Typography>
      </Box>
      {/* {type === "search" && (
                <Box className="!relative">
                  <CardCarousel
                    images={profile.profileImageUrls}
                    className="slider-class lg:w-full w-[90vw]"
                    profileId={profile.profileId}
                  />
                  <Typography className="!text-[--text-color] !text-sm CraftworkGroteskSemiBold !absolute !left-3 !top-3 !bg-[--profile-type] !rounded-[2px] !px-[6px] !py-[2px]">
                    {profile.profileType}
                  </Typography>
                </Box>
              )} */}
      <Box className="!px-5 ">
        <Box className="!pb-5">
          <Box className="!flex !justify-between !items-center">
            <Typography className="!text-[--text-color] !text-2xl font-craftWorkGX">
              {profile?.eventName && profile?.eventName.length > 12
                ? profile.eventName.substring(0, 12) + "..."
                : profile.eventName}
            </Typography>
            <Box className="!flex items-center">
              {/* <IconButton>
                        <AddCalender className="!text-2xl" />
                      </IconButton> */}
              {/* <IconButton>
                        <FilledHeart className="!text-2xl" />
                      </IconButton> */}
            </Box>
          </Box>
        </Box>
        <Box className="!flex !gap-x-3 !py-3">
          <CalenderIcon className="!text-2xl" />
          <Typography className="!text-[--text-color] !text-sm  font-craftWorkRegular">
            {formatDateTime(profile?.scheduleTime?.startDate)}
          </Typography>
        </Box>
        <Box className="!flex !items-center !gap-x-2">
          <span className="!text-2xl">
            <LocationSvg />
          </span>
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
            {generateLocationString(profile.venueLocation).length > 32
              ? generateLocationString(profile.venueLocation).substring(0, 32) + "..."
              : generateLocationString(profile.venueLocation)}
          </Typography>
        </Box>
        <Box className="flex gap-x-2 py-2">
          <CommonImage
            src={profile?.venueImageUrls?.[0] ?? Avatar}
            alt="avatar"
            className="size-6 rounded-full"
            width={10}
            height={10}
          />
          <Typography className="text-[--text-color] text-sm font-craftWorkRegular">
            {profile?.venueName}
          </Typography>
        </Box>
        {/* <Box className="flex gap-x-2 pb-2 pt-8">
                  <TicketSvg className="size-6" />
                  <Typography className="text-[--text-color] text-sm font-craftWorkRegular">
                    From $ 23.46
                  </Typography>
                </Box> */}
        <Box className="!flex !gap-1 !mb-4">
          <Button
            className="!bg-[--text-color] !w-full px-3 py-3 !gap-x-2"
            sx={{
              minWidth: 0,
              padding: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
            onClick={() => {
              clickHandler(profile.eventId);
            }}
          >
            <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
              View Event
            </Typography>
            <SouthEast className="size-5 text-[--bg-color]" />
            {/* <Ticket className="size-4" /> */}
          </Button>
          {/* </Link> */}
        </Box>
      </Box>
    </Box>
  );
};

export default EventSingleCard;
