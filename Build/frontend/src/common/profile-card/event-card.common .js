import { Box, Typography } from "@mui/material";
import React from "react";
import { Button, CommonImage, Loader } from "@/component";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import CardCarousel from "./profile-card.carousel.common";
import { generateLocationString } from "@/utils";
import CalenderIcon from "@/assets/svg/CalenderIcon.svg";
import EditSvg from "@/assets/svg/Edit.svg";
import DeleteIcon from "@/assets/svg/act-type.svg/DeleteSvg.svg";
import { useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import DeleteContainer from "@/containers/delete/delete-container";
import Paginate from "@/containers/paginate/paginate.container";
import Avatar from "@/assets/png/Avatar.png";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import classNames from "classnames";
import { deleteSpecialEventByIdAction } from "@/store/slice/specialevent/event.slice";
dayjs.extend(utc);
dayjs.extend(timezone);

const EventCard = ({ profiles, params, type, setShowForm, getdata }) => {
  const [open, setOpen] = React.useState("");
  const router = useRouter();
  const [selectedProfileData, setSelectedProfileData] = React.useState("");
  const dispatch = useDispatch();

  // Define selectedItem state to track selected event

  const handleClickOpen = (profileData) => {
    setSelectedProfileData(profileData);
    setOpen("deletePopup");
  };

  const handleClose = (data) => {
    setOpen(data);
  };

  // const clickHandler = (profileId) => {
  //   if (!token) {
  //     setIsTokenExpired(true);
  //     return;
  //   }

  //   router.push(`/${lang}/event/${profileId}/view`);
  // };

  const formatDateTime = (dateString) => {
    const timeZone = "America/Toronto"; // Set the timezone
    const date = dayjs(dateString).tz(timeZone);

    return `${date.format("MMM D, YYYY")} | ${date.format("hA")}, EST`;
  };

  const profilesData = profiles?.content?.filter((event) => event.eventId) ?? [];

  // if no data render null
  if (profilesData?.length === 0) {
    return null;
  }

  const eventLabels = {
    EVENT: "Event",
    SPECIAL_EVENT: "Special Event",
    FEATURED_EVENT: "Featured Event",
  };

  const handleEditClick = (profileData) => {
    setSelectedProfileData(profileData);

    router.push(`${window.location.pathname}?eventId=${profileData.eventId}`);

    setTimeout(() => {
      <Loader />;
      setShowForm(true);
    }, 2000);
  };
  //dispatch(deleteSpecialEventByIdAction(selectedProfileData?.eventId));

  const handleDelete = async () => {
    await dispatch(deleteSpecialEventByIdAction(selectedProfileData?.eventId)).unwrap();
    setOpen(""); // Close delete modal after successful deletion
  };

  return (
    <>
      <Box className="!flex !flex-wrap !gap-5 !mt-4 !pb-28 lg:!pb-0 !px-4 lg:!px-0">
        {profilesData &&
          profilesData?.length > 0 &&
          profilesData?.map((profile, index) => (
            <Box
              key={index}
              className={
                type === "specialEvent"
                  ? classNames(
                      "border mb-4 !w-full md:!max-w-xs lg:!w-[330px] rounded-[4px] bg-[--footer-bg]  border-[--divider-color]",
                    )
                  : "!border !border-[--divider-color] !w-[90vw] md:!max-w-xs lg:!w-[330px] !mb-4 !rounded-[4px] !bg-[--footer-bg]"
              }
            >
              <Box className="!relative">
                <CardCarousel
                  images={profile?.eventMediaInfo?.imageUrls}
                  className="slider-class"
                  profileId={profile.eventId}
                  type={type}
                  eventType={true}
                />
                <Typography className="!text-[--text-color] !text-sm CraftworkGroteskSemiBold !absolute !left-3 !top-3 !bg-[--profile-type] !rounded-[2px] !px-[6px] !py-[2px]">
                  {type === "specialEvent"
                    ? eventLabels["SPECIAL_EVENT"] || profile.eventName
                    : profile.eventName}
                </Typography>
              </Box>
              <Box className="!px-5 ">
                <Box className="!pb-5">
                  <Box className="!flex !justify-between !items-center">
                    <Typography className="!text-[--text-color] !text-2xl font-craftWorkGX">
                      {profile?.eventName && profile?.eventName.length > 12
                        ? profile.eventName.substring(0, 12) + "..."
                        : profile.eventName}
                    </Typography>
                  </Box>

                  <Box className="!flex !justify-between !items-center">
                    <Typography className="!text-[--text-color] !text-2xl font-craftWorkGX">
                      {profile?.aboutTheEvent && profile?.aboutTheEvent.length > 12
                        ? profile?.aboutTheEvent.substring(0, 12) + "..."
                        : profile.aboutTheEvent}
                    </Typography>
                  </Box>
                </Box>
                <Box className="!flex !gap-x-3 !py-3">
                  <CalenderIcon className="!text-2xl" />
                  <Typography className="!text-[--text-color] !text-sm font-craftWorkRegular">
                    {formatDateTime(profile?.scheduleTime?.startDate)}
                  </Typography>
                </Box>
                {type !== "specialEvent" && (
                  <Box className="!flex !items-center !gap-x-2">
                    <span className="!text-2xl">
                      <LocationSvg />
                    </span>
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                      {generateLocationString(profile.venueLocation).length > 32
                        ? generateLocationString(profile.venueLocation).substring(0, 32) + "..."
                        : generateLocationString(profile.venueLocation)}
                    </Typography>
                  </Box>
                )}
                {profile?.venueName && (
                  <Box className="flex gap-x-2 py-2">
                    <CommonImage
                      src={profile?.venueImageUrls?.[0] ?? Avatar}
                      alt="avatar"
                      className="size-6 rounded-full"
                      width={10}
                      height={10}
                    />
                    <Typography className="text-[--text-color] text-sm font-craftWorkRegular">
                      {profile?.venueName}
                    </Typography>
                  </Box>
                )}

                <Box className="!flex !items-center !justify-center !gap-1 !mb-4">
                  {type === "specialEvent" && (
                    <>
                      {/* <Link href={`/${lang}/event/${profile.eventId}/edit`}> */}
                      <Button
                        onClick={() => {
                          handleEditClick(profile);
                        }}
                        className="!border-[2px] !px-2 !border-[--text-color] !rounded-[4px]"
                        sx={{
                          minWidth: 0,
                          border: 0,
                          "&.MuiButtonBase-root": {
                            color: "transparent !important",
                          },
                        }}
                      >
                        <EditSvg className="!text-2xl" />
                      </Button>
                      {/* </Link> */}
                      <Button
                        className="!border-[2px] !px-2 !border-[--text-color] !rounded-[4px]"
                        type="button"
                        sx={{
                          minWidth: 0,
                          border: 0,
                          "&.MuiButtonBase-root": {
                            color: "transparent !important",
                          },
                        }}
                        onClick={() => handleClickOpen(profile)}
                      >
                        <DeleteIcon className="!text-2xl" />
                      </Button>
                    </>
                  )}
                  {/* <Button
                    className="!bg-[--text-color] !w-full px-3 py-3 !gap-x-2"
                    sx={{
                      minWidth: 0,
                      padding: 0,
                      "&.MuiButtonBase-root": {
                        color: "white !important",
                      },
                    }}
                    onClick={() => {
                      !profileTypeselecthandler && clickHandler(profile.eventId);
                      if (profileTypeselecthandler) {
                        setSelectedItemState(profile.eventId); // Update selectedItem state
                        profileTypeselecthandler(profile);
                      }
                    }}
                  >
                    <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                      {type === "specialEvent" && profileTypeselecthandler
                        ? selectedItem === profile.eventId
                          ? "Selected"
                          : "Select Event"
                        : "View Event"}
                    </Typography>
                    <SouthEast className="size-5 text-[--bg-color]" />
                  </Button> */}
                </Box>
              </Box>
            </Box>
          ))}

        {open !== "" && (
          // <DeleteContainer
          //   open={open}
          //   selectedProfileData={selectedProfileData}
          //   handleClose={handleClose}
          //   setOpen={setOpen}
          //   params={params}
          // />
          <DeleteContainer
            open={open}
            handleClose={handleClose}
            setOpen={setOpen}
            params={params}
            isEvent={true}
            eventId={selectedProfileData?.eventId}
            deleteSpecialEventByIdAction={handleDelete}
            getdata={getdata}
          />
        )}
      </Box>
      <Box className="flex justify-center">
        {profiles?.totalPages > 1 && (
          <Paginate totalRecords={profiles?.totalElements} perPageRecord={profiles.size} />
        )}
      </Box>
    </>
  );
};

export default EventCard;
