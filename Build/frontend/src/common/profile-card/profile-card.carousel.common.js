"use client";
import React from "react";
//import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { Box } from "@mui/material";
import { CommonImage } from "@/component";
import MediaImage from "@/assets/png/MediaImage.png";
import Link from "next/link";
import { useLocale } from "next-intl";

const CardCarousel = ({
  images,
  className,
  profileId,
  type,
  profileTypeselecthandler,
  eventType = false,
}) => {
  const lang = useLocale();
  // const settings = {
  //   className: "slider variable-width",
  //   dots: true,
  //   infinite: true,
  //   speed: 1000,
  //   slidesToShow: 1,
  //   slidesToScroll: 1,
  //   adaptiveHeight: true,
  //   swipeToSlide: true,
  //   autoplay: false,
  //   autoplaySpeed: 10000,
  //   prevArrow: <></>,
  //   nextArrow: <></>,
  // };
  return (
    <>
      <Box className={`${className} object-cover`}>
        {/* <Slider {...settings}>
          {images?.length > 0 ? (
            images?.map((image, index) => (
              <Link key={index} href={`/${lang}/${profileId}/view`}>
                <Box className="object-cover w-full h-[318px]">
                  <CommonImage
                    className="w-full h-[318px] object-cover"
                    src={
                      typeof image === "string" && image.includes("http")
                        ? image
                        : `/images/${image}`
                    }
                    alt={`Profile_image_${index}`}
                    fallBackSrc={MediaImage}
                    onLoad={() => URL.revokeObjectURL(image)}
                    width={300}
                    height={300}
                  />
                </Box>
              </Link>
            ))
          ) : (
            <Box className="w-full">
              <CommonImage
                className="object-cover w-full h-[318px]"
                src={MediaImage}
                alt={"profile_image"}
                fallBackSrc={MediaImage}
                width={300}
                height={300}
              />
            </Box>
          )}
        </Slider> */}
        <Box className="w-full">
          {profileTypeselecthandler || type === "specialEvent" ? (
            <CommonImage
              className="object-cover w-full h-[318px]"
              src={images?.[0] ?? MediaImage}
              alt={"profile_image"}
              fallBackSrc={MediaImage}
              width={300}
              height={300}
            />
          ) : (
            <Link href={`/${lang}/${eventType === true ? "event/" : ""}${profileId}/view`}>
              <CommonImage
                className="object-cover w-full h-[318px]"
                src={images?.[0] ?? MediaImage}
                alt={"profile_image"}
                fallBackSrc={MediaImage}
                width={300}
                height={300}
              />
            </Link>
          )}
        </Box>
      </Box>
    </>
  );
};

export default CardCarousel;
