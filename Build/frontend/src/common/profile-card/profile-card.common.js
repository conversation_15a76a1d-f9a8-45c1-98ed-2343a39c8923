import { Box, IconButton, Typography } from "@mui/material";
import React from "react";
import Rating from "@/component/rating/rating.components";
import Link from "next/link";
import EditSvg from "@/assets/svg/Edit.svg";
import { Button } from "@/component";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import CardCarousel from "./profile-card.carousel.common";
import { generateLocationString } from "@/utils";
import { useLocale, useTranslations } from "next-intl";
import DeleteIcon from "@/assets/svg/act-type.svg/DeleteSvg.svg";
import FilledHeart from "@/assets/svg/FilledHeart.svg";
import LikeIcon from "@/assets/svg/LikeIcon.svg";
import Play from "@/assets/svg/Play.svg";
import {
  addFavouriteByProfileID,
  removeFavouriteByProfileID,
} from "@/store/slice/common/favourite.slice";
import { useDispatch, useSelector } from "react-redux";
import { getFavProfile } from "@/store/slice/common/common.slice";
import { getPrivateSearchData } from "@/store/slice/common/search.slice";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import SocialMediaLinks from "../../common/(act)/act-review/social-media-links";
import { useAppContext } from "@/context/token-expired/token.expired.context";
import { useRouter } from "next/navigation";
import DeleteContainer from "@/containers/delete/delete-container";
import classNames from "classnames";
import Paginate from "@/containers/paginate/paginate.container";

const ProfileCard = ({
  profiles,
  type,
  params,
  profileTypeselecthandler,
  selectedItem,
  setSelectedItem,
  profileType,
}) => {
  const t = useTranslations("profiles");
  const s = useTranslations("search");
  // const p = useTranslations("actReview.actReviewLocation");
  const a = useTranslations("act");
  const lang = useLocale();
  const { showSnackbar } = useSnackbar();
  const [open, setOpen] = React.useState("");
  const { token } = useSelector((state) => state.login);
  const { setIsTokenExpired } = useAppContext();
  const router = useRouter();
  const [selectedProfileData, setSelectedProfileData] = React.useState("");
  const dispatch = useDispatch();

  const profilesData = profiles?.content;

  const handleClickOpen = (profileData) => {
    setSelectedProfileData(profileData);
    setOpen("deletePopup");
  };

  const handleClose = (data) => {
    setOpen(data);
  };

  const clickHandler = (profileId) => {
    if (!token) {
      setIsTokenExpired(true);
      return;
    }

    router.push(`/${lang}/${profileId}/view`);
  };

  const handleAddFavourite = (profileId) => {
    if (!token) {
      setIsTokenExpired(true);
      return;
    }

    dispatch(addFavouriteByProfileID({ profileId }))
      .unwrap()
      .then((response) => {
        showSnackbar(response.message, "success");
        if (type === "search")
          dispatch(getPrivateSearchData({ filter: params, profileType: profileType }));
      })
      .catch((error) => {
        //setLoading(false)
        showSnackbar(error, "error");
      });
  };
  const handleRemoveFavourite = (profileId) => {
    dispatch(removeFavouriteByProfileID({ profileId }))
      .unwrap()
      .then((response) => {
        if (type === "fav") dispatch(getFavProfile());
        if (type === "search")
          dispatch(getPrivateSearchData({ filter: params, profileType: profileType }));
        showSnackbar(response.message, "success");
      })
      .catch((error) => {
        //setLoading(false)
        showSnackbar(error, "error");
      });
  };
  const profileLabels = {
    ACT_PROFILE: "Act-profile",
    VIRTUAL_ACT_PROFILE: "Virtual-Act-Profile",
    VENUE_PROFILE: "Venue-Profile",
    VIRTUAL_VENUE_PROFILE: "Virtual-Venue-Profile",
  };
  // if no data render null
  if (profilesData?.length === 0) {
    return null;
  }
  return (
    <>
      <Box className="!flex !flex-wrap !gap-5 !mt-4 !pb-28 lg:!pb-0 !px-4 lg:!px-0">
        {profilesData?.length > 0 ? (
          profilesData?.map((profile, index) => (
            <Box
              key={index}
              // className="!border !border-[--divider-color] !w-full md:!max-w-xs lg:!w-[330px] !mb-4 !rounded-[4px] !bg-[--footer-bg]"
              className={classNames(
                "border mb-4 !w-full md:!max-w-xs lg:!w-[330px] rounded-[4px] bg-[--footer-bg] cursor-pointer",
                {
                  "border-[--inprogress-color]": selectedItem === profile.profileId,
                  "border-[--divider-color]": selectedItem !== index,
                },
              )}
            >
              {(type === "profile" || type === "fav" || type === "select-profile") && (
                <Box className="!relative">
                  <CardCarousel
                    images={profile.profileImageUrls}
                    className="slider-class"
                    profileId={profile.profileId}
                    profileTypeselecthandler={profileTypeselecthandler}
                  />
                  <Typography className="!text-[--text-color] !text-sm CraftworkGroteskSemiBold !absolute !left-3 !top-3 !bg-[--profile-type] !rounded-[2px] !px-[6px] !py-[2px]">
                    {profileLabels[profile.profileType] || ""}
                  </Typography>
                </Box>
              )}
              {type === "search" && (
                <Box className="!relative">
                  <CardCarousel
                    images={profile.profileImageUrls}
                    className="slider-class lg:w-full w-[90vw]"
                    profileId={profile.profileId}
                  />
                  <Typography className="!text-[--text-color] !text-sm CraftworkGroteskSemiBold !absolute !left-3 !top-3 !bg-[--profile-type] !rounded-[2px] !px-[6px] !py-[2px]">
                    {profileLabels[profile.profileType] || ""}
                  </Typography>
                </Box>
              )}
              <Box className="!px-5 ">
                {(type === "search" || type === "fav") && (
                  <Box className="!pb-5">
                    <Box className="!flex !justify-between !items-center">
                      <Typography className="!text-[--text-color] !text-2xl font-craftWorkGX">
                        {profile?.profileName && profile?.profileName?.length > 12
                          ? profile.profileName.substring(0, 12) + "..."
                          : profile.profileName}
                      </Typography>
                      <Box className="!flex !gap-x-2">
                        {/* <IconButton>
                          <AddCalender className="!text-2xl" />
                        </IconButton> */}
                        {type === "fav" ||
                          (type === "search" && profile.favouriteSelected === true && (
                            <IconButton onClick={() => handleRemoveFavourite(profile.profileId)}>
                              <FilledHeart className="!text-2xl" />
                            </IconButton>
                          ))}

                        {type === "search" && profile.favouriteSelected === false && (
                          <IconButton onClick={() => handleAddFavourite(profile.profileId)}>
                            <LikeIcon className="!text-2xl" />
                          </IconButton>
                        )}
                      </Box>
                    </Box>
                    {/* {type === "search" && (
                      <>
                        <Typography
                          className="CraftworkGroteskMedium cursor-pointer underline text-[--text-color] text-sm"
                          onClick={() => {
                            if (!token) {
                              setIsTokenExpired(true);
                              return;
                            }
                            router.push(`/${lang}/${profile.profileId}/leave-a-feedback`);
                          }}
                        >
                          {t("profileCard.writeAReview")}
                        </Typography>
                      </>
                    )} */}
                    <Typography className="!text-[--text-color] !text-sm font-craftWorkRegular">
                      {profile?.profileDescription?.length > 32
                        ? profile?.profileDescription?.substring(0, 31) + "..."
                        : profile?.profileDescription}
                    </Typography>
                  </Box>
                )}
                {(type === "profile" || type === "select-profile") && (
                  <>
                    <Typography className="!text-[--text-color] !text-2xl font-craftWorkGX">
                      {profile.profileName.length > 18
                        ? profile.profileName.substring(0, 18) + "..."
                        : profile.profileName}
                    </Typography>
                  </>
                )}

                {(type === "profile" || type === "select-profile") && (
                  <Box className="!flex !flex-wrap !gap-3 !items-center !my-4">
                    <Rating value={profile.popularityStars} readOnly />
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                      {profile.popularityStars}
                    </Typography>
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                      {`(${profile.numberOfReviews} ${a("reviews")})`}
                    </Typography>
                  </Box>
                )}
                {(type === "search" || type === "fav") && (
                  <Box className="lg:!flex !gap-x-3 !pb-5">
                    <Box className="!border !w-full !p-[10px] !border-[--light-border-color] !rounded-[2px]">
                      <Typography className="!text-left !text-[--text-color] font-craftWorkRegular">
                        {profile?.numberOfFollowers} <br /> {a("followers")}
                      </Typography>
                    </Box>
                    {(profile.profileType === "ACT_PROFILE" ||
                      profile.profileType === "VIRTUAL_ACT_PROFILE") && (
                      <Box className="!border !w-full !p-[10px] !border-[--light-border-color] !rounded-[2px]">
                        <Typography className="!text-left !text-sm !text-[--text-color] !font-craftWorkRegular">
                          {a("average")} ${profile?.averagePayForGig}k/gig*
                        </Typography>
                      </Box>
                    )}
                    {(profile.profileType === "VENUE_PROFILE" ||
                      profile.profileType === "VIRTUAL_VENUE_PROFILE") && (
                      <Box className="!border !w-full !p-[10px] !border-[--light-border-color] !rounded-[2px]">
                        <Typography className="!text-left !text-sm !text-[--text-color] !font-craftWorkRegular">
                          {a("average")} ${profile?.averagePricePerBooking} /bookings*
                        </Typography>
                      </Box>
                    )}
                  </Box>
                )}

                <Box className="!flex !items-center !gap-x-2 pb-4">
                  <span className="!text-2xl">
                    <LocationSvg />
                  </span>
                  <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                    {generateLocationString(profile.locationDto).length > 32
                      ? generateLocationString(profile.locationDto).substring(0, 32) + "..."
                      : generateLocationString(profile.locationDto)}
                  </Typography>
                </Box>
                {/* {(type === "search" || type === "fav") && (
                  <Box className="!flex !gap-x-3 !py-3">
                    <CalenderIcon className="!text-2xl" />
                    <Typography className="!text-[--text-color] !text-sm  font-craftWorkRegular">
                      {p("nextEvent")}
                    </Typography>
                  </Box>
                )} */}
                {/* {(type === "search" || type === "fav") && (
                  <Box className="flex gap-x-2 pb-2">
                    <RespondClock className="text-lg" />
                    <Typography className="text-[--hide-color] text-sm font-craftWorkRegular">
                      {p("respondHour")}
                    </Typography>
                  </Box>
                )} */}
                {type === "profile" && (
                  <Box className="!flex !justify-between !py-4">
                    <Box>
                      <Typography className="!text-[--hide-color] !text-sm CraftworkGroteskRegular ">
                        {t("profileCard.startedAt")}
                      </Typography>
                      <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy">
                        {`$ ${profile.minimumPrice} / ${profile.minPriceChargingType}`}
                      </Typography>
                    </Box>
                    <Box className="!flex !gap-x-1 !items-center">
                      <SocialMediaLinks socials={profile?.socialMediaLinks} />
                    </Box>
                  </Box>
                )}
                <Box className="!flex !gap-1 !mb-4">
                  {type === "profile" && (
                    <>
                      {" "}
                      <Link href={`/${lang}/${profile.profileId}/edit-information`}>
                        <Button
                          className="!border-[2px] !px-2 !border-[--text-color] !rounded-[4px]"
                          sx={{
                            minWidth: 0,
                            border: 0,
                            "&.MuiButtonBase-root": {
                              color: "transparent !important",
                            },
                          }}
                          //onClick={() => alert(profileId)}
                        >
                          <EditSvg className="!text-2xl" />
                        </Button>
                      </Link>
                      <Button
                        className="!border-[2px] !px-2 !border-[--text-color] !rounded-[4px]"
                        type="button"
                        sx={{
                          minWidth: 0,
                          border: 0,
                          "&.MuiButtonBase-root": {
                            color: "transparent !important",
                          },
                        }}
                        onClick={() => handleClickOpen(profile)}
                      >
                        <DeleteIcon className="!text-2xl" />
                      </Button>
                    </>
                  )}
                  {(type === "search" || type === "fav") && (
                    <Button
                      className="!border-[2px] !px-2 !border-[--text-color] !rounded-[4px]"
                      sx={{
                        minWidth: 0,
                        border: 0,
                        "&.MuiButtonBase-root": {
                          color: "white !important",
                        },
                      }}
                    >
                      <Play className="text-2xl" />
                    </Button>
                  )}
                  {/* <Link href={`/${lang}/${profile.profileId}/act`}> */}
                  <Button
                    className="!bg-[--text-color] !w-full px-3 py-1 !gap-x-2"
                    sx={{
                      minWidth: 0,
                      padding: 0,
                      "&.MuiButtonBase-root": {
                        color: "white !important",
                      },
                    }}
                    onClick={() => {
                      !profileTypeselecthandler && clickHandler(profile.profileId);
                      if (profileTypeselecthandler) {
                        setSelectedItem(profile.profileId);
                        profileTypeselecthandler(profile);
                      }
                    }}
                  >
                    <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                      {profileTypeselecthandler
                        ? selectedItem === profile.profileId
                          ? "Selected"
                          : "Select Profile"
                        : "View Profile"}
                    </Typography>
                    <ArrowSouthEast alt="arrow" />
                  </Button>
                  {/* </Link> */}
                </Box>
              </Box>
            </Box>
          ))
        ) : (
          <Typography className="!text-[--text-color] !text-2xl CraftworkGroteskMedium">
            {type === "profile" ? t("noProfile") : s("noSearch")}
          </Typography>
        )}

        {open !== "" && (
          <DeleteContainer
            open={open}
            selectedProfileData={selectedProfileData}
            handleClose={handleClose}
            setOpen={setOpen}
            params={params}
          />
        )}
      </Box>
      <Box className="flex justify-center">
        {profiles?.totalPages > 1 && (
          <Paginate totalRecords={profiles?.totalElements} perPageRecord={profiles.size} />
        )}
      </Box>
    </>
  );
};

export default ProfileCard;
