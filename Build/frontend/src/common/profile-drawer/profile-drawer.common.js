"use client";
import { But<PERSON>, Dropdown } from "@/component";
import { Clear, SouthEast } from "@mui/icons-material";
import { Box, Drawer, Typography } from "@mui/material";
import React from "react";
import { useSelector } from "react-redux";

const ProfileDrawer = ({ open, handleClose, setSelectedProfile, selectedProfile }) => {
  const { supportedProfiles } = useSelector((state) => state.common);

  const profiles = supportedProfiles;

  const handleSelectProfile = (value) => {
    if (value === "Select Profile") {
      value = "";
    }
    if (value === "Venue") {
      value = "VENUE_PROFILE";
    }
    if (value === "Act") {
      value = "ACT_PROFILE";
    }
    setSelectedProfile(value);
  };
  return (
    <Drawer
      open={open}
      anchor="bottom"
      sx={{
        "& .MuiPaper-root": {
          height: "95%",
          backgroundColor: "var(--bg-color)",
        },
      }}
    >
      <Box className="px-4 pt-5">
        <Box className="flex justify-between">
          <Typography className="text-[--text-color] CraftworkGroteskHeavy text-2xl">
            Filter
          </Typography>
        </Box>
        <Box className="border border-[--text-color] rounded-[4px] mt-6">
          <Dropdown
            options={["Select Profile", ...profiles]}
            onSelect={handleSelectProfile}
            selectedValue={
              selectedProfile === "ACT_PROFILE"
                ? "Act"
                : selectedProfile === "VENUE_PROFILE"
                  ? "Venue"
                  : selectedProfile
            }
            title="Select Profile"
            className="!text-[--text-color] w-full"
          />
        </Box>
      </Box>
      <Box
        className="flex justify-between bg-[--bg-color] px-4 py-5 border-t border-t-[--divider-color] bottom-0
        left-0 right-0 fixed"
      >
        <Button className="flex gap-1 items-center !normal-case" onClick={handleClose}>
          <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy underline">
            Cancel
          </Typography>
          <Clear className="text-[--text-color] text-xl" />
        </Button>
        <Button
          sx={{
            border: 0,
            "&.MuiButtonBase-root": {
              color: "white !important",
            },
          }}
          onClick={handleClose}
          className="flex gap-1 items-center !bg-[--text-color] rounded-[4px] !normal-case"
        >
          <Typography className="text-[--bg-color] text-sm CraftworkGroteskHeavy ">Ok</Typography>
          <SouthEast className="text-[--bg-color]" />
        </Button>
      </Box>
    </Drawer>
  );
};

export default ProfileDrawer;
