"use client";
import { Box, Typography } from "@mui/material";
import React from "react";
import Link from "next/link";
import { Button } from "@/component";
import { useRouter } from "next/navigation";
import { removeLocalStorage } from "@/utils";
import { useDispatch, useSelector } from "react-redux";
import { resetPreviewData } from "@/store/slice/act/act.slice";
import { useLocale } from "next-intl";
import LogoComponent from "../logo-component/logo-component.common";

const ProfileNavbar = ({
  pageNumber,
  tag,
  className,
  children,
  buttonClassName,
  isSaveUnPublished,
}) => {
  const lang = useLocale();
  const dispatch = useDispatch();
  const router = useRouter();
  const { token } = useSelector((state) => state.login);

  const handleSveUnpublished = () => {
    removeLocalStorage("previewData");
    removeLocalStorage("profileId");
    dispatch(resetPreviewData());
    router.push(`/${lang}/profiles`);
  };
  const { currentBookingStatus } = useSelector((state) => state.booking);

  if (!pageNumber) {
    pageNumber = `${currentBookingStatus?.currentPageCount}/${currentBookingStatus?.totalPageCount}`;
  }

  // Check if children has actual content after trimming
  const hasValidChildren =
    React.Children.count(children) > 0 &&
    React.Children.toArray(children).some((child) =>
      typeof child === "string" ? child.trim() !== "" : child !== null,
    );

  return (
    <Box
      className={`${className} !flex !justify-between !bg-[--bg-color] fixed top-0 right-0  z-20 left-0 !items-center lg:!px-12 md:!px-12 !px-4 lg:!border-b lg:!border-b-[--divider-color] md:!border-b md:!border-b-[--divider-color]`}
    >
      <Box className="!flex items-center lg:!gap-x-3 !gap-x-1">
        <Link href={token ? `/${lang}/search?profileType=ACT_PROFILE` : `/${lang}`}>
          <LogoComponent />
        </Link>
        <Typography className="!text-[--text-color] !text-base CraftworkGroteskGX">
          {tag}
        </Typography>
      </Box>
      {hasValidChildren &&
        (isSaveUnPublished ? (
          <Box>{children}</Box>
        ) : (
          <Button
            sx={{
              border: 0,
              "&.MuiButtonBase-root": {
                color: "transparent !important",
              },
              "&.MuiButtonBase-root:hover": {
                backgroundColor: "transparent !important",
              },
            }}
            className={`!flex normal-case cursor-pointer !gap-x-1 ${buttonClassName}`}
            onClick={handleSveUnpublished}
          >
            {children}
          </Button>
        ))}
    </Box>
  );
};

export default ProfileNavbar;
