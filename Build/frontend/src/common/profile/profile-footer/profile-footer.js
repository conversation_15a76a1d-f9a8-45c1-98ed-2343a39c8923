"use client";
import { Box, Typography, CircularProgress } from "@mui/material";
import Link from "next/link";
import { East, West } from "@mui/icons-material";
import { <PERSON><PERSON> } from "@/component";
import classNames from "classnames";
import { useLocale, useTranslations } from "next-intl";
import { usePathname, useRouter } from "next/navigation";
import { redirectBookingButtonRoute } from "@/utils/reduxhelper";
import { useDispatch, useSelector } from "react-redux";
import { previewContract, setBookingData } from "@/store/slice/booking/booking.slice";
import { useEffect, useState } from "react";

const ProfileFooter = ({
  backurl,
  loading,
  disabled = false,
  buttonName,
  type = "submit",
  backurlType = "",
  footerType = "",
  children,
  className,
  //onSubmit,
}) => {
  const t = useTranslations("profileFooter");
  const lang = useLocale();
  const router = useRouter();
  const dispatch = useDispatch();
  const { currentBookingStatus } = useSelector((state) => state.booking);
  const routePath = usePathname();
  const [canGoBack, setCanGoBack] = useState(false);

  useEffect(() => {
    if (footerType === "booking1") {
      dispatch(
        setBookingData({
          ...currentBookingStatus,
          previousRoute: routePath.substring(routePath.lastIndexOf("/") + 1),
        }),
      );

      redirectBookingButtonRoute(router, lang);
      dispatch(previewContract(currentBookingStatus?.contractId));
    }

    if (footerType === "negotiate") {
      dispatch(
        setBookingData({
          ...currentBookingStatus,
          previousRoute: routePath.substring(routePath.lastIndexOf("/") + 1),
        }),
      );

      redirectBookingButtonRoute(router, lang);
      dispatch(previewContract(currentBookingStatus?.contractId));
    }
  }, [footerType]);

  useEffect(() => {
    const isContractDetails = currentBookingStatus?.previousRoute === "contract-details";

    if (window.history.length > 1 && !isContractDetails) {
      setCanGoBack(true);
    } else {
      setCanGoBack(false);
    }
  }, []);

  return (
    <>
      <Box
        className={`${className} !flex !justify-between !fixed !bottom-0 !h-[79px] lg:right-[50%] right-0 !bg-[--bg-color] !left-0 !border-t !border-t-[--divider-color] !z-20 !py-5 lg:!px-12 md:!px-6 !px-4`}
      >
        {!backurlType && (
          <Link href={backurl} className="!flex !items-center !gap-x-3">
            <West className="!text-[--text-color]" />
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
              {t("Back")}
            </Typography>
          </Link>
        )}

        {backurlType === "router-back" && canGoBack && (
          <Button
            onClick={() => {
              router.back();
            }}
            className="!normal-case"
          >
            <West className="!text-[--text-color]" />
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
              {t("Back")}
            </Typography>
          </Button>
        )}

        <Box className="flex gap-2">
          {children}
          <Button
            disabled={disabled}
            className={classNames("!px-4 !py-2", {
              "!bg-[--disabled-color]": disabled,
              "!bg-[--text-color]": !disabled,
            })}
            sx={{
              border: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
            onClick={() => {
              // if(type === "submit") {
              //   return;
              // }
              //onSubmit && onSubmit();
              if (footerType === "location") {
                //onLocationHandler();
              }

              if (footerType === "booking") {
                dispatch(previewContract(currentBookingStatus?.contractId));
                if (
                  (currentBookingStatus?.previousRoute === "select-profile" ||
                    currentBookingStatus?.previousRoute === "user-data") &&
                  currentBookingStatus?.contactType
                ) {
                  let contactType = "";
                  if (
                    currentBookingStatus?.otherProfileType === "VENUE_PROFILE" ||
                    currentBookingStatus?.otherProfileType === "VIRTUAL_VENUE_PROFILE"
                  ) {
                    if (currentBookingStatus?.profileId) {
                      contactType = "venueBookAct";
                    }
                  }

                  if (
                    currentBookingStatus?.otherProfileType === "ACT_PROFILE" ||
                    currentBookingStatus?.otherProfileType === "VIRTUAL_ACT_PROFILE"
                  ) {
                    if (currentBookingStatus?.profileId) {
                      contactType = "actBookVenue";
                    }
                  }

                  dispatch(
                    setBookingData({
                      ...currentBookingStatus,
                      contactType: contactType ? contactType : currentBookingStatus?.contactType,
                      previousRoute: routePath.substring(routePath.lastIndexOf("/") + 1),
                    }),
                  );
                } else {
                  dispatch(
                    setBookingData({
                      ...currentBookingStatus,
                      //contactType: contactType,
                      previousRoute: routePath.substring(routePath.lastIndexOf("/") + 1),
                    }),
                  );
                }
                // let contactType = "";
                // if (currentBookingStatus?.previousRoute === "user-data") {
                //   if (
                //     currentBookingStatus?.otherProfileType === "VENUE_PROFILE" ||
                //     currentBookingStatus?.otherProfileType === "VIRTUAL_VENUE_PROFILE"
                //   ) {
                //     contactType = currentBookingStatus?.profileId
                //       ? "venueBookAct"
                //       : "userBookVenue";
                //   } else {
                //     contactType = currentBookingStatus?.profileId ? "actBookVenue" : "userBookAct";
                //   }
                // }

                redirectBookingButtonRoute(router, lang);
              }
            }}
            type={type}
          >
            {loading ? (
              <CircularProgress size={24} className="!text-black" />
            ) : (
              <>
                <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                  {buttonName}
                </Typography>
                <East className="!text-[--bg-color] !ml-3" />
              </>
            )}
          </Button>
        </Box>
      </Box>
    </>
  );
};
export default ProfileFooter;
