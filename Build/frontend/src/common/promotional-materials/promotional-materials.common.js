import { Button, CommonImage } from "@/component";
import { Box, Typography } from "@mui/material";
import React from "react";
import MediaImage from "@/assets/png/MediaImage.png";

const PromotionalMaterialsDetail = () => {
  return (
    <Box>
      <Box className="flex justify-center">
        <Button className="text-[--bg-color] !bg-[--text-color] font-craftWorkHeavy py-2 normal-case px-4">
          Submit for Poster
        </Button>
      </Box>
      <Typography className="text-base text-[--text-color] font-craftWorkMedium py-8">
        Reference Id
      </Typography>

      <Box className="w-full rounded-[8px] border border-[--footer-bg]">
        <CommonImage
          src={MediaImage}
          alt="media-image"
          className="w-full rounded-[8px] object-cover lg:h-[260px] md:h-[260px] h-[180px]"
        />
      </Box>
      <Box className="flex justify-center pt-12">
        <Button className="text-[--bg-color] !bg-[--text-color] font-craftWorkHeavy py-2 normal-case px-4">
          Mannually Add Poster
        </Button>
      </Box>
    </Box>
  );
};

export default PromotionalMaterialsDetail;
