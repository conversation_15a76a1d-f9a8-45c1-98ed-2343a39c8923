"use client";
import { Box, Typography } from "@mui/material";
import React from "react";
import RequestVector from "@/assets/svg/RequestVector.svg";
import RequestVector2 from "@/assets/svg/RequestVector2.svg";
import WhoBooking from "@/assets/svg/WhoBooking.svg";
import RoleBooking from "@/assets/svg/RoleBooking.svg";
import CalenderSvg from "@/assets/svg/CalenderSvg.svg";
import ClockSvg from "@/assets/svg/ClockSvg.svg";
import LocationIcon from "@/assets/svg/LocationIconSvg.svg";
import IGigSvg from "@/assets/svg/IGigSvg.svg";
import { useTranslations } from "next-intl";
import { useSelector } from "react-redux";
import { formatTime, formatTimeToAMPM } from "@/utils";

const RequestSummary = ({ contractType }) => {
  const t = useTranslations("requestSummary");
  const p = useTranslations("CreateProfiles");
  const s = useTranslations("feedback");
  const a = useTranslations("actReview.actReviewLocation");
  const { previewContract } = useSelector((state) => state.booking);

  return (
    <Box className="!hidden relative !bg-[--inprogress-color] !min-h-screen lg:!block lg:basis-1/2 !px-14 !pt-20">
      <RequestVector2 className="absolute top-14 right-[10%] w-[59px] h-[92px]" />
      <Typography variant="h2" className="text-2xl text-[--bg-color] CraftworkGroteskMedium">
        {t("requestSummary")}
      </Typography>
      <Box className="grid grid-cols-5 space-x-3 py-5">
        <Box className="col-span-3 bg-[--image-bg] p-5 rounded-[16px] flex justify-between">
          {/* <Box className="flex gap-2 items-center">
            <WhoBooking className="text-2xl" />
            <Typography className="text-lg text-[--bg-color] CraftworkGroteskMedium">
              {t("who")}
            </Typography>
          </Box> */}
          <Box className="flex gap-2 items-center">
            {/* <CommonImage src={AvatarImage} alt="image" width={24} height={24} /> */}
            <WhoBooking className="text-2xl" />
            <Typography className="text-sm text-[--bg-color] CraftworkGroteskMedium">
              {previewContract?.purchaserInfo?.firstName} {previewContract?.purchaserInfo?.lastName}
            </Typography>
          </Box>
        </Box>
        <Box className="col-span-2 items-center bg-[--image-bg] p-5 rounded-[16px] flex justify-between">
          <Box className="flex gap-2 items-center">
            <RoleBooking className="text-2xl" />
            <Typography className="text-lg text-[--bg-color] CraftworkGroteskMedium">
              {previewContract?.otherPartyInfo
                ? `${previewContract?.otherPartyInfo?.firstName} ${previewContract?.otherPartyInfo?.lastName}`
                : t("who")}
            </Typography>
          </Box>
          {!previewContract?.otherPartyInfo && (
            <Typography className="text-sm text-[--bg-color] CraftworkGroteskMedium">
              {t("toDefine")}
            </Typography>
          )}
        </Box>
      </Box>
      <Box className="bg-[--image-bg] rounded-[16px] flex items-center justify-between p-5">
        <Box className="flex gap-2 items-center">
          <CalenderSvg className="text-2xl" />
          <Typography className="text-lg text-[--bg-color] CraftworkGroteskMedium">
            {previewContract?.scheduleTime?.startDate
              ? formatTime(previewContract?.scheduleTime?.startDate)
              : t("when")}
          </Typography>
        </Box>
        <Typography className="text-sm text-[--bg-color] CraftworkGroteskMedium">
          {previewContract?.scheduleTime?.startDate ? "" : t("toDefine")}
        </Typography>
      </Box>
      <Box className="grid grid-cols-6 space-x-3 py-5">
        {contractType !== "USERVENUE" && (
          <Box className="col-span-2 items-center bg-[--image-bg] p-5 rounded-[16px] flex justify-between">
            <Box className="flex gap-2 items-center">
              <ClockSvg className="text-2xl" />
              <Typography className="text-lg text-[--bg-color] CraftworkGroteskMedium">
                {previewContract?.goodsAndServices?.loadingTime
                  ? formatTimeToAMPM(previewContract?.goodsAndServices?.loadingTime)
                  : t("load")}
              </Typography>
            </Box>
            <Typography className="text-sm text-[--bg-color] CraftworkGroteskMedium">-</Typography>
          </Box>
        )}
        <Box className="col-span-2 items-center bg-[--image-bg] p-5 rounded-[16px] flex justify-between">
          <Box className="flex gap-2 items-center">
            <ClockSvg className="text-2xl" />
            <Typography className="text-lg text-[--bg-color] CraftworkGroteskMedium">
              {previewContract?.goodsAndServices?.startDate
                ? formatTimeToAMPM(previewContract?.goodsAndServices?.startDate)
                : "start"}
            </Typography>
          </Box>
          <Typography className="text-sm text-[--bg-color] CraftworkGroteskMedium">-</Typography>
        </Box>
        <Box className="col-span-2 items-center bg-[--image-bg] p-5 rounded-[16px] flex justify-between">
          <Typography className="text-lg text-[--bg-color] CraftworkGroteskMedium">
            {previewContract?.goodsAndServices?.durationInHours
              ? previewContract?.goodsAndServices?.durationInHours
              : t("duration")}
          </Typography>
          <Typography className="text-sm text-[--bg-color] CraftworkGroteskMedium">-</Typography>
        </Box>
      </Box>
      <Box className="bg-[--image-bg] rounded-[16px] flex items-center justify-between p-5">
        {previewContract?.otherParty ? (
          <>
            <Box className="flex gap-2 items-center">
              <LocationIcon className="text-2xl" />
              <Typography className="text-lg text-[--bg-color] CraftworkGroteskMedium">
                {previewContract?.otherParty === "ACT" ? p("Act") : p("Venue")}
              </Typography>
            </Box>
          </>
        ) : (
          <Typography className="text-sm text-[--bg-color] CraftworkGroteskMedium">
            {t("toDefine")}
          </Typography>
        )}
      </Box>
      <Box className="pt-12">
        <Typography variant="h2" className="text-2xl text-[--bg-color] CraftworkGroteskMedium">
          {t("contractDetails")}
        </Typography>
        <Box className="flex justify-between items-center">
          <Typography className="text-[--bg-color] text-sm CraftworkGroteskRegular">
            {t("keySummaryPoint")}
          </Typography>
          {/* <Typography className="text-[--bg-color] text-sm CraftworkGroteskHeavy">CA$</Typography> */}
        </Box>
      </Box>
      <Box className="grid grid-rows-12 grid-flow-col pt-5 pb-16">
        <Box className="row-span-7 bg-[--image-bg] rounded-[16px] p-5 flex flex-col justify-between">
          <Typography className="text-sm CraftworkGroteskRegular text-[--bg-color]">
            {previewContract?.goodsAndServices?.paymentType === "FLAT_RATE" && (
              <>
                <span className="text-2xl">
                  {previewContract?.goodsAndServices?.flatRateCurrency}{" "}
                  {previewContract?.goodsAndServices?.flatRateAmount}
                </span>{" "}
                / {previewContract?.goodsAndServices?.flatRatePercentage}
              </>
            )}

            {previewContract?.goodsAndServices?.paymentType === "EXPOSURE_GIG" && (
              <>
                <span className="text-2xl">
                  {previewContract?.goodsAndServices?.exposureGigCurrency}{" "}
                  {previewContract?.goodsAndServices?.exposureGigFee}
                </span>{" "}
              </>
            )}

            {previewContract?.goodsAndServices?.paymentType === "DOOR_GIG" && (
              <>
                <span className="text-2xl">
                  {"Entry fee: "} {previewContract?.goodsAndServices?.doorGigEntryFee}
                  {"Venue Capacity: "} {previewContract?.goodsAndServices?.venueCapacity}
                </span>{" "}
                The door will be manned by the: {previewContract?.goodsAndServices?.doorManagedBy}{" "}
                {previewContract?.goodsAndServices?.maximumPercentage}% of door with a guaranteed
                minimum of {previewContract?.goodsAndServices?.guaranteedMaximum}
              </>
            )}
          </Typography>
          {!previewContract?.goodsAndServices && (
            <Typography className="text-sm CraftworkGroteskRegular text-[--bg-color]">
              {t("toDefine")}
            </Typography>
          )}
        </Box>
        <Box className="row-span-5 mt-5 bg-[--image-bg] rounded-[16px] p-5 flex flex-col justify-between">
          <Typography className="text-sm CraftworkGroteskRegular text-[--bg-color]">
            {t("soundAndLightening")}
          </Typography>
          <Typography className="text-sm CraftworkGroteskRegular text-[--bg-color]">
            {t("toDefine")}
          </Typography>
        </Box>
        <Box className="row-span-5 bg-[--image-bg] rounded-[16px] p-5 ml-5 flex flex-col justify-between">
          <Typography className="text-sm CraftworkGroteskRegular text-[--bg-color]">
            {s("rate")}
          </Typography>
          <Typography className="text-sm CraftworkGroteskRegular text-[--bg-color]">
            <span className="text-2xl">$3k</span>/{a("event")}
          </Typography>
        </Box>
        <Box className="row-span-7 bg-[--image-bg] rounded-[16px] p-5 ml-5 mt-5 flex flex-col justify-between">
          <IGigSvg className="text-2xl" />
          <Typography className="text-sm CraftworkGroteskRegular text-[--bg-color]">
            {t("nominal")}
          </Typography>
        </Box>
        <Box className="row-span-12 bg-[--image-bg] rounded-[16px] p-5 ml-5 flex flex-col justify-between">
          <Typography className="text-sm CraftworkGroteskRegular text-[--bg-color]">
            {previewContract?.goodsAndServices?.message}
          </Typography>
        </Box>
      </Box>
      <RequestVector className="absolute bottom-0 right-[6%] w-[249px] h-[219px]" />
    </Box>
  );
};

export default RequestSummary;
