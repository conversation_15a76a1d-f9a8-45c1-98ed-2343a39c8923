"use client";
import { Box, Chip, Drawer, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import SavedSearchPopupIcon from "@/assets/svg/SavedSearchPopupIcon.svg";
import { useForm } from "react-hook-form";
import { Button, Dropdown } from "@/component";
import { useDispatch } from "react-redux";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Clear, SouthEast } from "@mui/icons-material";
import SavedSearchSuccessDrawer from "../saved-search-success-drawer/saved-search-success-drawer.common";

const schema = yup.object().shape({
  searchName: yup.string().required("Search name is required"),
  isNotify: yup.boolean(),
  isDynamicDate: yup.boolean(),
  selectedEvent: yup.string(),
  selectedDynamicDate: yup.string(),
});

const SavedSearchDrawer = ({ open, filter, handleClose }) => {
  const dispatch = useDispatch();
  const {
    handleSubmit,
    watch,
    setValue,
    // formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      searchName: "",
      isNotify: false,
      isDynamicDate: false,
      selectedEvent: "Weekly",
      selectedDynamicDate: "Push the dates forward weekly",
    },
  });
  const [savedSearchData, setSavedSearchData] = useState([]);
  const [openSuccess, setOpenSuccess] = useState(false);
  //eslint-disable-next-line
  const [isUpdate, setIsUpdate] = useState(false);

  const handleOpenSuccess = () => {
    setOpenSuccess(true);
  };

  const handlecloseSuccess = () => {
    setOpenSuccess(false);
  };

  useEffect(() => {
    const searchCriteria = [];

    if (filter.searchStrings && filter.searchStrings.length > 0) {
      searchCriteria.push(...filter.searchStrings);
    }

    if (filter.searchFilter.searchLocation) {
      searchCriteria.push("Location");
    }

    if (filter.searchFilter.searchDate) {
      searchCriteria.push("Date");
    }

    if (filter.searchFilter.entertainmentTypesList) {
      filter.searchFilter.entertainmentTypesList.forEach((et) => {
        searchCriteria.push(et.name);
      });
    }

    if (filter.searchFilter.actRating) {
      searchCriteria.push("Overall Rating");
    }

    if (filter.searchFilter.musicGenreList) {
      if (Array.isArray(filter.searchFilter.musicGenreList)) {
        filter.searchFilter.musicGenreList.forEach((mg) => {
          searchCriteria.push(mg.name);
        });
      } else {
        searchCriteria.push(filter.searchFilter.musicGenreList.name);
      }
    }
    const filteredSearchCriteria = searchCriteria.filter((value) => value);

    setSavedSearchData(filteredSearchCriteria);
  }, [filter]);

  const onSubmit = (data) => {
    const payload = {
      ...filter,
      searchName: data.searchName,
    };
    dispatch(saveSavedSearches(payload))
      .unwrap()
      .then(() => {
        handleClose();
        handleOpenSuccess();
      })
      .catch(() => {
        // Handle error if necessary
      });
  };

  const Notify = ["Weekly", "Month", "Daily"];
  const dynamicDate = [
    "Push the dates forward weekly",
    "Push the dates forward monthly",
    "Push the dates forward daily",
  ];

  return (
    <>
      <Drawer
        open={open}
        anchor="bottom"
        sx={{
          "& .MuiPaper-root": {
            height: "95%",
            backgroundColor: "var(--bg-color)",
          },
        }}
        className="lg:hidden md:hidden inline"
      >
        {isUpdate ? (
          <>
            <Box className="p-6">
              <SavedSearchPopupIcon className="min-w-12 min-h-12" />
              <Typography className="text-lg py-5 text-[--text-color] CraftworkGroteskMedium">
                Save this search
              </Typography>
              <Typography className="text-sm CraftworkGroteskRegular pb-2 text-[--hide-color]">
                Applied filters
              </Typography>
              <Box className="flex flex-wrap gap-2">
                {savedSearchData.map((data) => (
                  <Chip
                    key={data}
                    label={data}
                    sx={{
                      "&.MuiChip-root": {
                        backgroundColor: "var(--text-color)",
                      },
                    }}
                  />
                ))}
              </Box>
              {/* <Controller
                name="isDynamicDate"
                control={control}
                render={({ field }) => (
                  <Box className="flex items-center !mt-2">
                    <CheckBox
                      {...field}
                      className="!max-w-[24px]"
                      disabled={true}
                      sx={{ color: "#EFEFEF", marginRight: "5px" }}
                    />
                    <label className="cursor-pointer flex gap-x-2 items-center">
                      <Typography className="text-[--text-color] pl-2 text-sm CraftworkGroteskRegular">
                        Keep dynamic date filter
                      </Typography>
                    </label>
                  </Box>
                )}
              /> */}
              {watch("isDynamicDate") && (
                <>
                  <Typography className="text-sm text-[--text-color] ml-10 CraftworkGroteskRegular">
                    Keep the date filter rolling forward
                  </Typography>
                  <Box className="border border-[--text-color] rounded-[2px] w-full my-3">
                    <Dropdown
                      options={dynamicDate}
                      onSelect={(value) => setValue("selectedDynamicDate", value)}
                      selectedValue={watch("selectedDynamicDate")}
                      title="Weekly"
                      className="!text-[--text-color] w-full"
                    />
                  </Box>
                </>
              )}
              {/* <Controller
                name="isNotify"
                control={control}
                render={({ field }) => (
                  <Box className="flex items-center ">
                    <CheckBox
                      {...field}
                      className="!max-w-[24px]"
                      disabled={true}
                      sx={{ color: "#EFEFEF", marginRight: "5px" }}
                    />
                    <label className="cursor-pointer flex gap-x-2 items-center">
                      <Typography className="text-[--text-color] pl-2 text-sm CraftworkGroteskRegular">
                        Notify me about updates
                      </Typography>
                    </label>
                  </Box>
                )}
              /> */}
              {watch("isNotify") && (
                <>
                  <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                    You will receive emails when search results for this saved search updates.
                  </Typography>
                  <Box className="border border-[--text-color] rounded-[2px] w-full my-3">
                    <Dropdown
                      options={Notify}
                      onSelect={(value) => setValue("selectedEvent", value)}
                      selectedValue={watch("selectedEvent")}
                      title="Weekly"
                      className="!text-[--text-color] w-full"
                    />
                  </Box>
                </>
              )}
            </Box>
            <Box
              className="flex justify-between bg-[--bg-color] px-4 py-3 items-center border-t border-t-[--divider-color] bottom-0
        left-0 right-0 fixed"
            >
              <Button className="flex gap-1 items-center !normal-case" onClick={handleClose}>
                <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy underline">
                  Cancel
                </Typography>
                <Clear className="text-[--text-color] text-xl" />
              </Button>
              <Button
                className="!bg-[--text-color] w-5/12 h-[39px] !flex !gap-x-2 items-center"
                onClick={handleSubmit(onSubmit)}
                sx={{
                  border: 0,
                  "&.MuiButtonBase-root": {
                    color: "white !important",
                  },
                }}
              >
                <Typography className="!normal-case CraftworkGroteskHeavy text-center !text-sm !leading-[15.4px] !text-[--bg-color]">
                  View event
                </Typography>
                <SouthEast className="text-[--bg-color] text-xl" />
              </Button>
            </Box>
          </>
        ) : (
          <Box className="p-5">
            <Typography className="text-[--text-color] text-2xl CraftworkGroteskHeavy">
              The search &#39;Search&#39; already exists.{" "}
            </Typography>
            <Typography className="text-[--text-color] text-sm CraftworkGroteskRegular py-3">
              Do you want to replace the existing one or keep both?
            </Typography>
            <Box className="flex flex-wrap gap-2">
              {savedSearchData.map((data) => (
                <Chip
                  key={data}
                  label={data}
                  sx={{
                    "&.MuiChip-root": {
                      backgroundColor: "var(--text-color)",
                    },
                  }}
                />
              ))}
            </Box>
            <Box
              className="flex justify-between bg-[--bg-color] px-4 py-3 items-center border-t border-t-[--divider-color] bottom-0
        left-0 right-0 fixed"
            >
              <Button
                className=" !flex !gap-x-2 items-center"
                onClick={handleClose}
                sx={{
                  border: 0,
                  "&.MuiButtonBase-root": {
                    color: "white !important",
                  },
                  "&.MuiButtonBase-root:hover": {
                    backgroundColor: "transparent !important",
                  },
                }}
              >
                <Typography className="!normal-case CraftworkGroteskHeavy underline text-center !text-sm !leading-[15.4px] !text-[--text-color]">
                  Replace
                </Typography>
              </Button>
              <Button
                className="!bg-[--text-color] h-[39px] !flex !gap-x-2 items-center"
                onClick={handleClose}
                sx={{
                  border: 0,
                  "&.MuiButtonBase-root": {
                    color: "white !important",
                  },
                }}
              >
                <Typography className="!normal-case CraftworkGroteskHeavy text-center !text-sm !leading-[15.4px] !text-[--bg-color]">
                  Keep both
                </Typography>
                <SouthEast className="text-[--bg-color] text-xl" />
              </Button>
            </Box>
          </Box>
        )}
      </Drawer>
      <SavedSearchSuccessDrawer open={openSuccess} handleClose={handlecloseSuccess} />
    </>
  );
};

export default SavedSearchDrawer;
