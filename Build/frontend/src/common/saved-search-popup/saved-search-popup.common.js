"use client";
import React, { useEffect, useState } from "react";
import {
  Box,
  Chip,
  Dialog,
  DialogContent,
  IconButton,
  InputAdornment,
  TextField,
  Typography,
  Drawer,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import SavedSearchPopupIcon from "@/assets/svg/SavedSearchPopupIcon.svg";
import { Clear, SouthEast } from "@mui/icons-material";
import DocumentSvg from "@/assets/svg/Document.svg";
import { Button, Dropdown } from "@/component";
import SavedSearchSuccess from "../saved-search-success/saved-search-success.common";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useDispatch } from "react-redux";
import { saveSavedSearches } from "@/store/slice/common/search.slice";
import { removeQueryParams } from "@/utils/queryparams";
import { useRouter, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";

const schema = yup.object().shape({
  searchName: yup.string().required("Search name is required"),
  isNotify: yup.boolean(),
  isDynamicDate: yup.boolean(),
  selectedEvent: yup.string(),
  selectedDynamicDate: yup.string(),
});

const SavedSearchPopup = ({ open, handleClose, filter }) => {
  const t = useTranslations("savedSearch");
  const p = useTranslations("leaveFeedback");
  const s = useTranslations("editActCommon");
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      searchName: "",
      isNotify: false,
      isDynamicDate: false,
      selectedEvent: "Weekly",
      selectedDynamicDate: "Push the dates forward weekly",
    },
  });

  const [savedSearchData, setSavedSearchData] = useState([]);
  const [openSuccess, setOpenSuccess] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  useEffect(() => {
    const searchCriteria = [];
    if (filter?.searchType) {
      searchCriteria.push(filter?.searchType);
    }
    if (filter.searchStrings && filter.searchStrings.length > 0) {
      searchCriteria.push(...filter.searchStrings);
    }

    if (filter.searchName) {
      setIsUpdate(true);
    }

    if (filter?.searchFilter?.searchLocation) {
      const { cityName, stateName, countryName } = filter.searchFilter.searchLocation;
      if (cityName || stateName || countryName) {
        searchCriteria.push("Location");
      }
      // if (cityName) searchCriteria.push("City: " + cityName);
      // if (stateName) searchCriteria.push("Location: " + stateName);
      // if (countryName) searchCriteria.push("Location: " + countryName);
    }

    if (filter.searchFilter.searchDate) {
      searchCriteria.push("Date");
    }

    if (filter?.searchFilter?.searchLocation?.distance) {
      searchCriteria.push("Distance");
    }
    if (filter.searchFilter.entertainmentTypesList) {
      filter.searchFilter.entertainmentTypesList.forEach((et) => {
        searchCriteria.push(et.name);
      });
    }

    if (filter.searchFilter.actRating) {
      searchCriteria.push("Overall Rating");
    }

    if (filter.searchFilter.musicGenreList) {
      filter.searchFilter.musicGenreList.forEach((et) => {
        searchCriteria.push(et.name);
      });
    }

    const filteredSearchCriteria = searchCriteria.filter((value) => value);

    setSavedSearchData(filteredSearchCriteria);
  }, [filter]);

  const Notify = ["Weekly", "Month", "Daily"];
  const dynamicDate = [
    "Push the dates forward weekly",
    "Push the dates forward monthly",
    "Push the dates forward daily",
  ];

  const handleOpenSuccess = () => {
    setOpenSuccess(true);
  };

  const handlecloseSuccess = () => {
    setOpenSuccess(false);
  };

  const onSubmit = (data) => {
    const payload = {
      ...filter,
      searchName: data.searchName,
    };
    dispatch(saveSavedSearches(payload))
      .unwrap()
      .then(() => {
        handleClose();
        handleOpenSuccess();
      })
      .catch(() => {
        handleClose();
        handleOpenSuccess();
      });
  };

  const updateFilter = () => {
    dispatch(
      saveSavedSearches({
        ...filter,
      }),
    )
      .unwrap()
      .then(() => {
        handleClose();
        handleOpenSuccess();
      })
      .catch(() => {
        handleClose();
        handleOpenSuccess();
      });
  };

  const content = (
    <DialogContent className="!max-w-xl !bg-[--footer-bg] lg:!border-[1px] md:!border-[1px] !px-4 lg:flex md:flex  gap-3 !pt-4 md:!border-[--text-color] lg:!border-[--text-color]">
      {!isUpdate ? (
        <>
          <SavedSearchPopupIcon className="min-w-12 min-h-12" />
          <Box className="flex flex-col items-start">
            <Typography className="text-lg text-[--text-color] CraftworkGroteskMedium">
              {t("saveThisSearch")}
            </Typography>
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular w-10/12">
              {t("searchTerm")}
            </Typography>
            <Box className="w-full h-[53px] my-3 flex items-center border-[1px] !border-[--text-color] rounded-[2px] ">
              <Controller
                name="searchName"
                control={control}
                render={({ field }) => (
                  <>
                    <TextField
                      {...field}
                      type="text"
                      size="small"
                      placeholder={t("searchName")}
                      InputLabelProps={{ style: { color: "#EFEFEF" } }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start" style={{ cursor: "pointer" }}>
                            <DocumentSvg className="w-5 h-5" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        "& input::placeholder": {
                          color: "#EFEFEF",
                          border: 0,
                        },
                        "& input": {
                          color: "#EFEFEF",
                          fontFamily: "var(--craftWorkRegular)",
                        },
                        "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderWidth: 0,
                        },
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderWidth: 0,
                        },
                        border: 0,
                      }}
                      className="!w-full !py-1 CraftworkGroteskRegular"
                      error={!!errors.searchName}
                    />
                    {errors.searchName && (
                      <Typography className="text-red-500 text-sm mt-1">
                        {errors.searchName.message}
                      </Typography>
                    )}
                  </>
                )}
              />
            </Box>
            <Typography className="text-sm CraftworkGroteskRegular pb-2 text-[--hide-color]">
              {t("apliedFilter")}
            </Typography>
            <Box className="flex flex-wrap gap-2">
              {savedSearchData.map((data) => (
                <Chip
                  key={data}
                  label={data}
                  sx={{
                    "&.MuiChip-root": {
                      backgroundColor: "var(--text-color)",
                    },
                  }}
                />
              ))}
            </Box>
            {/* <Controller
              name="isDynamicDate"
              control={control}
              render={({ field }) => (
                <Box className="flex items-center !mt-2">
                  <CheckBox
                    {...field}
                    className="!max-w-[24px]"
                    disabled={true}
                    sx={{ color: "#EFEFEF", marginRight: "5px" }}
                  />
                  <label className="cursor-pointer flex gap-x-2 items-center">
                    <Typography className="text-[--text-color] pl-2 text-sm CraftworkGroteskRegular">
                      {t("dateFilter")}
                    </Typography>
                  </label>
                </Box>
              )}
            /> */}
            {watch("isDynamicDate") && (
              <>
                <Typography className="text-sm text-[--text-color] ml-10 CraftworkGroteskRegular">
                  {t("filterRolling")}
                </Typography>
                <Box className="border border-[--text-color] rounded-[2px] w-full my-3">
                  <Dropdown
                    options={dynamicDate}
                    onSelect={(value) => setValue("selectedDynamicDate", value)}
                    selectedValue={watch("selectedDynamicDate")}
                    title="Weekly"
                    className="!text-[--text-color] w-full"
                  />
                </Box>
              </>
            )}
            {/* <Controller
              name="isNotify"
              control={control}
              render={({ field }) => (
                <Box className="flex items-center ">
                  <CheckBox
                    {...field}
                    className="!max-w-[24px]"
                    disabled={true}
                    sx={{ color: "#EFEFEF", marginRight: "5px" }}
                  />
                  <label className="cursor-pointer flex gap-x-2 items-center">
                    <Typography className="text-[--text-color] pl-2 text-sm CraftworkGroteskRegular">
                      {t("notifyMeUpdate")}
                    </Typography>
                  </label>
                </Box>
              )}
            /> */}
            {watch("isNotify") && (
              <>
                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                  {t("receiveMails")}
                </Typography>
                <Box className="border border-[--text-color] rounded-[2px] w-full my-3">
                  <Dropdown
                    options={Notify}
                    onSelect={(value) => setValue("selectedEvent", value)}
                    selectedValue={watch("selectedEvent")}
                    title="Weekly"
                    className="!text-[--text-color] w-full"
                  />
                </Box>
              </>
            )}
            <Box
              className={` ${isMobile ? "fixed flex justify-between items-center px-4 py-3 left-0 right-0 bottom-0 border-[1px] border-[--divider-color]" : "gap-x-4"}`}
            >
              <Button
                className=" w-5/12 h-[39px] flex lg:hidden !gap-x-2 items-center"
                onClick={handleClose}
                sx={{
                  border: 0,
                  "&.MuiButtonBase-root": {
                    color: "white !important",
                  },
                }}
              >
                <Typography className="!normal-case CraftworkGroteskHeavy !underline text-center !text-sm !leading-[15.4px] !text-[--text-color]">
                  {p("cancel")}
                </Typography>
                <Clear className="text-[--text-color] text-xl" />
              </Button>
              <Button
                className="!bg-[--text-color] h-[39px] !flex !gap-x-2 items-center lg:!mt-5 md:!mt-5"
                onClick={handleSubmit(onSubmit)}
                sx={{
                  border: 0,
                  "&.MuiButtonBase-root": {
                    color: "white !important",
                  },
                }}
              >
                <Typography className="!normal-case CraftworkGroteskHeavy text-center !text-sm !leading-[15.4px] !text-[--bg-color]">
                  {s("save")}
                </Typography>
                <SouthEast className="text-[--bg-color] text-xl" />
              </Button>
            </Box>
          </Box>
          <IconButton
            onClick={handleClose}
            sx={{
              "&.MuiButtonBase-root": {
                alignItems: "start",
              },
              "&.MuiButtonBase-root:hover": {
                backgroundColor: "transparent !important",
              },
            }}
            className="hidden lg:flex md:flex"
          >
            <Clear className="text-lg text-[--text-color]" />
          </IconButton>
        </>
      ) : (
        <Box>
          <Typography className="text-[--text-color] text-2xl CraftworkGroteskHeavy w-9/12">
            {t("theSearch")} &#39;{filter.searchName}&#39; {t("alreadyExist")}
          </Typography>
          <Typography className="text-[--text-color] text-sm CraftworkGroteskRegular py-3">
            {t("replace")}
          </Typography>
          <Box className="flex flex-wrap gap-2">
            {savedSearchData.map((data) => (
              <Chip
                key={data}
                label={data}
                sx={{
                  "&.MuiChip-root": {
                    backgroundColor: "var(--text-color)",
                  },
                }}
              />
            ))}
          </Box>
          <Box
            className={` ${isMobile ? "fixed flex justify-between items-center px-4 py-3 left-0 right-0 bottom-0 border-[1px] border-[--divider-color]" : "flex justify-between"}`}
          >
            <Button
              className=" h-[39px] !flex !gap-x-2 items-center !mt-5"
              onClick={handleClose}
              sx={{
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
            >
              <Typography className="!normal-case CraftworkGroteskHeavy text-center !underline !text-sm !leading-[15.4px] !text-[--text-color]">
                {p("cancel")}
              </Typography>
              <Clear className="text-[--text-color] text-xl" />
            </Button>

            <Button
              className="!bg-[--text-color] w-4/12 h-[39px] !flex !gap-x-2 items-center !mt-5"
              onClick={updateFilter}
              sx={{
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
            >
              <Typography className="!normal-case CraftworkGroteskHeavy text-center !text-sm !leading-[15.4px] !text-[--bg-color]">
                {s("save")}
              </Typography>
              <SouthEast className="text-[--bg-color] text-xl" />
            </Button>
            <Button
              className="!bg-[--text-color] w-4/12 h-[39px] !flex !gap-x-2 items-center !mt-5"
              onClick={() => {
                setIsUpdate(false);
                removeQueryParams(["searchName"], router, searchParams);
              }}
              sx={{
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
            >
              <Typography className="!normal-case CraftworkGroteskHeavy text-center !text-sm !leading-[15.4px] !text-[--bg-color]">
                {t("saveAs")}
              </Typography>
              <SouthEast className="text-[--bg-color] text-xl" />
            </Button>
          </Box>
        </Box>
      )}
    </DialogContent>
  );

  return (
    <>
      {isMobile ? (
        <Drawer
          anchor="bottom"
          open={open}
          onClose={handleClose}
          sx={{
            "& .MuiPaper-root": {
              height: "95%",
              backgroundColor: "var(--bg-color)",
            },
          }}
        >
          {content}
        </Drawer>
      ) : (
        <Dialog
          open={open}
          maxWidth={false}
          sx={{ "& .MuiBackdrop-root": { backgroundColor: "#181B1BE5" } }}
        >
          {content}
        </Dialog>
      )}
      <SavedSearchSuccess open={openSuccess} handleClose={handlecloseSuccess} />
    </>
  );
};

export default SavedSearchPopup;
