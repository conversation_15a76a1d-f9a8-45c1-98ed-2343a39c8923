"use client";
import { Box, Drawer, Typography } from "@mui/material";
import React from "react";
import SavedSearchSuccessSvg from "@/assets/svg/VirtualAct.svg";
import Link from "next/link";
import { useLocale } from "next-intl";
import { Button } from "@/component";
import { Clear } from "@mui/icons-material";

const SavedSearchSuccessDrawer = ({ open, handleClose }) => {
  const lang = useLocale();
  return (
    <Drawer
      open={open}
      anchor="bottom"
      sx={{
        "& .MuiPaper-root": {
          height: "95%",
          backgroundColor: "var(--bg-color)",
        },
      }}
      className="lg:hidden md:hidden inline "
    >
      <Box className="px-5">
        <SavedSearchSuccessSvg className="text-5xl" />
        <Typography className="text-2xl CraftworkGroteskMedium text-[--text-color] my-3">
          Search have been saved successfully!
        </Typography>
        <Typography className="text-sm CraftworkGroteskRegular pb-4 text-[--text-color]">
          You can find this saved search on the{" "}
          <Link href={`/${lang}/favourites/saved-searches`} className="underline">
            Favorites page
          </Link>
          .
        </Typography>
      </Box>
      <Box
        className=" bg-[--bg-color] px-4 py-3 items-center border-t border-t-[--divider-color] bottom-0
        left-0 right-0 fixed"
      >
        <Button className="flex gap-1 items-center !normal-case" onClick={handleClose}>
          <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy underline">
            Cancel
          </Typography>
          <Clear className="text-[--text-color] text-xl" />
        </Button>
      </Box>
    </Drawer>
  );
};

export default SavedSearchSuccessDrawer;
