"use client";
import { Button, CommonImage, Dropdown } from "@/component";
import { Box, Chip, Divider, IconButton, Typography, useMediaQuery, useTheme } from "@mui/material";
import React, { useEffect, useState } from "react";
import DocumentSvg from "@/assets/svg/Document.svg";
import { SouthEast } from "@mui/icons-material";
import UserIcon from "@/assets/svg/OutlinedUser.svg";
import SortSvg from "@/assets/svg/SortSvg.svg";
// import DrawerUi from "@/ui/drawer/drawer-ui";
import Searchbar from "../searchbar/searchbar.common";
import SearchFilter from "../search-filter/search-filter.common";
import { useRouter, useSearchParams } from "next/navigation";
import { appendQueryParams, removeQueryParams } from "@/utils/queryparams";
import Link from "next/link";
import Notification from "@/assets/svg/Notification.svg";
import { useLocale, useTranslations } from "next-intl";
import NotificationPopup from "../notifications-popup/notifications-popup.common";
import { useSelector } from "react-redux";
import MapImage from "@/assets/png/MapImage.png";
import ProfilecardImage from "@/assets/png/ProfilecardImage.png";
import MapIcon from "@/assets/svg/MapIcon.svg";

const SearchHeader = ({ type, total, setOpenSaveSearch }) => {
  const t = useTranslations("searchHeader");
  const s = useTranslations("searchFilter");
  const theme = useTheme();
  const router = useRouter();
  const lang = useLocale();
  const searchParams = useSearchParams();
  const profileType = searchParams.get("profileType") || "";
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const chipLabel = [
    {
      name: t("event"),
      value: "EVENT_PROFILE",
    },
    {
      name: t("act"),
      value: "ACT_PROFILE",
    },
    {
      name: t("venue"),
      value: "VENUE_PROFILE",
    },
  ];
  const eventRate = [
    t("highestRateFirst"),
    t("cheapestRateFirst"),
    t("mostPopularFirst"),
    t("lessPopularFirst"),
  ];

  useEffect(() => {
    if (profileType === "") {
      setSelectedChip("ACT_PROFILE"); // Set default value if empty
    } else {
      setSelectedChip(profileType); // Update chip when URL param changes
    }
  }, [profileType]); // Only depend on profileType changes
  const [selectedEvent, setSelectedEvent] = useState(eventRate[0]);
  // eslint-disable-next-line
  // const [anchorEl, setAnchorEl] = React.useState(null);
  const [selectedChip, setSelectedChip] = useState(profileType || "ACT_PROFILE");
  const [open, setOpen] = React.useState(false);
  const [openPopup, setOpenPopup] = useState(false);
  const handleClosePopup = () => {
    setOpenPopup(false);
  };
  const handleOpenPopup = () => {
    setOpenPopup(true);
  };

  // const [filterOpen, setFilterOpen] = useState(false);

  const toggleSortDrawer = (newOpen) => {
    setOpen(newOpen);
  };
  const handleChipClick = (data) => {
    setSelectedChip(data.value);
    appendQueryParams({ profileType: data.value }, router, searchParams);
  };
  // const toggleFilterDrawer = (newOpen) => {
  //   setFilterOpen(newOpen);
  // };

  const handleSelectEvent = (value) => {
    setSelectedEvent(value);
  };
  const { instantMessage } = useSelector((state) => state.instantMessage);
  return (
    <>
      <Box className="!pl-[-120px] lg:hidden inline">
        <Box className="flex justify-between">
          <Typography className="text-[--text-color] text-2xl CraftworkGroteskMedium">
            {t("search")}
          </Typography>
          <Box className="flex gap-2 items-center">
            {/* <Link href={`/${lang}/profiles`}>
              <Button className="flex gap-1 !normal-case">
                <Typography className="underline text-[--text-color] text-sm CraftworkGroteskHeavy">
                  {t("instaBook")}
                </Typography>
                <SouthEast className="text-[--text-color] text-2xl" />
              </Button>
            </Link> */}
            <IconButton sx={{ padding: 0 }} onClick={handleOpenPopup} className="relative">
              <Notification className="!w-6 !h-6 !cursor-pointer" />
              {instantMessage.length > 0 && (
                <Typography className="text-[12px] text-[--bg-color] font-craftWorkHeavy bg-[--text-color] px-1 rounded-full absolute top-[-8px] right-[-5px]">
                  {instantMessage.length > 0 &&
                    instantMessage.filter(
                      (item) => item?.dismissed === false && item?.seen === false,
                    ).length}
                </Typography>
              )}
            </IconButton>
            <Link href={`/${lang}/user/account-information`}>
              <UserIcon className="!w-6 !h-6 !cursor-pointer" />{" "}
            </Link>
          </Box>
        </Box>
        <NotificationPopup open={openPopup} handleClose={handleClosePopup} />
        <Box className="flex gap-2 items-center my-2">
          <Searchbar placeholder="Search" />
          <IconButton onClick={() => toggleSortDrawer(true)}>
            <SortSvg className="text-2xl" />
          </IconButton>
          {/* <IconButton>
            <Filter className="text-2xl" />
          </IconButton> */}
        </Box>
        <Box className="flex gap-x-2 items-center">
          {chipLabel.map((data, index) => (
            <Chip
              key={index}
              label={data.name}
              //disabled={index === 0}
              onClick={() => handleChipClick(data)}
              sx={{
                "&.MuiChip-root": {
                  backgroundColor: selectedChip !== data.value ? "" : "var(--text-color)",
                  color: selectedChip !== data.value ? "var(--text-color)" : "var(--bg-color)",
                  border: selectedChip !== data.value ? "1px solid var(--divider-color)" : "",
                },
              }}
              className="cursor-pointer"
            />
          ))}
          <Box className="">
            {type === "MAP" ? (
              <Box
                className="relative cursor-pointer"
                onClick={() => {
                  removeQueryParams(["type"], router, searchParams);
                }}
              >
                <CommonImage src={ProfilecardImage} alt="profile-image" width={149} height={45} />

                <Box className="absolute top-[40%] bottom-0 right-0 left-[20%] mx-auto my-auto ">
                  <Box className="flex gap-1">
                    <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy">
                      PROFILE
                    </Typography>
                    <SouthEast className="text-[--text-color] text-xl" />
                  </Box>
                </Box>
              </Box>
            ) : (
              <Box
                className="relative cursor-pointer"
                onClick={() => {
                  appendQueryParams({ type: "MAP" }, router, searchParams);
                }}
              >
                <CommonImage src={MapImage} alt="profile-image" width={149} height={45} />
                <Box className="absolute top-[40%] bottom-0 right-0 left-[20%] mx-auto my-auto ">
                  <Box className="flex gap-1">
                    <MapIcon />
                    <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy">
                      MAP
                    </Typography>
                    <SouthEast className="text-[--text-color]" />
                  </Box>
                </Box>
              </Box>
            )}
          </Box>
        </Box>
      </Box>
      {/* <DrawerUi
        type="Search"
        open={open}
        setOpenSaveSearch={setOpenSaveSearch}
        handleClose={() => toggleSortDrawer(false)}
       /> */}
      {isMobile && (
        <SearchFilter
          open={open}
          handleClose={() => toggleSortDrawer(false)}
          setOpenSaveSearch={setOpenSaveSearch}
        />
      )}
      <Box className="h-[70px] hidden px-6 border-b border-b-[--divider-color] justify-between lg:flex items-center bg-[--bg-color]">
        <Box className="flex gap-x-2 items-center">
          {chipLabel.map((data, index) => (
            <Chip
              key={index}
              label={data.name}
              //disabled={index === 0}
              onClick={() => handleChipClick(data)}
              sx={{
                "&.MuiChip-root": {
                  backgroundColor: selectedChip !== data.value ? "" : "var(--text-color)",
                  color: selectedChip !== data.value ? "var(--text-color)" : "var(--bg-color)",
                  border: selectedChip !== data.value ? "1px solid var(--divider-color)" : "",
                },
              }}
              className="cursor-pointer"
            />
          ))}
          <Box className="pl-10">
            {type === "MAP" ? (
              <Box
                className="relative cursor-pointer"
                onClick={() => {
                  removeQueryParams(["type"], router, searchParams);
                }}
              >
                <CommonImage src={ProfilecardImage} alt="profile-image" width={149} height={79} />

                <Box className="absolute top-[40%] bottom-0 right-0 left-[8%] mx-auto my-auto ">
                  <Box className="flex gap-1 items-center">
                    <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy">
                      PROFILE VIEW
                    </Typography>
                    <SouthEast className="text-[--text-color] text-lg" />
                  </Box>
                </Box>
              </Box>
            ) : (
              <Box
                className="relative cursor-pointer"
                onClick={() => {
                  appendQueryParams({ type: "MAP" }, router, searchParams);
                }}
              >
                <CommonImage src={MapImage} alt="profile-image" width={149} height={79} />
                <Box className="absolute top-[40%] bottom-0 right-0 left-[10%] mx-auto my-auto ">
                  <Box className="flex gap-1 items-center">
                    <MapIcon />
                    <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy">
                      MAP VIEW
                    </Typography>
                    <SouthEast className="text-[--text-color] text-lg" />
                  </Box>
                </Box>
              </Box>
            )}
          </Box>
        </Box>
        <Box className="flex gap-x-2">
          <Dropdown
            options={eventRate}
            onSelect={handleSelectEvent}
            selectedValue={selectedEvent}
            title="Events in progress"
            disabled={true}
            className="!text-[--hide-color] "
          />
          <Divider sx={{ borderLeft: "thin solid rgba(76, 78, 79, 0.5)" }} className="h-8" />
          <Typography className="text-[--text-color] text-sm Sora400 text-center pt-2">
            {total} {t("results")}
          </Typography>
          <Button
            className="flex gap-x-2"
            sx={{
              border: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
            onClick={() => setOpenSaveSearch()}
          >
            <DocumentSvg className="text-2xl" />
            <Typography className="text-sm CraftworkGroteskHeavy text-[--text-color] underline normal-case">
              {s("saveSearch")}
            </Typography>
          </Button>
          {/* <Link href={`/${lang}/create-booking`}>
            <Button
              className="flex gap-x-1 px-6 !bg-[--text-color]"
              sx={{
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
            >
              <SouthEast className="text-[--bg-color]" />
            </Button>
          </Link> */}
        </Box>
      </Box>
    </>
  );
};

export default SearchHeader;
