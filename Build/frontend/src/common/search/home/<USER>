"use client";
import { Box, Divider, InputAdornment, TextField, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import UserIcon from "@/assets/svg/UserIcon.svg";
import CalenderIcon from "@/assets/svg/CalenderIcon.svg";
import Button from "@/component/button/button.component";
import SearchIcon from "@/assets/svg/SearchIcon.svg";
import AutoCompleteLocation from "@/component/autocomplete/autocomplete-location.component";
import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { useRouter } from "next/navigation";
import { useHotkeys } from "react-hotkeys-hook";
import { stringifyParams } from "@/utils";
import { useDispatch, useSelector } from "react-redux";
import {
  setDefaultLocation,
  getDefaultLocation,
  deleteDefaultLocation,
} from "@/store/slice/common/search.slice";
import { useTranslations } from "next-intl";
import { Dropdown } from "@/component";

const HomeSearch = ({
  showLocationInput = true,
  showDatePicker = true,
  showSearchInput = true,
  showProfileTypeDropdown = true,
}) => {
  const options = ["Act", "Venue", "Events"];
  const t = useTranslations("searchHeader");
  const s = useTranslations("navbar");
  const [locationValue, setLocationValue] = React.useState("");
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date(new Date().setDate(new Date().getDate() + 14)));
  //const [search, setSearch] = useState("");
  const [selectOptions, setSelectOptions] = useState(options[0]);
  const router = useRouter();
  const dispatch = useDispatch();
  const { token } = useSelector((state) => state.login);
  const onChange = (dates) => {
    const [start, end] = dates;
    setStartDate(start);
    setEndDate(end);
  };

  const handleSelectOption = (value) => {
    setSelectOptions(value);
  };

  useEffect(() => {
    dispatch(getDefaultLocation())
      .unwrap()
      .then((data) => {
        if (data?.data) {
          if (data.data.cityName && data.data.countryName && data.data.stateName) {
            setLocationValue({
              city: data.data.cityName || "",
              country: data.data.countryName || "",
              state: data.data.stateName || "",
            });
          }
        }
      });
  }, []);

  const onSearchHadnler = () => {
    // router.push(
    //   `/search?searchStrings=${search}&${stringifyParams(locationValue)}&startDate=${
    //     startDate.toISOString().split("T")[0]
    //   }&endDate=${endDate.toISOString().split("T")[0]}&searchDateType=CUSTOM&profileType=${
    //     selectOptions === "Venue"
    //       ? "VENUE_PROFILE"
    //       : selectOptions === "Act"
    //         ? "ACT_PROFILE"
    //         : selectOptions === "Event"
    //           ? "EVENT_PROFILE"
    //           : ""
    //   }`,
    // );

    router.push(`/search?${stringifyParams(locationValue)}&profileType=ACT_PROFILE`);
  };

  useHotkeys("enter", (event) => {
    event.preventDefault();
    onSearchHadnler();
  });

  useEffect(() => {
    if (token && locationValue?.city && locationValue?.country && locationValue?.state) {
      dispatch(
        setDefaultLocation({
          cityName: locationValue.city,
          countryName: locationValue.country,
          stateName: locationValue.state,
        }),
      );
    }
  }, [locationValue]);

  const setCloseLocationValue = () => {
    dispatch(deleteDefaultLocation())
      .unwrap()
      .then(() => {
        setLocationValue("");
      });
  };

  return (
    <Box className="lg:flex gap-x-1 lg:border lg:border-[--divider-color] lg:items-center lg:rounded-full mt-12 max-w-4xl !bg-[--bg-color]">
      {showLocationInput && (
        <AutoCompleteLocation
          value={locationValue}
          setValue={setLocationValue}
          setClose={setCloseLocationValue}
          className=" lg:border-none border border-[--text-color] lg:rounded-s-full rounded-full bg-[--bg-color]"
          // className="!w-full lg:border-none border border-[--text-color] lg:rounded-s-full rounded-full bg-[--bg-color]"
          PopperProps={{
            placement: "bottom-start", // This ensures dropdown appears below the component
            modifiers: [
              {
                name: "flip",
                enabled: false, // Prevents flipping to top if there's not enough space below
              },
              {
                name: "preventOverflow",
                enabled: true,
                options: {
                  boundary: "viewport",
                },
              },
            ],
          }}
        />
      )}

      {showLocationInput && (showDatePicker || showSearchInput || showProfileTypeDropdown) && (
        <Divider
          orientation="vertical"
          sx={{ border: "thin solid rgba(76, 78, 79, 0.5)" }}
          className="!h-8 hidden lg:inline"
        />
      )}

      {showDatePicker && (
        <Box className="lg:border-none border border-[--text-color] rounded-full my-3 lg:my-0">
          <ReactDatePicker
            selected={startDate}
            onChange={onChange}
            startDate={startDate}
            endDate={endDate}
            selectsRange
            popperPlacement="bottom" // Forces popper to appear below
            minDate={new Date()}
            placeholderText="Any Date"
            showIcon
            icon={<CalenderIcon className="text-2xl lg:mt-2 mt-2" />}
            className=" lg:w-[201px] ml-8 border react-date-picker"
          />
        </Box>
      )}

      {showDatePicker && (showSearchInput || showProfileTypeDropdown) && (
        <Divider
          orientation="vertical"
          sx={{ border: "thin solid rgba(76, 78, 79, 0.5)" }}
          className="!ml-1 !h-8 hidden lg:inline"
        />
      )}

      {showSearchInput && (
        <Box className="w-full h-[56px] flex items-center border border-[--text-color] lg:rounded-none rounded-full lg:border-none !bg-[--bg-color] ">
          <TextField
            type="text"
            size="small"
            placeholder={s("looking")}
            // onChange={(event) => setSearch(event.target.value)}
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start" style={{ cursor: "pointer" }}>
                  <UserIcon className="w-5 h-5" />
                </InputAdornment>
              ),
            }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& input": {
                color: "#EFEFEF",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            className="!w-full !py-1 CraftworkGroteskRegular"
          />
        </Box>
      )}

      {showSearchInput && showProfileTypeDropdown && (
        <Divider
          orientation="vertical"
          sx={{ border: "thin solid rgba(76, 78, 79, 0.5)" }}
          className="!ml-1 !h-8 hidden lg:inline"
        />
      )}

      {showProfileTypeDropdown && (
        <Box className="lg:mt-0 mt-2 lg:w-6/12 w-full lg:border-none flex items-center border border-[--text-color] h-[56px] lg:rounded-none rounded-full">
          <Dropdown
            title="Event"
            options={options}
            onSelect={handleSelectOption}
            selectedValue={selectOptions}
            className="!text-[--text-color] w-full"
          />
        </Box>
      )}

      <Button
        onClick={onSearchHadnler}
        sx={{ minWidth: 0 }}
        className=" lg:!rounded-full !mt-2 lg:!mt-0 lg:w-36 lg:h-12 w-full !bg-[--text-color] !normal-case lg:mr-1"
      >
        <Typography className="text-[--bg-color] lg:hidden inline text-sm CraftworkGroteskHeavy ">
          {t("search")}
        </Typography>
        <SearchIcon className="text-2xl" />
      </Button>
    </Box>
  );
};

export default HomeSearch;
