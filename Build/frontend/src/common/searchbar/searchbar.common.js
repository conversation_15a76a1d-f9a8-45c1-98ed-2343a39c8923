import { Button } from "@/component";
import { getSavedSearches } from "@/store/slice/common/search.slice";
import { Clear } from "@mui/icons-material";
import { Box, Divider, InputAdornment, TextField } from "@mui/material";
import { useLocale } from "next-intl";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import SearchIcon from "@/assets/svg/SearchIcon.svg";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { appendQueryParams, removeQueryParams } from "@/utils/queryparams";
import { useHotkeys } from "react-hotkeys-hook";
import DocumentSvg from "@/assets/svg/Document.svg";
import { filterToURL } from "@/utils";

const Searchbar = ({ placeholder }) => {
  const lang = useLocale();
  const router = useRouter();
  const dispatch = useDispatch();
  const [anchorEl, setAnchorEl] = React.useState(null);
  const searchParams = useSearchParams();
  const [search, setSearch] = useState(searchParams.get("searchStrings") || "");

  useHotkeys("enter", (event) => {
    event.preventDefault();
    onSearchHadnler();
  });
  const open = Boolean(anchorEl);
  const handleClick = () => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = (data) => {
    filterToURL(router, lang, data);
    setAnchorEl(null);
  };
  const onSearchHadnler = () => {
    //router.push(`/${lang}/search?searchName=${search}`);
    appendQueryParams({ searchStrings: search }, router, searchParams, "search");
  };

  const { savedSearches } = useSelector((state) => state.search);
  useEffect(() => {
    open && dispatch(getSavedSearches());
  }, [open]);

  return (
    <>
      <Box className="!flex !border !h-[52px] !bg-[--footer-bg] w-full my-4 !items-center !border-[--divider-color] !rounded-full">
        <TextField
          size="small"
          value={search}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              e.preventDefault();
              onSearchHadnler();
            }
          }}
          placeholder={placeholder}
          className="Sora500 !text-[--text-color] !w-full"
          InputProps={{
            endAdornment: (
              <InputAdornment position="end" style={{ cursor: "pointer" }}>
                {!search && (
                  <div className="relative">
                    <Button onClick={handleClick}>
                      <DocumentSvg className="w-6 h-6" />
                      <KeyboardArrowDownIcon className="mt-1 !text-[--text-color]" />
                    </Button>
                    {open && (
                      <>
                        <div className="absolute rounded-sm py-1.5 top-12 right-0 z-[55] text-[#efefef] border border-[#4c4e4f80] bg-[#202222]">
                          {savedSearches.map((data) => (
                            <div
                              key={data.id}
                              onClick={() => handleClose(data)}
                              className="CraftworkGroteskMedium py-1 px-5"
                            >
                              {data.searchName}
                            </div>
                          ))}
                        </div>
                        <div
                          className="fixed inset-0 bg-transparent z-50"
                          onClick={() => handleClose("")}
                        />
                      </>
                    )}
                  </div>
                )}
                {search && (
                  <Clear
                    className="!text-[--text-color] !text-base"
                    onClick={() => {
                      setSearch("");
                      removeQueryParams(["searchName"], router, searchParams);
                    }}
                  />
                )}
              </InputAdornment>
            ),
          }}
          InputLabelProps={{ style: { color: "#EFEFEF" } }}
          sx={{
            "& input::placeholder": {
              color: "#EFEFEF",
              border: 0,
            },
            "& input": {
              color: "#EFEFEF",
              fontFamily: "var(--craftWorkRegular)",
            },
            "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            "& .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            "& .MuiInputBase-root": {
              paddingRight: 0,
            },
            border: 0,
          }}
          onChange={(event) => {
            setSearch(event.target.value);
          }}
        />
        <Divider sx={{ borderRight: "thin solid rgba(76, 78, 79, 0.5)" }} className="!h-6 !mr-2" />

        <Button
          className="!bg-[--text-color] !rounded-full !w-10 !h-10 !mx-1"
          sx={{ minWidth: 0 }}
          onClick={() => {
            onSearchHadnler();
            //router.push(`/${lang}/search?text=${search}`);
          }}
        >
          <SearchIcon className="!w-6 !h-6" />
        </Button>
      </Box>
    </>
  );
};

export default Searchbar;
