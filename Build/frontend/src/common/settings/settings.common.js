"use client";

import { KeyboardArrowRight } from "@mui/icons-material";
import { Box, Divider, Typography } from "@mui/material";
import { useLocale, useTranslations } from "next-intl";
import Link from "next/link";
import React from "react";

const SettingInfo = () => {
  const t = useTranslations("settings.settingsInfo");
  const lang = useLocale();
  // const notified = [
  //   {
  //     id: 0,
  //     text: t("notifyByEmail"),
  //     isChecked: true,
  //   },
  //   {
  //     id: 1,
  //     text: t("notifyBySms"),
  //     isChecked: false,
  //   },
  //   {
  //     id: 2,
  //     text: t("appNotifications"),
  //     isChecked: false,
  //   },
  // ];

  // const sendNotifications = [
  //   {
  //     id: 0,
  //     text: t("allowNotifications"),
  //     isChecked: true,
  //   },
  //   {
  //     id: 1,
  //     text: t("newBooking"),
  //     isChecked: true,
  //   },
  //   {
  //     id: 2,
  //     text: t("newMessages"),
  //     isChecked: true,
  //   },
  //   {
  //     id: 3,
  //     text: t("newFollowers"),
  //     isChecked: true,
  //   },
  //   {
  //     id: 4,
  //     text: t("upcomingEvents"),
  //     isChecked: true,
  //   },
  //   {
  //     id: 5,
  //     text: t("waitingBooking"),
  //     isChecked: true,
  //   },
  // ];

  return (
    <Box className="mb-8">
      {/* <Box className="mt-10">
        <Typography className="text-[--text-color] text-lg CraftworkGroteskMedium">
          {t("notifications")}
        </Typography>
        <Typography className="text-[--text-color] pb-4 text-sm CraftworkGroteskRegular mt-2">
          {t("howNotify")}
        </Typography>
        {notified.map((data) => (
          <>
            <Box key={data.id} className="flex justify-between items-center">
              <Typography className="text-[--text-color] text-sm CraftworkGroteskRegular">
                {data.text}
              </Typography>
              <Switch disabled checked={data.isChecked} />
            </Box>
            <Divider sx={{ borderBottom: "thin solid rgba(76, 78, 79, 0.5)" }} className="mb-1" />
          </>
        ))}
      </Box> */}
      {/* <Box className="mt-12">
        <Typography className="text-[--text-color] text-lg CraftworkGroteskMedium">
          {t("pushNotifications")}
        </Typography>
        <Typography className="text-[--text-color] pb-5 text-sm CraftworkGroteskRegular mt-2">
          {t("deviceSettings")}
        </Typography>
        {sendNotifications.map((data) => (
          <Box key={data.id}>
            <Box
              className={` flex justify-between items-center 
                ${data.id === 0 ? "pb-4" : "pb-0"}
              `}>
              <Typography className="text-[--text-color] text-sm CraftworkGroteskRegular">
                {data.text}
              </Typography>
              <Switch disabled checked={data.isChecked} />
            </Box>
            <Divider sx={{ borderBottom: "thin solid rgba(76, 78, 79, 0.5)" }} className="mb-1" />
          </Box>
        ))}
      </Box> */}
      <Box className="mt-12">
        <Typography className="text-[--text-color] text-lg CraftworkGroteskMedium">
          {t("privacy")}
        </Typography>
        <Link href={`/${lang}/change-password`}>
          <Box className="flex justify-between items-center py-2">
            <Typography className="text-[--text-color] text-sm CraftworkGroteskRegular">
              {t("changePassword")}
            </Typography>
            <KeyboardArrowRight className="text-[--text-color] " />
          </Box>
        </Link>
        <Divider sx={{ borderBottom: "thin solid rgba(76, 78, 79, 0.5)" }} className="mb-1" />
      </Box>
      {/* <Box className="mt-12">
        <Typography className="text-[--text-color] text-lg CraftworkGroteskMedium">
          {t("account")}
        </Typography>
        <Typography className="text-[--text-color] pb-5 text-sm CraftworkGroteskRegular">
          {t("deleteUserAccount")}
        </Typography>
        <Button sx={{ padding: 0 }} className="!normal-case flex gap-2">
          <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy underline">
            {t("deleteAccount")}
          </Typography>
          <Clear className="text-[--text-color] text-xl" />
        </Button>
      </Box> */}
    </Box>
  );
};

export default SettingInfo;
