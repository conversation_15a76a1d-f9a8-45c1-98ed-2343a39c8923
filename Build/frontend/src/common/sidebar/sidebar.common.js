"use client";
import React, { useEffect } from "react";
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Tooltip,
  Typography,
} from "@mui/material";
import DoubleArrowRight from "@/assets/svg/DoubleArrowRight.svg";
import DoubleArrowLeft from "@/assets/svg/DoubleArrowLeft.svg";
import Dashboard from "@/assets/svg/Dashboard.svg";
import CalenderIcon from "@/assets/svg/CalenderIcon.svg";
import Contracts from "@/assets/svg/Contracts.svg";
import Profiles from "@/assets/svg/Profiles.svg";
import Logout from "@/assets/svg/Logout.svg";
import { Button } from "@/component";
import { cn } from "@/lib/cn";
import { useAppContext } from "@/context/app/app.context";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import { logout } from "@/store/slice/auth/login.auth.slice";
import { usePathname } from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
import SearchIcon from "@/assets/svg/OutlinedSearch.svg";

const Sidebar = () => {
  const t = useTranslations("sidebar");
  const s = useTranslations("contracts.contractByMe");
  const lang = useLocale();
  const { showSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const router = useRouter();

  const handleLogout = () => {
    dispatch(logout())
      .unwrap()
      .then(() => {
        showSnackbar("Logout successfully", "success");
        router.replace(`/${lang}`);
      })
      .catch((error) => {
        showSnackbar(error, "error");
      });
  };

  const Menu = [
    {
      id: 0,
      text: "Search",
      icon: <SearchIcon className="!w-6 !h-6" />,
      path: `/${lang}/search?profileType=ACT_PROFILE`,
      isDisabled: false,
    },
    // {
    //   id: 1,
    //   text: t("eventCalendar"),
    //   icon: <CalenderIcon className="!w-6 !h-6" />,
    //   path: "#",
    //   isDisables: true,
    // },
    {
      id: 2,
      text: t("dashboard"),
      icon: <Dashboard className="!w-6 !h-6" />,
      path: `/${lang}/dashboard`,
      isDisables: false,
    },
    {
      id: 3,
      text: t("profiles"),
      icon: <Profiles className="!w-6 !h-6" />,
      path: `/${lang}/profiles`,
      isDisables: false,
    },
    {
      id: 4,
      text: s("contracts"),
      icon: <Contracts className="!w-6 !h-6" />,
      path: `/${lang}/contracts/contracts-by-me`,
      isBadge: true,
      isDisables: false,
    },
    // {
    //   id: 5,
    //   text: t("messages"),
    //   icon: <Messages className="!w-6 !h-6" />,
    //   path: "#",
    //   isDisables: true,
    // },
    {
      id: 6,
      text: t("calendar"),
      icon: <CalenderIcon className="!w-6 !h-6" />,
      path: `/${lang}/events`,
      isDisables: false,
    },
  ];

  const pathname = usePathname();
  const { isOpened, setIsOpened } = useAppContext();

  const toggleMenu = () => {
    setIsOpened((prev) => !prev);
  };

  useEffect(() => {
    setIsOpened(false);
  }, []);

  const handleNavigation = (path) => {
    if (!path || path === "#") return;
    router.push(path);
  };

  return (
    <Box
      className={cn(
        "drawer !w-[100px] !border-r !border-r-[--divider-color] !h-screen transition-all drawer !p-0 !fixed !z-50 !top-[72px] !bottom-0 !bg-[--bg-color]",
        {
          "w-0": !isOpened,
          "!w-[300px]": isOpened,
        },
      )}
    >
      {!isOpened ? (
        <Box className="!flex !flex-col !items-center !gap-y-4 !py-8">
          <Button
            onClick={toggleMenu}
            sx={{
              "&.MuiButtonBase-root": { backgroundColor: "tranparent !important" },
              "&:hover": { backgroundColor: "transparent !important" },
            }}
          >
            <DoubleArrowRight />
          </Button>
          <List sx={{ padding: 0 }}>
            {Menu.map((menu) => (
              <Tooltip key={menu.id} title={menu.text} placement="right">
                <ListItem sx={{ "&.MuiListItem-root": { paddingLeft: 0, paddingRight: 0 } }}>
                  <ListItemButton
                    disabled={menu.isDisables}
                    sx={{ padding: 0 }}
                    onClick={() => handleNavigation(menu.path)}
                    className={`hover:!bg-[--footer-bg] ${pathname === menu.path ? "bg-[--footer-bg] rounded-[4px] !py-4" : ""}`}
                  >
                    <ListItemIcon className="!flex !flex-col !items-center !py-2 !gap-y-4">
                      {menu.icon}
                      {/* {menu.isBadge && (
                        <Badge
                          variant="dot"
                          sx={{
                            "& .MuiBadge-dot": {
                              minWidth: "5px",
                              height: "5px",
                              backgroundColor: "#E07F54",
                            },
                            position: "absolute",
                            top: "8px",
                            right: "16px",
                          }}
                        />
                      )} */}
                    </ListItemIcon>
                  </ListItemButton>
                </ListItem>
              </Tooltip>
            ))}
          </List>
          <Box className="!absolute !bottom-20">
            <Tooltip title="Log Out" placement="right">
              <span>
                <Logout className="!w-6 !h-6 cursor-pointer mb-2 ml-1" onClick={handleLogout} />
              </span>
            </Tooltip>
            {/* <Tooltip title="Help" placement="right">
              <span>
                <Help className="!w-6 !h-6" />
              </span>
            </Tooltip> */}
          </Box>
        </Box>
      ) : (
        <Box className="!flex !flex-col !gap-y-4 !pl-10 !py-7">
          <Button
            onClick={toggleMenu}
            className="!flex !justify-start !gap-x-6 !normal-case"
            sx={{
              minWidth: 0,
              Padding: 0,
              "&.MuiButtonBase-root": { color: "tranparent !important" },
              "&:hover": { backgroundColor: "transparent !important" },
            }}
          >
            <DoubleArrowLeft className="!w-6 !h-6" />
            <Typography className="!text-sm !text-[--hide-color]">{t("hide")}</Typography>
          </Button>
          <List sx={{ padding: 0 }}>
            {Menu.map((menu) => (
              <ListItem key={menu.id} className="!pl-0 !py-2">
                <ListItemButton
                  disabled={menu.isDisables}
                  sx={{ padding: 0 }}
                  onClick={() => handleNavigation(menu.path)}
                  className={`${pathname === menu.path ? " !bg-[--footer-bg] !rounded-[4px]  !py-4 !px-1" : ""}`}
                >
                  <ListItemIcon>{menu.icon}</ListItemIcon>
                  <ListItemText
                    className="!text-[--text-color] !leading-[15.4px]"
                    primary={menu.text}
                    sx={{
                      "& .MuiTypography-root": {
                        fontFamily: "var(--craftWorkMedium)",
                        fontSize: "14px",
                      },
                    }}
                  />
                  {/* {menu.isBadge && (
                    <Typography className="!text-[--bg-color] !bg-[--text-color] !p-2 !text-sm CraftworkGroteskMedium !leading-[15.4px]">
                      4
                    </Typography>
                  )} */}
                </ListItemButton>
              </ListItem>
            ))}
          </List>
          <Box className="!absolute !flex flex-col !gap-x-6 !bottom-20">
            <Box className="!flex !gap-x-6 mb-2 cursor-pointer" onClick={handleLogout}>
              <Logout className="!w-6 !h-6" />
              <Typography className="!text-[--text-color] !text-sm CraftworkGroteskMedium !text-center">
                {t("logOut")}
              </Typography>
            </Box>
            {/* <Box className="!flex !gap-x-6">
              <Help className="!w-6 !h-6" />
              <Typography className="!text-[--text-color] !text-sm CraftworkGroteskMedium !text-center">
                {t("help")}
              </Typography>
            </Box> */}
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default Sidebar;
