"use client";
import React, { forwardRef, useImperativeHandle } from "react";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { Alert, Snackbar } from "@mui/material";

import { setSnackbarHandlers } from "@/utils/snackbar.utils";

const snackbarTypes = {
  success: "#4CAF50",
  error: "#f44336",
  info: "#2196F3",
  warning: "#ff9800",
};

const SnackbarMessage = (props, ref) => {
  const {
    showSnackbar,
    hideSnackbar,
    snackbarOpen,
    snackbarSeverity,
    snackbarMessage,
    snackbarDuration,
  } = useSnackbar(); // Use the useSnackbar hook
  // Expose methods for showing and hiding the snackbar
  useImperativeHandle(ref, () => ({
    showSnackbar: (message, severity = "info", duration) => {
      showSnackbar(message, severity, duration);
    },
    hideSnackbar: () => {
      hideSnackbar();
    },
  }));

  // Set handlers in the utility module
  setSnackbarHandlers({
    showSnackbar,
    hideSnackbar,
  });

  return (
    <Snackbar
      ref={ref}
      open={snackbarOpen}
      autoHideDuration={snackbarDuration}
      onClose={hideSnackbar}
      anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
    >
      <Alert
        onClose={hideSnackbar}
        severity={snackbarSeverity}
        className="Sora500 !text-base alertIcon !text-[--text-color]"
        sx={{
          backgroundColor: snackbarTypes[snackbarSeverity] || "#ccc",
        }}
      >
        {String(snackbarMessage)}
      </Alert>
    </Snackbar>
  );
};

export default forwardRef(SnackbarMessage);
