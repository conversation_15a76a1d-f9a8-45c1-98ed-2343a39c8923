import { Box, Typography } from "@mui/material";
import React from "react";
import CartSvg from "@/assets/svg/Cart.svg";
import OutlinedUser from "@/assets/svg/OutlinedUser.svg";

const MobileViewTitle = ({ title }) => {
  return (
    <Box className="!flex !justify-between !m-4 lg:!hidden">
      <Typography className="!text-[--text-color] !text-2xl CraftworkGroteskMedium">
        {title}
      </Typography>
      <Box className="!flex !gap-x-3">
        <CartSvg className="!text-2xl !cursor-pointer" />
        <OutlinedUser className="!text-2xl !cursor-pointer" />
      </Box>
    </Box>
  );
};

export default MobileViewTitle;
