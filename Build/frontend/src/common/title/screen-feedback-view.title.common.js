"use client";
import React, { useEffect } from "react";
import { Box, Typography } from "@mui/material";
import Link from "next/link";

const ScreenFeedbackViewTitle = ({ links, primaryText, setFeedbackType, feedbackType }) => {
  useEffect(() => {
    const active = links?.find((link) => link.text === "Received");
    if (active) {
      setFeedbackType(active.text);
    }
  }, [setFeedbackType]);

  const handleClick = (link) => {
    setFeedbackType(link.text);
  };

  return (
    <Box className="flex justify-start items-center !pl-4 gap-x-6">
      <Typography className="text-[--text-color] lg:inline hidden text-2xl CraftworkGroteskMedium">
        {primaryText}
      </Typography>
      {links?.map((link) => (
        <Link key={link.id} href={link.path}>
          <Typography
            onClick={() => handleClick(link)}
            className={`text-[--text-color] text-sm py-[10px] border-b-[--text-color] ${
              feedbackType === link.text
                ? "CraftworkGroteskHeavy border-b-[2px]"
                : "CraftworkGroteskRegular border-b-[1px]"
            }`}
          >
            {link.text}
          </Typography>
        </Link>
      ))}
    </Box>
  );
};

export default ScreenFeedbackViewTitle;
