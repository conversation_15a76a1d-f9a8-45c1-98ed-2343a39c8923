"use client";
import React, { useState, useEffect } from "react";
import { Box, Typography } from "@mui/material";
import Link from "next/link";
import { usePathname } from "next/navigation";

const ScreenViewTitle = ({ links, primaryText }) => {
  const currentPath = usePathname();
  const [activeLink, setActiveLink] = useState("");

  useEffect(() => {
    setActiveLink(currentPath);
  }, [currentPath]);

  const handleClick = (path) => {
    setActiveLink(path);
  };

  return (
    <Box className="flex justify-start items-center !pl-4 gap-x-6">
      <Typography className="text-[--text-color] lg:inline hidden text-2xl CraftworkGroteskMedium">
        {primaryText}
      </Typography>
      {links.map((link) => (
        <Link key={link.id} href={link.path}>
          <Typography
            onClick={() => handleClick(link.path)}
            className={`text-[--text-color] text-sm py-[10px] border-b-[--text-color] ${
              activeLink === link.path
                ? "CraftworkGroteskHeavy border-b-[2px]"
                : "CraftworkGroteskRegular border-b-[1px]"
            }`}
          >
            {link.text}
          </Typography>
        </Link>
      ))}
    </Box>
  );
};

export default ScreenViewTitle;
