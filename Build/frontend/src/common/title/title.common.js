"use client";
import { Box, Typography } from "@mui/material";
import React from "react";
import ArrowRight from "@/assets/svg/ArrowRight.svg";
import { useTranslations } from "next-intl";

const Title = ({ titleName }) => {
  const t = useTranslations("title");
  return (
    <Box className="!flex !justify-between !pt-16">
      <Typography className="!text-[--text-color] CraftworkGroteskMedium !text-2xl">
        {titleName}
      </Typography>
      <Box className="!flex !gap-2 cursor-pointer">
        <Typography className="!text-[--text-color] CraftworkGroteskRegular !underline !text-sm ">
          {t("seeAll")}
        </Typography>
        <ArrowRight />
      </Box>
    </Box>
  );
};

export default Title;
