"use client";
import { Button, CommonImage } from "@/component";
import { Box, Dialog, DialogContent, Typography } from "@mui/material";
import React from "react";
import ActImage from "@/assets/png/ActImage.png";
import { Clear, SouthEast } from "@mui/icons-material";
import { useTranslations } from "next-intl";
import { useDispatch, useSelector } from "react-redux";
import { blockUser, unblockUser } from "@/store/slice/act/act.slice";

const UnblockDialog = ({ open, handleClose, type, profileId, actData, onSuccess }) => {
  // type is used to differentiate between block and unblock dialog
  const dispatch = useDispatch();
  const { loading } = useSelector((state) => state.act);
  const t = useTranslations("blockList");
  //const s = useTranslations("actReview.actReviewLocation");
  const p = useTranslations("leaveFeedback");

  const handleAction = async () => {
    if (!profileId) return;

    try {
      const action = type === "block" ? blockUser : unblockUser;
      await dispatch(action(profileId)).unwrap();
      await onSuccess(profileId);
      handleClose();
    } catch (error) {
      //eslint-disable-next-line no-console
      console.error(`Failed to ${type} user:`, error);
    }
  };

  return (
    <Dialog open={open} maxWidth={false}>
      <DialogContent className="!max-w-lg !bg-[--footer-bg] !border-[1px] lg:!px-10 !py-7 !border-[--text-color]">
        <Typography className="text-2xl text-[--text-color] CraftworkGroteskHeavy">
          {type === "block"
            ? t("unblockDialog.blockAct", { name: actData?.name })
            : t("unblockDialog.unblockAct", { name: actData?.name })}
        </Typography>
        <Box className="bg-[--footer-bg] my-5 border border-[--divider-color] px-4 py-3 rounded-[2px]">
          <Box className="flex gap-x-3">
            <CommonImage
              src={actData?.image || ActImage}
              alt="act-image"
              className="w-10 h-10 rounded-full object-cover"
              width={64}
              height={64}
            />
            <Box>
              <Typography className="text-sm text-[--text-color] font-craftWorkHeavy">
                {actData?.name || "[Act name]"}
              </Typography>
              {/* <Box className="flex gap-x-1">
                <Typography className="text-[--text-color] text-sm CraftworkGroteskRegular">
                  {actData?.reviewCount || 285} {s("reviews")}
                </Typography>
                <Rating value={actData?.rating || 4.5} />
              </Box> */}
            </Box>
          </Box>
        </Box>
        <Box className="flex gap-6">
          <Button
            type="button"
            disabled={loading}
            sx={{
              border: 0,
              "&.MuiButtonBase-root": {
                color: "transparent !important",
              },
            }}
            className="flex items-center !normal-case"
            onClick={handleClose}
          >
            <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy underline">
              {p("cancel")}
            </Typography>
            <Clear className="text-lg text-[--text-color]" />
          </Button>
          <Button
            type="button"
            disabled={loading}
            sx={{
              border: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
            className="flex gap-2 items-center w-full !normal-case !bg-[--text-color]"
            onClick={handleAction}
          >
            <Typography className="text-sm text-[--bg-color] CraftworkGroteskHeavy">
              {type === "block" ? t("block") : t("unblock")}
            </Typography>
            <SouthEast className="text-[--bg-color] text-2xl" />
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default UnblockDialog;
