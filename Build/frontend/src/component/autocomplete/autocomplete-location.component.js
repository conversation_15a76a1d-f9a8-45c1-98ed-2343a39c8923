import TextField from "@mui/material/TextField";
import Autocomplete from "@mui/material/Autocomplete";
import { Grid, InputAdornment, Typography, Box } from "@mui/material";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import CloseIcon from "@mui/icons-material/Close"; // Import close icon
import { getLocationSearchData } from "@/store/slice/common/search.slice";
import { useDebounce } from "use-debounce";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import LocationSvg from "@/assets/svg/Location.svg";
import { useTranslations } from "next-intl";
import Popper from "@mui/material/Popper";

const AutoCompleteLocation = ({
  value,
  setValue,
  className,
  setClose,
  textFieldClass,
  showFilter = false,
  readOnly = false,
}) => {
  const dispatch = useDispatch();
  const t = useTranslations("autoLocation");
  const [locationData, setLocationData] = useState([]);

  const [inputValue, setInputValue] = useState("");
  const [options, setOptions] = useState([]);
  const [debounceSearch] = useDebounce(inputValue, 1000);

  useEffect(() => {
    if (inputValue) {
      dispatch(getLocationSearchData(inputValue))
        .unwrap()
        .then((response) => {
          let data = response.data;
          if (showFilter) {
            data = data.filter((location) => location.city && location.state && location.country);
          }
          setLocationData(data);
        })
        .catch((error) => {
          return error;
        });
    }
  }, [debounceSearch, showFilter, dispatch]);

  // Function to handle clear
  const handleClear = () => {
    if (setValue) {
      setValue(null);
    }
    setClose && setClose();
  };

  return (
    <Box position="relative">
      <Autocomplete
        id="google-map-demo"
        sx={{
          width: 300,
          "& .MuiAutocomplete-popupIndicator": {
            display: "none",
          },
          "& .MuiAutocomplete-clearIndicator": {
            color: "var(--text-color) !important",
            display: readOnly ? "none" : "flex", // Hide default clear button in readonly mode
          },
        }}
        PopperComponent={(props) => (
          <Popper
            {...props}
            placement="bottom-start"
            modifiers={[
              {
                name: "flip",
                enabled: false, // Disable position flipping
              },
              {
                name: "preventOverflow",
                enabled: true,
                options: {
                  altBoundary: true,
                },
              },
            ]}
            style={{
              minWidth: "300px", // Match Autocomplete width
              maxWidth: "300px", // Match Autocomplete width
              zIndex: 9999, // Ensure it's above other elements
            }}
          />
        )}
        getOptionLabel={(option) => {
          return typeof option === "string"
            ? option
            : [option.city, option.state, option.country]
                .filter((part) => part && part.toLowerCase() !== "null")
                .join(", ");
        }}
        filterOptions={(x) => x}
        options={locationData}
        autoComplete
        includeInputInList
        filterSelectedOptions
        value={value}
        readOnly={readOnly}
        noOptionsText={value ? "No location found" : "Type to search location"}
        onChange={(event, newValue, reason) => {
          setOptions(newValue ? [newValue, ...options] : options);
          if (reason === "clear") {
            handleClear();
          } else if (setValue) {
            setValue(newValue);
          }
        }}
        onInputChange={(event, newInputValue) => {
          setInputValue(newInputValue);
        }}
        renderInput={(params) => (
          <TextField
            {...params}
            placeholder={t("addLocation")}
            fullWidth
            InputProps={{
              ...params.InputProps,
              readOnly: readOnly,
              startAdornment: (
                <InputAdornment position="start">
                  <LocationSvg className="text-2xl" />
                </InputAdornment>
              ),
            }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& input": {
                color: "#EFEFEF",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            className={`${textFieldClass} w-full`}
          />
        )}
        className={className}
        renderOption={(props, option) => {
          return (
            <li {...props} key={`${option.city}, ${option.state}, ${option.country}`}>
              <Grid container alignItems="center">
                <Grid item sx={{ display: "flex", width: 44 }}>
                  <LocationOnIcon sx={{ color: "text.secondary" }} />
                </Grid>
                <Grid item sx={{ width: "calc(100% - 44px)", wordWrap: "break-word" }}>
                  <Typography variant="body2" color="text.secondary">
                    {option.city && `${option.city}, `}
                    {option.state && `${option.state}, `}
                    {option.country}
                  </Typography>
                </Grid>
              </Grid>
            </li>
          );
        }}
      />

      {/* Custom clear button that only shows in readonly mode when there's a value */}
      {readOnly && value && (
        <Box
          sx={{
            position: "absolute",
            right: "8px",
            top: "50%",
            transform: "translateY(-50%)",
            zIndex: 10,
            cursor: "pointer",
            color: "var(--text-color)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "24px",
            width: "24px",
          }}
          onClick={handleClear}
        >
          <CloseIcon fontSize="small" />
        </Box>
      )}
    </Box>
  );
};

export default AutoCompleteLocation;
