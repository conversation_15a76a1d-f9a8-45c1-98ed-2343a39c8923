import { Button, Typo<PERSON> } from "@mui/material";
import { Add } from "@mui/icons-material";
import React from "react";
import { useLocale, useTranslations } from "next-intl";
import { useDispatch } from "react-redux";
import { createContracts, setBookingData } from "@/store/slice/booking/booking.slice";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { redirectBookingButtonRoute } from "@/utils/reduxhelper";
import { useRouter } from "next/navigation";

const BookingButtonComponent = ({ profileId, profileType, className }) => {
  const p = useTranslations("actReview.actReviewLocation");
  const dispatch = useDispatch();
  const [loading, setLoading] = React.useState(false);
  const { showSnackbar } = useSnackbar();
  const lang = useLocale();
  const router = useRouter();
  const bookingButtonHandler = () => {
    setLoading(true);
    let contactType = "";
    if (profileType === "VENUE_PROFILE" || profileType === "VIRTUAL_VENUE_PROFILE") {
      contactType = "userBookVenue";
    } else {
      contactType = "userBookAct";
    }

    dispatch(
      createContracts({
        venueProfileId:
          profileType === "VENUE_PROFILE" || profileType === "VIRTUAL_VENUE_PROFILE"
            ? profileId
            : "",
        actProfileId:
          profileType === "ACT_PROFILE" || profileType === "VIRTUAL_ACT_PROFILE" ? profileId : "",
      }),
    )
      .unwrap()
      .then((response) => {
        setLoading(false);
        if (response?.data?.data) {
          dispatch(
            setBookingData({
              contractId: response?.data?.data,
              contactType: contactType,
              previousRoute: "add-booking",
              profileId: "",
              profileType: "",
              otherProfileId: profileId,
              otherProfileType: profileType,
              currentPageCount: 1,
              totalPageCount: 7,
            }),
          );
        } else {
          dispatch(
            setBookingData({
              contractId: response?.data?.data,
              contactType: contactType,
              previousRoute: "add-booking",
              profileId: "",
              profileType: "",
              otherProfileId: profileId,
              otherProfileType: profileType,
              currentPageCount: 1,
              totalPageCount: 7,
            }),
          );
        }
        redirectBookingButtonRoute(router, lang);
      })
      .catch((error) => {
        setLoading(false);
        showSnackbar(error, "error");
      });

    // dispatch(checkProfileOwner(profileId))
    //   .unwrap()
    //   .then((response) => {
    //     setLoading(false);
    //     if (response?.data?.status === "success") {
    //       if (profileType === "VENUE_PROFILE" || profileType === "VIRTUAL_VENUE_PROFILE") {
    //         contactType = "userBookVenue";
    //       } else {
    //          contactType = "userBookAct";
    //       }

    //       dispatch(
    //         createContracts({
    //           venueProfileId:
    //             profileType === "VENUE_PROFILE" || profileType === "VIRTUAL_VENUE_PROFILE"
    //               ? profileId
    //               : "",
    //           actProfileId:
    //             profileType === "ACT_PROFILE" || profileType === "VIRTUAL_ACT_PROFILE"
    //               ? profileId
    //               : "",
    //         }),
    //       )
    //         .unwrap()
    //         .then((response) => {
    //           setLoading(false);
    //           if (response?.data?.data) {
    //             dispatch(
    //               setBookingData({
    //                 contractId: response?.data?.data,
    //                 contactType: contactType,
    //                 previousRoute: "add-booking",
    //                 profileId: "",
    //                 profileType: "",
    //                 otherProfileId: profileId,
    //                 otherProfileType: profileType,
    //                 currentPageCount: 1,
    //                 totalPageCount: 7,
    //               }),
    //             );
    //           } else {
    //             dispatch(
    //               setBookingData({
    //                 contractId: response?.data?.data,
    //                 contactType: contactType,
    //                 previousRoute: "add-booking",
    //                 profileId: "",
    //                 profileType: "",
    //                 otherProfileId: profileId,
    //                 otherProfileType: profileType,
    //                 currentPageCount: 1,
    //                 totalPageCount: 7,
    //               }),
    //             );
    //           }
    //           redirectBookingButtonRoute(router, lang);
    //         })
    //         .catch((error) => {
    //           setLoading(false);
    //           showSnackbar(error, "error");
    //         });
    //     } else {
    //       showSnackbar(response?.data?.message, "error");
    //     }
    //   })
    //   .catch(() => {
    //     setLoading(false);
    //     showSnackbar("Something went wrong", "error");
    //   });
  };

  return (
    <Button
      className={`${className} !bg-[--text-color] !normal-case rounded-[4px] flex gap-2`}
      sx={{
        minWidth: 0,
      }}
      disabled={loading}
      onClick={bookingButtonHandler}
    >
      <Typography className="!text-sm CraftworkGroteskHeavy text-[--bg-color]">
        {p("bookNow")}
      </Typography>
      <Add className="text-[--bg-color] text-xl" />
    </Button>
  );
};

export default BookingButtonComponent;
