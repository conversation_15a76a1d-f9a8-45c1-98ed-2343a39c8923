"use client";
import React, { useEffect } from "react";
// import Modal from "../Modal";
import { Box, Dialog, DialogContent, IconButton, Typography } from "@mui/material";
import { MobileDateTimePicker } from "@mui/x-date-pickers";
import { Clear, SouthEast } from "@mui/icons-material";
import { Dropdown } from "../form";
import Button from "../button/button.component";
import timezones from "./timezones";
import { Controller } from "react-hook-form";
import { showSnackbar } from "@/utils/snackbar.utils";

//import { scheduleValidation } from "@/validation/act/act.validation";
import {
  createSchedules,
  getActByProfileId,
  updateContractSchedules,
  deleteSchedule,
  deleteContractSchedules,
  updateSchedules,
} from "@/store/slice/act/act.slice";
import { useDispatch, useSelector } from "react-redux";
import dayjs from "dayjs";
import classNames from "classnames";
import { useLocale, useTranslations } from "next-intl";
import { filterEventData } from "@/utils";
import { previewContract } from "@/store/slice/booking/booking.slice";
import { useRouter } from "next/navigation";
const EditEvent = ({
  profileId,
  setFetch,
  open,
  handleClose,
  control,
  setValue,
  handleSubmit,
  watch,
  contractId,
  type,
  setEvents,
  setSelectable,
  errors,
  setSelectedItem,
}) => {
  const t = useTranslations("availabilityList");

  //const selectedDate = [t("singleDate"), t("recurringDate")];
  const recurrenceTypeData = [t("weekly"), t("biWeekly"), t("monthly")];
  const { currentBookingStatus } = useSelector((state) => state.booking);
  const router = useRouter();
  const lang = useLocale();
  const days = [
    {
      day: "Mo",
      name: "MONDAY",
    },
    {
      day: "Tu",
      name: "TUESDAY",
    },
    {
      day: "We",
      name: "WEDNESDAY",
    },
    {
      day: "Th",
      name: "THURSDAY",
    },
    {
      day: "Fr",
      name: "FRIDAY",
    },
    {
      day: "Sa",
      name: "SATURDAY",
    },
    {
      day: "Su",
      name: "SUNDAY",
    },
  ];

  // const [eventColor, setEventColor] = React.useState(colorCodes[0]);
  // eslint-disable-next-line

  //const resolver = yupResolver(scheduleValidation);
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(getActByProfileId(profileId));
  }, []);
  const { currentProfile } = useSelector((state) => state.act);
  // useEffect(() => {
  //   if (defaultValues) {
  //     const { eventTitle, timezone, eventType, fromDate, toDate } = defaultValues;
  //     setEventTitle(eventTitle);
  //     setTimezone(timezone);
  //     setEventType(eventType);
  //     setFromDate(fromDate);
  //     setToDate(toDate);
  //     console.log("fromDate", fromDate);
  //     setValue("startDate", dayjs(fromDate));
  //     setValue("startTime", dayjs(toDate));
  //   }
  // }, [defaultValues]);

  const handleSubmitSchedule = (data) => {
    const scheduleData = {
      ...data,
      // useActProfile: data.useActProfile === 1 ? false : true,
    };

    if (type === "EVENT") {
      dispatch(
        updateContractSchedules({
          data: scheduleData,
          contractId: currentBookingStatus?.contractId,
          profileId,
        }),
      )
        .unwrap()
        .then((response) => {
          if (response.status === 200 || response.status === 208) {
            const continueBooking =
              response.status === 208
                ? window.confirm(
                    "The selected time overlaps with another scheduled event. You can continue or cancel the contract now.",
                  )
                : true;

            if (continueBooking) {
              handleClose();
              setSelectable(false);
              scheduleData.scheduleId = currentBookingStatus?.contractId;
              setEvents((prev) => [...prev, filterEventData(scheduleData, true)]);
              dispatch(previewContract(currentBookingStatus?.contractId));
              setSelectedItem(false);
              showSnackbar(response.data.message, "success");
            } else if (response.status === 208) {
              router.push(`/${lang}/search?profileType=ACT_PROFILE`);
              return;
            }
          }
        })
        .catch((error) => {
          if (error?.data?.status === "failure") {
            showSnackbar("This Slot is already booked please choose other time.", "error");
          } else {
            showSnackbar(error, "error");
          }
        });
    } else {
      if (contractId) {
        dispatch(updateSchedules({ data: scheduleData, profileId, scheduleId: contractId }))
          .unwrap()
          .then((response) => {
            if (response.status === 200) {
              handleClose();
              setFetch((prev) => prev + 1);
              showSnackbar(response.data.message, "success");
            }
          })
          .catch((error) => {
            showSnackbar(error, "error");
          });
      } else {
        dispatch(createSchedules({ data: scheduleData, profileId }))
          .unwrap()
          .then((response) => {
            if (response.status === 200) {
              handleClose();
              setFetch((prev) => prev + 1);
              showSnackbar(response.data.message, "success");
            }
          })
          .catch((error) => {
            //setLoading(false);
            showSnackbar(error, "error");
          });
      }
    }
  };

  const deleteScheduleHandler = () => {
    if (contractId) {
      if (type === "EVENT") {
        dispatch(deleteContractSchedules({ contractId }))
          .unwrap()
          .then((response) => {
            if (response.status === 200) {
              handleClose();
              setSelectable(true);
              setSelectedItem(true);
              setFetch((prev) => prev + 1);
              showSnackbar(response.data.message, "success");
            }
          })
          .catch(() => {
            //setLoading(false);
            //showSnackbar(error, "error");
          });
      } else {
        dispatch(deleteSchedule({ contractId, profileId }))
          .unwrap()
          .then((response) => {
            if (response.status === 200) {
              handleClose();

              setFetch((prev) => prev + 1);
              showSnackbar(response.data.message, "success");
              setSelectedItem(true);
            }
          })
          .catch(() => {
            //setLoading(false);
            //showSnackbar(error, "error");
          });
      }
    }
  };

  // const {
  //   handleSubmit,
  //   watch,
  //   setValue,
  //   formState: {
  //     //errors,
  //   },
  // } = useForm({
  //   resolver,
  //   mode: "onSubmit",
  //   defaultValues: {
  //     profileType: "ACT",
  //     scheduleName: "UNAVAILABLE",
  //     scheduleDescription: "",
  //     scheduleType: type,
  //     timeZone: selectedTimeZone,
  //     startDate: "2024-05-21T11:27:32.345Z",
  //     startTime: "2024-05-21T11:27:32.345Z",

  //     recurrence: {
  //       recurrenceType: "WEEKLY",
  //       interval: 0,
  //       count: 0,
  //       endDate: "string",
  //       daysOfWeek: ["MONDAY"],
  //       daysOfMonth: 0,
  //       monthsOfYear: ["JANUARY"],
  //       recurrenceEndType: {
  //         never: true,
  //         endDate: "2024-05-21T11:27:32.345Z",
  //         occurrences: 0,
  //       },
  //     },
  //   },
  // });

  useEffect(() => {
    currentProfile && setValue("profileType", currentProfile.profileType);
  }, [currentProfile]);

  return (
    <Dialog open={open}>
      <DialogContent className=" !max-w-lg !bg-[--footer-bg] !border-[1px] lg:!px-10 !px-4 !pt-10 !border-[--text-color]">
        <Box className="flex justify-between pb-2 items-center">
          <Typography className="text-[--text-color] pb-3 text-2xl CraftworkGroteskHeavy">
            {t("editDate")}
          </Typography>
          <IconButton onClick={handleClose}>
            <Clear className="text-xl text-[--text-color]" />
          </IconButton>
        </Box>
        <form onSubmit={handleSubmit(handleSubmitSchedule)}>
          {/* <Box className="flex gap-2 py-2 items-center">
            {selectedDate.map((data, index) => (
              <Controller
                name="recurring"
                control={control}
                key={index}
                render={({ field }) => (
                  <Typography
                    className={
                      (field?.value && data === t("recurringDate")) ||
                      (!field?.value && data === t("singleDate"))
                        ? "border text-[--inprogress-color] cursor-pointer text-center border-[--inprogress-color] rounded-[4px] py-2 px-4"
                        : "border text-[--text-color] text-center cursor-pointer border-[--divider-color] rounded-[4px] py-2 px-4"
                    }
                    onClick={() => {
                      if (data === t("recurringDate")) {
                        setValue("recurring", true);
                      } else {
                        setValue("recurring", false);
                      }
                    }}
                  >
                    {data}
                  </Typography>
                )}
              />
            ))}
          </Box> */}

          <Box className="py-2">
            <Typography className="text-sm text-[--text-color] py-2 CraftworkGroteskGX">
              {t("timeZone")}
            </Typography>
            <Box className="border border-[--text-color] w-full rounded-[4px] ">
              <Controller
                name="timeZone"
                control={control}
                render={({ field }) => (
                  <Dropdown
                    options={timezones}
                    selectedValue={field.value}
                    //onSelect={handleSelectTimeZone}
                    onSelect={(value) => {
                      field.onChange(value);
                    }}
                    className="w-full !text-[--text-color]"
                  />
                )}
              />
            </Box>
          </Box>

          <Box className="flex gap-3 lg:flex-row flex-col py-3 items-start">
            <Box className="w-full">
              <Typography className="text-sm pb-2 text-[--text-color] CraftworkGroteskGX">
                {t("startDate")}
              </Typography>

              <Controller
                name="startDate"
                control={control}
                render={({ field }) => (
                  <MobileDateTimePicker
                    //value={fromDate}
                    //onChange={(date) => setFromDate(date)}
                    value={dayjs(field.value)}
                    onChange={(date) => field.onChange(date)}
                    className="border w-full border-[--text-color]"
                    sx={{
                      "& .MuiInputBase-root": {
                        color: "var(--text-color)",
                        border: "1px solid var(--text-color)",
                      },
                    }}
                  />
                )}
              />
            </Box>
            <Box className="w-full">
              <Typography className="text-sm pb-2 text-[--text-color] CraftworkGroteskGX">
                {t("finishDate")}
              </Typography>

              <Controller
                name="endDate"
                control={control}
                render={({ field }) => (
                  <MobileDateTimePicker
                    //value={fromDate}
                    //onChange={(date) => setFromDate(date)}
                    value={dayjs(field.value)}
                    onChange={(date) => field.onChange(date)}
                    className="border w-full border-[--text-color]"
                    sx={{
                      "& .MuiInputBase-root": {
                        color: "var(--text-color)",
                        border: "1px solid var(--text-color)",
                      },
                    }}
                  />
                )}
              />
              {watch("recurring") && (
                <Box className="!border !border-[text-color] !rounded-[2px] !mt-2 !py-1">
                  <Controller
                    name="recurrence.recurrenceType"
                    control={control}
                    render={({ field }) => (
                      <Dropdown
                        onSelect={field.onChange}
                        options={recurrenceTypeData || []}
                        selectedValue={field.value}
                        title="Select type"
                        className="!text-[--text-color] !w-full"
                      />
                    )}
                  />
                </Box>
              )}
              {watch("recurring") && watch("recurrence.recurrenceType") !== "MONTHLY" && (
                <Box className="flex flex-wrap items-center gap-2 my-2">
                  {days.map((day, index) => (
                    <Controller
                      name="recurrence.daysOfWeek"
                      control={control}
                      key={index}
                      render={({ field }) => (
                        <Box
                          className={classNames(
                            "w-[37px] h-[37px] flex items-center justify-center border rounded-full",
                            {
                              "border-[--inprogress-color]": field.value.includes(day.name),
                              "border-[--divider-color]": !field.value.includes(day.name),
                            },
                          )}
                          onClick={() => {
                            const days = field.value;
                            if (days.includes(day.name)) {
                              const index = days.indexOf(day.name);
                              days.splice(index, 1);
                            } else {
                              days.push(day.name);
                            }
                            field.onChange(days);
                          }}
                          style={{ cursor: "pointer" }}
                        >
                          <Typography
                            className={classNames("text-sm CraftworkGroteskRegular", {
                              "text-[--inprogress-color]": field.value.includes(day.name),
                              "text-[--text-color]": !field.value.includes(day.name),
                            })}
                          >
                            {day.day}
                          </Typography>
                        </Box>
                      )}
                    />
                  ))}
                </Box>
              )}

              {errors?.endDate && (
                <Typography as="span" className="text-sm !text-red-600">
                  {errors?.endDate?.message}
                </Typography>
              )}
            </Box>
          </Box>

          {/* <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular py-3">
            {t("unavailableCalendar")}
          </Typography> */}
          <Box className="flex gap-2 items-center">
            {contractId && (
              <Button
                sx={{
                  border: 0,
                  "&.MuiButtonBase-root": {
                    color: "white !important",
                  },
                }}
                className="flex gap-1 normal-case w-full items-center"
                onClick={() => deleteScheduleHandler()}
              >
                <>
                  <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy underline">
                    {t("deleteTime")}
                  </Typography>
                  <Clear className="text-lg text-[--text-color]" />
                </>
              </Button>
            )}
            <Button
              type="submit"
              sx={{
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
              className="flex gap-1 !bg-[--text-color] py-2 w-full normal-case items-center"
            >
              <Typography className="text-sm text-[--bg-color] CraftworkGroteskHeavy underline">
                {t("apply")}
              </Typography>
              <SouthEast className="text-lg text-[--bg-color]" />
            </Button>
          </Box>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditEvent;
