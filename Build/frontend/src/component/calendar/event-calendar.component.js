"use client";

import { useRef } from "react";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import FullCalendar from "@fullcalendar/react";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import styled from "@emotion/styled";

import { BUTTON_TEXT, COLORS_SWATCH, HEADER_TOOLBAR, INITIAL_VIEW, PLUGINS } from "@/utils";
import useImageButton from "./hooks/useImageButton";
import useIconButton from "./hooks/useIconButton";
import renderEventContent from "./methods/renderEventContent";
import EventHeader from "./methods/EventHeader";
const TIMEZONE = "America/Toronto";

export const CalendarWarpper = styled.div`
  .fc .fc-col-header-cell-cushion,
  .fc td,
  .fc.th,
  .fc .fc-toolbar-title {
    color: var(--text-color);
    font-family: var(--craftworkMedium);
  }
  .fc .fc-button-primary {
    background-color: transparent;
    border: 0px;
  }
  .fc-event-main > div {
    max-height: 100%;
    height: 100%;
  }
  .fc-timegrid-event .fc-event-main {
    padding: 0;
  }

  .fc-view .fc-scrollgrid {
    border-radius: 8px;
    background-color: var(--footer-bg);
  }
  .fc-scrollgrid-section .fc-timegrid-divider {
    display: none;
  }

  .fc-theme-standard .fc-scrollgrid {
    border: 1px solid var(--divider-color);
  }

  .fc-timegrid-divider {
    display: hidden;
  }

  .fc-theme-standard td,
  .fc-theme-standard th {
    border: 1px solid var(--divider-color) !important;
  }
  .fc-event.fc-event-start.fc-event-today {
    border: 0px;
  }
  .fc-event-main {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    overflow: hidden;
  }
  .fc-event.fc-event-end.fc-event-today.fc-daygrid-event.fc-daygrid-block-event.fc-h-event {
    border: 0px;
    border-radius: 6px;
    overflow: hidden;
  }
  @media (max-width: 600px) {
    .fc {
      height: 700px !important;
    }
  }
`;

function EventCalender({
  editable = false,
  events,
  onNext,
  onPrev,
  onViewChange,
  handleFilterChange,
  profileData,
}) {
  const fullCalendarRef = useRef(null);
  const handleViewChange = (view) => {
    fullCalendarRef.current?.calendar?.changeView?.(view);
    const currentDate = fullCalendarRef.current?.calendar?.getDate();

    if (onViewChange) {
      onViewChange(view, currentDate);
    }
  };

  const goToToday = () => fullCalendarRef.current?.calendar?.today?.();
  const gotToPrev = () => {
    fullCalendarRef.current?.calendar?.prev?.();
    onPrev();
  };
  const gotToNext = () => {
    fullCalendarRef.current?.calendar?.next?.();
    onNext();
  };

  // Previous Button
  const { prevBtn } = useIconButton({
    name: "prevBtn",
    icon: <ArrowBackIcon fontSize="large" sx={{ color: "black", margin: "2px" }} />,
    alt: "Previous",
    title: "Previous",
    onClick: gotToPrev,
  });

  // Next Button
  const { nextBtn } = useIconButton({
    name: "nextBtn",
    icon: (
      <ArrowBackIcon
        fontSize="large"
        sx={{
          color: "black",
          margin: "2px",
          rotate: "180deg",
          display: "inline-block",
        }}
      />
    ),
    alt: "Next",
    title: "Next",
    onClick: gotToNext,
  });

  // Today Button
  const { customToday } = useImageButton({
    name: "customToday",
    src: "https://static.vecteezy.com/system/resources/previews/007/879/777/large_2x/today-icon-style-free-vector.jpg",
    alt: "Custom today",
    title: "Today",
    onClick: goToToday,
  });

  // Month Button
  const { customMonth } = useImageButton({
    name: "customMonth",
    src: "https://static.vecteezy.com/system/resources/previews/007/879/777/large_2x/today-icon-style-free-vector.jpg",
    alt: "Custom month",
    title: "Month",
    onClick: () => handleViewChange("dayGridMonth"),
  });

  // Week Button
  const { customWeek } = useImageButton({
    name: "customWeek",
    src: "https://static.vecteezy.com/system/resources/previews/007/879/777/large_2x/today-icon-style-free-vector.jpg",
    alt: "Custom week",
    title: "Week",
    onClick: () => handleViewChange("timeGridWeek"),
    imageStyles: { height: "28px", width: "28px", margin: "6px" },
  });

  // Day Button
  const { customDay } = useImageButton({
    name: "customDay",
    src: "https://static.vecteezy.com/system/resources/previews/007/879/777/large_2x/today-icon-style-free-vector.jpg",
    alt: "Custom day",
    title: "Day",
    onClick: () => handleViewChange("timeGridDay"),
  });

  // if (loading)
  //   return <div className="text-sm text-[--text-color] CraftworkGroteskGX">Loading...</div>;
  return (
    <CalendarWarpper>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <EventHeader
          ref={fullCalendarRef}
          onPrev={onPrev}
          onNext={onNext}
          handleFilterChange={handleFilterChange}
          profileData={profileData}
        />

        <FullCalendar
          ref={fullCalendarRef}
          plugins={PLUGINS}
          initialView={INITIAL_VIEW}
          weekends
          events={events || []}
          eventContent={renderEventContent}
          headerToolbar={HEADER_TOOLBAR}
          buttonText={BUTTON_TEXT}
          customButtons={{
            prevBtn,
            nextBtn,
            customToday,
            customMonth,
            customWeek,
            customDay,
          }}
          timezone={TIMEZONE}
          selectable={editable}
          eventStartEditable={false}
          selectMirror
          eventColor={COLORS_SWATCH[0].code}
          datesSet={(arg) => {
            if (onViewChange) {
              const currentDate = fullCalendarRef.current?.calendar?.getDate();

              onViewChange(arg.view.type, currentDate);
            }
          }}
        />
      </LocalizationProvider>
    </CalendarWarpper>
  );
}

export default EventCalender;
