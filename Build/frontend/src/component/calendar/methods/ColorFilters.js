import React, { useState, useEffect, useRef } from "react";
import Circle from "@uiw/react-color-circle";

const ColorFilters = () => {
  //const [colors, setColors] = useState([]);
  const events = [];
  const [filterColor, setFilterColor] = useState("");

  const eventsBackup = useRef(events);
  const eventsBackupSet = useRef(false);

  useEffect(() => {
    if (events.length > 0 && eventsBackupSet.current === false) {
      eventsBackup.current = events;
      eventsBackupSet.current = true;
    }
    if (events.length > 0) {
      //setColors([...new Set(events.map((event) => event.backgroundColor))]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [events]);

  useEffect(() => {
    if (filterColor === "") return;

    const newEvents = events.filter(
      (event) => event.backgroundColor.toLowerCase() === filterColor.toLowerCase(),
    );

    setFilteredEvents(newEvents);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterColor]);

  return (
    <Circle
      // colors={colors}
      color={filterColor}
      onChange={(color) => {
        setFilterColor(color.hex);
      }}
      pointProps={{
        style: {
          height: "20px",
          width: "20px",
          marginRight: "8px",
          padding: 0,
        },
      }}
    />
  );
};

export default ColorFilters;
