import React, { useState } from "react";
import { Box, Icon<PERSON><PERSON><PERSON>, <PERSON>ack, Tooltip, Typography } from "@mui/material";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import DateRangeIcon from "@mui/icons-material/DateRange";
import CalendarIcon from "@/assets/svg/CalenderIcon.svg";
import ColorFilters from "./ColorFilters";
import { KeyboardArrowLeft, KeyboardArrowRight } from "@mui/icons-material";
import { headerData } from "./header.data";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { useTranslations } from "next-intl";
import Button from "@/component/button/button.component";
import Filter from "@/assets/svg/Filter.svg";
import FilterDrawer from "./FilterDrawer";

const EventHeader = ({ onPrev, onNext, handleFilterChange, profileData }, ref) => {
  const t = useTranslations("calendarHeader");
  const calendarApi = ref.current?.calendar;

  // Track selected view and navigation state
  const [currentViewType, setCurrentViewType] = useState("timeGridWeek");
  const [navState, setNavState] = useState({ prev: false, next: false });
  const [open, setOpen] = useState(false);

  const toggleSortDrawer = (newOpen) => {
    setOpen(newOpen);
  };

  // Function to handle navigation
  const goToPrev = () => {
    if (calendarApi) {
      calendarApi.prev();
      setNavState({ prev: true, next: false });
      onPrev();
    }
  };

  const goToNext = () => {
    if (calendarApi) {
      calendarApi.next();
      setNavState({ prev: false, next: true });
      onNext();
    }
  };

  // Function to handle view change
  const handleViewChange = (view) => {
    if (calendarApi) {
      calendarApi.changeView(view);
      setCurrentViewType(view);
    }
  };

  return (
    <div className="mb-4 lg:px-5">
      <Box className="lg:flex items-center lg:mx-0 mx-4 justify-between">
        <Typography
          variant="h6"
          className="text-[--text-color] lg:text-2xl text-lg lg:CraftworkGroteskSemiBold CraftworkGroteskMedium"
        >
          {t("upcomingEvents")}
        </Typography>
        <Box className="flex-wrap flex lg:gap-x-7 lg:justify-normal justify-between items-center">
          <Box className="flex gap-2 items-end pt-2">
            <Typography className="CraftworkGroteskRegular text-sm text-[--text-color]">
              {calendarApi?.currentData?.viewTitle || ""}
            </Typography>
            <Tooltip title="Previous">
              <IconButton
                sx={{
                  padding: 0,
                  backgroundColor: navState.prev
                    ? "var(--primary-color)"
                    : "var(--disabled-bg-color)",
                  opacity: navState.prev ? 1 : 0.6,
                }}
                onClick={goToPrev}
              >
                <KeyboardArrowLeft className="text-lg text-[--text-color]" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Next">
              <IconButton
                sx={{
                  padding: 0,
                  backgroundColor: navState.next
                    ? "var(--primary-color)"
                    : "var(--disabled-bg-color)",
                  opacity: navState.next ? 1 : 0.6,
                }}
                onClick={goToNext}
              >
                <KeyboardArrowRight className="text-lg text-[--text-color]" />
              </IconButton>
            </Tooltip>
          </Box>

          {/* View Selection Buttons */}
          <Box className="flex gap-1 items-center">
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              {t("viewAs")}
            </Typography>

            {/* Day View Button */}
            <Tooltip title="Day View">
              <IconButton
                sx={{
                  height: "40px",
                  width: "40px",
                  backgroundColor:
                    currentViewType === "timeGridDay"
                      ? "var(--primary-color)"
                      : "var(--disabled-bg-color)",
                  opacity: currentViewType === "timeGridDay" ? 1 : 0.6,
                }}
                onClick={() => handleViewChange("timeGridDay")}
              >
                <CalendarIcon />
              </IconButton>
            </Tooltip>

            {/* Week View Button */}
            <Tooltip title="Week View">
              <IconButton
                sx={{
                  height: "40px",
                  width: "40px",
                  backgroundColor:
                    currentViewType === "timeGridWeek"
                      ? "var(--primary-color)"
                      : "var(--disabled-bg-color)",
                  opacity: currentViewType === "timeGridWeek" ? 1 : 0.6,
                }}
                onClick={() => handleViewChange("timeGridWeek")}
              >
                <DateRangeIcon
                  fontSize="medium"
                  sx={{ color: "var(--text-color)", margin: "6px" }}
                />
              </IconButton>
            </Tooltip>

            {/* Month View Button */}
            <Tooltip title="Month View">
              <IconButton
                sx={{
                  height: "40px",
                  width: "40px",
                  backgroundColor:
                    currentViewType === "dayGridMonth"
                      ? "var(--primary-color)"
                      : "var(--disabled-bg-color)",
                  opacity: currentViewType === "dayGridMonth" ? 1 : 0.6,
                }}
                onClick={() => handleViewChange("dayGridMonth")}
              >
                <CalendarMonthIcon
                  fontSize="medium"
                  sx={{ color: "var(--text-color)", margin: "6px" }}
                />
              </IconButton>
            </Tooltip>
          </Box>
          <Button onClick={() => toggleSortDrawer(true)} className="!normal-case flex gap-2">
            <Typography className="text-sm CraftworkGroteskHeavy underline text-[--text-color]">
              Open Filter
            </Typography>
            <Filter className="text-lg text-[--text-color]" />
          </Button>
        </Box>
      </Box>
      <FilterDrawer
        open={open}
        handleClose={() => toggleSortDrawer(false)}
        onFilterChange={handleFilterChange}
        profileData={profileData}
      />
      <ColorFilters />
      <Stack className="pt-4 lg:mx-0 mx-4 flex justify-between">
        <Box className="flex gap-5">
          {headerData.map((item) => (
            <Box key={item.id} className="flex flex-wrap gap-2 items-center">
              <IconButton
                className={`p-2 rounded-[6px] !w-[30px] !h-[30px] !bg-[${item?.bgColor}] !border !border-[--divider-color]`}
                style={{
                  backgroundColor: item.bgColor,
                }}
              >
                {item?.icon}
              </IconButton>
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                {item?.text}
              </Typography>
            </Box>
          ))}
        </Box>
      </Stack>
    </div>
  );
};

export default React.forwardRef(EventHeader);
