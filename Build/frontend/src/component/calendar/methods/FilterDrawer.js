import ConfirmedTag from "@/common/(tags)/tags-confirmed/tags-confirmed.common";
import NegotiatingTag from "@/common/(tags)/tags-negotiating/tags-negotiating.common";
import SentTag from "@/common/(tags)/tags-sent/tag-sent.common";
import { CheckBox } from "@/component/form";
import { Close } from "@mui/icons-material";
import { Box, Drawer, IconButton, Typography } from "@mui/material";
import React, { useState } from "react";

const FilterDrawer = ({ open, handleClose, onFilterChange, profileData }) => {
  const [selectedProfiles, setSelectedProfiles] = useState([]);
  const [selectedDisplay, setSelectedDisplay] = useState([]);
  const [selectedStatus, setSelectedStatus] = useState([]);

  // const profileData = [
  //   {
  //     id: "VENUE_PROFILE",
  //     name: "Venue Profiles",
  //     type: "Venue",
  //   },
  //   {
  //     id: "ACT_PROFILE",
  //     name: "Act Profiles",
  //     type: "Act",
  //   },
  // ];

  const displayData = [
    { id: "own", label: "My events" },
    { id: "favorite", label: "Events of favourite Profiles" },
  ];

  const statusData = [
    { id: "SENT", component: SentTag, props: { name: "SENT" } },
    { id: "CONFIRMED", component: ConfirmedTag, props: {} },
    { id: "NEGOTIATING", component: NegotiatingTag, props: {} },
    { id: "RECEIVED", component: SentTag, props: { name: "RECEIVED" } },
  ];
  const handleProfileChange = (profileId) => {
    const updatedProfiles = selectedProfiles.includes(profileId)
      ? selectedProfiles.filter((id) => id !== profileId)
      : [...selectedProfiles, profileId];

    setSelectedProfiles(updatedProfiles);
    applyFilters(updatedProfiles, selectedDisplay, selectedStatus);
  };

  const handleDisplayChange = (displayId) => {
    const updatedDisplay = selectedDisplay.includes(displayId)
      ? selectedDisplay.filter((id) => id !== displayId)
      : [...selectedDisplay, displayId];

    setSelectedDisplay(updatedDisplay);
    applyFilters(selectedProfiles, updatedDisplay, selectedStatus);
  };

  const handleStatusChange = (statusId) => {
    const updatedStatus = selectedStatus.includes(statusId)
      ? selectedStatus.filter((id) => id !== statusId)
      : [...selectedStatus, statusId];

    setSelectedStatus(updatedStatus);
    applyFilters(selectedProfiles, selectedDisplay, updatedStatus);
  };

  const applyFilters = (profiles, display, status) => {
    onFilterChange?.({
      profileTypes: profiles,
      displayTypes: display,
      statusTypes: status,
    });
  };

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={handleClose}
      sx={{
        "& .MuiPaper-root": {
          height: "100%",
          width: "320px",
          backgroundColor: "var(--bg-color)",
          padding: "12px",
        },
      }}
    >
      <Box className="flex justify-between items-center">
        <Typography className="text-lg font-craftWorkHeavy text-[--text-color]">Filter</Typography>
        <IconButton onClick={handleClose}>
          <Close className="text-lg text-[--text-color]" />
        </IconButton>
      </Box>
      <div className="my-4 h-[1px] bg-[--divider-color] w-full" />
      <Box>
        <Box className="flex justify-between items-center">
          <Typography className="text-lg font-craftWorkMedium text-[--text-color]">
            Profile
          </Typography>
          {/* <Add className="text-lg text-[--text-color]" /> */}
        </Box>
        <Box className="mt-2">
          {profileData.map((profile) => (
            <Box key={profile.id} className="flex items-center w-full">
              <CheckBox
                checked={selectedProfiles.includes(profile.id)}
                onChange={() => handleProfileChange(profile.id)}
                className="text-[--text-color]"
              />
              <Box className="flex justify-between items-center w-full">
                <Typography className="text-[--text-color] text-sm font-craftWorkHeavy">
                  {profile.name}
                </Typography>
                <Typography className="text-[--text-color] bg-[--footer-bg] px-2 py-1 rounded-[4px] text-sm font-craftWorkRegular">
                  {profile.type}
                </Typography>
              </Box>
            </Box>
          ))}
        </Box>
        <div className="my-4 h-[1px] bg-[--divider-color] w-full" />
        <Box className="flex justify-between items-center my-3">
          <Typography className="text-lg font-craftWorkMedium text-[--text-color]">
            Display
          </Typography>
          {/* <Add className="text-lg text-[--text-color]" /> */}
        </Box>
        <Box className="mt-2">
          {displayData.map((item) => (
            <Box key={item.id} className="flex items-center w-full">
              <CheckBox
                checked={selectedDisplay.includes(item.id)}
                onChange={() => handleDisplayChange(item.id)}
                className="text-[--text-color]"
              />
              <Box className="w-full">
                <Typography className="text-[--text-color] text-sm font-craftWorkHeavy">
                  {item.label}
                </Typography>
              </Box>
            </Box>
          ))}
        </Box>
        <div className="my-4 h-[1px] bg-[--divider-color] w-full" />
        <Box className="flex justify-between items-center my-3">
          <Typography className="text-lg font-craftWorkMedium text-[--text-color]">
            Status
          </Typography>
          {/* <Add className="text-lg text-[--text-color]" /> */}
        </Box>
        <Box className="mt-2">
          {statusData.map((item) => (
            <Box key={item.id} className="flex items-center">
              <CheckBox
                checked={selectedStatus.includes(item.id)}
                onChange={() => handleStatusChange(item.id)}
                className="text-[--text-color]"
              />
              <Box>
                <item.component {...item.props} />
              </Box>
            </Box>
          ))}
        </Box>
      </Box>
    </Drawer>
  );
};

export default FilterDrawer;
