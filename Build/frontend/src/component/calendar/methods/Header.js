import React, { useEffect, useState } from "react";
import { <PERSON>, IconButton, <PERSON>ack, Tooltip, Typography } from "@mui/material";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import CalendarIcon from "@/assets/svg/CalenderIcon.svg";
import ColorFilters from "./ColorFilters";
import { KeyboardArrowLeft, KeyboardArrowRight } from "@mui/icons-material";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { useTranslations } from "next-intl";
import { DateRangeIcon } from "@mui/x-date-pickers";

const Header = (props, ref) => {
  const t = useTranslations("calendarHeader");
  const calendarApi = ref.current?.calendar;
  const [currentViewTitle, setCurrentViewTitle] = useState(
    calendarApi?.currentData?.viewTitle || "",
  );
  const [currentViewType, setCurrentViewType] = useState("timeGridWeek");

  // Effect to update title when calendar is ready
  useEffect(() => {
    if (calendarApi?.currentData?.viewTitle) {
      setCurrentViewTitle(calendarApi.currentData.viewTitle);
    }
  }, [calendarApi?.currentData?.viewTitle]);

  const gotToPrev = () => {
    calendarApi?.prev?.();
    updateViewTitle();
  };

  const gotToNext = () => {
    calendarApi?.next?.();
    updateViewTitle();
  };

  const updateViewTitle = () => {
    setCurrentViewTitle(calendarApi?.currentData?.viewTitle || "");
  };

  const handleViewChange = (view) => {
    calendarApi?.changeView?.(view);
    updateViewTitle();
    setCurrentViewType(view);
  };

  return (
    <div className="mb-4 lg:px-5">
      <Box className="lg:flex items-center lg:mx-0 mx-4 justify-between">
        <Typography
          variant="h6"
          className="text-[--text-color] lg:text-2xl text-lg lg:CraftworkGroteskSemiBold CraftworkGroteskMedium"
        >
          {t("upcomingEvents")}
        </Typography>
        <Box className="flex lg:gap-x-7 lg:justify-normal justify-between items-center">
          <Box className="flex gap-2 items-end pt-2">
            <Typography className="CraftworkGroteskRegular text-sm text-[--text-color]">
              {currentViewTitle || ""}
            </Typography>
            <Tooltip title="Previous">
              <IconButton onClick={gotToPrev} sx={{ padding: 0 }}>
                <KeyboardArrowLeft className="text-lg text-[--text-color]" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Next">
              <IconButton onClick={gotToNext} sx={{ padding: 0 }}>
                <KeyboardArrowRight className="text-lg text-[--text-color]" />
              </IconButton>
            </Tooltip>
          </Box>
          <Box className="flex gap-1 items-center">
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              {t("viewAs")}
            </Typography>

            {/* View Selection Buttons */}
            <Box className="flex gap-1 items-center">
              {/* Day View Button */}
              <Tooltip title="Day View">
                <IconButton
                  sx={{
                    height: "40px",
                    width: "40px",
                    backgroundColor:
                      currentViewType === "timeGridDay"
                        ? "var(--primary-color)"
                        : "var(--disabled-bg-color)",
                    opacity: currentViewType === "timeGridDay" ? 1 : 0.6,
                  }}
                  onClick={() => handleViewChange("timeGridDay")}
                >
                  <CalendarIcon />
                </IconButton>
              </Tooltip>

              {/* Week View Button */}
              <Tooltip title="Week View">
                <IconButton
                  sx={{
                    height: "40px",
                    width: "40px",
                    backgroundColor:
                      currentViewType === "timeGridWeek"
                        ? "var(--primary-color)"
                        : "var(--disabled-bg-color)",
                    opacity: currentViewType === "timeGridWeek" ? 1 : 0.6,
                  }}
                  onClick={() => handleViewChange("timeGridWeek")}
                >
                  <DateRangeIcon
                    fontSize="medium"
                    sx={{ color: "var(--text-color)", margin: "6px" }}
                  />
                </IconButton>
              </Tooltip>

              {/* Month View Button */}
              <Tooltip title="Month View">
                <IconButton
                  sx={{
                    height: "40px",
                    width: "40px",
                    backgroundColor:
                      currentViewType === "dayGridMonth"
                        ? "var(--primary-color)"
                        : "var(--disabled-bg-color)",
                    opacity: currentViewType === "dayGridMonth" ? 1 : 0.6,
                  }}
                  onClick={() => handleViewChange("dayGridMonth")}
                >
                  <CalendarMonthIcon
                    fontSize="medium"
                    sx={{ color: "var(--text-color)", margin: "6px" }}
                  />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        </Box>
      </Box>
      <ColorFilters />
      <Stack className="pt-4 lg:mx-0 mx-4 flex justify-between">
        {/* Color filter elements can go here if needed */}
      </Stack>
    </div>
  );
};

export default React.forwardRef(Header);
