import * as React from "react";

export const FavoritesIcon = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={11} height={11} fill="none" {...props}>
    <path
      stroke="#EFEFEF"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      d="m5.5 8.744 2.16 1.184c.42.231.911-.142.83-.63L8.08 6.784l1.747-1.777c.34-.346.152-.95-.317-1.021L7.094 3.62l-1.08-2.287a.563.563 0 0 0-1.028 0l-1.08 2.287-2.415.367c-.47.07-.658.675-.317 1.022L2.92 6.787 2.51 9.299c-.08.488.41.862.831.63L5.5 8.745"
    />
  </svg>
);

export const NegotiatingIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={15}
    height={14}
    fill="none"
    viewBox="0 0 12 11"
    {...props}
  >
    <path
      stroke="#181B1B"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M8.526.5v1.125M3.464.5v1.125M1.214 3.875h9.562M10.776 4.438V2.75c0-.932-.755-1.688-1.687-1.688H2.9c-.932 0-1.687.756-1.687 1.688v5.063c0 .932.755 1.687 1.687 1.687H4.59"
    />
    <path
      stroke="#181B1B"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M6.214 6.944c0-.245.224-.444.5-.444h4c.276 0 .5.199.5.444V9.39c0 .245-.224.444-.5.444l-1.64.003-.916.664-.916-.667h-.528c-.276 0-.5-.199-.5-.444V6.944Z"
      clipRule="evenodd"
    />
  </svg>
);

export const ConfirmedIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={15}
    height={15}
    fill="none"
    viewBox="0 0 11 11"
    {...props}
  >
    <path
      stroke="#181B1B"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M8.026 1v1.125M2.964 1v1.125M.714 4.375h9.562M10.276 4.938V3.25c0-.932-.755-1.688-1.687-1.688H2.4c-.932 0-1.687.756-1.687 1.688v5.063C.714 9.245 1.469 10 2.4 10H4.09"
    />
    <path
      stroke="#181B1B"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="m9.714 7-2.5 3-1.5-1.8"
    />
  </svg>
);

export const ProfileIcon = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={15} height={14} fill="none" {...props}>
    <path
      stroke="#EFEFEF"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M7.454 5.5V4.111c0-.46-.406-.833-.906-.833v0c-.5 0-.905.373-.905.833"
    />
    <path
      stroke="#EFEFEF"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M7.453 4.111c0-.46.406-.833.906-.833v0c.5 0 .905.373.905.833"
    />
    <path
      stroke="#EFEFEF"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M9.265 5.5V1.333c0-.46.406-.833.905-.833v0c.5 0 .906.373.906.833v5.834c0 1.84-1.622 3.333-3.622 3.333h-1.08c-1.212 0-2.343-.557-3.014-1.484L1.23 6.078a.798.798 0 0 1 .115-1.07v0a1.014 1.014 0 0 1 1.349 0L3.83 6.057V1.333c0-.46.406-.833.906-.833v0c.5 0 .905.373.905.833V5.5"
    />
  </svg>
);

export const SentIcon = (props) => (
  <svg
    width="14"
    height="15"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M14 1V5"
      stroke="#181B1B"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M6 1V5"
      stroke="#181B1B"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M1 8H19"
      stroke="#181B1B"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M17 3H3C1.895 3 1 3.895 1 5V18C1 19.105 1.895 20 3 20H17C18.105 20 19 19.105 19 18V5C19 3.895 18.105 3 17 3Z"
      stroke="#181B1B"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M10.0127 11.729C9.87468 11.729 9.76268 11.841 9.76368 11.979C9.76368 12.117 9.87568 12.229 10.0137 12.229C10.1517 12.229 10.2637 12.117 10.2637 11.979C10.2637 11.841 10.1517 11.729 10.0127 11.729"
      stroke="#181B1B"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M15.0127 11.729C14.8747 11.729 14.7627 11.841 14.7637 11.979C14.7637 12.117 14.8757 12.229 15.0137 12.229C15.1517 12.229 15.2637 12.117 15.2637 11.979C15.2637 11.841 15.1517 11.729 15.0127 11.729"
      stroke="#181B1B"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M5.01268 15.729C4.87468 15.729 4.76268 15.841 4.76368 15.979C4.76368 16.117 4.87568 16.229 5.01368 16.229C5.15168 16.229 5.26368 16.117 5.26368 15.979C5.26368 15.841 5.15168 15.729 5.01268 15.729"
      stroke="#181B1B"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M10.0127 15.729C9.87468 15.729 9.76268 15.841 9.76368 15.979C9.76368 16.117 9.87568 16.229 10.0137 16.229C10.1517 16.229 10.2637 16.117 10.2637 15.979C10.2637 15.841 10.1517 15.729 10.0127 15.729"
      stroke="#181B1B"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
