import { Box, Stack } from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import CommonImage from "@/component/image/image.component";
import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";
//eslint-disable-next-line
import { ConfirmedIcon, FavoritesIcon, NegotiatingIcon, ProfileIcon, SentIcon } from "./icons";

dayjs.extend(utc);
dayjs.extend(timezone);

const backgroundColorStatus = {
  CONFIRMED: "#619c74",
  NEGOTIATING: "#7f7cdc",
  SENT: "var(--text-color)",
  CANCEL: "#e07f54",
};

const iconColorStatus = {
  CONFIRMED: "#7cad8d",
  NEGOTIATING: "#9593e0",
  SENT: "lightgray",
  CANCEL: "#e07f54",
};

function renderEventContentNoTimezone(eventInfo) {
  // Ensure valid time formatting
  const startTime = dayjs(eventInfo.event.start);
  const endTime = dayjs(eventInfo.event.end);

  const formattedStartTime = startTime.isValid() ? startTime.format("hh:mm A") : "";
  const formattedEndTime = endTime.isValid() ? ` - ${endTime.format("hh:mm A")}` : "";

  const props = eventInfo.event.extendedProps;
  const eventType = props.type; // Use correct field for type

  const gridType = eventInfo.view.type;
  const isWeekView = gridType === "timeGridWeek";
  const eventStatus = props.status;

  // Calculate event duration in minutes
  const durationMinutes = endTime.diff(startTime, "minute");
  // Dynamic background color based on event type
  const backgroundColor =
    eventType === "contract"
      ? backgroundColorStatus[eventStatus] || "var(--text-color)"
      : eventType === "favorite"
        ? "#555656"
        : eventType === "profile"
          ? "#555656"
          : "transparent"; // Default color

  const iconColor = eventType === "contract" ? iconColorStatus[eventStatus] : "#5d6060";

  return (
    <Stack
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      sx={{
        width: "100%",
        overflow: "hidden",
        backgroundColor: backgroundColor,
        textAlign: "center",
        boxShadow: "2px 2px 6px rgba(0, 0, 0, 0.2)", // Adds depth
        height: "100%",
        cursor: "pointer",
        borderRadius: "6px",
        overflow: "hidden",
      }}
    >
      {/* <Tooltip
        title={`${eventInfo.event.title}, ${formattedStartTime} ${formattedEndTime}`}
        className="event-tooltip"
        placement="top"
      > */}
      {/* Profile Image (Optional, only if event duration > 30 minutes) */}
      {props.imageUrl && (durationMinutes > 30 || !isWeekView) && (
        <CommonImage
          src={props.imageUrl}
          alt="Event Image"
          width={30}
          height={30}
          style={{
            objectFit: "cover",
            borderRadius: "50%",
            marginBottom: "4px",
            height: "32px",
            width: "32px",
          }}
          //showPreview
        />
      )}

      {/* Event Title & Time */}
      {(durationMinutes > 30 || !isWeekView) && (
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            gap: "6px",
          }}
        >
          {eventType === "favorite" || eventType === "profile" ? (
            <Box
              sx={{
                height: 20,
                width: 20,
                background: iconColor,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                borderRadius: "8px",
              }}
            >
              {/* {eventType === "contract" && eventStatus === "NEGOTIATING" && <NegotiatingIcon />} */}
              {/* {eventType === "contract" && eventStatus === "CONFIRMED" && <ConfirmedIcon />} */}
              {eventType === "favorite" && <FavoritesIcon />}
              {eventType === "profile" && <ProfileIcon />}
              {/* {eventType === "contract" && eventStatus === "SENT" && <SentIcon />} */}
            </Box>
          ) : (
            ""
          )}
          <Box
            sx={{
              fontSize: "11px",
              fontWeight: "bold",
              textOverflow: "ellipsis",
              overflow: "hidden",
              whiteSpace: "nowrap",
              maxWidth: "100%",
              color: eventType === "contract" && eventStatus === "SENT" ? "#555656" : "#fff", // Ensure text visibility
            }}
          >
            {formattedStartTime} {formattedEndTime}
          </Box>
        </Box>
      )}

      <Box
        sx={{
          fontSize: durationMinutes > 30 || !isWeekView ? "10px" : "14px",
          fontWeight: "normal",
          textOverflow: "ellipsis",
          overflow: "hidden",
          whiteSpace: "nowrap",
          maxWidth: "100%",
          color: eventType === "contract" && eventStatus === "SENT" ? "#555656" : "#fff",
        }}
      >
        {eventInfo.event.title}
      </Box>

      {/* Edit Icon for Editable Events */}
      {props?.editable && (
        <Box sx={{ marginTop: "4px" }}>
          <EditIcon sx={{ cursor: "pointer", fontSize: "12px", color: "#fff" }} />
        </Box>
      )}
      {/* </Tooltip> */}
    </Stack>
  );
}

export default renderEventContentNoTimezone;
