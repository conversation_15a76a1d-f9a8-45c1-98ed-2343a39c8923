const timezones = [
  "(GMT-11:00) Niue Time",
  "(GMT-11:00) Samoa Standard Time",
  "(GMT-10:00) Cook Islands Standard Time",
  "(GMT-10:00) Hawaii-Aleutian Standard Time",
  "(GMT-10:00) Tahiti Time",
  "(GMT-09:30) Marquesas Time",
  "(GMT-09:00) Gambier Time",
  "(GMT-09:00) Hawaii-Aleutian Time (Adak)",
  "(GMT-08:00) Alaska Time - Anchorage",
  "(GMT-08:00) Alaska Time - Juneau",
  "(GMT-08:00) Alaska Time - Metlakatla",
  "(GMT-08:00) Alaska Time - Nome",
  "(GMT-08:00) Alaska Time - Sitka",
  "(GMT-08:00) Alaska Time - Yakutat",
  "(GMT-08:00) Pitcairn Time",
  "(GMT-07:00) Mexican Pacific Standard Time - Hermosillo",
  "(GMT-07:00) Mexican Pacific Standard Time - Mazatlan",
  "(GMT-07:00) Mountain Standard Time - Dawson Creek",
  "(GMT-07:00) Mountain Standard Time - Fort Nelson",
  "(GMT-07:00) Mountain Standard Time - Phoenix",
  "(GMT-07:00) Pacific Time - Los Angeles",
  "(GMT-07:00) Pacific Time - Tijuana",
  "(GMT-07:00) Pacific Time - Vancouver",
  "(GMT-07:00) Yukon Time - Dawson",
  "(GMT-07:00) Yukon Time - Whitehorse",
  "(GMT-06:00) Central Standard Time - Bahia Banderas",
  "(GMT-06:00) Central Standard Time - Belize",
  "(GMT-06:00) Central Standard Time - Chihuahua",
  "(GMT-06:00) Central Standard Time - Costa Rica",
  "(GMT-06:00) Central Standard Time - El Salvador",
  "(GMT-06:00) Central Standard Time - Guatemala",
  "(GMT-06:00) Central Standard Time - Managua",
  "(GMT-06:00) Central Standard Time - Merida",
  "(GMT-06:00) Central Standard Time - Mexico City",
  "(GMT-06:00) Central Standard Time - Monterrey",
  "(GMT-06:00) Central Standard Time - Regina",
  "(GMT-06:00) Central Standard Time - Swift Current",
  "(GMT-06:00) Central Standard Time - Tegucigalpa",
  "(GMT-06:00) Easter Island Time",
  "(GMT-06:00) Galapagos Time",
  "(GMT-06:00) Mountain Time - Boise",
  "(GMT-06:00) Mountain Time - Cambridge Bay",
  "(GMT-06:00) Mountain Time - Ciudad Juárez",
  "(GMT-06:00) Mountain Time - Denver",
  "(GMT-06:00) Mountain Time - Edmonton",
  "(GMT-06:00) Mountain Time - Inuvik",
  "(GMT-05:00) Acre Standard Time - Eirunepe",
  "(GMT-05:00) Acre Standard Time - Rio Branco",
  "(GMT-05:00) Central Time - Beulah, North Dakota",
  "(GMT-05:00) Central Time - Center, North Dakota",
  "(GMT-05:00) Central Time - Chicago",
  "(GMT-05:00) Central Time - Knox, Indiana",
  "(GMT-05:00) Central Time - Matamoros",
  "(GMT-05:00) Central Time - Menominee",
  "(GMT-05:00) Central Time - New Salem, North Dakota",
  "(GMT-05:00) Central Time - Ojinaga",
  "(GMT-05:00) Central Time - Rankin Inlet",
  "(GMT-05:00) Central Time - Resolute",
  "(GMT-05:00) Central Time - Tell City, Indiana",
  "(GMT-05:00) Central Time - Winnipeg",
  "(GMT-05:00) Colombia Standard Time",
  "(GMT-05:00) Eastern Standard Time - Cancun",
  "(GMT-05:00) Eastern Standard Time - Jamaica",
  "(GMT-05:00) Eastern Standard Time - Panama",
  "(GMT-05:00) Ecuador Time",
  "(GMT-05:00) Peru Standard Time",
  "(GMT-04:00) Amazon Standard Time - Boa Vista",
  "(GMT-04:00) Amazon Standard Time - Campo Grande",
  "(GMT-04:00) Amazon Standard Time - Cuiaba",
  "(GMT-04:00) Amazon Standard Time - Manaus",
  "(GMT-04:00) Amazon Standard Time - Porto Velho",
  "(GMT-04:00) Atlantic Standard Time - Barbados",
  "(GMT-04:00) Atlantic Standard Time - Martinique",
  "(GMT-04:00) Atlantic Standard Time - Puerto Rico",
  "(GMT-04:00) Atlantic Standard Time - Santo Domingo",
  "(GMT-04:00) Bolivia Time",
  "(GMT-04:00) Chile Time",
  "(GMT-04:00) Cuba Time",
  "(GMT-04:00) Eastern Time - Detroit",
  "(GMT-04:00) Eastern Time - Grand Turk",
  "(GMT-04:00) Eastern Time - Indianapolis",
  "(GMT-04:00) Eastern Time - Iqaluit",
  "(GMT-04:00) Eastern Time - Louisville",
  "(GMT-04:00) Eastern Time - Marengo, Indiana",
  "(GMT-04:00) Eastern Time - Monticello, Kentucky",
  "(GMT-04:00) Eastern Time - New York",
  "(GMT-04:00) Eastern Time - Petersburg, Indiana",
  "(GMT-04:00) Eastern Time - Port-au-Prince",
  "(GMT-04:00) Eastern Time - Toronto",
  "(GMT-04:00) Eastern Time - Vevay, Indiana",
  "(GMT-04:00) Eastern Time - Vincennes, Indiana",
  "(GMT-04:00) Eastern Time - Winamac, Indiana",
  "(GMT-04:00) Guyana Time",
  "(GMT-04:00) Paraguay Time",
  "(GMT-04:00) Venezuela Time",
  "(GMT-03:00) Argentina Standard Time - Buenos Aires",
  "(GMT-03:00) Argentina Standard Time - Catamarca",
  "(GMT-03:00) Argentina Standard Time - Cordoba",
  "(GMT-03:00) Argentina Standard Time - Jujuy",
  "(GMT-03:00) Argentina Standard Time - La Rioja",
  "(GMT-03:00) Argentina Standard Time - Mendoza",
  "(GMT-03:00) Argentina Standard Time - Rio Gallegos",
  "(GMT-03:00) Argentina Standard Time - Salta",
  "(GMT-03:00) Argentina Standard Time - San Juan",
  "(GMT-03:00) Argentina Standard Time - San Luis",
  "(GMT-03:00) Argentina Standard Time - Tucuman",
  "(GMT-03:00) Argentina Standard Time - Ushuaia",
  "(GMT-03:00) Atlantic Time - Bermuda",
  "(GMT-03:00) Atlantic Time - Glace Bay",
  "(GMT-03:00) Atlantic Time - Goose Bay",
  "(GMT-03:00) Atlantic Time - Halifax",
  "(GMT-03:00) Atlantic Time - Moncton",
  "(GMT-03:00) Atlantic Time - Thule",
  "(GMT-03:00) Brasilia Standard Time - Araguaina",
  "(GMT-03:00) Brasilia Standard Time - Bahia",
  "(GMT-03:00) Brasilia Standard Time - Belem",
  "(GMT-03:00) Brasilia Standard Time - Fortaleza",
  "(GMT-03:00) Brasilia Standard Time - Maceio",
  "(GMT-03:00) Brasilia Standard Time - Recife",
  "(GMT-03:00) Brasilia Standard Time - Santarem",
  "(GMT-03:00) Brasilia Standard Time - Sao Paulo",
  "(GMT-03:00) Falkland Islands Standard Time",
  "(GMT-03:00) French Guiana Time",
  "(GMT-03:00) Palmer Time",
  "(GMT-03:00) Punta Arenas Time",
  "(GMT-03:00) Rothera Time",
  "(GMT-03:00) Suriname Time",
  "(GMT-03:00) Uruguay Standard Time",
  "(GMT-02:30) Newfoundland Time",
  "(GMT-02:00) Fernando de Noronha Standard Time",
  "(GMT-02:00) South Georgia Time",
  "(GMT-02:00) St Pierre & Miquelon Time",
  "(GMT-01:00) Cape Verde Standard Time",
  "(GMT-01:00) East Greenland Time",
  "(GMT-01:00) West Greenland Time",
  "(GMT+00:00) Azores Time",
  "(GMT+00:00) Coordinated Universal Time",
  "(GMT+00:00) Greenwich Mean Time",
  "(GMT+00:00) Greenwich Mean Time - Abidjan",
  "(GMT+00:00) Greenwich Mean Time - Bissau",
  "(GMT+00:00) Greenwich Mean Time - Danmarkshavn",
  "(GMT+00:00) Greenwich Mean Time - Monrovia",
  "(GMT+00:00) Greenwich Mean Time - Reykjavik",
  "(GMT+00:00) Greenwich Mean Time - São Tomé",
  "(GMT+01:00) Central European Standard Time - Algiers",
  "(GMT+01:00) Central European Standard Time - Tunis",
  "(GMT+01:00) Ireland Time",
  "(GMT+01:00) Morocco Time",
  "(GMT+01:00) United Kingdom Time",
  "(GMT+01:00) West Africa Standard Time - Lagos",
  "(GMT+01:00) West Africa Standard Time - Ndjamena",
  "(GMT+01:00) Western European Time - Canary",
  "(GMT+01:00) Western European Time - Faroe",
  "(GMT+01:00) Western European Time - Lisbon",
  "(GMT+01:00) Western European Time - Madeira",
  "(GMT+01:00) Western Sahara Time",
  "(GMT+02:00) Central Africa Time - Juba",
  "(GMT+02:00) Central Africa Time - Khartoum",
  "(GMT+02:00) Central Africa Time - Maputo",
  "(GMT+02:00) Central Africa Time - Windhoek",
  "(GMT+02:00) Central European Time - Amsterdam",
  "(GMT+02:00) Central European Time - Andorra",
  "(GMT+02:00) Central European Time - Belgrade",
  "(GMT+02:00) Central European Time - Berlin",
  "(GMT+02:00) Central European Time - Brussels",
  "(GMT+02:00) Central European Time - Budapest",
  "(GMT+02:00) Central European Time - Ceuta",
  "(GMT+02:00) Central European Time - Copenhagen",
  "(GMT+02:00) Central European Time - Gibraltar",
  "(GMT+02:00) Central European Time - Luxembourg",
  "(GMT+02:00) Central European Time - Madrid",
  "(GMT+02:00) Central European Time - Malta",
  "(GMT+02:00) Central European Time - Monaco",
  "(GMT+02:00) Central European Time - Oslo",
  "(GMT+02:00) Central European Time - Paris",
  "(GMT+02:00) Central European Time - Prague",
  "(GMT+02:00) Central European Time - Rome",
  "(GMT+02:00) Central European Time - Stockholm",
  "(GMT+02:00) Central European Time - Tirane",
  "(GMT+02:00) Central European Time - Vienna",
  "(GMT+02:00) Central European Time - Warsaw",
  "(GMT+02:00) Central European Time - Zurich",
  "(GMT+02:00) Eastern European Standard Time - Kaliningrad",
  "(GMT+02:00) Eastern European Standard Time - Tripoli",
  "(GMT+02:00) South Africa Standard Time",
  "(GMT+02:00) Troll Time",
  "(GMT+03:00) Arabian Standard Time - Baghdad",
  "(GMT+03:00) Arabian Standard Time - Qatar",
  "(GMT+03:00) Arabian Standard Time - Riyadh",
  "(GMT+03:00) East Africa Time",
  "(GMT+03:00) Eastern European Time - Athens",
  "(GMT+03:00) Eastern European Time - Beirut",
  "(GMT+03:00) Eastern European Time - Bucharest",
  "(GMT+03:00) Eastern European Time - Cairo",
  "(GMT+03:00) Eastern European Time - Chisinau",
  "(GMT+03:00) Eastern European Time - Gaza",
  "(GMT+03:00) Eastern European Time - Hebron",
  "(GMT+03:00) Eastern European Time - Helsinki",
  "(GMT+03:00) Eastern European Time - Kyiv",
  "(GMT+03:00) Eastern European Time - Nicosia",
  "(GMT+03:00) Eastern European Time - Riga",
  "(GMT+03:00) Eastern European Time - Sofia",
  "(GMT+03:00) Eastern European Time - Tallinn",
  "(GMT+03:00) Eastern European Time - Vilnius",
  "(GMT+03:00) Famagusta Time",
  "(GMT+03:00) Israel Time",
  "(GMT+03:00) Jordan Time",
  "(GMT+03:00) Kirov Time",
  "(GMT+03:00) Moscow Standard Time - Minsk",
  "(GMT+03:00) Moscow Standard Time - Moscow",
  "(GMT+03:00) Moscow Standard Time - Simferopol",
  "(GMT+03:00) Syria Time",
  "(GMT+03:00) Türkiye Time",
  "(GMT+03:00) Volgograd Standard Time",
  "(GMT+03:30) Iran Standard Time",
  "(GMT+04:00) Armenia Standard Time",
  "(GMT+04:00) Astrakhan Time",
  "(GMT+04:00) Azerbaijan Standard Time",
  "(GMT+04:00) Georgia Standard Time",
  "(GMT+04:00) Gulf Standard Time",
  "(GMT+04:00) Mauritius Standard Time",
  "(GMT+04:00) Réunion Time",
  "(GMT+04:00) Samara Standard Time",
  "(GMT+04:00) Saratov Time",
  "(GMT+04:00) Seychelles Time",
  "(GMT+04:00) Ulyanovsk Time",
  "(GMT+04:30) Afghanistan Time",
  "(GMT+05:00) French Southern & Antarctic Time",
  "(GMT+05:00) Maldives Time",
  "(GMT+05:00) Mawson Time",
  "(GMT+05:00) Pakistan Standard Time",
  "(GMT+05:00) Tajikistan Time",
  "(GMT+05:00) Turkmenistan Standard Time",
  "(GMT+05:00) Uzbekistan Standard Time - Samarkand",
  "(GMT+05:00) Uzbekistan Standard Time - Tashkent",
  "(GMT+05:00) Vostok Time",
  "(GMT+05:00) West Kazakhstan Time - Aktau",
  "(GMT+05:00) West Kazakhstan Time - Almaty",
  "(GMT+05:00) West Kazakhstan Time - Aqtobe",
  "(GMT+05:00) West Kazakhstan Time - Atyrau",
  "(GMT+05:00) West Kazakhstan Time - Kostanay",
  "(GMT+05:00) West Kazakhstan Time - Oral",
  "(GMT+05:00) West Kazakhstan Time - Qyzylorda",
  "(GMT+05:00) Yekaterinburg Standard Time",
  "(GMT+05:30) India Standard Time - Colombo",
  "(GMT+05:30) India Standard Time - Kolkata",
  "(GMT+05:45) Nepal Time",
  "(GMT+06:00) Bangladesh Standard Time",
  "(GMT+06:00) Bhutan Time",
  "(GMT+06:00) Indian Ocean Time",
  "(GMT+06:00) Kyrgyzstan Time",
  "(GMT+06:00) Omsk Standard Time",
  "(GMT+06:00) Urumqi Time",
  "(GMT+06:30) Cocos Islands Time",
  "(GMT+06:30) Myanmar Time",
  "(GMT+07:00) Barnaul Time",
  "(GMT+07:00) Christmas Island Time",
  "(GMT+07:00) Davis Time",
  "(GMT+07:00) Hovd Standard Time",
  "(GMT+07:00) Indochina Time - Bangkok",
  "(GMT+07:00) Indochina Time - Ho Chi Minh City",
  "(GMT+07:00) Krasnoyarsk Standard Time - Krasnoyarsk",
  "(GMT+07:00) Krasnoyarsk Standard Time - Novokuznetsk",
  "(GMT+07:00) Novosibirsk Standard Time",
  "(GMT+07:00) Tomsk Time",
  "(GMT+07:00) Western Indonesia Time - Jakarta",
  "(GMT+07:00) Western Indonesia Time - Pontianak",
  "(GMT+08:00) Australian Western Standard Time",
  "(GMT+08:00) Brunei Darussalam Time",
  "(GMT+08:00) Casey Time",
  "(GMT+08:00) Central Indonesia Time",
  "(GMT+08:00) China Standard Time - Macao",
  "(GMT+08:00) China Standard Time - Shanghai",
  "(GMT+08:00) Hong Kong Standard Time",
  "(GMT+08:00) Irkutsk Standard Time",
  "(GMT+08:00) Malaysia Time - Kuala Lumpur",
  "(GMT+08:00) Malaysia Time - Kuching",
  "(GMT+08:00) Philippine Standard Time",
  "(GMT+08:00) Singapore Standard Time",
  "(GMT+08:00) Taipei Standard Time",
  "(GMT+08:00) Ulaanbaatar Standard Time - Choibalsan",
  "(GMT+08:00) Ulaanbaatar Standard Time - Ulaanbaatar",
  "(GMT+08:45) Australian Central Western Standard Time",
  "(GMT+09:00) East Timor Time",
  "(GMT+09:00) Eastern Indonesia Time",
  "(GMT+09:00) Japan Standard Time",
  "(GMT+09:00) Korean Standard Time - Pyongyang",
  "(GMT+09:00) Korean Standard Time - Seoul",
  "(GMT+09:00) Palau Time",
  "(GMT+09:00) Yakutsk Standard Time - Chita",
  "(GMT+09:00) Yakutsk Standard Time - Khandyga",
  "(GMT+09:00) Yakutsk Standard Time - Yakutsk",
  "(GMT+09:30) Australian Central Standard Time",
  "(GMT+09:30) Central Australia Time - Adelaide",
  "(GMT+09:30) Central Australia Time - Broken Hill",
  "(GMT+10:00) Australian Eastern Standard Time - Brisbane",
  "(GMT+10:00) Australian Eastern Standard Time - Lindeman",
  "(GMT+10:00) Chamorro Standard Time",
  "(GMT+10:00) Chuuk Time",
  "(GMT+10:00) Eastern Australia Time - Hobart",
  "(GMT+10:00) Eastern Australia Time - Macquarie",
  "(GMT+10:00) Eastern Australia Time - Melbourne",
  "(GMT+10:00) Eastern Australia Time - Sydney",
  "(GMT+10:00) Papua New Guinea Time",
  "(GMT+10:00) Vladivostok Standard Time - Ust-Nera",
  "(GMT+10:00) Vladivostok Standard Time - Vladivostok",
  "(GMT+10:30) Lord Howe Time",
  "(GMT+11:00) Bougainville Time",
  "(GMT+11:00) Kosrae Time",
  "(GMT+11:00) Magadan Standard Time",
  "(GMT+11:00) New Caledonia Standard Time",
  "(GMT+11:00) Norfolk Island Time",
  "(GMT+11:00) Ponape Time",
  "(GMT+11:00) Sakhalin Standard Time",
  "(GMT+11:00) Solomon Islands Time",
  "(GMT+11:00) Srednekolymsk Time",
  "(GMT+11:00) Vanuatu Standard Time",
  "(GMT+12:00) Anadyr Standard Time",
  "(GMT+12:00) Fiji Standard Time",
  "(GMT+12:00) Gilbert Islands Time",
  "(GMT+12:00) Marshall Islands Time - Kwajalein",
  "(GMT+12:00) Marshall Islands Time - Majuro",
  "(GMT+12:00) Nauru Time",
  "(GMT+12:00) New Zealand Time",
  "(GMT+12:00) Petropavlovsk-Kamchatski Standard Time",
  "(GMT+12:00) Tuvalu Time",
  "(GMT+12:00) Wake Island Time",
  "(GMT+12:00) Wallis & Futuna Time",
  "(GMT+12:45) Chatham Time",
  "(GMT+13:00) Apia Standard Time",
  "(GMT+13:00) Phoenix Islands Time",
  "(GMT+13:00) Tokelau Time",
  "(GMT+13:00) Tonga Standard Time",
  "(GMT+14:00) Line Islands Time",
];

export default timezones;

export function extractTimezoneOffsets(timezone) {
  const regex = /\(GMT([+-]\d{2}:\d{2})\)/;

  const match = timezone.match(regex);
  if (match) {
    return match[1];
  }
  return null;
}
