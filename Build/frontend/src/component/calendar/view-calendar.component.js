import { useState, useRef, useEffect } from "react";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import FullCalendar from "@fullcalendar/react";

dayjs.extend(utc);
dayjs.extend(timezone);

const TIMEZONE = "America/Toronto";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import styled from "@emotion/styled";
import { useDispatch } from "react-redux";
import { getCalendar } from "../../store/slice/act/act.slice";

import {
  BUTTON_TEXT,
  COLORS_SWATCH,
  HEADER_TOOLBAR,
  INITIAL_VIEW,
  PLUGINS,
  defaultIntervalInMinutes,
  filterEventData,
} from "@/utils";
import useImageButton from "./hooks/useImageButton";
import useIconButton from "./hooks/useIconButton";
import EditEvent from "./EditEvent";
import Header from "./methods/Header";
import { useForm } from "react-hook-form";
import { scheduleValidation } from "@/validation/act/act.validation";
import { yupResolver } from "@hookform/resolvers/yup";
import Loader from "../loader/loader.component";
import renderEventContentNoTimezone from "./methods/renderEventContentNoTimezone";

export const CalendarWarpper = styled.div`
  .fc .fc-col-header-cell-cushion,
  .fc td,
  .fc.th,
  .fc .fc-toolbar-title {
    color: var(--text-color);
    font-family: var(--craftworkMedium);
  }
  .fc .fc-button-primary {
    background-color: transparent;
    border: 0px;
  }

  .fc-view .fc-scrollgrid {
    border-radius: 8px;
    background-color: var(--footer-bg);
  }
  .fc-scrollgrid-section .fc-timegrid-divider {
    display: none;
  }

  .fc-theme-standard .fc-scrollgrid {
    border: 1px solid var(--divider-color);
  }

  .fc-timegrid-divider {
    display: hidden;
  }

  .fc-theme-standard td,
  .fc-theme-standard th {
    border: 1px solid var(--divider-color) !important;
  }
  @media (max-width: 600px) {
    .fc {
      height: 700px !important;
    }
  }
`;

function ViewCalender({
  profileId,
  editable = false,
  type = "UNAVAILABLE",
  loading = false,
  onEventUpdate,
}) {
  const fullCalendarRef = useRef(null);
  const [openCreateEvent, setOpenCreateEvent] = useState(false);
  const [scheduleId, setScheduleId] = useState(null);
  const [currentView, setCurrentView] = useState(INITIAL_VIEW);
  const [currentDate, setCurrentDate] = useState(dayjs().tz(TIMEZONE).toDate());
  const [events, setEvents] = useState([]);
  const resolver = yupResolver(scheduleValidation);

  // Initial fetch when component mounts
  useEffect(() => {
    if (profileId) {
      fetchCalendarData(currentView, currentDate);
    }
  }, [profileId]);

  const handleViewChange = (view) => {
    //console.log('ViewChange:', { view });
    const calendarApi = fullCalendarRef.current?.calendar;
    if (calendarApi) {
      calendarApi.changeView(view);
      // Don't set state or fetch here - datesSet will handle it
    }
  };

  const goToToday = () => {
    const calendarApi = fullCalendarRef.current?.calendar;
    if (calendarApi) {
      calendarApi.today();
      const date = calendarApi.getDate();
      setCurrentDate(date);
      fetchCalendarData(currentView, date);
    }
  };

  const gotToPrev = () => {
    const calendarApi = fullCalendarRef.current?.calendar;
    if (calendarApi) {
      calendarApi.prev();
      const date = calendarApi.getDate();
      setCurrentDate(date);
      fetchCalendarData(currentView, date);
    }
  };

  const gotToNext = () => {
    const calendarApi = fullCalendarRef.current?.calendar;
    if (calendarApi) {
      calendarApi.next();
      const date = calendarApi.getDate();
      setCurrentDate(date);
      fetchCalendarData(currentView, date);
    }
  };

  const dispatch = useDispatch();

  const fetchCalendarData = (view, date) => {
    if (!profileId) return;

    const period = view === "timeGridDay" ? "DAY" : view === "timeGridWeek" ? "WEEK" : "MONTH";

    const startDate =
      view === "timeGridDay"
        ? dayjs(date).tz(TIMEZONE).startOf("day").toISOString()
        : view === "timeGridWeek"
          ? dayjs(date).tz(TIMEZONE).startOf("week").add(1, "day").toISOString()
          : dayjs(date).tz(TIMEZONE).startOf("month").toISOString();

    dispatch(
      getCalendar({
        profileId,
        params: {
          startDate,
          queryPeriod: period,
        },
      }),
    )
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          const responseData = response.data.data;

          // Process schedule data
          const scheduleEvents = responseData.scheduleTimeDtoList.map((item) => {
            return filterEventData(item, editable);
          });

          // Process special event data
          const specialEvents = (responseData.specialEventDtoList || []).map((item) => {
            return filterEventData(
              {
                ...item.scheduleTime,
                scheduleId: item.specialEventId,
                scheduleName: item.eventName,
                scheduleType: "SPECIAL EVENT",
              },
              editable,
            );
          });

          // Combine both types of events
          setEvents([...scheduleEvents, ...specialEvents]);
          //setLoading(false);
        }
      })
      .catch(() => {
        // setLoading(false);
        //showSnackbar(error, "error");
      });
  };
  // Handle view date range changes
  const handleDatesSet = (dateInfo) => {
    // console.log('DatesSet:', {
    //   view: dateInfo.view.type,
    //   start: dateInfo.start,
    //   currentView,
    //   currentDate
    // });

    const newView = dateInfo.view.type;
    const newDate = dateInfo.start;

    setCurrentView(newView);
    setCurrentDate(newDate);
    fetchCalendarData(newView, newDate);
  };

  const handleDateClick = (selectInfo) => {
    const dates = [];
    if (selectInfo.dateStr) {
      dates.push(dayjs(selectInfo.dateStr));
    }

    if (selectInfo.endStr) {
      dates.push(dayjs(selectInfo.endStr));
    } else {
      dates.push(dayjs(selectInfo.dateStr).add(defaultIntervalInMinutes, "minutes"));
    }

    setValue("startDate", dates[0]);
    setValue("endDate", dates[1]);
    editable && setOpenCreateEvent(true);
  };

  const handleDateSelect = (selectInfo) => {
    const dates = [];
    if (selectInfo.startStr) {
      dates.push(dayjs(selectInfo.startStr));
    }

    if (selectInfo.endStr) {
      let endDateStr = dayjs(selectInfo.endStr);
      if (selectInfo.view.type === "dayGridMonth") {
        endDateStr = endDateStr.subtract(1, "minute");
      }
      dates.push(endDateStr);
    } else {
      dates.push(dayjs(selectInfo.endStr).add(defaultIntervalInMinutes, "minutes"));
    }
    setValue("startDate", dates[0]);
    setValue("endDate", dates[1]);
    setOpenCreateEvent(true);
  };

  const handleClose = () => {
    setOpenCreateEvent(false);
    setScheduleId(null);
  };

  const {
    handleSubmit,
    control,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    resolver,
    defaultValues: {
      profileType: "ACT",
      scheduleName: "",
      scheduleDescription: "",
      scheduleType: type,
      timeZone: "(GMT-04:00) Eastern Time - Toronto",
      startDate: new Date(),
      endDate: new Date(),
      recurring: false,
      recurrence: {
        recurrenceType: "WEEKLY",
        interval: 0,
        count: 0,
        endDate: "string",
        daysOfWeek: [],
        daysOfMonth: 0,
        monthsOfYear: ["JANUARY"],
        recurrenceEndType: {
          never: true,
          endDate: "2024-05-21T11:27:32.345Z",
          occurrences: 0,
        },
      },
    },
  });

  // Previous Button
  const { prevBtn } = useIconButton({
    name: "prevBtn",
    icon: <ArrowBackIcon fontSize="large" sx={{ color: "black", margin: "2px" }} />,
    alt: "Previous",
    title: "Previous",
    onClick: gotToPrev,
  });

  // Next Button
  const { nextBtn } = useIconButton({
    name: "nextBtn",
    icon: (
      <ArrowBackIcon
        fontSize="large"
        sx={{
          color: "black",
          margin: "2px",
          rotate: "180deg",
          display: "inline-block",
        }}
      />
    ),
    alt: "Next",
    title: "Next",
    onClick: gotToNext,
  });

  // Today Button
  const { customToday } = useImageButton({
    name: "customToday",
    src: "https://static.vecteezy.com/system/resources/previews/007/879/777/large_2x/today-icon-style-free-vector.jpg",
    alt: "Custom today",
    title: "Today",
    onClick: goToToday,
  });

  // Month Button
  const { customMonth } = useImageButton({
    name: "customMonth",
    src: "https://static.vecteezy.com/system/resources/previews/007/879/777/large_2x/today-icon-style-free-vector.jpg",
    alt: "Custom month",
    title: "Month",
    onClick: () => handleViewChange("dayGridMonth"),
  });

  // Week Button
  const { customWeek } = useImageButton({
    name: "customWeek",
    src: "https://static.vecteezy.com/system/resources/previews/007/879/777/large_2x/today-icon-style-free-vector.jpg",
    alt: "Custom week",
    title: "Week",
    onClick: () => handleViewChange("timeGridWeek"),
    imageStyles: { height: "28px", width: "28px", margin: "6px" },
  });

  // Day Button
  const { customDay } = useImageButton({
    name: "customDay",
    src: "https://static.vecteezy.com/system/resources/previews/007/879/777/large_2x/today-icon-style-free-vector.jpg",
    alt: "Custom day",
    title: "Day",
    onClick: () => handleViewChange("timeGridDay"),
  });

  if (loading) return <Loader />;

  return (
    <CalendarWarpper>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Header ref={fullCalendarRef} />

        <FullCalendar
          ref={fullCalendarRef}
          plugins={PLUGINS}
          initialView={INITIAL_VIEW}
          weekends
          timezone={TIMEZONE}
          events={events}
          eventContent={renderEventContentNoTimezone}
          headerToolbar={HEADER_TOOLBAR}
          buttonText={BUTTON_TEXT}
          customButtons={{
            prevBtn,
            nextBtn,
            customToday,
            customMonth,
            customWeek,
            customDay,
          }}
          selectable={editable}
          eventStartEditable={false}
          selectMirror
          select={handleDateSelect}
          dateClick={handleDateClick}
          datesSet={handleDatesSet}
          eventColor={COLORS_SWATCH[0].code}
          editable={editable}
          eventClick={(info) => {
            if (editable) {
              const event = events.find((event) => event.id === info.event._def.extendedProps.id);
              if (!event) return;

              setValue("startDate", dayjs(event.startDate));
              setValue("endDate", dayjs(event.endDate));
              setValue("recurring", event.recurring || false);
              setValue("timeZone", event.timeZone);

              if (event.recurring) {
                setValue("recurrence.recurrenceType", event.recurrenceType);
                setValue("recurrence.daysOfWeek", event.daysOfWeek || []);
                setValue("recurrence.interval", event.interval || 0);
              }

              setOpenCreateEvent(true);
              setScheduleId(event.id);
            }
          }}
        />

        {openCreateEvent && (
          <EditEvent
            open={openCreateEvent}
            handleClose={handleClose}
            control={control}
            handleSubmit={handleSubmit}
            errors={errors}
            watch={watch}
            scheduleId={scheduleId}
            onSuccess={() => {
              onEventUpdate?.();
              handleClose();
            }}
            profileId={profileId}
          />
        )}
      </LocalizationProvider>
    </CalendarWarpper>
  );
}

export default ViewCalender;
