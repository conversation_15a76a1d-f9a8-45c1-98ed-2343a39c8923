"use client";
import { Box, Divider, Menu, MenuItem } from "@mui/material";
import React, { useState } from "react";
import { But<PERSON> } from "@/component";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";

const DropdownForm = ({ options, selectedValue, title = "", className, onSelect, ...props }) => {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMenuItemClick = (value) => {
    onSelect(value);
    handleClose();
  };

  return (
    <>
      <Button
        onClick={handleClick}
        className={`${className} CraftworkGroteskGX md:text-sm text-[15px] !max-h-[40px] !overflow-hidden normal-case justify-between`}
        sx={{
          "&:hover": {
            backgroundColor: "transparent",
          },
        }}
        {...props}
      >
        {selectedValue || title}
        <KeyboardArrowDownIcon className="mt-1" />
      </Button>
      <Menu
        anchorEl={anchorEl}
        keepMounted
        open={Boolean(anchorEl)}
        onClose={handleClose}
        className="mt-3 shadow-none overflow-y-auto"
        PaperProps={{
          style: {
            boxShadow: "none",
            backgroundColor: "var(--footer-bg)",
            padding: "0",
          },
        }}
      >
        {options?.map((option, index) => (
          <Box key={option}>
            <MenuItem
              className="CraftworkGroteskMedium py-3 md:px-4 text-[--text-color] mx-0 md:text-base text-sm"
              onClick={() => handleMenuItemClick(option)}
            >
              {option}
            </MenuItem>
            {index !== options.length - 1 && (
              <Divider
                sx={{ borderBottom: "thin solid var(--divider-color)" }}
                className="my-1 w-full"
              />
            )}
          </Box>
        ))}
      </Menu>
    </>
  );
};

export default DropdownForm;
