"use client";
import React, { useState } from "react";
import { TextField, InputAdornment } from "@mui/material";
import { Controller } from "react-hook-form";

const FormInput = ({
  name,
  title,
  type,
  control,
  icons,
  className,
  placeholder,
  errors,
  ...props
}) => {
  const [showPassword, setShowPassword] = useState(false);

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <>
      <Controller
        name={name}
        control={control}
        defaultValue=""
        render={({ field }) => (
          <TextField
            id={name}
            {...field}
            {...props}
            type={type === "password" && showPassword ? "text" : type}
            autoComplete="off"
            className={`${className}`}
            placeholder={placeholder}
            InputProps={{
              endAdornment: (
                <InputAdornment
                  position="end"
                  onClick={handleTogglePassword}
                  style={{ cursor: "pointer" }}
                >
                  {type === "password" ? (
                    <span className="!text-[--text-color] !text-sm  CraftworkGroteskBold">
                      {showPassword ? "Hide" : "Show"}
                    </span>
                  ) : null}
                </InputAdornment>
              ),
            }}
          />
        )}
      />
      {errors && errors[name] && (
        <span className="!mt-1 text-sm !text-red-600">{errors[name].message}</span>
      )}
    </>
  );
};

export default FormInput;
