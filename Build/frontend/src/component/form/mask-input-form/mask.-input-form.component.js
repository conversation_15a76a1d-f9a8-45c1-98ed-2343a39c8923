"use client";
import React from "react";
import { TextField } from "@mui/material";
import { Controller } from "react-hook-form";
import { IMaskInput } from "react-imask";

const FormMaskedInput = ({
  name,
  type,
  control,
  className,
  placeholder,
  errors,
  onAccept,
  ...props
}) => {
  return (
    <>
      <Controller
        name={name}
        control={control}
        defaultValue=""
        render={({ field }) => (
          <TextField
            {...field}
            {...props}
            className={`${className}`}
            placeholder={placeholder}
            InputProps={{
              inputComponent: TextMaskCustom,
            }}
          />
        )}
      />
      {errors && errors[name] && (
        <span className="!mt-1 text-sm !text-red-600">{errors[name].message}</span>
      )}
    </>
  );
};

export default FormMaskedInput;

const TextMaskCustom = React.forwardRef(function TextMaskCustom(props, ref) {
  const { onChange, ...other } = props;
  return (
    <IMaskInput
      {...other}
      mask="(#00) 000-0000"
      definitions={{
        "#": /[1-9]/,
      }}
      inputRef={ref}
      onAccept={(value) => onChange({ target: { name: props.name, value } })}
      overwrite
    />
  );
});
