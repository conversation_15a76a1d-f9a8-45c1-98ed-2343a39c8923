"use client";
import React from "react";
import OtpInput from "react-otp-input";

const OTPField = ({ numInputs, inputStyle, otp, setOtp, error }) => {
  return (
    <>
      <OtpInput
        value={otp}
        onChange={setOtp}
        numInputs={numInputs}
        inputStyle={`${inputStyle}`}
        renderSeparator={<span className="!text-[--text-color] Sora500 !mx-2">-</span>}
        renderInput={(props) => <input {...props} />}
      />
      {error && (
        <span className="!pt-2 text-sm !text-red-600">Please enter valid 6 digits otp</span>
      )}
    </>
  );
};

export default OTPField;
