"use client";

import Image from "next/image";
import { useState } from "react";
import { PhotoView } from "react-photo-view";

const CommonImage = ({ src, alt, className, fallBackSrc, showPreview = false, ...props }) => {
  const [error, setError] = useState(false);

  const imageComponent = (
    <Image
      src={error ? fallBackSrc : src}
      alt={alt}
      className={className}
      priority={true} // Add priority for above-the-fold images
      quality={85} // Add quality parameter
      {...props}
      onError={() => setError(true)}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" // Add responsive sizes
    />
  );
  return showPreview ? (
    <PhotoView src={error ? fallBackSrc : src}>{imageComponent}</PhotoView>
  ) : (
    imageComponent
  );
};

export default CommonImage;
