import { Clear } from "@mui/icons-material";
import {
  Box,
  Dialog,
  DialogContent,
  Drawer,
  IconButton,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import React from "react";
// import { CheckBox } from "../form";
import PublicSvg from "@/assets/svg/PublicSvg.svg";
import PrivateSvg from "@/assets/svg/PrivateSvg.svg";
import EditActFeedbackRate from "@/ui/edit-act-feedback-rate/edit-act-feedback-rate.ui";
import { useTranslations } from "next-intl";

const ViewFeedbackDialog = ({ open, handleClose, feedbackData }) => {
  const t = useTranslations("feedback");
  const rateData = [
    {
      id: 0,
      text: t("entertainment"),
      rating: feedbackData?.content?.feedbackMsgDto?.entertainmentValue,
    },
    {
      id: 1,
      text: t("professionalism"),
      rating: feedbackData?.content?.feedbackMsgDto?.professionalismValue,
    },
    {
      id: 2,
      text: t("draw"),
      rating: feedbackData?.content?.feedbackMsgDto?.drawAsExpectedValue,
    },
  ];
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  const content = (
    <DialogContent className="max-sm:!w-full !w-[40vw] !bg-[--footer-bg] p-6">
      <Box className="flex justify-end items-center">
        <IconButton onClick={handleClose}>
          <Clear className="text-[--text-color] text-lg" />
        </IconButton>
      </Box>
      <EditActFeedbackRate rateData={rateData} />
      <Box className="border border-[--divider-color] rounded-[4px] w-full p-4 mt-2">
        <Box className="flex gap-2 items-center">
          <PublicSvg />
          <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
            Public
          </Typography>
        </Box>
        <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular py-2">
          {feedbackData?.content?.feedbackMsgDto?.publicMessage}
        </Typography>
        {/* <Box className="flex items-center !mt-2">
          <CheckBox className="!max-w-[24px]" sx={{ color: "#EFEFEF", marginRight: "5px" }} />
          <label className="cursor-pointer flex gap-x-2 items-center">
            <Typography className="text-[--text-color] pl-2 text-sm CraftworkGroteskRegular">
              Add to my public account
            </Typography>
          </label>
        </Box> */}
      </Box>
      <Box className="border border-[--divider-color] rounded-[4px] w-full p-4 mt-4">
        <Box className="flex gap-2 items-center">
          <PrivateSvg />
          <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
            Private
          </Typography>
        </Box>
        <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular py-2">
          {feedbackData?.content?.feedbackMsgDto?.privateMessage}
        </Typography>
        {/* <Box className="flex items-center !mt-2">
          <CheckBox className="!max-w-[24px]" sx={{ color: "#EFEFEF", marginRight: "5px" }} />
          <label className="cursor-pointer flex gap-x-2 items-center">
            <Typography className="text-[--text-color] pl-2 text-sm CraftworkGroteskRegular">
              Add to my public account
            </Typography>
          </label>
        </Box> */}
      </Box>
      {/* <Box className="flex justity-end">
        <Button
          className="!bg-[--text-color] !flex !gap-x-1 !px-4 items-center !mt-5"
          sx={{
            border: 0,
            "&.MuiButtonBase-root": {
              color: "white !important",
            },
          }}
          onClick={handleClose}
        >
          <Typography className="!normal-case CraftworkGroteskHeavy text-center !text-sm !leading-[15.4px] !text-[--bg-color]">
            View Feedback
          </Typography>
          <SouthEast className="text-[--bg-color] text-xl" />
        </Button>
      </Box> */}
    </DialogContent>
  );
  return (
    <>
      {isMobile ? (
        <Drawer
          anchor="bottom"
          open={open}
          onClose={handleClose}
          sx={{
            "& .MuiPaper-root": {
              height: "95%",
              backgroundColor: "var(--bg-color)",
            },
          }}
          className="z-[9999]"
        >
          {content}
        </Drawer>
      ) : (
        <Dialog
          open={open}
          maxWidth={false}
          sx={{ "& .MuiBackdrop-root": { backgroundColor: "#181B1BE5" } }}
        >
          {content}
        </Dialog>
      )}
    </>
  );
};

export default ViewFeedbackDialog;
