// src/config/index.js
const baseURL = process.env.NEXT_PUBLIC_API_URL;
const wsURL = process.env.NEXT_PUBLIC_SOCKET_URL;

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

const oAuth2Config = {
  baseURL: baseURL?.replace("/api/v1/", ""),
  google: "/oauth2/authorization/google",
  facebook: "/oauth2/authorization/facebook",
};

// Fixed image configuration
const imageConfig = {
  domains: [
    { protocol: "https", hostname: "image4.com", pathname: "**" },
    { protocol: "https", hostname: "images.unsplash.com", pathname: "**" },
    { protocol: "https", hostname: "images.pexels.com", pathname: "**" },
    { protocol: "https", hostname: "example.com", pathname: "**" },
    { protocol: "https", hostname: process.env.NEXT_PUBLIC_API_HOST, pathname: "**" },
    { hostname: process.env.NEXT_PUBLIC_API_HOST, pathname: "**" },
  ],
};

module.exports = {
  baseURL,
  wsURL,
  firebaseConfig,
  oAuth2Config,
  imageConfig,
};
