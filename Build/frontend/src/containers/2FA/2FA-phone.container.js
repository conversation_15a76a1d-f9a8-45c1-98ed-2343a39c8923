"use client";
import { Typography, CircularProgress } from "@mui/material";
import { twoFaPhoneValidation } from "@/validation/auth/2FA/2FA.validation";
import { yupResolver } from "@hookform/resolvers/yup";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { Button, FormMaskedInput } from "@/component";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { twoFaPhone } from "@/store/slice/auth/2fa.auth.slice";
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { stringifyParams } from "@/utils";
import { useLocale, useTranslations } from "next-intl";
const TwoFAPhoneNumberForm = () => {
  const t = useTranslations("2faAuthentication");
  const lang = useLocale();
  const { showSnackbar } = useSnackbar();
  const { loading } = useSelector((state) => state.twoFa);
  const dispatch = useDispatch();
  const router = useRouter();
  const resolver = yupResolver(twoFaPhoneValidation);
  const {
    handleSubmit,
    formState: { errors },
    control,
  } = useForm({
    resolver,
    defaultValues: {
      phoneNumber: "",
    },
    mode: "onSubmit",
  });

  const handle2FA = (data) => {
    const phoneNumber = stringifyParams({ phoneNumber: data.phoneNumber });
    dispatch(twoFaPhone(phoneNumber))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          showSnackbar(response.data.message, "success");
          router.push(`/${lang}/2fa-authentication-otp`);
        } else if (response.status === 208) {
          router.push(`/${lang}/2fa-authentication-otp`);
          showSnackbar(response.data.message, "error");
        }
      })
      .catch((error) => {
        showSnackbar(error, "error");
      });
  };
  return (
    <form onSubmit={handleSubmit(handle2FA)}>
      <FormMaskedInput
        name="phoneNumber"
        control={control}
        errors={errors}
        size="small"
        sx={{
          "& input::placeholder": {
            color: "#EFEFEF",
            border: 0,
          },
          "& input": {
            color: "#EFEFEF",
            fontFamily: "var(--craftWorkRegular)",
          },
          "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          // "& input:-webkit-autofill": {
          //   "-webkit-text-fill-color": "transparent !important",
          //   "-webkit-box-shadow": "0 0 0 1000px #2E2E2E inset !important",
          //   transition: "background-color 5000s ease-in-out 0s",
          // },
          "& .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          border: 0,
        }}
        className="w-full !border !py-1 !border-white rounded-[2px] !mb-3"
        placeholder={"(XXX) XXXX-XXXX"}
      />
      <Button
        className="!bg-[--text-color] !flex !gap-x-4 !w-full !my-8 !py-3"
        type="submit"
        sx={{
          border: 0,
          "&.MuiButtonBase-root": {
            color: "white !important",
          },
        }}
      >
        {loading ? (
          <CircularProgress size={24} className="!text-black" />
        ) : (
          <>
            <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
              {t("2faAuthenticationForm.VerificationCode")}
            </Typography>
            <ArrowSouthEast />
          </>
        )}
      </Button>
    </form>
  );
};
export default TwoFAPhoneNumberForm;
