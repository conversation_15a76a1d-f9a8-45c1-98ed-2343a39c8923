"use client";
import { twoFaEmail } from "@/store/slice/auth/2fa.auth.slice";
import { Typography } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { stringifyParams } from "@/utils";
import { useTranslations } from "next-intl";

const TwoFAResendCode = () => {
  const t = useTranslations("2faAuthentication");
  const { showSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { data: userCredentail } = useSelector((state) => state.login);
  const handleResendCode = () => {
    const email = stringifyParams({ email: userCredentail.email });
    dispatch(twoFaEmail(email))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          showSnackbar(response.data.message, "success");
        } else if (response.status === 208) {
          showSnackbar(response.data.message, "error");
        }
      })
      .catch((error) => {
        showSnackbar(error, "error");
      });
  };
  return (
    <Typography
      className="!underline CraftworkGroteskHeavy !text-[--text-color] !text-sm !text-center !my-auto"
      onClick={handleResendCode}
      sx={{
        cursor: "pointer",
      }}
    >
      {t("2faResendCode.ResendCode")}
    </Typography>
  );
};
export default TwoFAResendCode;
