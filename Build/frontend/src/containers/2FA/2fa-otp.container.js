"use client";
import { useState } from "react";
import { Box, Typography, CircularProgress } from "@mui/material";
import { <PERSON>TP<PERSON>ield, But<PERSON> } from "@/component";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { useForm } from "react-hook-form";
import { verifyOTPPhoneNumer } from "@/store/slice/auth/2fa.auth.slice";
import { useDispatch } from "react-redux";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { useRouter } from "next/navigation";
import { stringifyParams } from "@/utils";
import { useLocale, useTranslations } from "next-intl";
const TwoFaOtpForm = () => {
  const t = useTranslations("2faAuthentication");
  const lang = useLocale();
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [otp, setOtp] = useState("");
  const [error, setError] = useState(false);
  const { handleSubmit } = useForm({
    mode: "onSubmit",
  });

  const handleTwoFASubmit = () => {
    if (otp.length < 6) {
      setError(true);
    } else {
      setLoading(true);
      const data = stringifyParams({ code: otp });
      setError(false);
      dispatch(verifyOTPPhoneNumer(data))
        .unwrap()
        .then((response) => {
          if (response.status === 200) {
            showSnackbar(response.data.message, "success");
            setLoading(false);
            router.replace(`/${lang}/dashboard`);
          }
        })
        .catch((error) => {
          showSnackbar(error, "error");
          setLoading(false);
        });
    }
  };
  return (
    <form onSubmit={handleSubmit(handleTwoFASubmit)}>
      <Box className="!flex !flex-col !justify-center !items-center">
        <OTPField
          numInputs={6}
          inputStyle="lg:!w-[63px] lg:!h-[50px] md:!w-[74px] md:!h-[50px] !w-[9vw] !h-[5vh]  !text-[--text-color] !text-center !bg-transparent !border !border-[--text-color]"
          otp={otp}
          setOtp={setOtp}
          error={error}
        />
      </Box>
      <Button
        className="!bg-[--text-color] !flex !gap-x-4 !w-full !my-8 !py-3"
        type="submit"
        sx={{
          border: 0,
          "&.MuiButtonBase-root": {
            color: "white !important",
          },
        }}
      >
        {loading ? (
          <CircularProgress size={24} className="!text-black" />
        ) : (
          <>
            <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
              {t("2faOTPForm.Setup")}
            </Typography>
            <ArrowSouthEast />
          </>
        )}
      </Button>
    </form>
  );
};
export default TwoFaOtpForm;
