import { Box, TextField, Typography, InputLabel, InputAdornment } from "@mui/material";
import React, { useEffect } from "react";
import { useTranslations } from "next-intl";
import AutoCompleteLocation from "@/component/autocomplete/autocomplete-location.component";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import { USER_CONSTANTS } from "@/validation/auth/constants";

const AccountInformationAddressData = ({
  register,
  errors,
  setValue,
  locationValue,
  setLocationValue,
}) => {
  const t = useTranslations("actLocation");
  const s = useTranslations("accountInformation.accountInfoForm");

  useEffect(() => {
    if (locationValue) {
      const locationParts = [];

      // if (locationValue?.streetAddress) {
      //     locationParts.push(locationValue?.streetAddress);
      // }
      if (locationValue?.city) {
        locationParts.push(locationValue?.city);
        setValue("location.city", locationValue?.city);
      }
      if (locationValue?.state) {
        locationParts.push(locationValue?.state);
        setValue("location.state", locationValue?.state);
      }
      if (locationValue?.country) {
        locationParts.push(locationValue?.country);
        setValue("location.country", locationValue?.country);
      }

      const location = locationParts.join(", ");
      if (location) {
        setLocationValue(location);
      }
    }
  }, [locationValue, setLocationValue]);

  return (
    <Box className="my-10">
      <Typography className="text-[--text-color] text-lg CraftworkGroteskMedium py-6">
        {s("addressData")}
      </Typography>
      <Typography className="text-[--text-color] text-sm CraftworkGroteskRegular pb-2">
        {s("address")}
      </Typography>

      <Box className="w-full">
        <AutoCompleteLocation
          value={locationValue}
          setValue={setLocationValue}
          textFieldClass="border !border-[--text-color] rounded-[2px]"
          className="w-full"
          showFilter={true}
          readOnly={!!locationValue}
        />
        {errors && (errors.city || errors.state || errors.country || errors.streetAddress) && (
          <Typography as="span" className="text-sm !text-red-600">
            {t("ActLocationForm.invalidLocation")}
          </Typography>
        )}
      </Box>

      <Box className="!w-full !mt-4">
        <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX">
          {t("ActLocationForm.StreetAddress")}
        </InputLabel>
        <TextField
          type="text"
          size="small"
          placeholder={t("ActLocationForm.addressLine")}
          inputProps={{
            maxLength: USER_CONSTANTS.LOCATION.STREET_ADDRESS_MAX_LENGTH,
          }}
          InputLabelProps={{ style: { color: "#EFEFEF" } }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start" style={{ cursor: "pointer" }}>
                <LocationSvg className="!w-6 !h-6" />
              </InputAdornment>
            ),
          }}
          sx={{
            "& input::placeholder": {
              color: "#EFEFEF",
              border: 0,
            },
            "& input": {
              color: "#EFEFEF",
              fontFamily: "var(--craftWorkRegular)",
            },
            "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            "& .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            border: 0,
          }}
          name="streetAddress"
          className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !my-2"
          {...register("location.streetAddress")}
        />
        {errors && errors.streetAddress && (
          <Typography as="span" className="text-sm !text-red-600">
            {errors.streetAddress.message}
          </Typography>
        )}
      </Box>

      <Box className="!w-full !mt-3">
        <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX">
          {t("ActLocationForm.Zip")}
        </InputLabel>
        <TextField
          type="text"
          size="small"
          placeholder="000000"
          InputLabelProps={{ style: { color: "#EFEFEF" } }}
          inputProps={{
            maxLength: USER_CONSTANTS.LOCATION.ZIP_CODE_MAX_LENGTH,
          }}
          InputProps={{
            maxlength: 6,
          }}
          sx={{
            "& input::placeholder": {
              color: "#EFEFEF",
              border: 0,
            },
            "& input": {
              color: "#EFEFEF",
              fontFamily: "var(--craftWorkRegular)",
            },
            "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            "& .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            border: 0,
          }}
          className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !my-2"
          name="zipCode"
          {...register("location.zipCode")}
        />
        {errors && errors.zipCode && (
          <Typography as="span" className="text-sm !text-red-600">
            {errors.zipCode.message}
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export default AccountInformationAddressData;
