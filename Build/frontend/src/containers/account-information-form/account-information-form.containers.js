"use client";
import { data } from "@/common/countrycode-dropdown/countrycode-dropdown.data.common";
import { Box, CircularProgress, Typography, useMediaQuery, useTheme } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useLocale } from "next-intl";
import AccountInformationPersonalData from "./account-information-personal-data";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { userPersonalValidation } from "@/validation/act/act.validation";
import { useDispatch, useSelector } from "react-redux";
import AccountInformationAddressData from "./account-information-address-data";
import { getCurrentUserEmail, putCurrentUserEmail } from "@/store/slice/auth/login.auth.slice";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { <PERSON><PERSON>, Loader } from "@/component";
import { ProfileFooter } from "@/common/profile";
import { useRouter } from "next/navigation";

const AccountInformationForm = ({ type }) => {
  const lang = useLocale();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const [selectedCountryCode, setSelectedCountryCode] = useState(data[0].code);
  const [locationValue, setLocationValue] = useState("");
  const currentUser = useSelector((state) => state.login.currentUser);
  const dispatch = useDispatch();
  const handleSelectCountryCode = (value) => {
    setSelectedCountryCode(value);
  };
  const [loading, setLoading] = useState(false);
  const resolver = yupResolver(userPersonalValidation);
  const { showSnackbar } = useSnackbar();
  const router = useRouter();
  const {
    handleSubmit,
    setValue,
    control,
    register,
    formState: { errors },
  } = useForm({
    resolver,
    defaultValues: {
      firstName: "",
      lastName: "",
      phoneNumber: "",
      email: "",
      firstAlternativePhoneNumber: "",
      secondAlternativePhoneNumber: "",
      location: {
        city: "",
        state: "",
        country: "",
        streetAddress: "",
        zipCode: "",
      },
    },
  });

  useEffect(() => {
    return () => {
      // Reset form when component unmounts
      handleSubmit(() => {})();
      setValue("firstName", "");
      setValue("lastName", "");
      setValue("email", "");
      setValue("phoneNumber", "");
      setValue("firstAlternativePhoneNumber", "");
      setValue("secondAlternativePhoneNumber", "");
      setValue("location.streetAddress", "");
      setValue("location.zipCode", "");
      setValue("location.city", "");
      setValue("location.state", "");
      setValue("location.country", "");
      setLocationValue("");
    };
  }, []);

  useEffect(() => {
    if (!currentUser) {
      // Reset form when currentUser is null (after logout)
      setValue("firstName", "");
      setValue("lastName", "");
      setValue("email", "");
      setValue("phoneNumber", "");
      setValue("firstAlternativePhoneNumber", "");
      setValue("secondAlternativePhoneNumber", "");
      setValue("location.streetAddress", "");
      setValue("location.zipCode", "");
      setValue("location.city", "");
      setValue("location.state", "");
      setValue("location.country", "");
      setLocationValue("");
    } else {
      setValue("firstName", currentUser.firstName || "");
      setValue("lastName", currentUser.lastName || "");
      setValue("email", currentUser.email || "");
      setValue("phoneNumber", currentUser.phoneNumber || "");
      setValue("firstAlternativePhoneNumber", currentUser.firstAlternativePhoneNumber || "");
      setValue("secondAlternativePhoneNumber", currentUser.secondAlternativePhoneNumber || "");
      setValue("location.streetAddress", currentUser.location?.streetAddress || "");
      setValue("location.zipCode", currentUser.location?.zipCode || "");

      const { city, state, country } = currentUser?.location || {};
      const locationParts = [];

      if (city) {
        locationParts.push(city);
        setValue("location.city", city);
      }
      if (state) {
        locationParts.push(state);
        setValue("location.state", state);
      }
      if (country) {
        locationParts.push(country);
        setValue("location.country", country);
      }

      const location = locationParts.join(", ");

      if (location) {
        setLocationValue(location);
      }
    }
  }, [currentUser, setValue]);

  const updateAccountInformation = (data) => {
    setLoading(true);

    dispatch(putCurrentUserEmail({ ...data }))
      .unwrap()
      .then(() => {
        dispatch(getCurrentUserEmail())
          .unwrap()
          .then(() => {
            setLoading(false);
            type === "user-data" && router.push(`/${lang}/select-profile`);
            showSnackbar("Profile updated Successfully", "success");
          });
      })
      .catch((error) => {
        setLoading(false);
        const errorMessage = error;
        showSnackbar(errorMessage, "error");
      });
  };
  if (loading) {
    <Loader />;
  }
  return (
    <form onSubmit={handleSubmit(updateAccountInformation)} className="pb-8">
      <AccountInformationPersonalData
        selectedCountryCode={selectedCountryCode}
        onSelectCountryCode={handleSelectCountryCode}
        control={control}
        register={register}
        errors={errors}
        setValue={setValue}
        type={type}
      />
      <AccountInformationAddressData
        control={control}
        register={register}
        errors={errors}
        setValue={setValue}
        locationValue={locationValue}
        setLocationValue={setLocationValue}
      />
      {type === "account" && (
        <Box className="w-full pb-20 lg:pb-0 flex justify-end">
          <Button type="submit" className="px-8 py-3 rounded-[4px] !bg-[--text-color] !normal-case">
            {loading ? (
              <CircularProgress size={24} className="!text-black" />
            ) : (
              <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                Submit
              </Typography>
            )}
          </Button>
        </Box>
      )}
      {type === "user-data" && (
        <Box className="pb-8">
          <ProfileFooter
            buttonName={isSmallScreen ? "Next" : "Next"}
            backurl={`/${lang}/contract-details`}
            // type="button"
            backurlType="router-back"
            footerType="booking"
            className="lg:!pl-28 lg:!px-2 lg:!max-w-[49%]"
          />
        </Box>
      )}
    </form>
  );
};

export default AccountInformationForm;
