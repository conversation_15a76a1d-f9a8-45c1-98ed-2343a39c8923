import {
  Box,
  TextField,
  Typography,
  InputLabel,
  //RadioGroup,
  // FormControlLabel,
  //Radio,
} from "@mui/material";
import React from "react";
import { useTranslations } from "next-intl";
import CountryCodeDropdown from "@/common/countrycode-dropdown/countrycode-dropdown.common";
import { Controller } from "react-hook-form";
import { USER_CONSTANTS } from "@/validation/auth/constants";
//import { RadioButtonUnchecked } from "@mui/icons-material";
//import RadioButtonIcon from "@/assets/svg/RadioButton.svg";

const AccountInformationPersonalData = ({
  selectedCountryCode,
  onSelectCountryCode,
  control,
  errors,
  //type,
}) => {
  const t = useTranslations("accountInformation.accountInfoForm");
  const s = useTranslations("distributionList");
  //const options = ["I am as basic user", "Corporation"];
  return (
    <Box className="mt-8">
      <Typography className="text-[--text-color] text-lg CraftworkGroteskMedium">
        {t("personalData")}
      </Typography>
      {/* {type === "user-data" && (
        <Box className="!w-full !mt-5">
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskGX">
            Who am I representing?
          </Typography>
          <Box className="!flex !gap-x-10">
            <Controller
              name="authorizedRepresendter"
              control={control}
              defaultValue={options[0]}
              render={({ field }) => (
                <RadioGroup
                  row
                  value={field.value || options[0]}
                  onChange={(e) => field.onChange(e.target.value)}
                >
                  {options.map((data, id) => (
                    <FormControlLabel
                      key={id}
                      value={data}
                      control={
                        <Radio
                          icon={<RadioButtonUnchecked className="!w-6 !h-6 !text-[--text-color]" />}
                          checkedIcon={<RadioButtonIcon className="!w-6 !h-6" />}
                        />
                      }
                      label={
                        <Typography
                          className="!text-[--text-color] !normal-case Poppins400 !text-sm"
                          htmlFor={`radio-${id}`}
                        >
                          {data}
                        </Typography>
                      }
                    />
                  ))}
                </RadioGroup>
              )}
            />
          </Box>
        </Box>
      )} */}
      <Box className="w-full">
        <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
          {t("emailAddress")}
        </InputLabel>
        <Controller
          name="email"
          control={control}
          defaultValue=""
          render={({ field }) => (
            <TextField
              type="email"
              size="small"
              {...field}
              value={field.value || ""}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              inputProps={{
                maxLength: USER_CONSTANTS.EMAIL.MAX_LENGTH,
              }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px]"
              placeholder="<EMAIL>"
            />
          )}
        />
        {errors.email && (
          <Typography as="span" className="text-sm !text-red-600">
            {errors.email.message}
          </Typography>
        )}
      </Box>
      <Box className="lg:flex gap-x-4 w-full">
        <Box className="w-full">
          <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
            {s("firstName")}
          </InputLabel>
          <Controller
            name="firstName"
            control={control}
            render={({ field }) => (
              <TextField
                type="text"
                size="small"
                {...field}
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                inputProps={{
                  maxLength: USER_CONSTANTS.FIRST_NAME.MAX_LENGTH,
                }}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                    fontFamily: "var(--craftWorkRegular)",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  border: 0,
                }}
                className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px]"
                placeholder="MyName"
              />
            )}
          />
          {errors.firstName && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.firstName.message}
            </Typography>
          )}
        </Box>
        <Box className="w-full">
          <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
            {s("lastName")}
          </InputLabel>
          <Controller
            name="lastName"
            control={control}
            render={({ field }) => (
              <TextField
                type="text"
                size="small"
                {...field}
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                inputProps={{
                  maxLength: USER_CONSTANTS.LAST_NAME.MAX_LENGTH,
                }}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                    fontFamily: "var(--craftWorkRegular)",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  border: 0,
                }}
                className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px]"
                placeholder="MyName"
              />
            )}
          />
          {errors.lastName && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.lastName.message}
            </Typography>
          )}
        </Box>
      </Box>
      <Box className="!w-full lg:flex gap-x-4">
        <Box className="w-full">
          <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
            {t("principalPhone")}
          </InputLabel>
          <Box className="flex !gap-4">
            <CountryCodeDropdown
              selectedValue={selectedCountryCode}
              onSelect={onSelectCountryCode}
            />
            <Controller
              name="phoneNumber"
              control={control}
              render={({ field }) => (
                <TextField
                  size="small"
                  placeholder="0000000000"
                  {...field}
                  InputLabelProps={{ style: { color: "#EFEFEF" } }}
                  sx={{
                    "& input::placeholder": {
                      color: "#EFEFEF",
                      border: 0,
                    },
                    "& input": {
                      color: "#EFEFEF",
                      fontFamily: "var(--craftWorkRegular)",
                    },
                    "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      borderWidth: 0,
                    },
                    "& .MuiOut</Box>linedInput-notchedOutline": {
                      borderWidth: 0,
                    },
                    border: 0,
                  }}
                  autoComplete="off"
                  type="tel"
                  inputProps={{
                    //pattern: "\\(\\d{3}\\) \\d{3}-\\d{4}",
                    maxLength: 14,
                  }}
                  className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px]"
                />
              )}
            />
            {errors.phoneNumber && (
              <Typography as="span" className="text-sm !text-red-600">
                {errors.phoneNumber.message}
              </Typography>
            )}
          </Box>
        </Box>
        <Box className="w-full">
          <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
            {t("alternativePhone")}
          </InputLabel>
          <Box className="flex !gap-4">
            <CountryCodeDropdown
              selectedValue={selectedCountryCode}
              onSelect={onSelectCountryCode}
            />
            <Controller
              name="firstAlternativePhoneNumber"
              control={control}
              render={({ field }) => (
                <TextField
                  size="small"
                  placeholder="0000000000"
                  {...field}
                  InputLabelProps={{ style: { color: "#EFEFEF" } }}
                  sx={{
                    "& input::placeholder": {
                      color: "#EFEFEF",
                      border: 0,
                    },
                    "& input": {
                      color: "#EFEFEF",
                      fontFamily: "var(--craftWorkRegular)",
                    },
                    "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      borderWidth: 0,
                    },
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderWidth: 0,
                    },
                    border: 0,
                  }}
                  autoComplete="off"
                  type="tel"
                  inputProps={{
                    //pattern: "\\(\\d{3}\\) \\d{3}-\\d{4}",
                    maxLength: 14,
                  }}
                  className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px]"
                />
              )}
            />
            {errors.firstAlternativePhoneNumber && (
              <Typography as="span" className="text-sm !text-red-600">
                {errors.firstAlternativePhoneNumber.message}
              </Typography>
            )}
          </Box>
        </Box>
      </Box>
      <Box className="lg:w-[48%] w-full">
        <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
          {t("alternativePhone")}
        </InputLabel>
        <Box className="flex !gap-4">
          <CountryCodeDropdown selectedValue={selectedCountryCode} onSelect={onSelectCountryCode} />
          <Controller
            name="secondAlternativePhoneNumber"
            control={control}
            render={({ field }) => (
              <TextField
                size="small"
                placeholder="0000000000"
                {...field}
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                    fontFamily: "var(--craftWorkRegular)",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  border: 0,
                }}
                autoComplete="off"
                type="tel"
                inputProps={{
                  //pattern: "\\(\\d{3}\\) \\d{3}-\\d{4}",
                  maxLength: 14,
                }}
                className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px]"
              />
            )}
          />
          {errors.secondAlternativePhoneNumber && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.secondAlternativePhoneNumber.message}
            </Typography>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default AccountInformationPersonalData;
