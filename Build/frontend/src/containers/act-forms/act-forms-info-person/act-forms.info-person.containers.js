"use client";
import { useEffect, useState } from "react";
import {
  Box,
  InputAdornment,
  InputLabel,
  TextField,
  Typography,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@mui/material";
import React from "react";
import SpotifyIcon from "@/assets/svg/Spotify.svg";
import FacebookOutlinedIcon from "@mui/icons-material/FacebookOutlined";
import { Instagram, YouTube } from "@mui/icons-material";
import SoundCloudIcon from "@/assets/svg/SoundCloud.svg";
import { ProfileFooter } from "@/common/profile";
import { useForm, Controller, useWatch } from "react-hook-form";
import { useRouter } from "next/navigation";
import { actInfoPersonValidation } from "@/validation/act/act.validation";
import { yupResolver } from "@hookform/resolvers/yup";
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import RadioButtonUncheckedIcon from "@mui/icons-material/RadioButtonUnchecked";
import RadioButtonIcon from "@/assets/svg/RadioButton.svg";
import { setPreviewData, createActProfileInfo } from "@/store/slice/act/act.slice";
import { useLocale, useTranslations } from "next-intl";
import { ACT_CONSTANTS } from "@/validation/auth/constants";

const ActInfoPersonForm = () => {
  const p = useTranslations("profileFooter");
  const t = useTranslations("actInfoPerson");
  const s = useTranslations("editActCommon");
  const label = [s("children"), s("adult")];
  const lang = useLocale();
  const { showSnackbar } = useSnackbar();
  const { previewData } = useSelector((state) => state.act);
  const profileId = previewData?.actInfo?.profileId;

  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();
  const router = useRouter();
  const resolver = yupResolver(actInfoPersonValidation);
  const {
    handleSubmit,
    register,
    control,
    formState: { errors, defaultValues },
  } = useForm({
    resolver,
    mode: "onChange",
    defaultValues: {
      bio: previewData?.actInfoSocial?.bio || "",
      spotifyLink: previewData?.actInfoSocial?.spotifyLink || "",
      soundCloudLink: previewData?.actInfoSocial?.soundCloudLink || "",
      instagramLink: previewData?.instagramLink || "",
      youtubeLink: previewData?.actInfoSocial?.youtubeLink || "",
      facebookLink: previewData?.actInfoSocial?.facebookLink || "",
      suitableForAdultsOrChildren: previewData?.actInfoSocial?.suitableForAdultsOrChildren || false,
    },
  });

  const isPreviewDataEmpty = Object.values(defaultValues).every((value) =>
    typeof value === "string" ? value.trim() === "" : !value,
  );

  const preViewdata = useWatch({ control });

  const handleActInfo = (data) => {
    const socialMediaLinks = [];
    if (data.spotifyLink) {
      socialMediaLinks.push(`https:/open.spotify.com/${data.spotifyLink}`);
    }
    if (data.soundCloudLink) {
      socialMediaLinks.push(`https:/soundcloud.com/${data.soundCloudLink}`);
    }
    if (data.instagramLink) {
      socialMediaLinks.push(`https:/instagram.com/${data.instagramLink}`);
    }
    if (data.youtubeLink) {
      socialMediaLinks.push(`https:/youtube.com/${data.youtubeLink}`);
    }
    if (data.facebookLink) {
      socialMediaLinks.push(`https:/facebook/com/${data.facebookLink}`);
    }

    const actInfoData = {
      bio: data.bio,
      suitableForAdultsOnly:
        data.suitableForAdultsOrChildren === "Suitable for adults only" ? true : false,
      suitableForChildren:
        data.suitableForAdultsOrChildren === "Suitable for children" ? true : false,
      socialMediaLinks: socialMediaLinks,
    };
    setLoading(true);
    dispatch(
      isPreviewDataEmpty === true
        ? createActProfileInfo({ profileId, data: actInfoData })
        : createActProfileInfo({ profileId, data: actInfoData }),
    )
      .unwrap()
      .then((response) => {
        if (response.status === 200 || response.status === 208) {
          setLoading(false);

          if (
            previewData?.actInfo?.profileType === "VIRTUAL_VENUE_PROFILE" ||
            previewData?.actInfo?.profileType === "VENUE_PROFILE"
          ) {
            router.push(`/${lang}/media`);
          } else {
            router.push(`/${lang}/entertainment-type`);
          }
        }
      })
      .catch((error) => {
        setLoading(false);
        showSnackbar(error, "error");
      });
  };

  useEffect(() => {
    dispatch(setPreviewData({ actInfoSocial: preViewdata }));
  }, [preViewdata]);

  return (
    <form
      className="lg:!mt-12 md:!mt-12 !mt-4 !mb-28 !max-w-[750px]"
      onSubmit={handleSubmit(handleActInfo)}
    >
      <Box className="!w-full">
        {/** Bio */}
        <Box>
          <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !py-2">
            {t("ActInfoPersonForm.Bio")}
          </InputLabel>
          <TextField
            type="text"
            size="small"
            multiline
            rows={5}
            placeholder={t("ActInfoPersonForm.Bio")}
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            inputProps={{
              maxLength: ACT_CONSTANTS.ACT_INFO.BIO_MAX_LENGTH
            }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& .MuiOutlinedInput-root": {
                color: "var(--text-color)",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            name="bio"
            className="!border !w-full !h-[140px] !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !mb-5"
            {...register("bio")}
          />
          {errors && errors.bio && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.bio.message}
            </Typography>
          )}
        </Box>

        {previewData?.actInfo?.profileType === "ACT_PROFILE" ||
          (previewData?.actInfo?.profileType === "VIRTUAL_ACT_PROFILE" && (
            <Box className="!w-full">
              {/** Radio button */}
              <Controller
                name="suitableForAdultsOrChildren"
                control={control}
                render={({ field }) => (
                  <RadioGroup
                    row
                    value={field.value}
                    onChange={(e) => field.onChange(e.target.value)}
                  >
                    {label.map((data, id) => (
                      <FormControlLabel
                        key={id}
                        value={data}
                        control={
                          <Radio
                            icon={
                              <RadioButtonUncheckedIcon className="!w-6 !h-6 !text-[--text-color]" />
                            }
                            checkedIcon={<RadioButtonIcon className="!w-6 !h-6" />}
                          />
                        }
                        label={
                          <Typography
                            className="!text-[--text-color] !normal-case Poppins400 !text-sm"
                            htmlFor={`radio-${id}`}
                          >
                            {data}
                          </Typography>
                        }
                      />
                    ))}
                  </RadioGroup>
                )}
              />
            </Box>
          ))}
        {/** social media links */}
        <Box className="!mt-14">
          <Typography className="!text-2xl !text-[--text-color] CraftworkGroteskMedium">
            {t("ActInfoPersonForm.Social")}
          </Typography>
          <Box className="!w-full">
            <TextField
              type="text"
              size="small"
              placeholder={t("ActInfoPersonForm.YourLink")}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start" style={{ cursor: "pointer", marginRight: 0 }}>
                    <SpotifyIcon className="!w-6 !h-6 !mr-4" />
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                      https:/open.spotify.com/
                    </Typography>
                  </InputAdornment>
                ),
              }}
              inputProps={{
                maxLength: ACT_CONSTANTS.ACT_INFO.SPOTIFY_LINK_MAX_LENGTH
              }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !my-5"
              name="spotifyLink"
              {...register("spotifyLink")}
            />
            <TextField
              type="text"
              size="small"
              placeholder={t("ActInfoPersonForm.YourLink")}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start" style={{ cursor: "pointer", marginRight: 0 }}>
                    <SoundCloudIcon className="!w-6 !h-6 !mr-4" />
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                      https:/soundcloud.com/
                    </Typography>
                  </InputAdornment>
                ),
              }}
              inputProps={{
                maxLength: ACT_CONSTANTS.ACT_INFO.SOUNDCLOUD_LINK_MAX_LENGTH
              }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              name="soundCloudLink"
              className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !mb-5"
              {...register("soundCloudLink")}
            />
            <TextField
              type="text"
              size="small"
              placeholder={t("ActInfoPersonForm.YourLink")}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start" style={{ cursor: "pointer", marginRight: 0 }}>
                    <Instagram className="!w-6 !h-6 !mr-4 !text-[--text-color]" />
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                      https:/instagram.com/
                    </Typography>
                  </InputAdornment>
                ),
              }}
              inputProps={{
                maxLength: ACT_CONSTANTS.ACT_INFO.INSTAGRAM_LINK_MAX_LENGTH
              }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              name="instagramLink"
              className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !mb-5"
              {...register("instagramLink")}
            />
            <TextField
              type="text"
              size="small"
              placeholder={t("ActInfoPersonForm.YourLink")}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start" style={{ cursor: "pointer", marginRight: 0 }}>
                    <YouTube className="!w-6 !h-6 !mr-4 !text-[--text-color]" />
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                      https:/youtube.com/
                    </Typography>
                  </InputAdornment>
                ),
              }}
              inputProps={{
                maxLength: ACT_CONSTANTS.ACT_INFO.YOUTUBE_LINK_MAX_LENGTH
              }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              name="youtubeLink"
              className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !mb-5"
              {...register("youtubeLink")}
            />
            <TextField
              type="text"
              size="small"
              placeholder={t("ActInfoPersonForm.YourLink")}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start" style={{ cursor: "pointer", marginRight: 0 }}>
                    <FacebookOutlinedIcon className="!w-6 !h-6 !mr-4 !text-[--text-color]" />
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                      https:/facebook.com/
                    </Typography>
                  </InputAdornment>
                ),
              }}
              inputProps={{
                maxLength: ACT_CONSTANTS.ACT_INFO.FACEBOOK_LINK_MAX_LENGTH
              }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              name="facebookLink"
              {...register("facebookLink")}
              className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !mb-5"
            />
          </Box>
        </Box>
      </Box>
      <ProfileFooter backurl={`/${lang}/location`} loading={loading} buttonName={p("Next")} />
    </form>
  );
};

export default ActInfoPersonForm;
