"use client";
import { useEffect } from "react";
import { CheckBox, Dropdown, Loader } from "@/component";
import { Add, Remove } from "@mui/icons-material";
import {
  Box,
  InputLabel,
  Radio,
  TextField,
  Typography,
  FormControlLabel,
  RadioGroup,
} from "@mui/material";
import React, { useState } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import RadioButtonUncheckedIcon from "@mui/icons-material/RadioButtonUnchecked";
import RadioButtonIcon from "@/assets/svg/RadioButton.svg";
import ActInfoIAm from "@/ui/act-info/act-info.iam/act-info.iam.ui";
import ActInfoAnother from "@/ui/act-info/act-info.another/act-info.another.ui";
import { ProfileFooter } from "@/common/profile";
import { useRouter } from "next/navigation";
import { useSelector, useDispatch } from "react-redux";
import { actLanguages, actRoles, createProfile, getProfile } from "@/store/slice/act/act.slice";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { actInformationValidation } from "@/validation/act/act.validation";
import { yupResolver } from "@hookform/resolvers/yup";
import { setPreviewData, updateProfile } from "@/store/slice/act/act.slice";
import { getCurrentUserEmail } from "@/store/slice/auth/login.auth.slice";
//import { resetPreviewData } from "@/store/slice/act/act.slice";

import { useLocale, useTranslations } from "next-intl";
import { ACT_CONSTANTS } from "@/validation/auth/constants";

const ActInfoForm = () => {
  const p = useTranslations("profileFooter");
  const t = useTranslations("actInformation");
  const s = useTranslations("CreateProfiles");
  const a = useTranslations("dropdown");
  const options = [t("ActInfoForm.IAm"), t("ActInfoForm.Another")];
  const lang = useLocale();
  const { previewData, profileId } = useSelector((state) => state.act);
  const { currentUser } = useSelector((state) => state.login);
  const resolver = yupResolver(actInformationValidation({ previewData }));
  const { showSnackbar } = useSnackbar();
  const router = useRouter();
  const dispatch = useDispatch();

  const {
    handleSubmit,
    control,
    formState: { errors },
    register,
    reset,
    watch,
    setValue,
  } = useForm({
    resolver,
    defaultValues: {
      profileName: previewData?.actInfo?.profileName || "",
      profileRole: previewData?.actInfo?.profileRole || "",
      performanceLanguages: previewData?.actInfo?.performanceLanguages || [],
      communicationLanguages: previewData?.actInfo?.communicationLanguages || [],
      preferredLanguage: previewData?.actInfo?.preferredLanguage || "",
      authorizedRepresendter: previewData?.authorizedRepresendter || t("ActInfoForm.IAm"),
      authorizedRepresenterEmail: previewData?.actInfo?.authorizedRepresenterEmail || "",
      authorizedRepresenterPhoneNumber:
        previewData?.actInfo?.authorizedRepresenterPhoneNumber || "",
      useMyEmail: previewData?.actInfo?.useMyEmail || false,
      profileEmail: previewData?.actInfo?.profileEmail || "",
      numMembers: previewData?.actInfo?.numMembers || 1,
      profileType: "",
      virtualContactDto: {},
      authorizedRepresenterPhoneNumberCountryCode: "",
    },
    mode: "onChange",
  });

  const authorizedRepresendter = watch("authorizedRepresendter");

  const { loading, roles: actRole, languages: actLanguage } = useSelector((state) => state.act);

  const [Loading, setLoading] = useState(false);

  {
    /** handle next functionality */
  }
  const handleProfile = (data) => {
    setLoading(true);

    let profileType = "ACT_PROFILE";
    if (previewData?.profile?.option === "Venue") {
      profileType =
        data.authorizedRepresendter === t("ActInfoForm.IAm")
          ? "VENUE_PROFILE"
          : "VIRTUAL_VENUE_PROFILE";
    } else {
      profileType =
        data.authorizedRepresendter === t("ActInfoForm.IAm")
          ? "ACT_PROFILE"
          : "VIRTUAL_ACT_PROFILE";
    }

    const actInfoData = {
      profileName: data.profileName,
      profileRole: data.profileRole,
      numMembers: data.numMembers,
      profileId: profileId,
      owner: true,
      useMyEmail: data.useMyEmail,
      performanceLanguages: data.performanceLanguages,
      communicationLanguages: data.communicationLanguages,
      preferredLanguage: data.preferredLanguage,
      profileEmail: watch("useMyEmail") ? currentUser?.email : data.profileEmail,
      profileType: profileType,
      virtualContactDto:
        data.authorizedRepresendter === t("ActInfoForm.IAm")
          ? {}
          : {
              contactName: data?.authorizedRepresenter,
              contactEmail: data?.authorizedRepresenterEmail,
              //contactPhone: `${data.authorizedRepresenterPhoneNumberCountryCode}` + data.authorizedRepresenterPhoneNumber,
              contactPhone: data?.authorizedRepresenterPhoneNumber,
            },
    };
    //console.log(data, actInfoData,"actInfoData");
    dispatch(setPreviewData({ ...previewData, actInfo: actInfoData }));
    dispatch(
      profileId ? updateProfile({ data: actInfoData, profileId }) : createProfile(actInfoData),
    )
      .unwrap()
      .then((response) => {
        setLoading(false);
        showSnackbar(response.data.message, "success");
        router.push(`/${lang}/location`);
      })
      .catch((error) => {
        //dispatch(resetPreviewData());
        setLoading(false);
        showSnackbar(error, "error");
      });

    // dispatch(actValidName(actInfoData.profileName))
    //   .unwrap()
    //   .then((response) => {
    //     if (response.status === 200) {

    //     }
    //   })
    //   .catch((error) => {
    //     setLoading(false);
    //     showSnackbar(error, "error");
    //   });
  };

  {
    /** fetch roles and languages */
  }

  useEffect(() => {
    dispatch(actLanguages());
    dispatch(actRoles());
    dispatch(getCurrentUserEmail());
  }, []);

  /**  get user profile */
  useEffect(() => {
    if (profileId) {
      dispatch(getProfile(profileId))
        .unwrap()
        .then((response) => {
          if (response.status == 200) {
            const profileData = response.data.data;
            dispatch(setPreviewData({ ...previewData, actInfo: profileData }));
            reset((prev) => ({ ...prev, ...profileData }));
          }
        })
        .catch(() => {});
    }
  }, [profileId, reset]);

  if (loading) {
    return <Loader />;
  }
  return (
    <form
      onSubmit={handleSubmit(handleProfile)}
      className="lg:!mt-12 md:!mt-12 !mt-4 !mb-28 !max-w-[750px]"
    >
      <Box className="flex !gap-x-5">
        <Box className="!w-full">
          <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
            {previewData?.profile?.option === s("Act")
              ? t("ActInfoForm.NameOfAct")
              : "Name of Venue"}
          </InputLabel>
          <TextField
            type="text"
            size="small"
            placeholder={previewData?.profile?.option === s("Act") ? "Act name" : "Venue name"}
            name="profileName"
            inputProps={{
              maxLength: ACT_CONSTANTS.PROFILE_NAME.MAX_LENGTH,
            }}
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& input": {
                color: "#EFEFEF",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !mb-5"
            {...register("profileName")}
          />
          {errors && errors.profileName && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.profileName.message}
            </Typography>
          )}
        </Box>
        {previewData?.profile?.option === s("Act") && (
          <Box className="!w-[70%]">
            <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
              {t("ActInfoForm.NumberOfMembers")}
            </InputLabel>
            <Controller
              name="numMembers"
              control={control}
              defaultValue={1}
              render={({ field }) => (
                <Box className="!border !w-full !flex !justify-between  !py-3 CraftworkGroteskRegular !border-white rounded-[2px] !mb-5">
                  <Remove
                    className="!text-[--text-color] cursor-pointer !mx-2"
                    onClick={() => {
                      field.onChange(field.value - 1); // Decrement count
                    }}
                  />
                  <Typography className="!text-sm !text-[--text-color] CraftworkGroteskRegular">
                    {field.value} {/* Use field.value as the value */}
                  </Typography>
                  <Add
                    className="!text-[--text-color] cursor-pointer !mx-2"
                    onClick={() => {
                      field.onChange(field.value + 1); // Increment count
                    }}
                  />
                </Box>
              )}
            />
          </Box>
        )}
      </Box>
      <Box className="!flex lg:!flex-row md:!flex-row !flex-col !gap-x-5">
        <Box className="!w-full !mt-5">
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskGX">
            {previewData?.profile?.option === "Venue"
              ? "Who is authorized representer of that venue?"
              : t("ActInfoForm.Authorize")}
          </Typography>
          <Box className="!flex !gap-x-10">
            <Controller
              name="authorizedRepresendter"
              control={control}
              render={({ field }) => (
                <RadioGroup
                  row
                  value={field.value || ""}
                  onChange={(e) => field.onChange(e.target.value)}
                >
                  {options.map((data, id) => (
                    <FormControlLabel
                      key={id}
                      value={data}
                      control={
                        <Radio
                          icon={
                            <RadioButtonUncheckedIcon className="!w-6 !h-6 !text-[--text-color]" />
                          }
                          checkedIcon={<RadioButtonIcon className="!w-6 !h-6" />}
                        />
                      }
                      label={
                        <Typography
                          className="!text-[--text-color] !normal-case Poppins400 !text-sm"
                          htmlFor={`radio-${id}`}
                        >
                          {data}
                        </Typography>
                      }
                    />
                  ))}
                </RadioGroup>
              )}
            />
          </Box>
        </Box>
        {previewData?.profile?.option === s("Act") && (
          <Box className="lg:!w-[70%] md:!w-[70%] !w-full">
            <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
              {t("ActInfoForm.Role")}
            </InputLabel>
            <Box className="!border !border-[text-color] !rounded-[2px] !mt-2 !py-1">
              <Controller
                name="profileRole"
                control={control}
                render={({ field }) => (
                  <Dropdown
                    onSelect={field.onChange}
                    options={actRole || []}
                    selectedValue={field.value}
                    title={a("selectRole")}
                    className="!text-[--text-color] !w-full"
                  />
                )}
              />
            </Box>
            {errors && errors.profileRole && (
              <Typography as="span" className="text-sm !text-red-600">
                {errors.profileRole.message}
              </Typography>
            )}
          </Box>
        )}
      </Box>
      {authorizedRepresendter === t("ActInfoForm.IAm") ? (
        <ActInfoIAm
          control={control}
          currentUserEmail={watch("useMyEmail") && currentUser?.email}
        />
      ) : (
        <ActInfoAnother register={register} errors={errors} setValue={setValue} />
      )}
      {authorizedRepresendter === t("ActInfoForm.IAm") && !watch("useMyEmail") && (
        <Box className="!w-full">
          <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
            {t("ActInfoForm.ProfileEmail")}
          </InputLabel>
          <TextField
            type="text"
            size="small"
            placeholder={t("ActInfoForm.EnterEmail")}
            name="profileEmail"
            inputProps={{
              maxLength: ACT_CONSTANTS.PROFILE_EMAIL.MAX_LENGTH,
            }}
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& input": {
                color: "#EFEFEF",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !mb-5"
            {...register("profileEmail")}
          />
          {errors && errors.profileEmail && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.profileEmail.message}
            </Typography>
          )}
        </Box>
      )}

      {/** performance language */}
      {previewData?.profile?.option === "Act" && (
        <Box className="!w-full !my-5">
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskGX">
            {t("ActInfoForm.Performance")}
          </Typography>
          <Box className="lg:!flex lg:!flex-wrap !grid md:!grid-cols-3 !grid-cols-2 gap-x-8">
            <Controller
              name="performanceLanguages"
              control={control}
              defaultValue={[]}
              render={({ field }) => (
                <>
                  {actLanguage?.map((language, index) => (
                    <Box key={index} className="flex items-center">
                      <CheckBox
                        className="!max-w-[24px]"
                        sx={{ color: "#EFEFEF", marginRight: "5px" }}
                        checked={field.value.includes(language)}
                        onChange={(e) => {
                          const newSelectedLanguages = e.target.checked
                            ? [...field.value, language]
                            : field.value.filter(
                                (selectedLanguage) => selectedLanguage !== language,
                              );
                          field.onChange(newSelectedLanguages);
                        }}
                      />
                      <label
                        htmlFor={`language-checkbox-${index}`}
                        className="!text-[--text-color] !text-sm CraftworkGroteskRegular cursor-pointer"
                      >
                        {language}
                      </label>
                    </Box>
                  ))}
                </>
              )}
            />
            {errors && errors.performanceLanguages && (
              <Typography as="span" className="text-sm !text-red-600">
                {errors.performanceLanguages.message}
              </Typography>
            )}
          </Box>
        </Box>
      )}
      {/** communication language */}
      <Box className="!w-full my-3">
        <Typography className="!text-[--text-color] !text-sm CraftworkGroteskGX">
          {previewData?.profile?.option === "Venue"
            ? "Language support"
            : t("ActInfoForm.Communication")}
        </Typography>
        <Box className="lg:!flex lg:!flex-wrap !grid md:!grid-cols-3 !grid-cols-2 gap-x-8">
          <Controller
            name="communicationLanguages"
            control={control}
            defaultValue={[]}
            render={({ field }) => (
              <>
                {actLanguage?.map((language, index) => (
                  <Box key={index} className="flex items-center">
                    <CheckBox
                      className="!max-w-[24px]"
                      sx={{ color: "#EFEFEF", marginRight: "5px" }}
                      checked={field.value.includes(language)}
                      onChange={(e) => {
                        const newSelectedLanguages = e.target.checked
                          ? [...field.value, language]
                          : field.value.filter((selectedLanguage) => selectedLanguage !== language);
                        field.onChange(newSelectedLanguages);
                      }}
                    />
                    <label
                      htmlFor={`language-checkbox-${index}`}
                      className="!text-[--text-color] !text-sm CraftworkGroteskRegular cursor-pointer"
                    >
                      {language}
                    </label>
                  </Box>
                ))}
              </>
            )}
          />
          {errors && errors.communicationLanguages && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.communicationLanguages.message}
            </Typography>
          )}
        </Box>

        {/** prefered language */}

        <Box className="!w-full !my-5">
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskGX">
            {t("ActInfoForm.Preferred")}
          </Typography>
          <Box className="!border !border-[--text-color] !w-full !rounded-[4px] !my-3">
            <Controller
              name="preferredLanguage"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <Dropdown
                  onSelect={field.onChange}
                  options={actLanguage || []}
                  selectedValue={field.value}
                  title={a("selectLanguage")}
                  className="m-[1px] text-[--text-color] !w-full"
                />
              )}
            />
          </Box>
          {errors && errors.preferredLanguage && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.preferredLanguage.message}
            </Typography>
          )}
        </Box>
      </Box>
      <ProfileFooter
        backurl={`/${lang}/create-profiles`}
        buttonName={p("Next")}
        loading={Loading}
      />
    </form>
  );
};

export default ActInfoForm;
