"use client";
import { <PERSON><PERSON><PERSON>, Loader } from "@/component";
import { Box, InputAdornment, InputLabel, TextField, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import { ProfileFooter } from "@/common/profile";
import { useRouter } from "next/navigation";
import { Controller, useForm } from "react-hook-form";
import { actLocationValidation } from "@/validation/act/act.validation";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  setPreviewData,
  createActLocation,
  getActLocation,
  actValidName,
} from "@/store/slice/act/act.slice";
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
//import { actSupportedCountries } from "@/store/slice/act/act.slice";
import { useLocale, useTranslations } from "next-intl";
import AutoCompleteLocation from "@/component/autocomplete/autocomplete-location.component";
import ActNameExists from "@/ui/act-info/act-name-exsists/act-name-exsists";
import { ACT_CONSTANTS } from "@/validation/auth/constants";

const ActLocationForm = () => {
  const t = useTranslations("actLocation");
  const p = useTranslations("profileFooter");
  const lang = useLocale();
  const { showSnackbar } = useSnackbar();
  const { previewData, loading: actLoading } = useSelector((state) => state.act);
  const profileId = previewData?.actInfo?.profileId;
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();
  const resolver = yupResolver(actLocationValidation);
  const router = useRouter();
  const [openPopupCancel, setOpenPopupCancel] = useState(false);

  const handleClosePopup = () => {
    setOpenPopupCancel(false);
  };
  const {
    handleSubmit,
    control,
    register,
    setValue,
    reset,
    formState: { errors, defaultValues },
  } = useForm({
    resolver,
    mode: "onChange",
    defaultValues: {
      state: previewData?.actLocation?.state || "",
      country: previewData?.actLocation?.country || "",
      city: previewData?.actLocation?.city || "",
      streetAddress: previewData?.actLocation?.streetAddress || "",
      zipCode: previewData?.actLocation?.zipCode || "",
      useMyLocation: false,
      canTravelLongDistance: previewData?.actLocation?.canTravelLongDistance || false,
    },
  });
  const isEmpty = Object.values(defaultValues).every((value) =>
    typeof value === "string" ? value.trim() === "" : !value,
  );
  const [locationValue, setLocationValue] = React.useState(
    previewData?.actLocation
      ? `${previewData?.actLocation?.city}, ${previewData?.actLocation?.state}, ${previewData?.actLocation?.country}`
      : null,
  );

  useEffect(() => {
    if (locationValue) {
      setValue("city", locationValue.city);
      setValue("state", locationValue.state);
      setValue("country", locationValue.country);
    }
  }, [locationValue]);

  const handleActLocation = (data) => {
    const actLocationData = {
      country: data.country,
      city: data.city,
      state: data.state,
      streetAddress: data.streetAddress,
      zipCode: data.zipCode,
      // useMyLocation: data.useMyLocation,
      canTravelLongDistance: data.canTravelLongDistance,
    };
    setLoading(true);
    dispatch(
      previewData?.actLocation
        ? createActLocation({ data: actLocationData, profileId })
        : createActLocation({ data: actLocationData, profileId }),
    )
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setLoading(false);
          dispatch(setPreviewData({ actLocation: actLocationData }));
          //showSnackbar(response.data.message, "success");
          onLocationHandler();
        } else if (response.status === 208) {
          setLoading(false);
          showSnackbar(response.data.message, "error");
          // change the url to act-info-person in case of 208
          onLocationHandler();
        }
      })
      .catch((error) => {
        console;
        setLoading(false);
        showSnackbar(error, "error");
      });
  };

  const onLocationHandler = () => {
    dispatch(actValidName({ name: previewData?.actInfo?.profileName, profileId }))
      .unwrap()
      .then((response) => {
        if (response.data.data) {
          redirectLocationButtonRoute();
        } else {
          setOpenPopupCancel(true);
        }
      })
      .catch((error) => {
        //setLoading(false);
        showSnackbar(error, "error");
      });
    //router.push(`/${lang}/create-profiles/act-location`);
  };

  const redirectLocationButtonRoute = () => {
    if (
      previewData?.actInfo?.profileType === "ACT_PROFILE" ||
      previewData?.actInfo?.profileType === "VENUE_PROFILE"
    ) {
      router.push(`/${lang}/info-person`);
    }

    if (
      previewData?.actInfo?.profileType === "VIRTUAL_ACT_PROFILE" ||
      previewData?.actInfo?.profileType === "VIRTUAL_VENUE_PROFILE"
    ) {
      router.push(`/${lang}/media`);
    }
  };

  {
    /** get location */
  }
  useEffect(() => {
    if (!isEmpty) {
      dispatch(getActLocation(profileId))
        .unwrap()
        .then((response) => {
          if (response.status === 200) {
            const actLocation = response.data.data;

            dispatch(setPreviewData({ actLocation: actLocation }));
            reset((prev) => ({ ...prev, ...actLocation }));
          }
        })
        .catch(() => {});
    }
  }, [isEmpty]);

  if (actLoading) {
    return <Loader />;
  }

  return (
    <>
      <form
        className="lg:!mt-12 md:!mt-12 !mt-4 !mb-28 !max-w-[750px]"
        onSubmit={handleSubmit(handleActLocation)}
      >
        {/** use my location */}
        {/* <Controller
        name="useMyLocation"
        control={control}
        render={({ field }) => (
          <Box className="flex items-center !mb-2">
            <CheckBox
              className="!max-w-[24px]"
              sx={{ color: "#EFEFEF", marginRight: "5px" }}
              checked={field.value}
              onChange={(e) => field.onChange(e.target.checked)}
            />
            <label
              htmlFor="location"
              className="!text-[--text-color] !text-sm CraftworkGroteskRegular cursor-pointer"
            >
              {t("ActLocationForm.UseLocation")}
            </label>
          </Box>
        )}
      /> */}
        <Box className="w-full">
          <AutoCompleteLocation
            value={locationValue}
            setValue={setLocationValue}
            textFieldClass="border !border-[--text-color] rounded-[2px]"
            className="w-full"
            showFilter={true}
            readOnly={!!locationValue}
            inputProps={{
              maxLength: ACT_CONSTANTS.LOCATION.CITY_MAX_LENGTH,
            }}
          />

          {errors && (errors.city || errors.state || errors.country) && (
            <Typography as="span" className="text-sm !text-red-600">
              Please select a valid location
            </Typography>
          )}
        </Box>

        {/** street Address */}
        <Box className="!w-full !mt-6">
          <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX">
            {t("ActLocationForm.StreetAddress")}
          </InputLabel>
          <TextField
            type="text"
            size="small"
            placeholder={t("ActLocationForm.addressLine")}
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start" style={{ cursor: "pointer" }}>
                  <LocationSvg className="!w-6 !h-6" />
                </InputAdornment>
              ),
              inputProps: {
                maxLength: ACT_CONSTANTS.LOCATION.STREET_ADDRESS_MAX_LENGTH,
              },
            }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& input": {
                color: "#EFEFEF",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            name="streetAddress"
            className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !my-2"
            {...register("streetAddress")}
          />
          {errors && errors.streetAddress && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.streetAddress.message}
            </Typography>
          )}
        </Box>
        {/** zip code */}
        <Box className="!w-full !mt-6">
          <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX">
            {t("ActLocationForm.Zip")}
          </InputLabel>
          <TextField
            type="text"
            size="small"
            placeholder="000000"
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            InputProps={{
              inputProps: {
                maxLength: ACT_CONSTANTS.LOCATION.ZIP_CODE_MAX_LENGTH,
              },
            }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& input": {
                color: "#EFEFEF",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !my-2"
            name="zipCode"
            {...register("zipCode")}
          />
          {errors && errors.zipCode && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.zipCode.message}
            </Typography>
          )}
        </Box>

        {/** can travel long distance */}
        {previewData?.actInfo?.profileType === "ACT_PROFILE" && (
          <Controller
            control={control}
            name="canTravelLongDistance"
            render={({ field }) => (
              <Box className="flex items-center !mb-4">
                <CheckBox
                  className="!max-w-[24px]"
                  sx={{ color: "#EFEFEF", marginRight: "5px" }}
                  checked={field.value}
                  onChange={(e) => field.onChange(e.target.checked)}
                />
                <label
                  htmlFor="location"
                  className="!text-[--text-color] !text-sm CraftworkGroteskRegular cursor-pointer"
                >
                  {t("ActLocationForm.LongDistance")}
                </label>
              </Box>
            )}
          />
        )}
        <ProfileFooter
          backurl={`/${lang}/act-information`}
          loading={loading}
          buttonName={p("Next")}
          footerType="location"
          profileId={profileId}
        />
      </form>

      <ActNameExists
        open={openPopupCancel}
        handleClose={handleClosePopup}
        profileId={profileId}
        actName={previewData?.actInfo?.profileName}
        profileData={{
          profileName: previewData?.actInfo?.profileName,
          profileEmail: previewData?.actInfo?.profileEmail,
          profileRole: previewData?.actInfo?.profileRole,
          performanceLanguages: previewData?.actInfo?.performanceLanguages ?? [],
          communicationLanguages: previewData?.actInfo?.communicationLanguages ?? [],
          preferredLanguage: previewData?.actInfo?.preferredLanguage ?? [],
          profileType: previewData?.actInfo?.profileType,
        }}
        redirectLocationButtonRoute={redirectLocationButtonRoute}
      />
    </>
  );
};

export default ActLocationForm;
