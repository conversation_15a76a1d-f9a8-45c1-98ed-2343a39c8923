"use client";
import { CheckBox, Dropdown, Loader } from "@/component";
import { Box, FormControlLabel, Radio, RadioGroup, TextField, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { ProfileFooter } from "@/common/profile";
import { Controller, useForm, useWatch } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useSelector, useDispatch } from "react-redux";
import {
  getActPayment,
  createActPayment,
  updateActPayment,
  NotForRentPayment,
} from "@/store/slice/act/act.slice";
import { yupResolver } from "@hookform/resolvers/yup";
import { actPaymentValidation } from "@/validation/act/act.validation";
import { setPreviewData } from "@/store/slice/act/act.slice";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { useLocale, useTranslations } from "next-intl";
import { RadioButtonUnchecked } from "@mui/icons-material";
import RadioButtonIcon from "@/assets/svg/RadioButton.svg";
import { ACT_CONSTANTS } from "@/validation/auth/constants";
const ActPaymentForm = () => {
  const t = useTranslations("actPayment.actPaymentForm");
  const s = useTranslations("CreateProfiles");
  const p = useTranslations("profileFooter");
  const a = useTranslations("dropdown");
  const lang = useLocale();
  const label = ["For rent", "Not for rent"];
  const { showSnackbar } = useSnackbar();
  const router = useRouter();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const { actPayment, loading: actLoading, previewData } = useSelector((state) => state.act);
  const profileId = previewData?.actInfo?.profileId;
  const resolver = yupResolver(actPaymentValidation({ previewData }));

  const {
    handleSubmit,
    control,
    register,
    formState: { errors, defaultValues },
    setValue,
    watch,
  } = useForm({
    resolver,
    defaultValues: {
      paymentMethod: previewData?.actPayment?.paymentMethod || [],
      typicalPrice: previewData?.actPayment?.typicalPrice || "",
      standardPriceCurrency: previewData?.actPayment?.standardPriceCurrency || "",
      standardPricePer: previewData?.actPayment?.standardPricePer || "",
      charityPrice: previewData?.actPayment?.charityPrice || "",
      minimalPriceCurrency: previewData?.actPayment?.minimalPriceCurrency || "",
      minimalPricePer: previewData?.actPayment?.minimalPricePer || "",
      forRentOrNot: previewData?.actPayment?.forRentOrNot || "Not for rent",
    },
    mode: "onChange",
  });
  const isPreviewDataEmpty = Object.values(defaultValues).every((value) => {
    if (Array.isArray(value)) {
      return value.length === 0; // Check if array is empty
    } else {
      return value === ""; // Check if other values are empty strings
    }
  });

  const handleActPayment = (data) => {
    const actPaymentData = {
      currency: data.standardPriceCurrency,
      typicalPrice: Number(data.typicalPrice) || 0,
      minimumPrice: Number(data.charityPrice) || 0,
      acceptablePaymentMethods: data.paymentMethod,
      minPriceChargingType: data.minimalPricePer.toUpperCase(),
      typicalPriceChargingType: data.standardPricePer.toUpperCase(),
    };

    if (data.forRentOrNot === "For rent") {
      actPaymentData["forRent"] = true;
    }

    setLoading(true);

    if (previewData?.profile?.option !== s("Act") && data.forRentOrNot === "Not for rent") {
      // Extra condition: Dispatch for Not For Rent
      dispatch(
        NotForRentPayment({ data: data.forRentOrNot === "For rent" ? true : false, profileId }),
      )
        .unwrap()
        .then((response) => {
          if (response.status === 200) {
            setLoading(false);
            router.push(`/${lang}/review`);
          }
        })
        .catch((error) => {
          setLoading(false);
          showSnackbar(error, "error");
        });
    } else {
      // Continue with existing flow for creating or updating payment
      dispatch(
        isPreviewDataEmpty
          ? createActPayment({ data: actPaymentData, profileId })
          : updateActPayment({ data: actPaymentData, profileId }),
      )
        .unwrap()
        .then((response) => {
          if (response.status === 200) {
            setLoading(false);
            router.push(`/${lang}/review`);
          }
        })
        .catch((error) => {
          setLoading(false);
          showSnackbar(error, "error");
        });
    }
  };

  const actPaymentData = useWatch({ control });

  useEffect(() => {
    dispatch(getActPayment());
  }, []);

  useEffect(() => {
    dispatch(setPreviewData({ actPayment: actPaymentData }));
  }, [actPaymentData]);

  if (actLoading) {
    return <Loader />;
  }

  return (
    <form className="!mt-12" onSubmit={handleSubmit(handleActPayment)}>
      {/** typical payment */}

      {previewData?.profile?.option !== s("Act") && (
        <Box className="w-full pb-10">
          <Controller
            name="forRentOrNot"
            control={control}
            defaultValue={""}
            render={({ field }) => (
              <RadioGroup
                row
                value={field.value}
                onChange={(e) => {
                  field.onChange(e.target.value);
                }}
              >
                {label.map((data, id) => (
                  <FormControlLabel
                    key={id}
                    value={data}
                    control={
                      <Radio
                        icon={<RadioButtonUnchecked className="!w-6 !h-6 !text-[--text-color]" />}
                        checkedIcon={<RadioButtonIcon className="!w-6 !h-6" />}
                      />
                    }
                    label={
                      <Typography
                        className="!text-[--text-color] !normal-case CraftworkGroteskHeavy !text-sm"
                        htmlFor={`radio-${id}`}
                      >
                        {data}
                      </Typography>
                    }
                  />
                ))}
              </RadioGroup>
            )}
          />
        </Box>
      )}

      {(previewData?.profile?.option == s("Act") || watch("forRentOrNot") === "For rent") && (
        <Box>
          <Typography className="!text-[--text-color] !mb-2 !text-lg CraftworkGroteskMedium">
            {t("typicalPrice")}
          </Typography>
          <Box className="!w-full !inline-flex !items-center lg:!gap-x-5 !gap-x-3">
            <Box className="w-full">
              <TextField
                size="small"
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                    fontFamily: "var(--craftWorkRegular)",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  border: 0,
                }}
                className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px]"
                {...register("typicalPrice")}
                type="number"
                inputProps={{
                  min: 0,
                  onChange: (e) => {
                    const maxLength = Math.pow(10, ACT_CONSTANTS.PAYMENT.TYPICAL_PRICE_MAX_LENGTH) - 1;
                    if (e.target.value > maxLength) {
                      e.target.value = e.target.value.slice(0, String(maxLength).length);
                    }
                  }
                 }}
              />
              <Box className=" h-8 lg:h-4">
                {errors.typicalPrice ? (
                  <Typography as="span" className="text-sm !text-red-600">
                    {errors.typicalPrice.message}
                  </Typography>
                ) : (
                  <Typography as="span" className="text-sm" style={{ color: "transparent" }}>
                    &nbsp;
                  </Typography>
                )}
              </Box>
            </Box>

            <Box className="!w-4/5">
              <Box className="!border !border-[--text-color]  !w-full !py-1 !rounded-[4px]">
                <Controller
                  name="standardPriceCurrency"
                  control={control}
                  render={({ field }) => (
                    <Dropdown
                      options={actPayment?.currencies}
                      onSelect={(selectedPayment) => {
                        setValue("minimalPriceCurrency", selectedPayment);
                        field.onChange(selectedPayment);
                        errors.minimalPriceCurrency = null;
                      }}
                      selectedValue={field.value}
                      className="!text-[--text-color] !w-full !max-h-[40px]"
                      title={a("selectCurrency")}
                    />
                  )}
                />
              </Box>
              <Box className=" h-8 lg:h-4">
                {errors.standardPriceCurrency ? (
                  <Typography as="span" className="text-sm !text-red-600">
                    {errors.standardPriceCurrency.message}
                  </Typography>
                ) : (
                  <Typography as="span" className="text-sm" style={{ color: "transparent" }}>
                    &nbsp;
                  </Typography>
                )}
              </Box>
            </Box>

            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular pb-3 !text-center">
              /{t("per")}
            </Typography>
            <Box className="!w-4/5">
              <Box className="!border !border-[--text-color] !w-full !py-1 !rounded-[4px]">
                <Controller
                  name="standardPricePer"
                  control={control}
                  render={({ field }) => (
                    <Dropdown
                      options={actPayment?.paymentOptions}
                      onSelect={field.onChange}
                      selectedValue={field.value}
                      className="!text-[--text-color] !w-full !max-h-[40px]"
                      title={a("selectPer")}
                    />
                  )}
                />
              </Box>
              <Box className=" h-8 lg:h-4">
                {errors.standardPricePer ? (
                  <Typography as="span" className="text-sm !text-red-600">
                    {errors.standardPricePer.message}
                  </Typography>
                ) : (
                  <Typography as="span" className="text-sm" style={{ color: "transparent" }}>
                    &nbsp;
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>
        </Box>
      )}

      {/** charity payment */}
      {(previewData?.profile?.option == s("Act") || watch("forRentOrNot") === "For rent") && (
        <Box className="!mt-9">
          <Typography className="!text-[--text-color] !text-lg CraftworkGroteskMedium">
            {t("minimalPrice")}
          </Typography>
          <Typography className="!text-[--text-color] !mb-4 !text-sm CraftworkGroteskGX">
            {previewData?.profile?.option == s("Act")
              ? t("charity")
              : "Сharity or Cause Event with benefits to Venue"}
          </Typography>
          <Box className="!w-full !inline-flex !items-center lg:!gap-x-5 !gap-x-3">
            <Box className="w-full">
              <TextField
                size="small"
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                    fontFamily: "var(--craftWorkRegular)",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  border: 0,
                }}
                className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px]"
                {...register("charityPrice")}
                type="number"
                inputProps={{
                  min: 0,
                  onChange: (e) => {
                    const maxLength = Math.pow(10, ACT_CONSTANTS.PAYMENT.MINIMAL_PRICE_MAX_LENGTH) - 1;
                    if (e.target.value > maxLength) {
                      e.target.value = e.target.value.slice(0, String(maxLength).length);
                    }
                  }
                }}
              />
              <Box className=" h-8 lg:h-4">
                {errors.charityPrice ? (
                  <Typography as="span" className="text-sm !text-red-600">
                    {errors.charityPrice.message}
                  </Typography>
                ) : (
                  <Typography as="span" className="text-sm" style={{ color: "transparent" }}>
                    &nbsp;
                  </Typography>
                )}
              </Box>
            </Box>
            <Box className="!w-4/5">
              <Box className="!border !border-[--text-color] !w-full !py-1 !rounded-[4px]">
                <Controller
                  name="minimalPriceCurrency"
                  control={control}
                  render={({ field }) => (
                    <Dropdown
                      options={actPayment?.currencies}
                      onSelect={field.onChange}
                      selectedValue={field.value}
                      className="!text-[--text-color] !w-full"
                      disabled={watch("standardPriceCurrency") ? true : false}
                      title={a("selectCurrency")}
                    />
                  )}
                />
              </Box>
              <Box className=" h-8 lg:h-4">
                {errors.minimalPriceCurrency ? (
                  <Typography as="span" className="text-sm !text-red-600">
                    {errors.minimalPriceCurrency.message}
                  </Typography>
                ) : (
                  <Typography as="span" className="text-sm" style={{ color: "transparent" }}>
                    &nbsp;
                  </Typography>
                )}
              </Box>
            </Box>

            <Typography className="!text-[--text-color] !text-sm pb-3 CraftworkGroteskRegular !text-center">
              /{t("per")}
            </Typography>
            <Box className="!w-4/5">
              <Box className="!border !border-[--text-color] !w-full !py-1 !rounded-[4px]">
                <Controller
                  name="minimalPricePer"
                  control={control}
                  render={({ field }) => (
                    <Dropdown
                      options={actPayment?.paymentOptions}
                      onSelect={field.onChange}
                      selectedValue={field.value}
                      title={a("selectPer")}
                      className="!text-[--text-color] !w-full"
                    />
                  )}
                />
              </Box>
              <Box className=" h-8 lg:h-4">
                {errors.minimalPricePer ? (
                  <Typography as="span" className="text-sm !text-red-600">
                    {errors.minimalPricePer.message}
                  </Typography>
                ) : (
                  <Typography as="span" className="text-sm" style={{ color: "transparent" }}>
                    &nbsp;
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>
        </Box>
      )}

      {(previewData?.profile?.option == s("Act") || watch("forRentOrNot") === "For rent") && (
        <Box>
          <Typography className="!text-[--text-color] !text-lg CraftworkGroteskMedium !mt-12">
            {t("paymentMethod")}
          </Typography>
          <Box className="!flex !flex-wrap !gap-4 !w-full">
            <Controller
              name="paymentMethod"
              control={control}
              defaultValue={[]}
              render={({ field }) => (
                <>
                  {actPayment?.paymentMethods?.map((paymentMethod, index) => (
                    <Box key={index} className="flex items-center !gap-x-5">
                      <CheckBox
                        className="!max-w-[24px]"
                        sx={{ color: "#EFEFEF", marginRight: "5px" }}
                        checked={field.value.includes(paymentMethod)}
                        onChange={(e) => {
                          const newSelectedPaymentMethod = e.target.checked
                            ? [...field.value, paymentMethod]
                            : field.value.filter(
                                (selectedPaymentMethod) => selectedPaymentMethod !== paymentMethod,
                              );
                          field.onChange(newSelectedPaymentMethod);
                        }}
                      />
                      <label
                        htmlFor={`payment-checkbox-${index}`}
                        className="!text-[--text-color] !text-lg CraftworkGroteskMedium"
                      >
                        {paymentMethod}
                      </label>
                    </Box>
                  ))}
                </>
              )}
            />
          </Box>
          {errors.paymentMethod ? (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.paymentMethod.message}
            </Typography>
          ) : (
            <Typography as="span" className="text-sm" style={{ color: "transparent" }}>
              &nbsp;
            </Typography>
          )}
        </Box>
      )}

      <ProfileFooter backurl={`/${lang}/media`} loading={loading} buttonName={p("Next")} />
    </form>
  );
};

export default ActPaymentForm;
