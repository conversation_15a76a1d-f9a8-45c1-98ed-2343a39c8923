import { useState, useEffect } from "react";
import { Box, TextField, InputAdornment, Typography } from "@mui/material";
import { Clear } from "@mui/icons-material";
import Search from "@/assets/svg/act-type.svg/Search.svg";
import classNames from "classnames";
import { CheckBox, Loader } from "@/component";
import { ProfileFooter } from "@/common/profile";
import { useForm, Controller } from "react-hook-form";
import { setPreviewData, createActSkills } from "@/store/slice/act/act.slice";
import { useDispatch, useSelector } from "react-redux";
import { getMusicGenre, getMusicGenreSearch } from "@/store/slice/act/act.slice";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useDebounce } from "use-debounce";
import { useLocale, useTranslations } from "next-intl";
export const HighlightSubstring = ({ text, substring, className }) => {
  const words = text.split(/\b/);
  const highlightedText = [];
  if (!text || substring === "") {
    return <label className={className}>{text}</label>;
  } else {
    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      if (word.toLowerCase().indexOf(substring.toLowerCase()) !== -1) {
        highlightedText.push(
          <span key={i} className="!text-[--inprogress-color]">
            {word}
          </span>,
        );
      } else {
        highlightedText.push(<span key={i}>{word}</span>);
      }
    }
    return <label className={className}>{highlightedText}</label>;
  }
};

const MusicGenreForm = () => {
  const t = useTranslations("musicGenre");
  const p = useTranslations("profileFooter");
  const lang = useLocale();
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState("");
  const [debounceSearch] = useDebounce(search, 1000);
  const {
    musicGenres,
    loading: actLoading,
    previewData,
    profileId,
  } = useSelector((state) => state.act);

  const { handleSubmit, control, setValue } = useForm({
    defaultValues: {
      musicGenre: previewData?.musicGenre ?? [],
    },
  });

  const handleActSkills = (data) => {
    const actSkillsData = {
      entertainmentType: {
        name: previewData?.selectedActType,
      },
      musicGenreList: data.musicGenre,
    };
    setLoading(true);
    dispatch(createActSkills({ data: actSkillsData, profileId }))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setLoading(false);
          showSnackbar(response.data.message, "success");
          router.push(`/${lang}/media`);
        } else if (response.status === 208) {
          setLoading(false);
          showSnackbar(response.data.message, "error");
          router.push(`/${lang}/media`);
        }
      })
      .catch((error) => {
        setLoading(false);
        let errorMessage = null;
        if (error.status === 400) {
          errorMessage = error.data;
          router.push(`/${lang}/media`);
        } else {
          errorMessage = error?.data?.message ?? t("musicGenreForm.errorOccurred");
        }
        showSnackbar(errorMessage, "error");
      });
  };

  /** get all the music genre */
  useEffect(() => {
    if (debounceSearch === "") {
      dispatch(getMusicGenre());
    } else {
      dispatch(getMusicGenreSearch(debounceSearch));
    }
  }, [debounceSearch]);

  /** set the musicGenre to empty when previewData?.musicGenre.length === 0 */
  useEffect(() => {
    previewData?.musicGenre?.length === 0 && setValue("musicGenre", []);
  }, [previewData]);

  if (actLoading) return <Loader />;

  return (
    <>
      <form onSubmit={handleSubmit(handleActSkills)}>
        <Box className="!flex !border !h-[52px] !w-full !items-center !border-[--text-color] !rounded-[4px] !mt-8">
          <TextField
            size="small"
            placeholder={t("musicGenreForm.quickSearch")}
            className=" !text-[--text-color] !w-full"
            InputProps={{
              startAdornment: (
                <InputAdornment
                  position="start"
                  style={{
                    cursor: "pointer",
                    marginLeft: 0,
                    marginRight: 12,
                  }}
                >
                  <Search className="!text-2xl" />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end" style={{ cursor: "pointer" }}>
                  {search && (
                    <Clear
                      className="!text-[--text-color] !text-base"
                      onClick={() => setSearch("")}
                    />
                  )}
                </InputAdornment>
              ),
            }}
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& input": {
                color: "#EFEFEF",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            value={search}
            onChange={(event) => setSearch(event.target.value)}
          />
        </Box>
        <Box className="!mt-8">
          <Controller
            control={control}
            name="musicGenre"
            render={({ field }) => {
              return musicGenres?.length > 0 ? (
                musicGenres?.map((music, index) => (
                  <Box key={index}>
                    <Box
                      className={classNames(
                        "!border cursor-pointer !mt-4 !p-4 !rounded-[4px] !flex !justify-between",
                        {
                          "border-[--inprogress-color]": field.value.some(
                            (item) => item.name === music.name,
                          ),
                          "border-[--divider-color]": !field.value.some(
                            (item) => item.name === music.name,
                          ),
                        },
                      )}
                    >
                      <Box className="flex items-center !gap-x-2">
                        <CheckBox
                          className="!max-w-[24px]"
                          sx={{ color: "#EFEFEF", marginRight: "5px" }}
                          checked={field.value.some((item) => item.name === music.name)}
                          onChange={(e) => {
                            const selectedMusicGenre = e.target.checked
                              ? [...field.value, { name: music.name, members: [] }]
                              : field.value.filter((item) => item.name !== music.name);

                            field.onChange(selectedMusicGenre);
                            dispatch(setPreviewData({ musicGenre: selectedMusicGenre }));
                          }}
                        />
                        <HighlightSubstring
                          text={music?.name}
                          substring={search}
                          className="!text-[--text-color] !text-lg CraftworkGroteskMedium capitalize"
                        />
                      </Box>
                      <Box>
                        <Image src={music.iconUrl} width={16} height={16} alt={music.name} />
                      </Box>
                    </Box>
                    {field.value.some((item) => item.name === music.name) &&
                      music.members.filter((subGenre) => subGenre !== "").length > 0 && (
                        <Box className="!flex !flex-wrap items-center text-[--text-color] text-base font-craftWorkSemiBold">
                          Eg:-
                          {music.members.map((subGenre, index) => (
                            <Box key={index} className="flex items-center p-2">
                              <Box
                                className="max-w-[24px]"
                                sx={{ color: "#EFEFEF", marginRight: "5px" }}
                                checked={field.value.some(
                                  (item) =>
                                    item.name === music.name && item.members.includes(subGenre),
                                )}
                                onChange={(e) => {
                                  const selectedMusicGenre = e.target.checked
                                    ? field.value.map((item) =>
                                        item.name === music.name
                                          ? {
                                              ...item,
                                              members: [...item.members, subGenre],
                                            }
                                          : item,
                                      )
                                    : field.value.map((item) =>
                                        item.name === music.name
                                          ? {
                                              ...item,
                                              members: item.members.filter(
                                                (member) => member !== subGenre,
                                              ),
                                            }
                                          : item,
                                      );

                                  field.onChange(selectedMusicGenre);
                                  dispatch(
                                    setPreviewData({
                                      musicGenre: selectedMusicGenre,
                                    }),
                                  );
                                }}
                              />
                              <HighlightSubstring
                                text={subGenre}
                                substring={search}
                                className="!text-[--text-color] !text-sm CraftworkGroteskRegular"
                              />
                            </Box>
                          ))}
                        </Box>
                      )}
                  </Box>
                ))
              ) : (
                <Typography className="!text-[--text-color] !text-lg CraftworkGroteskRegular">
                  {t("musicGenreForm.noFound")}
                </Typography>
              );
            }}
          />
        </Box>
        <ProfileFooter
          backurl={`/${lang}/entertainment-type`}
          loading={loading}
          disabled={!previewData?.musicGenre}
          buttonName={p("Next")}
        />
      </form>
    </>
  );
};
export default MusicGenreForm;
