"use client";
import { RadioButtonUnchecked } from "@mui/icons-material";
import {
  Box,
  Button,
  FormControlLabel,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from "@mui/material";
import React from "react";
import { Controller } from "react-hook-form";
import RadioButtonIcon from "@/assets/svg/RadioButton.svg";
import { useTranslations } from "next-intl";
import PDFIcon from "@/assets/svg/PDFIcon.svg";
import ViewIcon from "@/assets/svg/ViewIcon.svg";

const ActRidersForm = ({
  control,
  errors,
  setValue,
  riderProfileType,

  riderList,
  currentBookingStatus,
}) => {
  const t = useTranslations("rider");
  // const reason = [
  //   "Unavailable",
  //   "Doesn't fit profile",
  //   "Doesn't fit budget range",
  //   "Other (specify)",
  // ];
  // const options = [
  //   "Decline or subject to negotiation",
  //   "Accepted",
  //   "Accepted with the following exceptions",
  // ];
  const options = ["Accepted", "Accepted with the following exceptions"];
  return (
    <Box>
      <Box>
        {riderList?.length > 0 && (
          <>
            <Typography className="text-lg text-[--text-color] CraftworkGroteskMedium mt-6">
              View {riderProfileType === "Act" ? "Act" : "Venue"} rider
            </Typography>

            <Box className="w-full">
              <Controller
                name="riderUrl"
                control={control}
                render={({ field }) => (
                  <RadioGroup
                    //value={field.value}
                    {...field}
                    className="w-full"
                    // onChange={(e) => {
                    //   field.onChange(e.target.value);
                    //   console.log(e.target.value);
                    // }}
                  >
                    {riderList.map((data, id) =>
                      id === 0 ? (
                        <FormControlLabel
                          key={id}
                          value={data?.riderDocument}
                          className="!w-[100%] mb-4"
                          sx={{
                            "& .MuiFormControlLabel-label": {
                              width: "100% !important",
                            },
                          }}
                          control={
                            <Radio
                              icon={
                                <RadioButtonUnchecked className="!w-6 !h-6 !text-[--text-color]" />
                              }
                              checkedIcon={<RadioButtonIcon className="!w-6 !h-6" />}
                              //value={data.riderDocument}
                            />
                          }
                          label={
                            <Box className="flex justify-between w-full p-4 bg-[--footer-bg] rounded-[4px] border border-[--divider-color]">
                              <Box className="flex gap-3 items-center">
                                <PDFIcon className="w-10 h-10" />
                                <Typography className="text-sm w-full text-[--text-color] CraftworkGroteskMedium">
                                  {data?.riderDocument?.split("/").pop()}
                                </Typography>
                              </Box>
                              <Button
                                className="!bg-[--text-color] flex gap-2 !normal-case px-4"
                                onClick={() => {
                                  if (riderProfileType === "Act") {
                                    const pID =
                                      currentBookingStatus?.contactType === "userBookAct" ||
                                      currentBookingStatus?.contactType === "actBookVenue"
                                        ? currentBookingStatus?.otherProfileId
                                        : currentBookingStatus?.profileId;
                                    const url = `/${pID}/pdf-view?pdf=${data.riderDocument.split("/").pop()}`;
                                    const newWindow = window.open(
                                      url,
                                      "_blank",
                                      "noopener,noreferrer",
                                    );
                                    if (newWindow) newWindow.opener = null;
                                  }

                                  if (riderProfileType === "Venue") {
                                    const pID =
                                      currentBookingStatus?.contactType === "userBookVenue" ||
                                      currentBookingStatus?.contactType === "venueBookAct"
                                        ? currentBookingStatus?.otherProfileId
                                        : currentBookingStatus?.profileId;
                                    const url = `/${pID}/pdf-view?pdf=${data.riderDocument.split("/").pop()}`;
                                    const newWindow = window.open(
                                      url,
                                      "_blank",
                                      "noopener,noreferrer",
                                    );
                                    if (newWindow) newWindow.opener = null;
                                  }
                                  // if (
                                  //   currentBookingStatus?.contactType === "userBookAct" ||
                                  //   currentBookingStatus?.contactType === "userBookVenue"
                                  // ) {
                                  //   const url = `/${currentBookingStatus?.otherProfileId}/pdf-view?pdf=${data.riderDocument.split("/").pop()}`;
                                  //   const newWindow = window.open(
                                  //     url,
                                  //     "_blank",
                                  //     "noopener,noreferrer",
                                  //   );
                                  //   if (newWindow) newWindow.opener = null;
                                  // } else {
                                  //   const url = `/${currentBookingStatus?.profileId}/pdf-view?pdf=${data.riderDocument.split("/").pop()}`;
                                  //   const newWindow = window.open(
                                  //     url,
                                  //     "_blank",
                                  //     "noopener,noreferrer",
                                  //   );
                                  //   if (newWindow) newWindow.opener = null;
                                  // }

                                  // router.push(
                                  //   `/${profileId}/pdf-view?pdf=${rider.riderDocument.split("/").pop()}`,
                                  // )
                                }}
                              >
                                <Typography className="text-sm text-[--bg-color] CraftworkGroteskHeavy">
                                  View
                                </Typography>
                                <ViewIcon className="w-4 h-4" />
                              </Button>
                            </Box>
                          }
                        />
                      ) : null,
                    )}
                  </RadioGroup>
                )}
              />
              {errors.riderUrl && (
                <Typography as="span" className="text-sm !text-red-600">
                  {errors.riderUrl.message}
                </Typography>
              )}
            </Box>
          </>
        )}

        {/* Instructions Section */}
        <Box className="mb-6 p-4 bg-[--footer-bg] rounded-[4px] border border-[--divider-color]">
          <Typography className="text-lg text-red-600 CraftworkGroteskMedium mb-2">
            Instructions:
          </Typography>

          {riderProfileType === "Act" && (
            <>
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular mb-2">
                If you are the <span className="font-bold">ACT</span>, then please indicate if your
                rider above should be:
              </Typography>
              <Box className="ml-4 mb-2">
                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular flex items-center">
                  <span className="mr-2">•</span> Accepted &quot;As Is&quot; (select option 1)
                </Typography>
                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular flex items-center">
                  <span className="mr-2">•</span> Modified (select option 2 and add your
                  modifications)
                </Typography>
              </Box>
            </>
          )}

          {riderProfileType === "Venue" && (
            <>
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular mb-2">
                If you are the <span className="font-bold">VENUE</span>, then you should indicate if
                you:
              </Typography>
              <Box className="ml-4">
                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular flex items-center">
                  <span className="mr-2">•</span> Want to DECLINE the rider subject to negotiation
                  (select option 1 and choose your reason)
                </Typography>
                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular flex items-center">
                  <span className="mr-2">•</span> Accept the rider &quot;As Is&quot; (select option
                  1)
                </Typography>
                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular flex items-center">
                  <span className="mr-2">•</span> Accept the rider with exceptions (select option 2
                  and propose your changes)
                </Typography>
              </Box>
            </>
          )}
        </Box>

        <Typography className="text-lg text-[--text-color] CraftworkGroteskMedium ">
          Review {riderProfileType === "Act" ? "Act" : "Venue"}
          ’s rider
        </Typography>
        <Controller
          name="fee"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value || ""}
              onChange={(e) => {
                field.onChange(e.target.value);
                if (e.target.value === "Decline or subject to negotiation") {
                  setValue("riderAccepted", false);
                  setValue("riderRejected", true);
                  setValue("acceptedWithConditions", false);
                } else if (e.target.value === "Accepted") {
                  setValue("riderAccepted", true);
                  setValue("riderRejected", false);
                  setValue("acceptedWithConditions", false);
                } else if (e.target.value === "Accepted with the following exceptions") {
                  setValue("riderAccepted", false);
                  setValue("riderRejected", false);
                  setValue("acceptedWithConditions", true);
                }

                //setSelectedOption(e.target.value);
              }}
            >
              {options.map((data, id) => (
                <Box key={id}>
                  <FormControlLabel
                    value={data}
                    control={
                      <Radio
                        icon={<RadioButtonUnchecked className="!w-6 !h-6 !text-[--text-color]" />}
                        checkedIcon={<RadioButtonIcon className="!w-6 !h-6" />}
                        className="mr-2"
                      />
                    }
                    label={
                      <Typography className="!text-[--text-color] !normal-case Poppins400 !text-sm">
                        {data}
                      </Typography>
                    }
                    className="flex items-center"
                  />
                  {/* {id === 0 && (
                    <>
                      <Box className="!border ml-6 !border-[text-color] !rounded-[2px] !mt-2 !py-1">
                        <Controller
                          name="riderRejectionReason"
                          control={control}
                          render={({ field }) => (
                            <Dropdown
                              onSelect={field.onChange}
                              options={reason || []}
                              selectedValue={field.value}
                              title={t("selectAReason")}
                              className="!text-[--text-color] !w-full"
                            />
                          )}
                        />
                      </Box>
                      <Box>
                        {watch("riderRejectionReason") === "Other (specify)" && (
                          <Controller
                            name="riderRejectionReasonMessage"
                            control={control}
                            render={({ field }) => (
                              <TextField
                                type="text"
                                size="small"
                                multiline
                                rows={8}
                                {...field}
                                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                                sx={{
                                  "& input::placeholder": {
                                    color: "#EFEFEF",
                                    border: 0,
                                  },
                                  "& .MuiOutlinedInput-root": {
                                    color: "var(--text-color)",
                                    fontFamily: "var(--craftWorkRegular)",
                                  },
                                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline":
                                    {
                                      borderWidth: 0,
                                    },
                                  "& .MuiOutlinedInput-notchedOutline": {
                                    borderWidth: 0,
                                  },
                                  border: 0,
                                }}
                                className="!border !w-full !h-[180px] !py-1 CraftworkGroteskRegular !border-[--text-color] rounded-[2px] !my-3"
                                placeholder={t("anyIllegalSubtances")}
                              />
                            )}
                          />
                        )}
                      </Box>
                    </>
                  )} */}
                  {id === 1 && (
                    <Box className="ml-6">
                      <Controller
                        name="acceptanceConditions"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            type="text"
                            size="small"
                            multiline
                            rows={8}
                            {...field}
                            InputLabelProps={{ style: { color: "#EFEFEF" } }}
                            sx={{
                              "& input::placeholder": {
                                color: "#EFEFEF",
                                border: 0,
                              },
                              "& .MuiOutlinedInput-root": {
                                color: "var(--text-color)",
                                fontFamily: "var(--craftWorkRegular)",
                              },
                              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline":
                                {
                                  borderWidth: 0,
                                },
                              "& .MuiOutlinedInput-notchedOutline": {
                                borderWidth: 0,
                              },
                              border: 0,
                            }}
                            className="!border !w-full !h-[180px] !py-1 CraftworkGroteskRegular !border-[--text-color] rounded-[2px] !my-3"
                            placeholder={t("anyIllegalSubtances")}
                          />
                        )}
                      />
                      {errors.acceptanceConditions && (
                        <Typography as="span" className="text-sm !text-red-600">
                          {errors.acceptanceConditions.message}
                        </Typography>
                      )}
                    </Box>
                  )}
                </Box>
              ))}
            </RadioGroup>
          )}
        />
      </Box>
    </Box>
  );
};

export default ActRidersForm;
