"use client";
import { FormInput, Button } from "@/component";
import { Typography, CircularProgress } from "@mui/material";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { useForm } from "react-hook-form";
import { changePasswordValidation } from "@/validation/auth/change-password/change-password.validation";
import { yupResolver } from "@hookform/resolvers/yup";
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { changePassword } from "@/store/slice/auth/change-password.auth.slice";
import { useTranslations } from "next-intl";
import { AUTH_CONSTANTS } from "@/validation/auth/constants";

const ChangePasswordForm = () => {
  const t = useTranslations("changePassword.changePasswordForm");
  const { showSnackbar } = useSnackbar();
  const { loading } = useSelector((state) => state.changePassword);
  const dispatch = useDispatch();
  const resolver = yupResolver(changePasswordValidation);
  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
  } = useForm({
    resolver,
    defaultValues: {
      oldPassword: "",
      newPassword: "",
      repeatPassword: "",
    },
    mode: "onChange",
  });

  const handleChangePassword = (data) => {
    const changePasswordData = {
      oldPassword: data.oldPassword,
      newPassword: data.newPassword,
    };
    dispatch(changePassword(changePasswordData))
      .unwrap()
      .then((response) => {
        showSnackbar(response.data.message, "success");
        reset();
      })
      .catch((error) => {
        showSnackbar(error, "error");
      });
  };

  return (
    <form onSubmit={handleSubmit(handleChangePassword)}>
      <FormInput
        name="oldPassword"
        type="password"
        placeholder={t("changePassword")}
        control={control}
        errors={errors}
        size="small"
        inputProps={{
          maxLength: AUTH_CONSTANTS.PASSWORD.MAX_LENGTH,
        }}
        sx={{
          "& input::placeholder": {
            color: "#EFEFEF",
            border: 0,
          },
          "& input": {
            color: "#EFEFEF",
            fontFamily: "var(--craftWorkRegular)",
          },
          "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          border: 0,
        }}
        className="w-full !border !py-1 !border-white rounded-[2px] !mb-3"
      />
      <FormInput
        name="newPassword"
        type="password"
        placeholder={t("newPassword")}
        control={control}
        errors={errors}
        size="small"
        inputProps={{
          maxLength: AUTH_CONSTANTS.PASSWORD.MAX_LENGTH,
        }}
        sx={{
          "& input::placeholder": {
            color: "#EFEFEF",
            border: 0,
          },
          "& input": {
            color: "#EFEFEF",
            fontFamily: "var(--craftWorkRegular)",
          },
          "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          border: 0,
        }}
        className="w-full !border !py-1 !border-white rounded-[2px] !mb-3"
      />
      <FormInput
        name="repeatPassword"
        type="password"
        placeholder={t("repeatPassword")}
        control={control}
        errors={errors}
        size="small"
        inputProps={{
          maxLength: AUTH_CONSTANTS.PASSWORD.MAX_LENGTH,
        }}
        sx={{
          "& input::placeholder": {
            color: "#EFEFEF",
            border: 0,
          },
          "& input": {
            color: "#EFEFEF",
            fontFamily: "var(--craftWorkRegular)",
          },
          "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          border: 0,
        }}
        className="w-full !border !py-1 !border-white rounded-[2px]"
      />
      <Button
        className="!bg-[--text-color] !flex !gap-x-4 !w-full !my-8 !py-3"
        type="submit"
        sx={{
          border: 0,
          "&.MuiButtonBase-root": {
            color: "white !important",
          },
        }}
      >
        {loading ? (
          <CircularProgress size={24} className="!text-black" />
        ) : (
          <>
            <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
              {t("resetPassword")}
            </Typography>
            <ArrowSouthEast />
          </>
        )}
      </Button>
    </form>
  );
};

export default ChangePasswordForm;
