"use client";
import { <PERSON><PERSON>, <PERSON>Box } from "@/component";
import {
  Box,
  FormControlLabel,
  InputAdornment,
  InputLabel,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { Controller } from "react-hook-form";
import "react-datepicker/dist/react-datepicker.css";
import { RadioButtonUnchecked } from "@mui/icons-material";
import RadioButtonIcon from "@/assets/svg/RadioButton.svg";
import FlatRateUi from "@/ui/flat-rate/flat-rate.ui";
import DoorGigUi from "@/ui/door-gig/door-gig.ui";
import ExposureGigUi from "@/ui/exposure-gig/exposure-gig.ui";
import { TimePicker } from "@mui/x-date-pickers";
import dayjs from "dayjs";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { useDispatch, useSelector } from "react-redux";
import { gsMsgTemplates } from "@/store/slice/booking/booking.slice";
import ContractDetailsPopup from "@/common/contract-details-popup/contract-details-popup";
import { getActPayment } from "@/store/slice/act/act.slice";
import { CONTRACT_DETAILS } from "@/validation/auth/constants";

const ContractDetailsForm = ({
  control,
  setValue,
  watch,
  errors,
  clearErrors,
  contractType,
  previousOption,
}) => {
  const [open, setOpen] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState("");
  const [type, setType] = useState("");
  const [options, setOptions] = useState([
    {
      name: "Flat rate",
      value: "FLAT_RATE",
    },
  ]);

  const toggleSortDrawer = (newOpen) => {
    setOpen(newOpen);
  };

  const label = [
    {
      name: "by Purchaser",
      value: "PURCHASER",
    },
    {
      name: "by Performer",
      value: "PERFORMER",
    },
    {
      name: "Not Applicable",
      value: "NOT_APPLICABLE",
    },
  ];
  const [prevMess, setPrevMess] = useState([]);

  const dispatch = useDispatch();
  const { actPayment } = useSelector((state) => state.act);
  useEffect(() => {
    dispatch(gsMsgTemplates())
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setPrevMess(response.data.data);
        }
      })
      .catch(() => {});
    dispatch(getActPayment());
  }, []);

  useEffect(() => {
    if (contractType && (contractType === "ACTVENUE" || contractType === "VENUEACT")) {
      setOptions([
        {
          name: "Flat rate",
          value: "FLAT_RATE",
        },
        {
          name: "Door Gig",
          value: "DOOR_GIG",
        },
        {
          name: "Exposure Gig",
          value: "EXPOSURE_GIG",
        },
      ]);
    }
  }, [contractType]);

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box className="">
        <Typography className="text-[--text-color] text-lg CraftworkGroteskMedium">
          {contractType === "USERVENUE" ? "Venue details" : "Performer details"}
        </Typography>
        {contractType !== "USERVENUE" && (
          <Box className="w-full pt-4">
            <InputLabel className="text-[--text-color] text-sm CraftworkGroteskRegular">
              Performer role
            </InputLabel>
            <Controller
              name="performersRole"
              control={control}
              render={({ field }) => (
                <TextField
                  type="text"
                  size="small"
                  {...field}
                  inputProps={{
                    maxLength: CONTRACT_DETAILS.PERFORMER_ROLE.MAX_LENGTH,
                  }}
                  InputLabelProps={{ style: { color: "#EFEFEF" } }}
                  sx={{
                    "& input::placeholder": {
                      color: "#EFEFEF",
                      border: 0,
                    },
                    "& input": {
                      color: "#EFEFEF",
                      fontFamily: "var(--craftWorkRegular)",
                    },
                    "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      borderWidth: 0,
                    },
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderWidth: 0,
                    },
                    border: 0,
                  }}
                  className="!border !w-full mt-4 !py-2 CraftworkGroteskRegular !border-white rounded-[2px]"
                  placeholder="Eg: HeadLiner"
                />
              )}
            />
            {errors.performersRole && (
              <Typography as="span" className="text-[12px] !text-red-600">
                {errors.performersRole.message}
              </Typography>
            )}
          </Box>
        )}
        <Box className="flex gap-x-4 lg:!flex-nowrap md:!flex-nowrap !flex-wrap pt-4 lg:pb-0 md:pb-0 pb-4 w-full">
          <Box className="w-full">
            <InputLabel className="text-[--text-color] text-sm CraftworkGroteskRegular">
              {contractType === "USERVENUE" ? "Event time" : "Perform time"}
            </InputLabel>
            <Box className=" border border-[--text-color] rounded-[2px] my-4">
              <Controller
                name="startDate"
                control={control}
                render={({ field }) => (
                  <TimePicker
                    value={dayjs(field.value)}
                    onChange={(date) => field.onChange(date.toISOString())}
                    className="border w-full border-[--text-color] focus-visible:outline-none"
                    sx={{
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderWidth: "0 !important",
                        borderColor: "transparent !important",
                      },
                      "& .MuiInputBase-root": {
                        color: "var(--text-color)",
                        border: "1px solid var(--text-color)",
                      },
                      "& .MuiSvgIcon-root": {
                        color: "var(--text-color)",
                      },
                    }}
                  />
                )}
              />
            </Box>
            {errors.startDate && (
              <Typography as="span" className="text-[12px] !text-red-600">
                {errors.startDate.message}
              </Typography>
            )}
          </Box>
          <Box className="w-full">
            <InputLabel className="text-[--text-color] text-sm CraftworkGroteskRegular">
              Duration
            </InputLabel>
            <Controller
              name="durationInHours"
              control={control}
              render={({ field }) => (
                <TextField
                  type="text"
                  size="small"
                  {...field}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end" style={{ cursor: "pointer" }}>
                        <Typography className="text-[--text-color] CraftworkGroteskRegular text-sm">
                          hours
                        </Typography>
                      </InputAdornment>
                    ),
                  }}
                  InputLabelProps={{ style: { color: "#EFEFEF" } }}
                  sx={{
                    "& input::placeholder": {
                      color: "#EFEFEF",
                      border: 0,
                    },
                    "& input": {
                      color: "#EFEFEF",
                      fontFamily: "var(--craftWorkRegular)",
                    },
                    "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      borderWidth: 0,
                    },
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderWidth: 0,
                    },
                    border: 0,
                  }}
                  className="!border !w-full mt-4 !py-2 CraftworkGroteskRegular !border-white rounded-[2px]"
                  placeholder="amount of"
                />
              )}
            />
          </Box>
        </Box>
        {contractType !== "USERVENUE" && (
          <Box className="w-full md:!w-[48%]">
            <InputLabel className="text-[--text-color] text-sm CraftworkGroteskRegular">
              Load in Time
            </InputLabel>
            <Box className=" border border-[--text-color] rounded-[2px] my-3 ">
              <Controller
                name="loadingTime"
                control={control}
                render={({ field }) => (
                  <TimePicker
                    value={dayjs(field.value)}
                    onChange={(date) => field.onChange(date.toISOString())}
                    className="border w-full border-[--text-color] focus-visible:outline-none"
                    sx={{
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderWidth: "0 !important",
                        borderColor: "transparent !important",
                      },
                      "& .MuiInputBase-root": {
                        color: "var(--text-color)",
                        border: "1px solid var(--text-color)",
                      },
                      "& .MuiSvgIcon-root": {
                        color: "var(--text-color)",
                      },
                    }}
                  />
                )}
              />
            </Box>
            {errors.loadingTime && (
              <Typography as="span" className="text-[12px] !text-red-600">
                {errors.loadingTime.message}
              </Typography>
            )}
          </Box>
        )}
        <Typography className="text-[--text-color] text-lg pt-8 CraftworkGroteskMedium">
          Fee
        </Typography>
        <Controller
          name="paymentType"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value}
              onChange={(e) => {
                field.onChange(e.target.value);
                if (previousOption.current === "FLAT_RATE") {
                  clearErrors(["flatRateCurrency", "flatRateAmount", "flatRatePercentage"]);
                }
                if (previousOption.current === "DOOR_GIG") {
                  clearErrors([
                    "doorGigEntryFee",
                    "venueCapacity",
                    "doorManagedBy",
                    "maximumPercentage",
                    "guaranteedMaximum",
                  ]);
                }
                if (previousOption.current === "EXPOSURE_GIG") {
                  clearErrors(["exposureGigCurrency", "exposureGigFee"]);
                }
                previousOption.current = e.target.value;
              }}
            >
              {options.map((data, id) => (
                <Box key={id}>
                  <FormControlLabel
                    value={data.value}
                    control={
                      <Radio
                        icon={<RadioButtonUnchecked className="!w-6 !h-6 !text-[--text-color]" />}
                        checkedIcon={<RadioButtonIcon className="!w-6 !h-6" />}
                        className="mr-2"
                      />
                    }
                    label={
                      <Typography className="!text-[--text-color] !normal-case Poppins400 !text-sm">
                        {data.name}
                      </Typography>
                    }
                    className="flex items-center"
                  />
                  {data.name === "Flat rate" && (
                    <FlatRateUi
                      control={control}
                      errors={errors}
                      selectedOption={field.value}
                      data={data.name}
                      currencies={actPayment?.currencies}
                    />
                  )}
                  {data.name === "Door Gig" &&
                    (contractType === "ACTVENUE" || contractType === "VENUEACT") && (
                      <DoorGigUi
                        control={control}
                        errors={errors}
                        selectedOption={field.value}
                        data={data.name}
                        watch={watch}
                      />
                    )}
                  {data.name === "Exposure Gig" &&
                    (contractType === "ACTVENUE" || contractType === "VENUEACT") && (
                      <ExposureGigUi
                        control={control}
                        errors={errors}
                        selectedOption={field.value}
                        data={data.name}
                        currencies={actPayment?.currencies}
                      />
                    )}
                </Box>
              ))}
            </RadioGroup>
          )}
        />
        {contractType !== "USERVENUE" && (
          <>
            <Typography className="text-lg pt-8 CraftworkGroteskMedium text-[--text-color]">
              Production Equipment
            </Typography>
            <Typography className="text-sm pt-1 CraftworkGroteskRegular text-[--text-color]">
              Sound and lighting provided
            </Typography>
            <Controller
              name="equipmentProvider"
              control={control}
              render={({ field }) => (
                <RadioGroup
                  row
                  value={field.value}
                  className="flex justify-between"
                  onChange={(e) => field.onChange(e.target.value)}
                >
                  {label.map((data, id) => (
                    <FormControlLabel
                      key={id}
                      value={data.value}
                      control={
                        <Radio
                          icon={<RadioButtonUnchecked className="!w-6 !h-6 !text-[--text-color]" />}
                          checkedIcon={<RadioButtonIcon className="!w-6 !h-6" />}
                        />
                      }
                      label={
                        <Typography
                          className="!text-[--text-color] !normal-case Poppins400 !text-sm"
                          htmlFor={`radio-${id}`}
                        >
                          {data.name}
                        </Typography>
                      }
                    />
                  ))}
                </RadioGroup>
              )}
            />
            <Typography className="text-lg pt-8 CraftworkGroteskMedium text-[--text-color]">
              Meals, Accommodation
            </Typography>

            <Box className="!w-full">
              <Controller
                control={control}
                name="mealsProvidedByPurchaser"
                render={({ field }) => (
                  <Box className="flex items-center">
                    <CheckBox
                      className="!max-w-[24px]"
                      sx={{ color: "#EFEFEF", marginRight: "5px" }}
                      checked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    />
                    <label
                      htmlFor="mealsProvidedByPurchaser"
                      className="!text-[--text-color] !text-sm CraftworkGroteskRegular cursor-pointer"
                    >
                      Meals provided by Purchaser
                    </label>
                  </Box>
                )}
              />
            </Box>

            <Box className="!w-full">
              <Controller
                control={control}
                name="accommodationProvided"
                render={({ field }) => (
                  <Box className="flex items-center">
                    <CheckBox
                      className="!max-w-[24px]"
                      sx={{ color: "#EFEFEF", marginRight: "5px" }}
                      checked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    />
                    <label
                      htmlFor="accommodationProvided"
                      className="!text-[--text-color] !text-sm CraftworkGroteskRegular cursor-pointer"
                    >
                      Accommodation provided to touring talent
                    </label>
                  </Box>
                )}
              />
            </Box>
            <Typography className="text-lg pt-8 CraftworkGroteskMedium text-[--text-color]">
              Special provisions
            </Typography>

            <Box className="!w-full">
              <Controller
                control={control}
                name="merchandiseSalesAllowed"
                render={({ field }) => (
                  <Box className="flex items-center">
                    <CheckBox
                      className="!max-w-[24px]"
                      sx={{ color: "#EFEFEF", marginRight: "5px" }}
                      checked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    />
                    <label
                      htmlFor="merchandiseSalesAllowed"
                      className="!text-[--text-color] !text-sm CraftworkGroteskRegular cursor-pointer"
                    >
                      CD/merchandise sales at venue are permitted
                    </label>
                  </Box>
                )}
              />
            </Box>

            <Box className="!w-full">
              <Controller
                control={control}
                name="performerMemberOfUnion"
                render={({ field }) => (
                  <Box className="flex items-center">
                    <CheckBox
                      className="!max-w-[24px]"
                      sx={{ color: "#EFEFEF", marginRight: "5px" }}
                      checked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    />
                    <label
                      htmlFor="performerMemberOfUnion"
                      className="!text-[--text-color] !text-sm CraftworkGroteskRegular cursor-pointer"
                    >
                      Performer is/are members of appropriate union
                    </label>
                  </Box>
                )}
              />
            </Box>
          </>
        )}
        <Box className="flex justify-between items-center">
          <Typography className="lg:text-lg md:text-lg text-base pt-8 CraftworkGroteskMedium text-[--text-color]">
            Your message or other important details
          </Typography>
          {prevMess.length < 5 && (
            <Button
              onClick={() => {
                setType("save-as");
                toggleSortDrawer(true);
              }}
              className="!bg-[--text-color] !normal-case CraftworkGroteskBold !flex !gap-x-4 !px-4 !mt-5"
              sx={{
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "var(--bg-color) !important",
                },
              }}
            >
              Save As
            </Button>
          )}
          {selectedMessage && (
            <Button
              onClick={() => {
                setType("save");
                toggleSortDrawer(true);
              }}
              className="!bg-[--text-color] !normal-case CraftworkGroteskBold !flex !gap-x-4 !px-4 !mt-5"
              sx={{
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "var(--bg-color) !important",
                },
              }}
            >
              Save
            </Button>
          )}
        </Box>
        <ContractDetailsPopup
          open={open}
          handleClose={() => toggleSortDrawer(false)}
          type={type}
          message={watch("message")}
          setPrevMess={setPrevMess}
          selectedMessage={selectedMessage}
          setSelectedMessage={setSelectedMessage}
        />
        <Controller
          control={control}
          name="message"
          render={({ field }) => (
            <TextField
              type="text"
              size="small"
              multiline
              rows={8}
              {...field}
              inputProps={{
                maxLength: CONTRACT_DETAILS.MESSAGE.MAX_LENGTH,
              }}
              placeholder="We're looking for an incredible performer to elevate the atmosphere every Friday evening at our Venue place."
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& .MuiOutlinedInput-root": {
                  color: "var(--text-color)",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              className="!border !w-full !h-[180px] !py-1 CraftworkGroteskRegular !border-[--text-color] rounded-[2px] !my-3"
            />
          )}
        />

        <Box className="flex gap-3 flex-wrap items-center">
          <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
            Prev messages:
          </Typography>
          {prevMess.map((data) => (
            <Typography
              key={data.name}
              className={`text-sm text-[--text-color] cursor-pointer border ${selectedMessage?.name === data.name ? "border-[--text-color]" : "border-[--divider-color]"} rounded-[2px] p-1 CraftworkGroteskRegular`}
              onClick={() => {
                setValue("message", data.message);
                setSelectedMessage(data);
              }}
            >
              {data.name.substring(0, 20)}
            </Typography>
          ))}
        </Box>
      </Box>
    </LocalizationProvider>
  );
};

export default ContractDetailsForm;
