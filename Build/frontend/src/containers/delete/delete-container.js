import ConfirmDeleteDialog from "@/ui/confirm-delete-dialog/confirm-delete-dialog.ui";
import ConfirmDeleteDialogSuccess from "@/ui/confirm-delete-dialog/confirm-delete-dialog.success.ui";
import { useDispatch, useSelector } from "react-redux";
import { deleteActByProfileId } from "@/store/slice/act/act.slice";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import DeleteDialog from "@/ui/delete-dialog/delete-dialog.ui";
import { passwordLogin } from "@/store/slice/auth/2fa.auth.slice";

const DeleteContainer = ({
  open,
  handleClose,
  selectedProfileData,
  setOpen,
  params,
  isEvent = false,
  eventId = null,
  deleteSpecialEventByIdAction = null,
  getdata,
}) => {
  const { token, currentUser } = useSelector((state) => state.login);
  const { showSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const selectedId = isEvent ? eventId : selectedProfileData?.profileId;

  let deletDialogtile = isEvent ? "Delete this event permanently" : "Delete this act permanently";

  let deletDialogMessage = isEvent
    ? "The event will no longer be available and all data will be permanently deleted. This action cannot be undone."
    : "The act will no longer available and all data in the act will be permanently deleted. This action cannot be undone.";

  let deleteSuccessMessage = isEvent
    ? "The event has been successfully deleted"
    : `The ${selectedProfileData?.profileName} account successfully deleted`;

  if (!isEvent && selectedProfileData?.profileStatus === "STATUS_PUBLISHED") {
    deletDialogtile = "Profile will be unpublished";
    deletDialogMessage = "This profile will be unpublished, it will no longer be searchable.";
    deleteSuccessMessage = `The ${selectedProfileData?.profileName} account successfully unblished`;
  }

  const passwordConfirmHandler = (password) => {
    if (!token) {
      setIsTokenExpired(true);
      return;
    }

    dispatch(passwordLogin(password))
      .unwrap()
      .then((response) => {
        if (response.data.data) {
          if (isEvent && deleteSpecialEventByIdAction) {
            // Delete event with password confirmation
            dispatch(deleteSpecialEventByIdAction({ eventId: selectedId }))
              .unwrap()
              .then(() => {
                setOpen("deleteConfirmPopup");
              })
              .catch((error) => {
                showSnackbar(error, "error");
              });
          } else {
            // Delete profile with password confirmation
            dispatch(deleteActByProfileId({ profileId: selectedId }))
              .unwrap()
              .then(() => {
                setOpen("deleteConfirmPopup");
              })
              .catch((error) => {
                showSnackbar(error, "error");
              });
          }
        } else {
          showSnackbar(response?.message || response?.data?.message, "error");
        }
      })
      .catch((error) => {
        showSnackbar(error, "error");
      });
  };

  const deleteProfileHamdler = () => {
    if (!token) {
      setIsTokenExpired(true);
      return;
    }

    // For events, handle deletion directly
    if (isEvent) {
      dispatch(deleteSpecialEventByIdAction)
        .then(() => {
          getdata();
          handleClose();
        })
        .catch((error) => {
          showSnackbar(error, "error");
        });
      return;
    }

    // For profiles: If published, always unpublish first
    if (selectedProfileData?.profileStatus === "STATUS_PUBLISHED") {
      dispatch(deleteActByProfileId({ profileId: selectedId }))
        .unwrap()
        .then(() => {
          setOpen("deleteConfirmPopup");
        })
        .catch((error) => {
          showSnackbar(error, "error");
        });
      return;
    }

    // For unpublished profiles: If social login user, delete without password
    if (currentUser?.socialLoginUser === true) {
      dispatch(deleteActByProfileId({ profileId: selectedId }))
        .unwrap()
        .then(() => {
          setOpen("deleteConfirmPopup");
        })
        .catch((error) => {
          showSnackbar(error, "error");
        });
    } else {
      // For non-social login users, require password
      setOpen("deleteWithPasswordPopup");
    }
  };

  return (
    <>
      {open === "deleteWithPasswordPopup" && (
        <ConfirmDeleteDialog
          open={open === "deleteWithPasswordPopup" ? true : false}
          handleClose={handleClose}
          passwordConfirmHandler={passwordConfirmHandler}
        />
      )}

      <DeleteDialog
        open={open === "deletePopup" ? true : false}
        handleClose={handleClose}
        deleteProfileHamdler={() => deleteProfileHamdler(selectedId)}
        title={deletDialogtile}
        message={deletDialogMessage}
      />

      <ConfirmDeleteDialogSuccess
        open={open === "deleteConfirmPopup" ? true : false}
        deleteSuccessMessage={deleteSuccessMessage}
        handleClose={handleClose}
        params={params}
      />
    </>
  );
};

export default DeleteContainer;
