"use client";
import React, { useState } from "react";
import NotFoundSvg from "@/assets/svg/NotFound.svg";
import {
  Box,
  Dialog,
  DialogContent,
  FormControlLabel,
  IconButton,
  InputAdornment,
  InputLabel,
  Radio,
  RadioGroup,
  TextField,
  Typography,
  Divider,
} from "@mui/material";
import { Button, CommonImage, Dropdown } from "@/component";
import { Add, Clear, SouthEast } from "@mui/icons-material";
import SearchIcon from "@/assets/svg/act-type.svg/Search.svg";
import { Controller, useForm } from "react-hook-form";
import RadioButtonUncheckedIcon from "@mui/icons-material/RadioButtonUnchecked";
import RadioButtonIcon from "@/assets/svg/RadioButton.svg";
import Avatar from "@/assets/png/Avatar.png";
import Image from "@/assets/png/Image.png";
import { actDistributionValidation } from "@/validation/act/act.validation";
import { yupResolver } from "@hookform/resolvers/yup";
import { createDistribution } from "@/store/slice/act/act.slice";
import { useDispatch } from "react-redux";
import { showSnackbar } from "@/utils/snackbar.utils";
import DeleteSvg from "@/assets/svg/act-type.svg/DeleteSvg.svg";
import { useTranslations } from "next-intl";

const EditActDistributionListForm = ({
  profileId,
  data,
  setLoading,
  setFetch,
  setSearch,
  search,
}) => {
  const t = useTranslations("distributionList");
  const s = useTranslations("dropdown");
  const dispatch = useDispatch();
  const label = [
    {
      id: 1,
      val: false,
      src: Avatar,
      text: t("venueAcc"),
    },
    {
      id: 2,
      val: true,
      src: Image,
      text: t("userAcc"),
    },
  ];
  const distributionType = ["CONTRACT_VERSION"];

  const resolver = yupResolver(actDistributionValidation);

  const [open, setOpen] = React.useState(false);
  //const [search, setSearch] = React.useState("");
  const eventVersion = ["Event Version", "Event Version"];

  const [selectedEventVersion, setSelectedEventVersion] = useState(eventVersion[0]);

  const handleSelectEventVersion = (value) => {
    setSelectedEventVersion(value);
  };

  const handleSubmitDistribution = (data) => {
    const distributionData = {
      ...data,
      useActProfile: data.useActProfile === 1 ? false : true,
    };
    setLoading(true);
    dispatch(createDistribution({ data: distributionData, profileId }))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setFetch(...(fetch + 1));
          //dispatch(getDistributionList(profileId));
          setOpen(false);
          reset();
          setLoading(false);
          showSnackbar(response.data.message, "success");
        }
      })
      .catch((error) => {
        setLoading(false);
        showSnackbar(error?.data?.message ?? "Something went wrong", "error");
      });
  };

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const {
    handleSubmit,
    control,
    register,
    reset,
    setValue,
    formState: { errors },
  } = useForm({
    resolver,
    mode: "onSubmit",
    defaultValues: {
      useActProfile: false,
      firstName: "",
      lastName: "",
      receiverEmail: "",
      role: "",
      distributionType: "CONTRACT_VERSION",
    },
  });

  return (
    <>
      {data && data.length < 0 && (
        <Box className="!flex !flex-col !items-center !mt-16">
          <NotFoundSvg className="!w-12 !h-12" />
          <Typography className="!text-[--text-color] !text-2xl CraftworkGroteskMedium !pt-1">
            {t("noOne")}
          </Typography>
          <Typography className="!text-sm !text-[--text-color] CraftworkGroteskRegular !py-2">
            {t("startCreating")}
          </Typography>
          <Button
            className="!flex !gap-x-2 !normal-case"
            sx={{
              border: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
            onClick={handleClickOpen}
          >
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
              {t("addList")}
            </Typography>
            <Add className="!text-[--text-color] !text-lg" />
          </Button>
        </Box>
      )}
      <Dialog
        open={open}
        maxWidth={false}
        sx={{ "& .MuiBackdrop-root": { backgroundColor: "#181B1BE5" } }}
      >
        <form onSubmit={handleSubmit(handleSubmitDistribution)}>
          <DialogContent className=" !max-w-lg !bg-[--footer-bg] !border-[1px] lg:!px-10 !px-5 !pt-10 !border-[--text-color]">
            <Box className="!flex !justify-between">
              <Typography className="!text-[--text-color] !text-2xl font-craftWorkHeavy">
                {t("addMember")}
              </Typography>
              <IconButton onClick={handleClose}>
                <Clear className="!text-[--text-color] !text-base" />
              </IconButton>
            </Box>
            <Box className="!w-full !py-4">
              <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                {t("selectAccount")}
              </InputLabel>
              {/** Radio button */}
              <Controller
                name="useActProfile"
                control={control}
                render={({ field }) => {
                  return (
                    <RadioGroup
                      row
                      value={field.id}
                      onChange={(e) => {
                        setValue("useActProfile", e.target.value);
                        //console.log(e.target.value, 'e.target');
                        //field.onChange(true)}
                      }}
                    >
                      {label.map((data, id) => (
                        <FormControlLabel
                          key={id}
                          value={data.id}
                          control={
                            <Radio
                              icon={
                                <RadioButtonUncheckedIcon className="!w-6 !h-6 !text-[--text-color]" />
                              }
                              checkedIcon={<RadioButtonIcon className="!w-6 !h-6" />}
                            />
                          }
                          label={
                            <Box className="!flex !gap-x-2 !items-center" htmlFor={`radio-${id}`}>
                              <CommonImage
                                src={data.src}
                                alt="image"
                                className="!w-[18px] !h-[18px]"
                              />
                              <Typography className="!text-[--text-color] !normal-case Poppins400 !text-sm">
                                {data.text}
                              </Typography>
                            </Box>
                          }
                        />
                      ))}
                    </RadioGroup>
                  );
                }}
              />
            </Box>
            <Box className="w-full">
              <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                {t("firstName")}
              </InputLabel>
              <TextField
                size="small"
                name="firstName"
                placeholder={t("enterFirstName")}
                className="Sora500 !text-[--text-color] !mb-4 mt-2 py-[5px] !rounded-[2px] !border !w-full"
                {...register("firstName")}
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  border: 0,
                }}
              />
              {errors && errors.firstName && (
                <Typography as="span" className="text-sm !text-red-600">
                  {errors.firstName.message}
                </Typography>
              )}
            </Box>
            <Box className="w-full">
              <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                {t("lastName")}
              </InputLabel>
              <TextField
                size="small"
                name="lastName"
                placeholder={t("enterLastName")}
                className="Sora500 !text-[--text-color] !mb-4 mt-2 py-[5px] !rounded-[2px] !border !w-full"
                {...register("lastName")}
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  border: 0,
                }}
              />
              {errors && errors.lastName && (
                <Typography as="span" className="text-sm !text-red-600">
                  {errors.lastName.message}
                </Typography>
              )}
            </Box>
            <Box className="w-full">
              <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                {t("theUser")}
              </InputLabel>
              <TextField
                size="small"
                name="receiverEmail"
                placeholder={t("enterEmail")}
                className="Sora500 !text-[--text-color] !mb-4 mt-2 py-[5px] !rounded-[2px] !border !w-full"
                {...register("receiverEmail")}
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  border: 0,
                }}
              />
              {errors && errors.receiverEmail && (
                <Typography as="span" className="text-sm !text-red-600">
                  {errors.receiverEmail.message}
                </Typography>
              )}
            </Box>
            <Box className="!flex gap-x-2 !items-center">
              <Box className="w-full">
                <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                  {t("role")}
                </InputLabel>
                <TextField
                  size="small"
                  {...register("role")}
                  placeholder={t("manager")}
                  className="Sora500 !text-[--text-color] !my-2 !rounded-[2px] py-[5px] !border !w-full"
                  InputLabelProps={{ style: { color: "#EFEFEF" } }}
                  sx={{
                    "& input::placeholder": {
                      color: "#EFEFEF",
                      border: 0,
                    },
                    "& input": {
                      color: "#EFEFEF",
                    },
                    "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      borderWidth: 0,
                    },
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderWidth: 0,
                    },
                    border: 0,
                  }}
                />
                {errors && errors.role && (
                  <Typography as="span" className="text-sm !text-red-600">
                    {errors.role.message}
                  </Typography>
                )}
              </Box>

              <Box className="!w-full">
                <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX">
                  {t("distributive")}
                </InputLabel>
                <Box className="!border !border-[text-color] !rounded-[2px] !my-2 !py-1">
                  <Controller
                    name="distributionType"
                    control={control}
                    render={({ field }) => (
                      <Dropdown
                        onSelect={(selectedValue) => {
                          field.onChange(selectedValue);
                        }}
                        options={distributionType}
                        selectedValue={field.value}
                        title={s("selectDistributionType")}
                        className="!text-[--text-color] !w-full h-[40px]"
                      />
                    )}
                  />
                  {errors && errors.distributionType && (
                    <Typography as="span" className="text-sm !text-red-600">
                      {errors.distributionType.message}
                    </Typography>
                  )}
                </Box>
              </Box>
            </Box>

            <Button
              type="submit"
              className="!flex !gap-x-2 !w-full !py-3 !normal-case !bg-[--text-color]"
              sx={{
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
            >
              <Typography
                onClick={handleClickOpen}
                className="!text-[--bg-color] !text-sm CraftworkGroteskHeavy "
              >
                {t("addList")}
              </Typography>
              <SouthEast className="!text-[--bg-color] !text-lg" />
            </Button>
          </DialogContent>
        </form>
      </Dialog>
      /
      <Typography className="!text-sm !text-[--text-color] CraftworkGroteskGX">
        {t("listOfPeople")}
      </Typography>
      <Box className="!flex !items-center">
        <TextField
          size="small"
          placeholder={t("search")}
          className="Sora500 !text-[--text-color] !my-5 !rounded-[2px] !border !w-full"
          InputProps={{
            startAdornment: (
              <InputAdornment position="start" style={{ cursor: "pointer" }}>
                <SearchIcon className="!w-6 !h-6" />
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end" style={{ cursor: "pointer" }}>
                {search && <Clear className="!text-[--text-color] !w-6 !text-base" />}
              </InputAdornment>
            ),
          }}
          InputLabelProps={{ style: { color: "#EFEFEF" } }}
          sx={{
            "& input::placeholder": {
              color: "#EFEFEF",
              border: 0,
            },
            "& input": {
              color: "#EFEFEF",
            },
            "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            "& .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            border: 0,
          }}
          value={search}
          onChange={(event) => {
            setSearch(event.target.value);
          }}
        />
      </Box>
      <Box className="!flex !justify-between !items-center">
        <Typography className="!text-sm !text-[--hide-color] CraftworkGroteskRegular">
          {data.length} {t("contractList")}
        </Typography>
        <Button
          className="!flex !gap-x-2 !normal-case"
          sx={{
            border: 0,
            "&.MuiButtonBase-root": {
              color: "white !important",
            },
          }}
        >
          <Typography
            onClick={handleClickOpen}
            className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline"
          >
            {t("addList")}
          </Typography>
          <Add className="!text-[--text-color] !text-lg" />
        </Button>
      </Box>
      <Divider sx={{ borderTop: "thin solid rgba(76, 78, 79, 0.5)" }} className="!my-3" />
      {data &&
        data.map((item, index) => (
          <Box key={index} className="!flex !justify-between !items-center">
            <Box className="!flex !gap-x-3 !items-center">
              {/* <CommonImage
                        src={DistributionImage}
                        alt="image"
                    /> */}
              <Box className="">
                <Typography className="!text-sm !text-[--text-color] CraftworkGroteskRegular">
                  {item.firstName} {item.lastName}
                  <span>({item.role})</span>
                </Typography>
                <Typography className="!text-sm !text-[--hide-color] CraftworkGroteskRegular">
                  {item.receiverEmail}
                </Typography>
              </Box>
            </Box>
            <Box className="!flex !items-center !gap-x-3">
              <Dropdown
                onSelect={handleSelectEventVersion}
                options={eventVersion}
                selectedValue={selectedEventVersion}
                title={t("selectVersion")}
                className="!text-[--text-color] !w-full"
              />
              <DeleteSvg className="!text-lg !cursor-pointer" />
            </Box>
          </Box>
        ))}
    </>
  );
};

export default EditActDistributionListForm;
