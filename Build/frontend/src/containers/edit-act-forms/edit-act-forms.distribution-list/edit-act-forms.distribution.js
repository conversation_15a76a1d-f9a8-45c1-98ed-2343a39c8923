"use client";
import { Box, Divider, InputAdornment, TextField, Typography } from "@mui/material";
import React, { useState } from "react";
import SearchIcon from "@/assets/svg/act-type.svg/Search.svg";
import { Add, Clear } from "@mui/icons-material";
import { Button, Dropdown } from "@/component";
import DeleteSvg from "@/assets/svg/act-type.svg/DeleteSvg.svg";
import { useTranslations } from "next-intl";

const EditActDistributionForm = ({ data }) => {
  const s = useTranslations("distributionList");
  const eventVersion = ["Event Version", "Event Version"];
  const [search, setSearch] = useState("");
  const [selectedEventVersion, setSelectedEventVersion] = useState(eventVersion[0]);

  const handleSelectEventVersion = (value) => {
    setSelectedEventVersion(value);
  };

  return (
    <>
      <Typography className="!text-sm !text-[--text-color] CraftworkGroteskGX">
        {s("listOfPeople")}
      </Typography>
      <Box className="!flex !items-center">
        <TextField
          size="small"
          placeholder={s("search")}
          className="Sora500 !text-[--text-color] !my-5 !rounded-[2px] !border !w-full"
          InputProps={{
            startAdornment: (
              <InputAdornment position="start" style={{ cursor: "pointer" }}>
                <SearchIcon className="!w-6 !h-6" />
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end" style={{ cursor: "pointer" }}>
                {search && <Clear className="!text-[--text-color] !w-6 !text-base" />}
              </InputAdornment>
            ),
          }}
          InputLabelProps={{ style: { color: "#EFEFEF" } }}
          sx={{
            "& input::placeholder": {
              color: "#EFEFEF",
              border: 0,
            },
            "& input": {
              color: "#EFEFEF",
            },
            "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            "& .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            border: 0,
          }}
          value={search}
          onChange={(event) => setSearch(event.target.value)}
        />
      </Box>
      <Box className="!flex !justify-between !items-center">
        <Typography className="!text-sm !text-[--hide-color] CraftworkGroteskRegular">
          1 {s("contractList")}
        </Typography>
        <Button
          className="!flex !gap-x-2 !normal-case"
          sx={{
            border: 0,
            "&.MuiButtonBase-root": {
              color: "white !important",
            },
          }}
        >
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
            {s("addList")}
          </Typography>
          <Add className="!text-[--text-color] !text-lg" />
        </Button>
      </Box>
      <Divider sx={{ borderTop: "thin solid rgba(76, 78, 79, 0.5)" }} className="!my-3" />
      {data &&
        data.map((item, index) => (
          <Box key={index} className="!flex !justify-between !items-center">
            <Box className="!flex !gap-x-3 !items-center">
              {/* <CommonImage
                        src={DistributionImage}
                        alt="image"
                    /> */}
              <Box className="">
                <Typography className="!text-sm !text-[--text-color] CraftworkGroteskRegular">
                  {item.firstName} {item.lastName}
                  <span>({item.role})</span>
                </Typography>
                <Typography className="!text-sm !text-[--hide-color] CraftworkGroteskRegular">
                  {item.receiverEmail}
                </Typography>
              </Box>
            </Box>
            <Box className="!flex !items-center !gap-x-3">
              <Dropdown
                onSelect={handleSelectEventVersion}
                options={eventVersion}
                selectedValue={selectedEventVersion}
                title={s("selectVersion")}
                className="!text-[--text-color] !w-full"
              />
              <DeleteSvg className="!text-lg !cursor-pointer" />
            </Box>
          </Box>
        ))}
    </>
  );
};

export default EditActDistributionForm;
