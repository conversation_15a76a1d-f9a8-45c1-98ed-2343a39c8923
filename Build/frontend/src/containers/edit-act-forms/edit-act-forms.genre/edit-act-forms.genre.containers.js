"use client";
import { useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Loader } from "@/component";
import { Box, InputAdornment, TextField, Typography } from "@mui/material";
import React, { useState } from "react";
import { Controller } from "react-hook-form";
import Search from "@/assets/svg/act-type.svg/Search.svg";
import { Clear } from "@mui/icons-material";
import Image from "next/image";
import { useDebounce } from "use-debounce";
import { getMusicGenre, getMusicGenreSearch } from "@/store/slice/act/act.slice";
import { useDispatch, useSelector } from "react-redux";
import { useTranslations } from "next-intl";

export const HighlightSubstring = ({ text, substring, className }) => {
  const words = text.split(/\b/);
  const highlightedText = [];
  if (!text || substring === "") {
    return <label className={className}>{text}</label>;
  } else {
    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      if (word.toLowerCase().indexOf(substring.toLowerCase()) !== -1) {
        highlightedText.push(
          <span key={i} className="!text-[--inprogress-color]">
            {word}
          </span>,
        );
      } else {
        highlightedText.push(<span key={i}>{word}</span>);
      }
    }
    return <label className={className}>{highlightedText}</label>;
  }
};

const EditActGenreForm = ({ setValue, control, watch }) => {
  const t = useTranslations("musicGenre");
  const dispatch = useDispatch();
  const [search, setSearch] = useState("");
  const [debounceSearch] = useDebounce(search, 1000);
  const { musicGenres, loading: actLoading } = useSelector((state) => state.act);

  const musicGenreList = watch("musicGenre");

  useEffect(() => {
    if (debounceSearch === "") {
      dispatch(getMusicGenre());
    } else {
      dispatch(getMusicGenreSearch(debounceSearch));
    }
  }, [debounceSearch]);

  /** handle clear sub genre */
  const handleClearSubGenre = (subGenre) => {
    const updatedMusicGenreList = musicGenreList.map((genre) => {
      return {
        ...genre,
        members: genre.members.filter((member) => member !== subGenre),
      };
    });
    setValue("musicGenre", updatedMusicGenreList);
  };
  const handleClearGenre = (genre) => {
    const updatedMusicGenreList = musicGenreList.filter((musicGenre) => musicGenre.name !== genre);
    setValue("musicGenre", updatedMusicGenreList);
  };

  if (actLoading) return <Loader />;

  return (
    <>
      <Box>
        <TextField
          size="small"
          placeholder={t("musicGenreForm.quickSearch")}
          InputLabelProps={{ style: { color: "#EFEFEF" } }}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end" style={{ cursor: "pointer" }}>
                {search ? (
                  <Clear
                    className="!text-[--text-color] !text-base"
                    onClick={() => setSearch("")}
                  />
                ) : (
                  <Search className="!text-2xl" />
                )}
              </InputAdornment>
            ),
          }}
          sx={{
            "& input::placeholder": {
              color: "#EFEFEF",
              border: 0,
            },
            "& input": {
              color: "#EFEFEF",
              fontFamily: "var(--craftWorkRegular)",
            },
            "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            "& .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            border: 0,
          }}
          className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px]"
          type="text"
          value={search}
          onChange={(event) => setSearch(event.target.value)}
        />
      </Box>

      {/*** pre-selected music-genre */}
      {musicGenreList?.length > 0 && (
        <Box className="!flex !flex-wrap !gap-2 !py-3 !items-center">
          {musicGenreList?.map((musicGenre, index) => (
            <>
              <Box
                key={index}
                onClick={() => handleClearGenre(musicGenre.name)}
                className="flex !gap-2 !items-center !p-1 !bg-[--divider-color] !rounded-[2px] cursor-pointer"
              >
                <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                  {musicGenre.name}
                </Typography>
                <Clear className="!text-lg !text-[--text-color]" />
              </Box>
              {musicGenre?.members?.length > 0 &&
                musicGenre?.members?.map((member, index) => (
                  <Box
                    key={index}
                    onClick={() => handleClearSubGenre(member)}
                    className="flex !gap-2 !items-center !p-1 !bg-[--divider-color] !rounded-[2px] cursor-pointer"
                  >
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                      {member}
                    </Typography>
                    <Clear className="!text-lg !text-[--text-color]" />
                  </Box>
                ))}
            </>
          ))}
          <Button
            sx={{
              border: 0,
              padding: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
            onClick={() => setValue("musicGenre", [])}
            className="!flex !gap-2 !normal-case !items-center"
          >
            <Typography className="!text-sm !text-[--text-color] !underline font-craftWorkHeavy">
              {t("musicGenrePreview.clearAll")}
            </Typography>
            <Clear className="!text-lg !text-[--text-color]" />
          </Button>
        </Box>
      )}

      <Controller
        control={control}
        name="musicGenre"
        render={({ field }) => {
          return musicGenres?.length > 0 ? (
            musicGenres?.map((music, index) => (
              <>
                <Box
                  key={index}
                  className="!flex !items-center !justify-between !p-4 mt-5 !border !border-[--divider-color] !bg-[--bg-color] !rounded-[4px] mb-3"
                >
                  <Box className="!flex !items-center">
                    <CheckBox
                      className="!max-w-[24px]"
                      sx={{ color: "#EFEFEF", marginRight: "5px" }}
                      checked={field.value.some((item) => item.name === music.name)}
                      onChange={(e) => {
                        const selectedMusicGenre = e.target.checked
                          ? [...field.value, { name: music.name, members: [] }]
                          : field.value.filter((item) => item.name !== music.name);

                        field.onChange(selectedMusicGenre);
                      }}
                    />
                    <HighlightSubstring
                      text={music?.name}
                      substring={search}
                      className="!text-[--text-color] !text-lg CraftworkGroteskMedium capitalize"
                    />
                  </Box>
                  <Box>
                    <Image src={music.iconUrl} width={12} height={12} alt={music.name} />
                  </Box>
                </Box>
                {field.value.some((item) => item.name === music.name) &&
                  music.members.filter((subGenre) => subGenre !== "").length > 0 && (
                    <Box className="!flex !flex-wrap items-center text-[--text-color] text-base font-craftWorkSemiBold !py-2">
                      Eg:-
                      {music.members.map((subGenre, index) => (
                        <Box
                          key={index}
                          className="!flex !items-center !justify-between px-1 !bg-[--bg-color]"
                        >
                          <Box
                            className="max-w-[24px]"
                            sx={{ color: "#EFEFEF", marginRight: "5px" }}
                            checked={field.value.some(
                              (item) =>
                                item.name === music.name &&
                                item.members &&
                                item.members.includes(subGenre),
                            )}
                            onChange={(e) => {
                              const selectedMusicGenre = e.target.checked
                                ? field.value.map((item) =>
                                    item.name === music.name
                                      ? {
                                          ...item,
                                          members: [...item.members, subGenre],
                                        }
                                      : item,
                                  )
                                : field.value.map((item) =>
                                    item.name === music.name
                                      ? {
                                          ...item,
                                          members: item.members.filter(
                                            (member) => member !== subGenre,
                                          ),
                                        }
                                      : item,
                                  );

                              field.onChange(selectedMusicGenre);
                            }}
                          />
                          <HighlightSubstring
                            text={subGenre}
                            substring={search}
                            className="!text-[--text-color] !text-lg CraftworkGroteskMedium"
                          />
                        </Box>
                      ))}
                    </Box>
                  )}
              </>
            ))
          ) : (
            <Typography className="!text-[--text-color] py-5 !text-lg CraftworkGroteskRegular">
              {t("musicGenreForm.noFound")}
            </Typography>
          );
        }}
      />
    </>
  );
};

export default EditActGenreForm;
