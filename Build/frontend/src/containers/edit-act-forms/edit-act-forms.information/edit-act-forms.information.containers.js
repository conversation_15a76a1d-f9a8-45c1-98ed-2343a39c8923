"use client";
import { useEffect } from "react";
import { CheckBox, Dropdown, Loader } from "@/component";
import ActInfoIAm from "@/ui/act-info/act-info.iam/act-info.iam.ui";
import { Add, Remove } from "@mui/icons-material";
import { Box, InputLabel, TextField, Typography } from "@mui/material";
import React from "react";
import { Controller } from "react-hook-form";
import { actLanguages, actRoles, getProfile } from "@/store/slice/act/act.slice";
import { getCurrentUserEmail } from "@/store/slice/auth/login.auth.slice";
import { useDispatch, useSelector } from "react-redux";
import { useTranslations } from "next-intl";
import ActInfoAnother from "@/ui/act-info/act-info.another/act-info.another.ui";
import { ACT_CONSTANTS } from "@/validation/auth/constants";

const EditActInformationForm = ({
  register,
  control,
  watch,
  reset,
  errors,
  profileId,
  setValue,
}) => {
  const dispatch = useDispatch();

  const {
    loading,
    roles: profileRole,
    languages: actLanguage,
    currentProfile,
  } = useSelector((state) => state.act);

  const t = useTranslations("actInformation");
  const { currentUser } = useSelector((state) => state.login);

  useEffect(() => {
    dispatch(actLanguages());
    dispatch(actRoles());
    dispatch(getCurrentUserEmail());
  }, []);

  {
    /** get user profile */
  }
  useEffect(() => {
    dispatch(getProfile(profileId))
      .unwrap()
      .then((response) => {
        const profileData = response.data.data;
        reset((prev) => ({ ...prev, ...profileData }));
      })
      .catch(() => {});
  }, [reset]);

  if (loading) {
    return <Loader />;
  }

  return (
    <>
      {(currentProfile?.profileType === "ACT_PROFILE" ||
        currentProfile?.profileType === "VIRTUAL_ACT_PROFILE") && (
        <Box className=" !w-full">
          <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
            {t("editActInfo.whoYouAct")}
          </InputLabel>
          <Box className="!border !border-[text-color] !rounded-[2px] !mt-2 !py-1">
            <Controller
              name="profileRole"
              control={control}
              render={({ field }) => (
                <Dropdown
                  onSelect={field.onChange}
                  options={profileRole || []}
                  selectedValue={field.value}
                  title="Select Role"
                  className="!text-[--text-color] !w-full"
                />
              )}
            />
          </Box>
          {errors && errors.profileRole && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.profileRole.message}
            </Typography>
          )}
        </Box>
      )}
      {currentProfile?.profileType === "VIRTUAL_ACT_PROFILE" && (
        <ActInfoAnother register={register} errors={errors} setValue={setValue} />
      )}

      <ActInfoIAm control={control} currentUserEmail={watch("useMyEmail") && currentUser?.email} />
      {!watch("useMyEmail") && (
        <Box className="!w-full">
          <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
            Profile Email Address
          </InputLabel>
          <TextField
            type="text"
            size="small"
            placeholder="Enter profile email address"
            name="profileEmail"
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            inputProps={{
              maxLength: ACT_CONSTANTS.PROFILE_EMAIL.MAX_LENGTH,
            }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& input": {
                color: "#EFEFEF",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !mb-5"
            {...register("profileEmail")}
          />
          {errors && errors.profileEmail && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.profileEmail.message}
            </Typography>
          )}
        </Box>
      )}
      {(currentProfile?.profileType === "ACT_PROFILE" ||
        currentProfile?.profileType === "VIRTUAL_ACT_PROFILE") && (
        <Box className="!w-full !my-5">
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskGX">
            {t("ActInfoForm.Performance")}
          </Typography>
          <Box className="lg:flex lg:flex-wrap grid md:grid-cols-3 grid-cols-2 gap-x-8">
            <Controller
              name="performanceLanguages"
              control={control}
              defaultValue={[]}
              render={({ field }) => (
                <>
                  {actLanguage?.map((language, index) => (
                    <Box key={index} className="flex items-center">
                      <CheckBox
                        className="!max-w-[24px]"
                        sx={{ color: "#EFEFEF", marginRight: "5px" }}
                        checked={field.value.includes(language)}
                        onChange={(e) => {
                          const newSelectedLanguages = e.target.checked
                            ? [...field.value, language]
                            : field.value.filter(
                                (selectedLanguage) => selectedLanguage !== language,
                              );
                          field.onChange(newSelectedLanguages);
                        }}
                      />
                      <label
                        htmlFor={`language-checkbox-${index}`}
                        className="!text-[--text-color] !text-sm CraftworkGroteskRegular cursor-pointer"
                      >
                        {language}
                      </label>
                    </Box>
                  ))}
                </>
              )}
            />

            {errors && errors.performanceLanguages && (
              <Typography as="span" className="text-sm !text-red-600">
                {errors.performanceLanguages.message}
              </Typography>
            )}
          </Box>
        </Box>
      )}
      <Box className="!w-full !my-5">
        <Typography className="!text-[--text-color] !text-sm CraftworkGroteskGX">
          {t("ActInfoForm.Communication")}
        </Typography>
        <Box className="lg:flex lg:flex-wrap grid md:grid-cols-3 grid-cols-2 gap-x-8">
          <Controller
            name="communicationLanguages"
            control={control}
            defaultValue={[]}
            render={({ field }) => (
              <>
                {actLanguage?.map((language, index) => (
                  <Box key={index} className="flex items-center">
                    <CheckBox
                      className="!max-w-[24px]"
                      sx={{ color: "#EFEFEF", marginRight: "5px" }}
                      checked={field.value.includes(language)}
                      onChange={(e) => {
                        const newSelectedLanguages = e.target.checked
                          ? [...field.value, language]
                          : field.value.filter((selectedLanguage) => selectedLanguage !== language);
                        field.onChange(newSelectedLanguages);
                      }}
                    />
                    <label
                      htmlFor={`language-checkbox-${index}`}
                      className="!text-[--text-color] !text-sm CraftworkGroteskRegular cursor-pointer"
                    >
                      {language}
                    </label>
                  </Box>
                ))}
              </>
            )}
          />

          {errors && errors.communicationLanguages && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.communicationLanguages.message}
            </Typography>
          )}
        </Box>
      </Box>
      <Box className=" !w-full">
        <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
          {t("ActInfoForm.Preferred")}
        </InputLabel>
        <Box className="!border !border-[text-color] !rounded-[2px] !mt-2 !py-1">
          <Controller
            name="preferredLanguage"
            control={control}
            render={({ field }) => (
              <Dropdown
                onSelect={field.onChange}
                options={actLanguage || []}
                selectedValue={field.value}
                title="Select Language"
                className="!text-[--text-color] !w-full"
              />
            )}
          />
        </Box>
        {errors && errors.preferredLanguage && (
          <Typography as="span" className="text-sm !text-red-600">
            {errors.preferredLanguage.message}
          </Typography>
        )}
      </Box>
      {/* <Box className="flex !pt-8 !gap-x-5"> */}
      <Box className="!w-full">
        {currentProfile?.profileType === "ACT_PROFILE" ||
        currentProfile?.profileType === "VIRTUAL_ACT_PROFILE" ? (
          <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
            {t("ActInfoForm.NameOfAct")}
          </InputLabel>
        ) : (
          <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
            Name of Venue
          </InputLabel>
        )}

        <TextField
          type="text"
          size="small"
          placeholder="Act name"
          name="profileName"
          InputLabelProps={{ style: { color: "#EFEFEF" } }}
          inputProps={{
            maxLength: ACT_CONSTANTS.PROFILE_NAME.MAX_LENGTH,
          }}
          sx={{
            "& input::placeholder": {
              color: "#EFEFEF",
              border: 0,
            },
            "& input": {
              color: "#EFEFEF",
              fontFamily: "var(--craftWorkRegular)",
            },
            "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            "& .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            border: 0,
          }}
          className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px]"
          {...register("profileName")}
        />
        {errors && errors.profileName && (
          <Typography as="span" className="text-sm !text-red-600">
            {errors.profileName.message}
          </Typography>
        )}
      </Box>
      {(currentProfile?.profileType === "ACT_PROFILE" ||
        currentProfile?.profileType === "VIRTUAL_ACT_PROFILE") && (
        <Box className="!w-full">
          <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
            {t("ActInfoForm.NumberOfMembers")}
          </InputLabel>
          <Controller
            name="numMembers"
            control={control}
            defaultValue={1}
            render={({ field }) => (
              <Box className="!border !w-full !flex !justify-between  !py-3 CraftworkGroteskRegular !border-white rounded-[2px] !mb-5">
                <Remove
                  className="!text-[--text-color] cursor-pointer !mx-2"
                  onClick={() => {
                    field.onChange(field.value - 1); // Decrement count
                  }}
                />
                <Typography className="!text-sm !text-[--text-color] CraftworkGroteskRegular">
                  {field.value} {/* Use field.value as the value */}
                </Typography>
                <Add
                  className="!text-[--text-color] cursor-pointer !mx-2"
                  onClick={() => {
                    field.onChange(field.value + 1); // Increment count
                  }}
                />
              </Box>
            )}
          />
        </Box>
      )}

      {/* </Box> */}
    </>
  );
};

export default EditActInformationForm;
