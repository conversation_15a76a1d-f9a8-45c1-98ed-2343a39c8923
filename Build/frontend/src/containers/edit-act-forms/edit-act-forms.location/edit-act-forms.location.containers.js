"use client";
import { <PERSON><PERSON><PERSON>, Loader } from "@/component";
import {
  Box,
  // FormControlLabel,
  InputAdornment,
  InputLabel,
  // Radio,
  // RadioGroup,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect } from "react";
import { Controller } from "react-hook-form";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
// import RadioButtonUncheckedIcon from "@mui/icons-material/RadioButtonUnchecked";
// import RadioButtonIcon from "@/assets/svg/RadioButton.svg";
import { useSelector } from "react-redux";
import { useTranslations } from "next-intl";
import AutoCompleteLocation from "@/component/autocomplete/autocomplete-location.component";
import { ACT_CONSTANTS } from "@/validation/auth/constants";

const EditActLocationForm = ({ control, register, errors, setValue }) => {
  const t = useTranslations("actLocation");

  const { loading: actLoading } = useSelector((state) => state.act);
  const { currentProfile } = useSelector((state) => state.act);
  const [locationValue, setLocationValue] = React.useState("");

  useEffect(() => {
    if (currentProfile?.locationDto) {
      setLocationValue(
        `${currentProfile?.locationDto?.city}, ${currentProfile?.locationDto?.state}, ${currentProfile?.locationDto?.country}`,
      );
    }
  }, [currentProfile]);

  useEffect(() => {
    if (locationValue) {
      setValue("city", locationValue.city);
      setValue("state", locationValue.state);
      setValue("country", locationValue.country);
    }
  }, [locationValue]);

  if (actLoading) {
    return <Loader />;
  }

  return (
    <>
      {/* <Controller
        name="useMyLocation"
        control={control}
        render={({ field }) => (
          <Box className="flex items-center !mb-2">
            <CheckBox
              className="!max-w-[24px]"
              sx={{ color: "#EFEFEF", marginRight: "5px" }}
              checked={field.value}
              onChange={(e) => field.onChange(e.target.checked)}
            />
            <label
              htmlFor="location"
              className="!text-[--text-color] !text-sm CraftworkGroteskRegular cursor-pointer"
            >
              {t("ActLocationForm.UseLocation")}
            </label>
          </Box>
        )}
      /> */}
      <Box className="w-full">
        <AutoCompleteLocation
          value={locationValue}
          setValue={setLocationValue}
          textFieldClass="border !border-[--text-color] rounded-[2px]"
          className="w-full"
          showFilter={true}
          readOnly={!!locationValue}
          inputProps={{
            maxLength: ACT_CONSTANTS.LOCATION.CITY_MAX_LENGTH,
          }}
        />

        {errors && (errors.city || errors.state || errors.country) && (
          <Typography as="span" className="text-sm !text-red-600">
            Please select a valid location
          </Typography>
        )}
      </Box>
      <Box className="!w-full !mt-4">
        <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX">
          {t("ActLocationForm.StreetAddress")}
        </InputLabel>
        <TextField
          type="text"
          size="small"
          placeholder={t("ActLocationForm.addressLine")}
          InputLabelProps={{ style: { color: "#EFEFEF" } }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start" style={{ cursor: "pointer" }}>
                <LocationSvg className="!w-6 !h-6" />
              </InputAdornment>
            ),
            inputProps: {
              maxLength: ACT_CONSTANTS.LOCATION.STREET_ADDRESS_MAX_LENGTH,
            },
          }}
          sx={{
            "& input::placeholder": {
              color: "#EFEFEF",
              border: 0,
            },
            "& input": {
              color: "#EFEFEF",
              fontFamily: "var(--craftWorkRegular)",
            },
            "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            "& .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            border: 0,
          }}
          name="streetAddress"
          className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !my-2"
          {...register("streetAddress")}
        />
        {errors && errors.streetAddress && (
          <Typography as="span" className="text-sm !text-red-600">
            {errors.streetAddress.message}
          </Typography>
        )}
      </Box>
      {/** zip code */}
      <Box className="!w-full !mt-3">
        <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX">
          {t("ActLocationForm.Zip")}
        </InputLabel>
        <TextField
          type="text"
          size="small"
          placeholder="000000"
          InputLabelProps={{ style: { color: "#EFEFEF" } }}
          InputProps={{
            inputProps: {
              maxLength: ACT_CONSTANTS.LOCATION.ZIP_CODE_MAX_LENGTH,
            },
          }}
          sx={{
            "& input::placeholder": {
              color: "#EFEFEF",
              border: 0,
            },
            "& input": {
              color: "#EFEFEF",
              fontFamily: "var(--craftWorkRegular)",
            },
            "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            "& .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            border: 0,
          }}
          className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !my-2"
          name="zipCode"
          {...register("zipCode")}
        />
        {errors && errors.zipCode && (
          <Typography as="span" className="text-sm !text-red-600">
            {errors.zipCode.message}
          </Typography>
        )}
      </Box>
      {currentProfile?.profileType === "ACT_PROFILE" && (
        <Box className="!w-full">
          <Controller
            control={control}
            name="canTravelLongDistance"
            render={({ field }) => (
              <Box className="flex items-center !mb-4">
                <CheckBox
                  className="!max-w-[24px]"
                  sx={{ color: "#EFEFEF", marginRight: "5px" }}
                  checked={field.value}
                  onChange={(e) => field.onChange(e.target.checked)}
                />
                <label
                  htmlFor="location"
                  className="!text-[--text-color] !text-sm CraftworkGroteskRegular cursor-pointer"
                >
                  {t("ActLocationForm.LongDistance")}
                </label>
              </Box>
            )}
          />
        </Box>
      )}
    </>
  );
};

export default EditActLocationForm;
