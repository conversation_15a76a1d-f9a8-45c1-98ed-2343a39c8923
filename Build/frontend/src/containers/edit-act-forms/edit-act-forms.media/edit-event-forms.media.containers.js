"use client";
import React, { useEffect } from "react";
import { useForm, Controller } from "react-hook-form";
import PhotoIcon from "@/assets/svg/act-type.svg/PhotoIcon.svg";
import { Box, IconButton, Typography } from "@mui/material";
import { CommonImage } from "@/component";
import DragAndDropIcon from "@/assets/svg/act-type.svg/DragAndDrop.svg";
import NoteList from "@/ui/note-list/note-list.ui";
import { useDispatch } from "react-redux";
import { actMediaValidation } from "@/validation/act/act.validation";
import { yupResolver } from "@hookform/resolvers/yup";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { useDropzone } from "react-dropzone";
import classNames from "classnames";
import DeleteIcon from "@/assets/svg/DeleteIcon.svg";
import { useTranslations } from "next-intl";
import { createEventMedia, deleteEventMedia } from "@/store/slice/booking/booking.slice";
import { cn } from "@/lib/cn";

const EditEventMediaForm = ({ profileId, setFetch, actPhotos, mediaClass = "w-[131px]" }) => {
  const { showSnackbar } = useSnackbar();
  const t = useTranslations("actMedia");
  const resolver = yupResolver(actMediaValidation);
  const dispatch = useDispatch();
  //const [loading, setLoading] = useState(false);

  const {
    formState: { errors },
    control,
    watch,
    setValue,
  } = useForm({
    resolver,
    defaultValues: {
      actPhotos: [],
      videoLink: [],
      audioLink: [],
    },
    mode: "onSubmit",
  });
  const imageNotes = [
    t("actMediaForm.photoSize"),
    t("actMediaForm.uploadingPhotos"),
    t("actMediaForm.perfectSize"),
  ];

  useEffect(() => {
    setValue("actPhotos", actPhotos);
  }, [actPhotos]);
  {
    /** upload photo */
  }
  const { getRootProps, getInputProps } = useDropzone({
    accept: { "image/*": [".jpeg", ".png", ".jpg"] },
    onDrop: (acceptedFiles) => {
      for (let i = 0; i < acceptedFiles.length; i++) {
        const formData = new FormData();
        const file = acceptedFiles[i];
        formData.append("file", file);
        handleUploadMedia(formData, profileId);
      }
      // dispatch(getActMedia({ profileId }))
      //   .unwrap()
      //   .then((response) => {
      //     if (response.status === 200) {
      //       let imageUrl = response.data.data.imageUrls;
      //       setValue("actPhotos", imageUrl);
      //     }
      //   })
      //   .catch((error) => {
      //     showSnackbar(error, "error");
      //   });
    },
  });

  /** handle delete image */
  const handleDeleteImage = (event, fileUrl) => {
    // get file mame from the url
    const imageName = fileUrl.split("/").pop();
    event.stopPropagation();
    event.preventDefault();
    dispatch(deleteEventMedia({ imageName, profileId }))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          //setLoading(false);
          setFetch((fetch) => fetch + 1);
          showSnackbar(response.data.message, "success");
        }
      })
      .catch((error) => {
        //setLoading(false);
        showSnackbar(error, "error");
      });

    // event.stopPropagation();
    // event.preventDefault();
    // setValue(
    //   "actPhotos",
    //   getValues("actPhotos").filter((_, index) => index !== indexToDelete)
    // );
  };

  const handleUploadMedia = (formData, profileId) => {
    dispatch(createEventMedia({ file: formData, profileId }))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          //setLoading(false);
          setFetch((fetch) => fetch + 1);
          showSnackbar(response.data.message, "success");
        }
      })
      .catch((error) => {
        //setLoading(false);
        showSnackbar(error, "error");
      });
  };

  return (
    <form>
      <Box className="!flex !gap-x-3 !pt-6">
        <PhotoIcon className="!text-2xl" />
        <Typography className="!text-[--text-color] !text-lg CraftworkGroteskMedium">
          {t("actMediaForm.photos")}
        </Typography>
      </Box>
      {/** photos */}
      <Box className="!flex !flex-wrap !gap-3 !pt-2">
        <Controller
          name="actPhotos"
          control={control}
          defaultValue={watch("actPhotos")}
          render={({}) => {
            const photos = watch("actPhotos");
            const isEmpty = photos && photos?.length === 0;
            return (
              <Box
                {...getRootProps({
                  className: "cursor-pointer",
                })}
                className={classNames("!mt-3  !w-full  !rounded-[2px]", {
                  "!border-dashed lg:!w-[85%] !h-[220px] !flex !justify-center !items-center !border-[2px]":
                    isEmpty,
                })}
              >
                <Box>
                  <input {...getInputProps({})} />
                  {isEmpty ? (
                    // Render default upload interface when no files are selected
                    <Box className="!h-full">
                      <Box className="!flex !gap-x-4">
                        <DragAndDropIcon className="!text-base" />
                        <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy">
                          {t("actMediaForm.drag&Drop")}
                        </Typography>
                      </Box>
                      <Typography className="!text-center !text-[--text-color] !text-sm !CraftworkGroteskRegular !py-3">
                        {t("actMediaForm.duploaia")}
                      </Typography>
                    </Box>
                  ) : (
                    // Render file names when files are selected
                    <Box className="!flex !flex-wrap !gap-3 relative">
                      {photos &&
                        photos.length > 0 &&
                        photos.map((file, index) => (
                          <Box key={index} className="relative">
                            <CommonImage
                              src={file.includes("http") ? file : ""}
                              alt={"Preview 'media'}"}
                              className={cn(
                                "!border !relative h-[131px] !rounded-[4px] !border-[--divider-color]",
                                mediaClass,
                              )}
                              onLoad={() => URL.revokeObjectURL(file)}
                              width={1}
                              height={1}
                              layout="responsive"
                            />
                            {index === 0 && (
                              <Typography className="!text-[--text-color] !text-xs Poppins700 !absolute !left-0 !px-1 !py-2 !top-2 !bg-[--inprogress-color] !rounded-e-[8px]">
                                {t("actMediaForm.cover")}
                              </Typography>
                            )}
                            <IconButton
                              className="!p-1 !rounded-full !bg-[--text-color] !absolute !right-2 !top-2"
                              onClick={(event) => {
                                handleDeleteImage(event, file);
                                event.stopPropagation();
                              }}
                            >
                              <DeleteIcon className="!text-[21px] " />
                            </IconButton>
                          </Box>
                        ))}
                      <Box
                        className={cn(
                          "!border-dashed !border-[2px] !rounded-[2px] !border-[--text-color] !h-[131px]",
                          mediaClass,
                        )}
                      >
                        <Box className="!h-full !flex !p-2 !flex-col !items-center !justify-center">
                          <DragAndDropIcon className="!text-base" />
                          <Typography className="!text-[--text-color] !text-center !leading-[22.4px] !text-sm CraftworkGroteskHeavy">
                            {t("actMediaForm.drag&Drop")}
                          </Typography>
                          <Typography className="!text-center !text-[--text-color] !text-sm !CraftworkGroteskRegular !pt-4">
                            {t("actMediaForm.toUpload")}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  )}
                </Box>
              </Box>
            );
          }}
        />

        <NoteList notes={imageNotes} />
        {errors && errors.actPhotos && (
          <span className="!mt-1 text-sm !text-red-600">{errors.actPhotos.message}</span>
        )}
      </Box>

      {/** video link */}
      {/* <Box className="!w-full">
        <InputLabel className="!flex !gap-2 !py-2">
          <VideoSvg className="!text-2xl" />
          <Typography className="!text-lg !text-[--text-color] CraftworkGroteskMedium">
            {t("actMediaForm.videoLink")}
          </Typography>
        </InputLabel>
        <Box className="!flex">
          <TextField
            type="text"
            size="small"
            placeholder={t("actMediaForm.vimeo")}
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            InputProps={{
              startAdornment: (
                <InputAdornment
                  position="start"
                  style={{
                    cursor: "pointer",
                  }}
                >
                  <LinkSvg className="!text-2xl" />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end" style={{ cursor: "pointer" }}>
                  {search && (
                    <DeleteSvg
                      className="!text-[--text-color] !text-lg"
                      onClick={() => setSearch("")}
                    />
                  )}
                </InputAdornment>
              ),
            }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& input": {
                color: "#EFEFEF",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px]"
            {...register(`videoLink.${0}.videoLink`)}
          />
          {videoFields.length > 1 && (
            <IconButton onClick={() => removeVideo(videoFields[0])}>
              <RemoveIcon className="!text-xl !text-[--text-color] mt-2" />
            </IconButton>
          )}
        </Box>
        {videoFields.slice(1).map((fields, index) => (
          <Box className="!flex" key={index}>
            <TextField
              key={fields.id}
              type="text"
              size="small"
              placeholder="link to Vimeo or Youtube"
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              InputProps={{
                startAdornment: (
                  <InputAdornment
                    position="start"
                    style={{
                      cursor: "pointer",
                    }}
                  >
                    <LinkSvg className="!text-2xl" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end" style={{ cursor: "pointer" }}>
                    {search && (
                      <DeleteSvg
                        className="!text-[--text-color] !text-lg"
                        onClick={() => setSearch("")}
                      />
                    )}
                  </InputAdornment>
                ),
              }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] mt-2"
              {...register(`videoLink.${index + 1}.videoLink`)}
            />
            <IconButton onClick={() => removeVideo(index + 1)}>
              <RemoveIcon className="!text-xl !text-[--text-color] mt-2" />
            </IconButton>
          </Box>
        ))}

        <Button
          className="!flex !gap-x-1 !normal-case"
          sx={{
            border: 0,
            "&.MuiButtonBase-root": {
              color: "transparent !important",
            },
          }}
          onClick={() => appendVideo({ videoLink: "" })}
        >
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy">
            {t("actMediaForm.addLink")}
          </Typography>
          <Add className="!text-xl !text-[--text-color]" />
        </Button>

        <NoteList notes={videoNotes} />
      </Box> */}

      {/** audio link */}
      {/* <Box className="!w-full">
        <InputLabel className="!flex !gap-2 !py-6">
          <AudioSvg className="!text-2xl" />
          <Typography className="!text-lg !text-[--text-color] CraftworkGroteskMedium">
            {t("actMediaForm.audioLink")}
          </Typography>
        </InputLabel>
        <Box className="!flex">
          <TextField
            type="text"
            size="small"
            placeholder={t("actMediaForm.soundCloud")}
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            InputProps={{
              startAdornment: (
                <InputAdornment
                  position="start"
                  style={{
                    cursor: "pointer",
                  }}
                >
                  <LinkSvg className="!text-2xl" />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end" style={{ cursor: "pointer" }}>
                  {search && (
                    <DeleteSvg
                      className="!text-[--text-color] !text-base"
                      onClick={() => setSearch("")}
                    />
                  )}
                </InputAdornment>
              ),
            }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& input": {
                color: "#EFEFEF",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px]"
            {...register(`audioLink.${0}.audioLink`)}
          />
          {audioFields.length > 1 && (
            <IconButton onClick={() => removeAudio(audioFields[0])}>
              <RemoveIcon className="!text-xl !text-[--text-color] mt-2" />
            </IconButton>
          )}
        </Box>
        {audioFields.slice(1).map((fields, index) => (
          <Box className="!flex" key={index}>
            <TextField
              key={fields.id}
              type="text"
              size="small"
              placeholder={t("actMediaForm.soundCloud")}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              InputProps={{
                startAdornment: (
                  <InputAdornment
                    position="start"
                    style={{
                      cursor: "pointer",
                    }}
                  >
                    <LinkSvg className="!text-2xl" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end" style={{ cursor: "pointer" }}>
                    {search && (
                      <DeleteSvg
                        className="!text-[--text-color] !text-base"
                        onClick={() => setSearch("")}
                      />
                    )}
                  </InputAdornment>
                ),
              }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] mt-2"
              {...register(`audioLink.${index + 1}.audioLink`)}
            />
            <IconButton onClick={() => removeAudio(index + 1)}>
              <RemoveIcon className="!text-xl !text-[--text-color] mt-2" />
            </IconButton>
          </Box>
        ))}
        <Button
          className="!flex !gap-x-1 mb-4 !normal-case"
          sx={{
            border: 0,
            "&.MuiButtonBase-root": {
              color: "transparent !important",
            },
          }}
          onClick={() => appendAudio({ audioLink: "" })}
        >
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy">
            {t("actMediaForm.addLink")}
          </Typography>
          <Add className="!text-xl !text-[--text-color]" />
        </Button>
      </Box> */}
    </form>
  );
};

export default EditEventMediaForm;
