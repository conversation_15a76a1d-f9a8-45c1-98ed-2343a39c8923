"use client";
import { CheckBox, Dropdown, Loader } from "@/component";
import { Box, FormControlLabel, Radio, RadioGroup, TextField, Typography } from "@mui/material";
import React, { useEffect } from "react";
import { Controller } from "react-hook-form";
import { getActPayment } from "@/store/slice/act/act.slice";
import { useSelector, useDispatch } from "react-redux";
import { useTranslations } from "next-intl";
import { RadioButtonUnchecked } from "@mui/icons-material";
import RadioButtonIcon from "@/assets/svg/RadioButton.svg";
import { ACT_CONSTANTS } from "@/validation/auth/constants";

const EditActPaymentForm = ({ register, control, errors, watch, setValue }) => {
  const t = useTranslations("actPayment");
  const label = ["For rent", "Not for rent"];
  const { actPayment, loading: actLoading, currentProfile } = useSelector((state) => state.act);
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(getActPayment());
  }, []);

  if (actLoading) {
    return <Loader />;
  }
  return (
    <>
      {(currentProfile?.profileType === "VENUE_PROFILE" ||
        currentProfile?.profileType === "VIRTUAL_VENUE_PROFILE") && (
        <Box className="w-full pt-8">
          <Controller
            name="forRentOrNot"
            control={control}
            defaultValue={""}
            render={({ field }) => (
              <RadioGroup
                row
                value={field.value}
                onChange={(e) => {
                  field.onChange(e.target.value);
                }}
              >
                {label.map((data, id) => (
                  <FormControlLabel
                    key={id}
                    value={data}
                    control={
                      <Radio
                        icon={<RadioButtonUnchecked className="!w-6 !h-6 !text-[--text-color]" />}
                        checkedIcon={<RadioButtonIcon className="!w-6 !h-6" />}
                      />
                    }
                    label={
                      <Typography
                        className="!text-[--text-color] !normal-case CraftworkGroteskHeavy !text-sm"
                        htmlFor={`radio-${id}`}
                      >
                        {data}
                      </Typography>
                    }
                  />
                ))}
              </RadioGroup>
            )}
          />
        </Box>
      )}

      {(currentProfile?.profileType === "ACT_PROFILE" ||
        currentProfile?.profileType === "VIRTUAL_ACT_PROFILE" ||
        watch("forRentOrNot") === "For rent") && (
        <Box className="!mt-4">
          <Typography className="!text-[--text-color] !mb-2 !text-lg CraftworkGroteskMedium">
            {t("actPaymentForm.typicalPrice")}
          </Typography>
          <Box className="!w-full !inline-flex !items-center lg:gap-x-3 gap-x-2">
            <Box className="!w-full">
              <TextField
                size="small"
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                    fontFamily: "var(--craftWorkRegular)",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-input": {
                    padding: 0,
                    paddingY: 1,
                  },
                  border: 0,
                }}
                className="!border !w-full !py-1 !px-1 CraftworkGroteskRegular !border-white rounded-[2px]"
                {...register("typicalPrice")}
                type="number"
                inputProps={{
                  min: 0,
                  onChange: (e) => {
                    const maxLength =
                      Math.pow(10, ACT_CONSTANTS.PAYMENT.TYPICAL_PRICE_MAX_LENGTH) - 1;
                    if (e.target.value > maxLength) {
                      e.target.value = e.target.value.slice(0, String(maxLength).length);
                    }
                  },
                }}
              />
              <Box className=" h-8 lg:h-4">
                {errors && errors.typicalPrice ? (
                  <Typography as="span" className="text-sm !text-red-600">
                    {errors.typicalPrice.message}
                  </Typography>
                ) : (
                  <Typography as="span" className="text-sm" style={{ color: "transparent" }}>
                    &nbsp;
                  </Typography>
                )}
              </Box>
            </Box>

            <Box className=" !w-4/5">
              <Box className="!border !border-[--text-color]  !w-full !py-1 !rounded-[4px]">
                <Controller
                  name="standardPriceCurrency"
                  control={control}
                  render={({ field }) => (
                    <Dropdown
                      options={actPayment?.currencies}
                      onSelect={(selectedPayment) => {
                        setValue("minimalPriceCurrency", selectedPayment);
                        field.onChange(selectedPayment);
                      }}
                      title={t("actPaymentForm.selectCurrency")}
                      selectedValue={field.value}
                      className="!text-[--text-color] !w-full !max-h-[40px]"
                    />
                  )}
                />
              </Box>
              <Box className=" h-8 lg:h-4">
                {errors && errors.standardPriceCurrency ? (
                  <Typography as="span" className="text-sm !text-red-600">
                    {errors.standardPriceCurrency.message}
                  </Typography>
                ) : (
                  <Typography as="span" className="text-sm" style={{ color: "transparent" }}>
                    &nbsp;
                  </Typography>
                )}
              </Box>
            </Box>

            <Typography className="!text-[--text-color] pb-3 !text-sm CraftworkGroteskRegular !text-center">
              /{t("actPaymentForm.per")}
            </Typography>
            <Box className="!w-4/5">
              <Box className="!border !border-[--text-color] !w-full !py-1 !rounded-[4px]">
                <Controller
                  name="standardPricePer"
                  control={control}
                  render={({ field }) => (
                    <Dropdown
                      options={actPayment?.paymentOptions}
                      onSelect={field.onChange}
                      selectedValue={field.value}
                      title={t("actPaymentForm.paymentOption")}
                      className="!text-[--text-color] !lowercase !w-full !max-h-[40px]"
                    />
                  )}
                />
              </Box>
              <Box className=" h-8 lg:h-4">
                {errors && errors.standardPricePer ? (
                  <Typography as="span" className="text-sm !text-red-600">
                    {errors.standardPricePer.message}
                  </Typography>
                ) : (
                  <Typography as="span" className="text-xs" style={{ color: "transparent" }}>
                    &nbsp;
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>
        </Box>
      )}

      {/** charity payment */}
      {(currentProfile?.profileType === "ACT_PROFILE" ||
        currentProfile?.profileType === "VIRTUAL_ACT_PROFILE" ||
        watch("forRentOrNot") === "For rent") && (
        <Box className="!mt-9">
          <Typography className="!text-[--text-color] !text-lg CraftworkGroteskMedium">
            {t("actPaymentForm.minimalPrice")}
          </Typography>
          <Typography className="!text-[--text-color] !mb-4 !text-sm CraftworkGroteskGX">
            {currentProfile?.profileType === "ACT_PROFILE" ||
            currentProfile?.profileType === "VIRTUAL_ACT_PROFILE"
              ? t("actPaymentForm.charity")
              : "Сharity or Cause Event with benefits to Venue"}
          </Typography>
          <Box className="!w-full !inline-flex !items-center lg:gap-x-3 gap-x-2">
            <Box className=" !w-full">
              <TextField
                size="small"
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                    fontFamily: "var(--craftWorkRegular)",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-input": {
                    padding: 0,
                    paddingY: 1,
                  },
                  border: 0,
                }}
                className="!border !w-full !py-1 !px-1 CraftworkGroteskRegular !border-white rounded-[2px]"
                {...register("charityPrice")}
                type="number"
                inputProps={{
                  min: 0,
                  onChange: (e) => {
                    const maxLength =
                      Math.pow(10, ACT_CONSTANTS.PAYMENT.MINIMAL_PRICE_MAX_LENGTH) - 1;
                    if (e.target.value > maxLength) {
                      e.target.value = e.target.value.slice(0, String(maxLength).length);
                    }
                  },
                }}
              />
              <Box className=" h-8 lg:h-4">
                {errors && errors.charityPrice ? (
                  <Typography as="span" className="text-sm !text-red-600">
                    {errors.charityPrice.message}
                  </Typography>
                ) : (
                  <Typography as="span" className="text-sm" style={{ color: "transparent" }}>
                    &nbsp;
                  </Typography>
                )}
              </Box>
            </Box>
            <Box className=" !w-4/5">
              <Box className="!border !border-[--text-color] !w-full !py-1 !rounded-[4px]">
                <Controller
                  name="minimalPriceCurrency"
                  control={control}
                  render={({ field }) => (
                    <Dropdown
                      options={actPayment?.currencies}
                      onSelect={field.onChange}
                      selectedValue={field.value}
                      title={t("actPaymentForm.selectCurrency")}
                      className="!text-[--text-color] !w-full !max-h-[40px]"
                      disabled={watch("standardPriceCurrency") ? true : false}
                    />
                  )}
                />
              </Box>
              <Box className=" h-8 lg:h-4">
                {errors && errors.minimalPriceCurrency ? (
                  <Typography as="span" className="text-sm !text-red-600">
                    {errors.minimalPriceCurrency.message}
                  </Typography>
                ) : (
                  <Typography as="span" className="text-sm" style={{ color: "transparent" }}>
                    &nbsp;
                  </Typography>
                )}
              </Box>
            </Box>

            <Typography className="!text-[--text-color] pb-3 !text-sm CraftworkGroteskRegular !text-center">
              /{t("actPaymentForm.per")}
            </Typography>
            <Box className="!w-4/5">
              <Box className="!border !border-[--text-color] !w-full !py-1 !rounded-[4px]">
                <Controller
                  name="minimalPricePer"
                  control={control}
                  render={({ field }) => (
                    <Dropdown
                      options={actPayment?.paymentOptions}
                      onSelect={field.onChange}
                      selectedValue={field.value}
                      title={t("actPaymentForm.paymentOption")}
                      className="!text-[--text-color] !lowercase !max-h-[40px] !w-full"
                    />
                  )}
                />
              </Box>
              <Box className=" h-8 lg:h-4">
                {errors && errors.minimalPricePer ? (
                  <Typography as="span" className="text-sm !text-red-600">
                    {errors.minimalPricePer.message}
                  </Typography>
                ) : (
                  <Typography as="span" className="text-sm" style={{ color: "transparent" }}>
                    &nbsp;
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>
        </Box>
      )}

      {(currentProfile?.profileType === "ACT_PROFILE" ||
        currentProfile?.profileType === "VIRTUAL_ACT_PROFILE" ||
        watch("forRentOrNot") === "For rent") && (
        <Box>
          <Typography className="!text-[--text-color] !text-lg CraftworkGroteskMedium !mt-8">
            {t("actPaymentForm.paymentMethod")}
          </Typography>
          <Box className="!flex !flex-wrap !gap-4 !w-full">
            <Controller
              name="paymentMethod"
              control={control}
              defaultValue={[]}
              render={({ field }) => (
                <>
                  {actPayment?.paymentMethods?.map((paymentMethod, index) => (
                    <Box key={index} className="flex items-center !gap-x-5">
                      <CheckBox
                        className="!max-w-[24px]"
                        sx={{ color: "#EFEFEF", marginRight: "5px" }}
                        checked={field.value?.includes(paymentMethod)}
                        onChange={(e) => {
                          const newSelectedPaymentMethod = e.target.checked
                            ? [...(field.value || []), paymentMethod]
                            : (field.value || []).filter(
                                (selectedPaymentMethod) => selectedPaymentMethod !== paymentMethod,
                              );
                          field.onChange(newSelectedPaymentMethod);
                        }}
                      />
                      <label
                        htmlFor={`payment-checkbox-${index}`}
                        className="!text-[--text-color] !text-lg CraftworkGroteskMedium"
                      >
                        {paymentMethod}
                      </label>
                    </Box>
                  ))}
                </>
              )}
            />
          </Box>
          {errors && errors.paymentMethod && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.paymentMethod.message}
            </Typography>
          )}
        </Box>
      )}
    </>
  );
};

export default EditActPaymentForm;
