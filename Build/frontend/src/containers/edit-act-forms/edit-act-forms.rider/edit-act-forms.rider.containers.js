"use client";
import { Box, Typography } from "@mui/material";
import React, { useEffect } from "react";
import PDFIcon from "@/assets/svg/PDFIcon.svg";
import UploadIcon from "@/assets/svg/UploadIcon.svg";
import NoteList from "@/ui/note-list/note-list.ui";
import { Button } from "@/component";
import ViewIcon from "@/assets/svg/ViewIcon.svg";
import DeleteSvg from "@/assets/svg/act-type.svg/DeleteSvg.svg";
import { Add } from "@mui/icons-material";
import { useSelector } from "react-redux";
import { useTranslations } from "next-intl";

const EditActRiderForm = ({ handleUploadMedia, profileId, handleDeleteMedia }) => {
  const t = useTranslations("rider");
  const fileUploadRef = React.useRef(null);
  const { riderList } = useSelector((state) => state.act);
  const noteList = [t("document"), t("recommandons")];
  const { currentProfile } = useSelector((state) => state.act);
  const [addRider, setAddRider] = React.useState(true);

  const [fileName, setFileName] = React.useState(t("actRider"));

  useEffect(() => {
    if (
      currentProfile.profileType === "VENUE_PROFILE" ||
      currentProfile.profileType === "VIRTUAL_VENUE_PROFILE"
    ) {
      setFileName("Venue’s rider");
    }
  }, [currentProfile]);

  const handleInputChange = (e) => {
    setFileName(e.target.files[0].name);
    const formData = new FormData();
    const file = e.target.files[0];
    formData.append("file", file);
    handleUploadMedia(formData);
  };

  return (
    <>
      {!addRider && (
        <Box className="!border-[2px] !flex !justify-between !p-5 !rounded-[5px] !border-dashed !border-[--divider-color] !w-full">
          <Box className="!flex !gap-x-3 !items-center">
            <PDFIcon className="!w-10 !h-10" />
            <Typography className="!text-sm !text-[--text-color] CraftworkGroteskHeavy">
              {fileName}
            </Typography>
          </Box>
          <Box className="!flex !gap-x-1 cursor-pointer !items-center">
            <input
              type="file"
              accept="application/pdf"
              onChange={handleInputChange}
              style={{ display: "none" }}
              ref={fileUploadRef}
            />
            <Typography
              className="!text-sm !text-[--text-color] !underline CraftworkGroteskHeavy"
              onClick={() => fileUploadRef.current.click()}
            >
              {t("upload")}
            </Typography>
            <UploadIcon className="!w-6 !h-6" />
          </Box>
        </Box>
      )}

      {!addRider && <NoteList notes={noteList} />}
      {riderList &&
        riderList.map((rider, index) => {
          return (
            <>
              <Box
                key={index}
                className="!bg-[--footer-bg] !flex !justify-between !p-4 !rounded-[5px] !border !border-[--divider-color] !w-full"
              >
                <Box className="!flex !gap-x-1 !items-center">
                  <PDFIcon className="!w-10 !h-10" />
                  <Typography className="!text-sm !text-[--text-color] CraftworkGroteskHeavy">
                    {(
                      (rider.riderDocument && rider.riderDocument.split("/").pop()) ||
                      "Act’s rider"
                    ).substring(0, 18)}
                    {(
                      (rider.riderDocument && rider.riderDocument.split("/").pop()) ||
                      "Act’s rider"
                    ).length > 18
                      ? "..."
                      : ""}
                  </Typography>
                </Box>
                <Box className="!flex !gap-x-2">
                  <Button
                    className="!bg-[--text-color] !py-1 lg:!flex lg:!gap-x-2"
                    sx={{
                      minWidth: 0,
                      border: 0,
                      "&.MuiButtonBase-root": {
                        color: "white !important",
                      },
                    }}
                    onClick={() => {
                      const url = `/${profileId}/pdf-view?pdf=${rider.riderDocument.split("/").pop()}`;
                      const newWindow = window.open(url, "_blank", "noopener,noreferrer");
                      if (newWindow) newWindow.opener = null;
                      // router.push(
                      //   `/${profileId}/pdf-view?pdf=${rider.riderDocument.split("/").pop()}`,
                      // )
                    }}
                  >
                    <Typography className="!normal-case hidden lg:inline CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                      {t("view")}
                    </Typography>
                    <ViewIcon className="!text-lg" />
                  </Button>
                  <Button
                    className="!border-[2px] !px-2 !border-[--text-color] !rounded-[4px]"
                    sx={{
                      minWidth: 0,
                      border: 0,
                      "&.MuiButtonBase-root": {
                        color: "white !important",
                      },
                    }}
                    onClick={() => handleDeleteMedia(rider.riderDocument.split("/").pop())}
                  >
                    <DeleteSvg className="!text-xl" />
                  </Button>
                </Box>
              </Box>
              <Typography className="!text-sm !text-[--text-color] CraftworkGroteskRegular !pt-1">
                {`Uploaded ${
                  rider.uploadedTime && new Date(rider.uploadedTime).toLocaleDateString()
                }`}
              </Typography>
              <Button
                className="!normal-case !mt-12"
                sx={{
                  border: 0,
                  padding: 0,
                  "&.MuiButtonBase-root": {
                    color: "white !important",
                  },
                }}
              />
            </>
          );
        })}

      {addRider && (
        <span
          onClick={() => setAddRider(false)}
          className="flex gap-2 items-center cursor-pointer justify-center"
        >
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
            {t("addRider")}
          </Typography>
          <Add className="!text-[--text-color] !text-lg" />
        </span>
      )}
    </>
  );
};

export default EditActRiderForm;
