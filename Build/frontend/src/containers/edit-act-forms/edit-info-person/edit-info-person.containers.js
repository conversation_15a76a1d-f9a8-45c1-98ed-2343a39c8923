"use client";
import {
  Box,
  InputAdornment,
  InputLabel,
  TextField,
  Typography,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@mui/material";
import React from "react";
import SpotifyIcon from "@/assets/svg/Spotify.svg";
import FacebookOutlinedIcon from "@mui/icons-material/FacebookOutlined";
import { Instagram, YouTube } from "@mui/icons-material";
import SoundCloudIcon from "@/assets/svg/SoundCloud.svg";
import { Controller } from "react-hook-form";
import RadioButtonUncheckedIcon from "@mui/icons-material/RadioButtonUnchecked";
import RadioButtonIcon from "@/assets/svg/RadioButton.svg";
import { useTranslations } from "next-intl";
import VenueWorkingHour from "@/common/venue-working-hour/venue-working-hour.common";
import { useSelector } from "react-redux";
import { ACT_CONSTANTS } from "@/validation/auth/constants";

const EditActInfoPersonContainer = ({ register, control, errors, setValue, watch }) => {
  const t = useTranslations("actInfoPerson");
  const s = useTranslations("editActCommon");
  const label = [s("children"), s("adult")];
  const { currentProfile } = useSelector((state) => state.act);
  return (
    <div className="lg:!mt-12 md:!mt-12 !mt-4 !max-w-[750px]">
      <Box className="!w-full">
        {/** Bio */}
        <Box>
          <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !py-2">
            {t("ActInfoPersonForm.Bio")}
          </InputLabel>
          <TextField
            type="text"
            size="small"
            multiline
            rows={5}
            placeholder={t("ActInfoPersonForm.Bio")}
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            inputProps={{
              maxLength: ACT_CONSTANTS.ACT_INFO.BIO_MAX_LENGTH,
            }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& .MuiOutlinedInput-root": {
                color: "var(--text-color)",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            name="bio"
            className="!border !w-full !h-[140px] !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !mb-5"
            {...register("bio")}
          />
          {errors && errors.bio && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.bio.message}
            </Typography>
          )}
        </Box>

        {currentProfile?.profileType === "ACT_PROFILE" ||
          (currentProfile?.profileType === "VIRTUAL_ACT_PROFILE" && (
            <Box className="!w-full">
              {/** Radio button */}
              <Controller
                name="suitableForAdultsOrChildren"
                control={control}
                render={({ field }) => (
                  <RadioGroup
                    row
                    value={field.value}
                    onChange={(e) => field.onChange(e.target.value)}
                  >
                    {label.map((data, id) => (
                      <FormControlLabel
                        key={id}
                        value={data}
                        control={
                          <Radio
                            icon={
                              <RadioButtonUncheckedIcon className="!w-6 !h-6 !text-[--text-color]" />
                            }
                            checkedIcon={<RadioButtonIcon className="!w-6 !h-6" />}
                          />
                        }
                        label={
                          <Typography
                            className="!text-[--text-color] !normal-case Poppins400 !text-sm"
                            htmlFor={`radio-${id}`}
                          >
                            {data}
                          </Typography>
                        }
                      />
                    ))}
                  </RadioGroup>
                )}
              />
            </Box>
          ))}
        {/** social media links */}
        <Box className="!mt-14">
          <Typography className="!text-2xl !text-[--text-color] CraftworkGroteskMedium">
            {t("ActInfoPersonForm.Social")}
          </Typography>
          <Box className="!w-full">
            <TextField
              type="text"
              size="small"
              placeholder={t("ActInfoPersonForm.YourLink")}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start" style={{ cursor: "pointer", marginRight: 0 }}>
                    <SpotifyIcon className="!w-6 !h-6 !mr-4" />
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                      https:/open.spotify.com/
                    </Typography>
                  </InputAdornment>
                ),
              }}
              inputProps={{
                maxLength: ACT_CONSTANTS.ACT_INFO.SPOTIFY_LINK_MAX_LENGTH,
              }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !my-5"
              name="spotifyLink"
              {...register("spotifyLink")}
            />
            <TextField
              type="text"
              size="small"
              placeholder={t("ActInfoPersonForm.YourLink")}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start" style={{ cursor: "pointer", marginRight: 0 }}>
                    <SoundCloudIcon className="!w-6 !h-6 !mr-4" />
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                      https:/soundcloud.com/
                    </Typography>
                  </InputAdornment>
                ),
              }}
              inputProps={{
                maxLength: ACT_CONSTANTS.ACT_INFO.SOUNDCLOUD_LINK_MAX_LENGTH,
              }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              name="soundCloudLink"
              className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !mb-5"
              {...register("soundCloudLink")}
            />
            <TextField
              type="text"
              size="small"
              placeholder={t("ActInfoPersonForm.YourLink")}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start" style={{ cursor: "pointer", marginRight: 0 }}>
                    <Instagram className="!w-6 !h-6 !mr-4 !text-[--text-color]" />
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                      https:/instagram.com/
                    </Typography>
                  </InputAdornment>
                ),
              }}
              inputProps={{
                maxLength: ACT_CONSTANTS.ACT_INFO.INSTAGRAM_LINK_MAX_LENGTH,
              }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              name="instagramLink"
              className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !mb-5"
              {...register("instagramLink")}
            />
            <TextField
              type="text"
              size="small"
              placeholder={t("ActInfoPersonForm.YourLink")}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start" style={{ cursor: "pointer", marginRight: 0 }}>
                    <YouTube className="!w-6 !h-6 !mr-4 !text-[--text-color]" />
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                      https:/youtube.com/
                    </Typography>
                  </InputAdornment>
                ),
              }}
              inputProps={{
                maxLength: ACT_CONSTANTS.ACT_INFO.YOUTUBE_LINK_MAX_LENGTH,
              }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              name="youtubeLink"
              className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !mb-5"
              {...register("youtubeLink")}
            />
            <TextField
              type="text"
              size="small"
              placeholder={t("ActInfoPersonForm.YourLink")}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start" style={{ cursor: "pointer", marginRight: 0 }}>
                    <FacebookOutlinedIcon className="!w-6 !h-6 !mr-4 !text-[--text-color]" />
                    <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                      https:/facebook.com/
                    </Typography>
                  </InputAdornment>
                ),
              }}
              inputProps={{
                maxLength: ACT_CONSTANTS.ACT_INFO.FACEBOOK_LINK_MAX_LENGTH,
              }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              name="facebookLink"
              {...register("facebookLink")}
              className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !mb-5"
            />
          </Box>
        </Box>
        {(currentProfile?.profileType === "VIRTUAL_VENUE_PROFILE" ||
          currentProfile?.profileType === "VENUE_PROFILE") && (
          <VenueWorkingHour
            control={control}
            //register={register}
            setValue={setValue}
            watch={watch}
            // getValues={getValues}
            //fields={fields}
            currentProfile={currentProfile}
          />
        )}
      </Box>
    </div>
  );
};

export default EditActInfoPersonContainer;
