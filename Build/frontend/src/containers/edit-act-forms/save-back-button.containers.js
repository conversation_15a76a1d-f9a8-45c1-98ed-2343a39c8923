"use client";
import {
  <PERSON>,
  <PERSON><PERSON>,
  CircularProgress,
  <PERSON>po<PERSON>,
  Icon<PERSON><PERSON><PERSON>,
  Drawer,
  useMedia<PERSON><PERSON>y,
  useTheme,
} from "@mui/material";
import ProfileNavbar from "@/common/profile-navbar/profile-navbar.common";
import { useLocale, useTranslations } from "next-intl";
import { useDispatch, useSelector } from "react-redux";
import { usePathname, useRouter } from "next/navigation";
import { Menu, South, West } from "@mui/icons-material";
import EditSidebar from "../../app/[locale]/(private)/(profile)/[id]/edit-sidebar/page";
import { useState } from "react";
import { publishActProfileInfo } from "@/store/slice/act/act.slice";
import { redirectButtonRoute } from "@/utils";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import {
  editSidebarDataAct,
  editSidebarDataVenue,
} from "@/app/[locale]/(private)/(profile)/[id]/edit-sidebar/edit-sidebar.data.common";

const SaveBackButtonContainers = ({ loading, croute }) => {
  const s = useTranslations("editActCommon");
  const t = useTranslations("profileSuccess");
  const p = useTranslations("profileFooter");
  const { currentProfile } = useSelector((state) => state.act);
  const router = useRouter();
  const routePath = usePathname();
  const pathName = routePath.split("/")[3];
  const [isSidebar, setIsSidebar] = useState(false);
  const dispatch = useDispatch();
  const profileId = routePath.split("/")[2];
  const theme = useTheme();
  const lang = useLocale();
  const isMobile = useMediaQuery(theme.breakpoints.down("lg"));
  const { showSnackbar } = useSnackbar();

  const toggleButton = () => {
    setIsSidebar(true);
  };

  const toggleClose = () => {
    setIsSidebar(false);
  };

  const handlePublishHandler = () => {
    //if (currentProfile?.profileStatus === "STATUS_DELETED") {
    dispatch(publishActProfileInfo({ profileId }))
      .unwrap()
      .then((response) => {
        showSnackbar(response.data.message, "success");
        if (currentProfile?.profileStatus === "STATUS_DELETED") {
          redirectButtonRoute(
            router,
            routePath,
            "save",
            isMobile,
            currentProfile?.profileType,
            currentProfile?.profileStatus,
          );
        } else {
          router.push(`/${lang}/profiles`);
        }
      })
      .catch((error) => {
        setLoading(false);
        showSnackbar(error, "error");
      });
    // }
  };

  const onSaveButtonHandler = () => {
    if (currentProfile?.profileStatus === "STATUS_DELETED") {
      handlePublishHandler();
    } else if (pathName === "edit-media") {
      handleUpdate();
    } else if (pathName === "edit-rider") {
      submitHandlerRider();
    } else if (pathName === "edit-availability-schedule") {
      handleSchedule();
    } else if (pathName === "edit-distribution-list") {
      distributionListHandler();
    } else if (pathName === "edit-feedback") {
      feedbackHandler();
    } else if (pathName === "edit-setting") {
      router.push(`/${lang}/profiles`);
    } else {
      otherkHandler();
    }
  };
  const handleUpdate = () => {
    showSnackbar("Act Media update successfully", "success");
    isMobile || currentProfile?.profileStatus === "STATUS_CREATED"
      ? router.push(`/${lang}/${profileId}/edit-location`)
      : router.push(`/${lang}/profiles`);
  };
  const submitHandlerRider = () => {
    isMobile || currentProfile?.profileStatus === "STATUS_CREATED"
      ? router.push(`/${lang}/${profileId}/edit-availability-schedule`)
      : router.push(`/${lang}/profiles`);
  };

  const handleSchedule = () => {
    isMobile || currentProfile?.profileStatus === "STATUS_CREATED"
      ? router.push(`/${lang}/${profileId}/edit-distribution-list`)
      : router.push(`/${lang}/profiles`);
  };

  const distributionListHandler = () => {
    isMobile || currentProfile?.profileStatus === "STATUS_CREATED"
      ? router.push(`/${lang}/${profileId}/edit-feedback`)
      : router.push(`/${lang}/profiles`);
  };

  const feedbackHandler = () => {
    isMobile || currentProfile?.profileStatus === "STATUS_CREATED"
      ? router.push(`/${lang}/${profileId}/edit-setting`)
      : router.push(`/${lang}/profiles`);
  };

  const publistStatus =
    currentProfile?.profileStatus === "STATUS_DELETED" ||
    (currentProfile?.profileStatus === "STATUS_CREATED" && pathName === "edit-setting")
      ? true
      : false;

  const otherkHandler = () => {
    isMobile || currentProfile?.profileStatus === "STATUS_CREATED"
      ? redirect()
      : router.push(`/${lang}/profiles`);
  };

  const redirect = () => {
    const sidebarData =
      currentProfile?.profileType === "VENUE_PROFILE" ||
      currentProfile?.profileType === "VIRTUAL_VENUE_PROFILE"
        ? editSidebarDataVenue
        : editSidebarDataAct;

    // Find current route index
    const currentIndex = sidebarData.findIndex((item) => item.path === croute);
    // If found and not last item, redirect to next route
    if (currentIndex < sidebarData.length - 1) {
      const nextRoute = sidebarData[currentIndex + 1].path;
      router.push(`/${lang}/${profileId}${nextRoute}`);
    } else {
      // If last item or not found, go to profiles
      router.push(`/${lang}/profiles`);
    }
  };

  return (
    <>
      <Box className="!hidden lg:!inline">
        <ProfileNavbar
          isSaveUnPublished={true}
          tag={s("editProfile")}
          className=" h-[64px] !fixed !top-0 !right-0 !left-0  !bg-[--bg-color] !z-20"
        >
          <Button
            type={publistStatus ? "button" : "submit"}
            onClick={() => {
              if (publistStatus) {
                handlePublishHandler();
              } else {
                onSaveButtonHandler();
              }
            }}
            className="!bg-[--text-color] flex gap-x-4 !px-4 !py-3"
            sx={{
              border: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
          >
            {loading ? (
              <CircularProgress size={24} className="!text-black" />
            ) : publistStatus ? (
              <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                {t("publish")}
              </Typography>
            ) : currentProfile?.profileStatus === "STATUS_CREATED" ? (
              <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                {p("Next")}
              </Typography>
            ) : (
              <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                {s("save&Close")}
              </Typography>
            )}
          </Button>
        </ProfileNavbar>
      </Box>
      <Box className="!flex !justify-between lg:!hidden fixed right-0 left-0 z-20 bg-[--bg-color] top-0  !p-4">
        <Box className="flex gap-4">
          <IconButton onClick={toggleButton}>
            <Menu className="text-2xl text-[--text-color]" />
          </IconButton>
          {pathName !== "edit-information" && (
            <Button
              className="!flex !gap-x-2 !normal-case"
              sx={{
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
              onClick={() => {
                redirectButtonRoute(
                  router,
                  routePath,
                  "back",
                  isMobile,
                  currentProfile?.profileType,
                  currentProfile?.profileStatus,
                );
              }}
            >
              <West className="!text-[--text-color] !text-xl" />
              <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
                {s("back")}
              </Typography>
            </Button>
          )}
        </Box>
        <Button
          className="!flex !gap-x-2 !normal-case"
          type={publistStatus ? "button" : "submit"}
          onClick={() => {
            if (publistStatus) {
              handlePublishHandler();
            } else {
              onSaveButtonHandler();
            }
          }}
          sx={{
            border: 0,
            "&.MuiButtonBase-root": {
              color: "white !important",
            },
          }}
        >
          {publistStatus ? (
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
              {t("publish")}
            </Typography>
          ) : (
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
              {s("save")}
            </Typography>
          )}

          <South className="!text-[--text-color] !text-xl" />
        </Button>
      </Box>

      <Box className="lg:block hidden">
        <EditSidebar />
      </Box>

      {isSidebar && (
        <Drawer
          open={isSidebar}
          anchor="left"
          onClose={toggleClose}
          sx={{
            "& .MuiDrawer-paper": {
              marginTop: "72px",
              width: "100%",
            },
          }}
        >
          <EditSidebar />
        </Drawer>
      )}
    </>
  );
};

export default SaveBackButtonContainers;
