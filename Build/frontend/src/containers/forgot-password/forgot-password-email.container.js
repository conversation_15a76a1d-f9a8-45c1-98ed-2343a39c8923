"use client";
import { FormInput, Button } from "@/component";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { Typography, CircularProgress } from "@mui/material";
import { useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { forgotPasswordEmailValidation } from "@/validation/auth/forgot-password/forgot-password.validation";
import { yupResolver } from "@hookform/resolvers/yup";
import { forgotPassword } from "@/store/slice/auth/forgot-password.auth.slice";
import { useRouter } from "next/navigation";
import { setLocalStorage } from "@/utils";
import { useLocale } from "next-intl";
import { AUTH_CONSTANTS } from "@/validation/auth/constants";

const ForgotPasswordForm = () => {
  const lang = useLocale();
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const { loading } = useSelector((state) => state.forgotPassword);
  const dispatch = useDispatch();
  const resolver = yupResolver(forgotPasswordEmailValidation);
  const {
    handleSubmit,
    formState: { errors },
    control,
  } = useForm({
    resolver,
    defaultValues: {
      email: "",
    },
    mode: "onChange",
  });
  const handleForgotPassword = (data) => {
    dispatch(forgotPassword(data.email))
      .unwrap()
      .then((response) => {
        if (response && response.status === 200) {
          if (response && response.data.data.twoFaEnabled) {
            setLocalStorage("twoFaEnabled", response.data.data.twoFaEnabled);
            router.push(`/${lang}/forgot-password-otp`);
          } else {
            router.push(`/${lang}/login`);
          }
          showSnackbar(response.data.message, "success", 20000);
        } else if (response.status === 208) {
          showSnackbar(response.data, "error");
          router.push(`/${lang}/forgot-password-otp`);
        }
      })
      .catch((error) => {
        showSnackbar(error, "error");
      });
  };
  return (
    <form onSubmit={handleSubmit(handleForgotPassword)}>
      <FormInput
        name="email"
        type="email"
        placeholder="Email"
        control={control}
        errors={errors}
        size="small"
        inputProps={{
          maxLength: AUTH_CONSTANTS.EMAIL.MAX_LENGTH,
        }}
        sx={{
          "& input::placeholder": {
            color: "#EFEFEF",
            border: 0,
          },
          "& input": {
            color: "#EFEFEF",
            fontFamily: "var(--craftWorkRegular)",
          },
          "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          border: 0,
        }}
        className="w-full !border !py-1 !border-white rounded-[2px] !mb-3"
      />
      <Button
        className="!bg-[--text-color] !flex !gap-x-4 !w-full !my-8 !py-3"
        sx={{
          border: 0,
          "&.MuiButtonBase-root": {
            color: "white !important",
          },
        }}
        type="submit"
      >
        {loading ? (
          <CircularProgress size={24} className="!text-black" />
        ) : (
          <>
            <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
              Reset Password
            </Typography>
            <ArrowSouthEast />
          </>
        )}
      </Button>
    </form>
  );
};
export default ForgotPasswordForm;
