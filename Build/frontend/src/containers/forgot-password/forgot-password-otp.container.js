"use client";
import { useState } from "react";
import { Box, Typography, CircularProgress } from "@mui/material";
import { O<PERSON><PERSON>ield, But<PERSON> } from "@/component";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { useForm } from "react-hook-form";
import { getLocalStorage } from "@/utils";
import { verifyForgotOtp } from "@/store/slice/auth/forgot-password.auth.slice";
import { useDispatch } from "react-redux";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { stringifyParams } from "@/utils";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";

const ForgotPasswordOTpForm = () => {
  const lang = useLocale();
  const { showSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [otp, setOtp] = useState("");
  const [error, setError] = useState(false);
  const { handleSubmit } = useForm({
    mode: "onSubmit",
  });

  const handleForgotPasswordOtPSubmit = () => {
    if (otp.length < 6) {
      setError(true);
    } else {
      setError(false);
      setLoading(true);
      const forgotPasswordOtp = stringifyParams({
        email: getLocalStorage("forgotPasswordEmail"),
        code: otp,
      });
      dispatch(verifyForgotOtp(forgotPasswordOtp))
        .unwrap()
        .then((response) => {
          if (response && response.status === 200) {
            setLoading(false);
            router.replace(`/${lang}/login`);
            showSnackbar(response.data.message, "success", 20000);
          } else if (response && response.status === 208) {
            setLoading(false);
            showSnackbar(response.data, "error");
          }
        })
        .catch((error) => {
          setLoading(false);
          showSnackbar(error, "error");
        });
    }
  };
  return (
    <form onSubmit={handleSubmit(handleForgotPasswordOtPSubmit)}>
      <Box className="!flex !flex-col !justify-center !items-center">
        <OTPField
          numInputs={6}
          inputStyle="lg:!w-[63px] lg:!h-[50px] md:!w-[74px] md:!h-[50px] !w-[9vw] !h-[5vh]  !text-[--text-color] !text-center !bg-transparent !border !border-[--text-color]"
          otp={otp}
          setOtp={setOtp}
          error={error}
        />
      </Box>
      <Button
        className="!bg-[--text-color] !flex !gap-x-4 !w-full !my-8 !py-3"
        type="submit"
        sx={{
          border: 0,
          "&.MuiButtonBase-root": {
            color: "white !important",
          },
        }}
      >
        {loading ? (
          <CircularProgress size={24} className="!text-black" />
        ) : (
          <>
            <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
              Verify OTP
            </Typography>
            <ArrowSouthEast />
          </>
        )}
      </Button>
    </form>
  );
};
export default ForgotPasswordOTpForm;
