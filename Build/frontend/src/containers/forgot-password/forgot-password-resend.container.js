"use client";
import { Typography } from "@mui/material";
import { useDispatch } from "react-redux";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { getLocalStorage } from "@/utils";
import { twoFaEmail } from "@/store/slice/auth/2fa.auth.slice";
import { stringifyParams } from "@/utils";

const ForgotPasswordResendCode = () => {
  const { showSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const handleResendCode = () => {
    const email = stringifyParams({
      email: getLocalStorage("forgotPasswordEmail"),
    });
    dispatch(twoFaEmail(email))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          showSnackbar(response.data.message, "success");
        } else if (response.status === 208) {
          showSnackbar(response.data.message, "error");
        }
      })
      .catch((error) => {
        showSnackbar(error, "error");
      });
  };
  return (
    <Typography
      className="!underline CraftworkGroteskHeavy !text-[--text-color] !text-sm !text-center !my-16"
      onClick={handleResendCode}
      sx={{
        cursor: "pointer",
      }}
    >
      Resend code
    </Typography>
  );
};
export default ForgotPasswordResendCode;
