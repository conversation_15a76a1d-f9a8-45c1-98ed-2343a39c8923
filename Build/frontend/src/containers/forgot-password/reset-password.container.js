"use client";
import { FormInput, Button } from "@/component";
import { Typography, CircularProgress } from "@mui/material";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { useForm } from "react-hook-form";
import { resetPasswordValidation } from "@/validation/auth/forgot-password/forgot-password.validation";
import { yupResolver } from "@hookform/resolvers/yup";
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { useSearchParams } from "next/navigation";
import { resetPassword } from "@/store/slice/auth/forgot-password.auth.slice";
import { clearLocalStorage, stringifyParams } from "@/utils";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { AUTH_CONSTANTS } from "@/validation/auth/constants";

const ResetPasswordForm = () => {
  const lang = useLocale();
  const router = useRouter();
  const searchParms = useSearchParams();
  const token = searchParms.get("token") ?? "";
  const { showSnackbar } = useSnackbar();
  const { loading } = useSelector((state) => state.forgotPassword);
  const dispatch = useDispatch();
  const resolver = yupResolver(resetPasswordValidation);
  const {
    handleSubmit,
    formState: { errors },
    control,
  } = useForm({
    resolver,
    defaultValues: {
      password: "",
      repeatPassword: "",
    },
    mode: "onChange",
  });
  const handleResetPassword = (data) => {
    const reserPasswordData = {
      newPassword: data.password,
      token,
    };
    const reserPasswordParms = stringifyParams(reserPasswordData);
    dispatch(resetPassword(reserPasswordParms))
      .unwrap()
      .then((response) => {
        showSnackbar(response.message, "success");
        clearLocalStorage();
        router.push(`/${lang}/login`);
      })
      .catch((error) => {
        showSnackbar(error, "error");
      });
  };
  return (
    <form onSubmit={handleSubmit(handleResetPassword)}>
      <FormInput
        name="password"
        type="password"
        placeholder="New Password"
        control={control}
        errors={errors}
        size="small"
        inputProps={{
          maxLength: AUTH_CONSTANTS.PASSWORD.MAX_LENGTH,
        }}
        sx={{
          "& input::placeholder": {
            color: "#EFEFEF",
            border: 0,
          },
          "& input": {
            color: "#EFEFEF",
            fontFamily: "var(--craftWorkRegular)",
          },
          "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          border: 0,
        }}
        className="w-full !border !py-1 !border-white rounded-[2px] !mb-3"
      />
      <FormInput
        name="repeatPassword"
        type="password"
        placeholder="Repeat New Password"
        control={control}
        errors={errors}
        size="small"
        inputProps={{
          maxLength: AUTH_CONSTANTS.PASSWORD.MAX_LENGTH,
        }}
        sx={{
          "& input::placeholder": {
            color: "#EFEFEF",
            border: 0,
          },
          "& input": {
            color: "#EFEFEF",
            fontFamily: "var(--craftWorkRegular)",
          },
          "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          border: 0,
        }}
        className="w-full !border !py-1 !border-white rounded-[2px] !mb-3"
      />

      <Button
        className="!bg-[--text-color] !flex !gap-x-4 !w-full !my-8 !py-3"
        type="submit"
        sx={{
          border: 0,
          "&.MuiButtonBase-root": {
            color: "white !important",
          },
        }}
      >
        {loading ? (
          <CircularProgress size={24} className="!text-black" />
        ) : (
          <>
            <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
              Create password
            </Typography>
            <ArrowSouthEast />
          </>
        )}
      </Button>
    </form>
  );
};
export default ResetPasswordForm;
