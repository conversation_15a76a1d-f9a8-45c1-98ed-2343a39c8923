"use client";
import Rating from "@/component/rating/rating.components";
import { Box, TextField, Typography } from "@mui/material";
import React from "react";
import PublicSvg from "@/assets/svg/PublicSvg.svg";
import PrivateSvg from "@/assets/svg/PrivateSvg.svg";
import { Button } from "@/component";
import { East } from "@mui/icons-material";
import FeedbackSvg from "@/assets/svg/Feedback.svg";
import { useLocale, useTranslations } from "next-intl";
import { giveActFeedback } from "@/store/slice/common/common.slice";
import { yupResolver } from "@hookform/resolvers/yup";
import { actFeedbackValidation } from "@/validation/act/act.validation";
import { useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import { CircularProgress } from "@mui/material";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { useRouter } from "next/navigation";

const LeaveFeedbackForms = ({ feedbackId, feedbackData }) => {
  const [loading, setLoading] = React.useState(false);
  const t = useTranslations("leaveFeedback.leaveFeedbackForm");
  const s = useTranslations("feedback");
  const { showSnackbar } = useSnackbar();

  const lang = useLocale();
  const ratingData = [s("entertainment"), s("professionalism"), t("drawAsExpected")];
  const resolver = yupResolver(actFeedbackValidation);
  const dispatch = useDispatch();
  const router = useRouter();
  const {
    handleSubmit,
    register,
    setValue,
    formState: { errors },
  } = useForm({
    resolver,
    defaultValues: {
      providerProfileId: "",
      receiverProfileId: "",
      professionalismValue: "",
      entertainmentValue: "",
      drawAsExpectedValue: "",
      publicMessage: "",
      privateMessage: "",
      userFeedback: false,
    },
  });

  const [ratings, setRatings] = React.useState(new Array(ratingData.length).fill(0));

  const handleRatingChange = (index, newValue) => {
    const newRatings = [...ratings];
    newRatings[index] = newValue;
    setValue("entertainmentValue", newRatings[0]);
    setValue("professionalismValue", newRatings[1]);
    setValue("drawAsExpectedValue", newRatings[2]);
    setRatings(newRatings);
  };

  const handleFeedBack = (data) => {
    setLoading(true);
    dispatch(
      giveActFeedback({
        feedbackId: feedbackId,
        data: {
          ...data,
          providerProfileId: feedbackData?.providerProfileId,
          receiverProfileId: feedbackData?.receiverProfileId,
          userFeedback: feedbackData?.userFeedback,
        },
      }),
    )
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setLoading(false);
          showSnackbar(response.data.message, "success");
          router.push(`/${lang}/success-feedback`);
        } else if (response.status === 208) {
          setLoading(false);
          showSnackbar(response.data.message, "error");
          router.push(`/${lang}/success-feedback`);
        }
      })
      .catch((error) => {
        setLoading(false);
        showSnackbar(error, "error");
      });
  };

  return (
    <>
      <form onSubmit={handleSubmit(handleFeedBack)}>
        <Box className="lg:pt-44 pt-28 lg:px-10 px-4 pb-24 max-w-4xl">
          <FeedbackSvg className="h-10 w-10" />
          <Box className="lg:flex justify-between py-6 items-end">
            <Typography className="CraftworkGroteskMedium text-[--text-color] text-2xl">
              {t("leaveFeedbackTo")}
            </Typography>
            <Box className="flex gap-2 items-center lg:pt-0 pt-4">
              {/* {currentProfile?.mediaDto?.imageUrls.length > 0 && (
                <CommonImage
                  src={currentProfile?.mediaDto?.imageUrls[0]}
                  alt="image"
                  width={64}
                  height={64}
                  className="rounded-full w-10 h-10 pt-2 lg:pt-0"
                />
              )} */}
              <Typography className="CraftworkGroteskHeavy text-sm text-[--text-color]">
                {feedbackData?.otherPartyName}
              </Typography>
            </Box>
          </Box>
          {/* {profileData && profileData.length > 0 && (
            <Box className="!w-full">
              <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX">
                Select Profile
              </InputLabel>
              <Box className="!border !border-[text-color] !rounded-[2px] !my-2 !py-1">
                <Controller
                  name="providerProfileId"
                  control={control}
                  render={({ field }) => (
                    <DropdownFormOptions
                      onSelect={(data) => {
                        field.onChange(data);
                      }}
                      options={profileData}
                      selectedValue={field.value}
                      title="Select Profile"
                      className="!text-[--text-color] !w-full"
                    />
                  )}
                />
              </Box>
              {errors && errors.providerProfileId && (
                <Typography as="span" className="text-sm !text-red-600">
                  {errors.providerProfileId.message}
                </Typography>
              )}
            </Box>
          )} */}

          <Typography className="text-[--text-color] text-lg CraftworkGroteskMedium lg:pt-10 pt-0 pb-6">
            {s("rate")}
          </Typography>
          {ratingData.map((data, index) => (
            <Box
              key={index}
              className="border border-[--divider-color] mb-2 flex items-center justify-between rounded-[4px] px-3 py-1"
            >
              <Typography className="text-[--text-color] text-sm CraftworkGroteskRegular">
                {data}
              </Typography>
              <Box className="flex gap-2">
                <Typography className="text-[--text-color] text-sm font-craftWorkRegular">
                  ({ratings[index] ? ratings[index] : 0})
                </Typography>
                <Rating
                  value={ratings[index]}
                  onChange={(event, newValue) => handleRatingChange(index, newValue)}
                />
              </Box>
            </Box>
          ))}
          <Box className="w-full lg:pt-12 pt-6">
            <Box className="flex gap-2 items-center py-1">
              <PublicSvg className="h-5 w-4" />
              <Typography className="text-lg text-[--text-color] font-craftWorkMedium">
                {t("public")}
              </Typography>
            </Box>
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              {t("visibleProfile")}
            </Typography>
            <TextField
              type="text"
              size="small"
              multiline
              rows={5}
              {...register("publicMessage")}
              placeholder={t("feedbackMessage")}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& .MuiOutlinedInput-root": {
                  color: "var(--text-color)",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              className="!border !w-full !h-[180px] !py-1 CraftworkGroteskRegular !border-[--text-color] rounded-[2px] my-2"
            />
            {errors && errors.publicMessage && (
              <Typography as="span" className="text-sm !text-red-600">
                {errors.publicMessage.message}
              </Typography>
            )}
          </Box>
          <Box className="w-full lg:pt-12 pt-6">
            <Box className="flex gap-2 items-center py-1">
              <PrivateSvg className="h-5 w-4" />
              <Typography className="text-lg text-[--text-color] font-craftWorkMedium">
                {t("private")}
              </Typography>
            </Box>
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              {t("performer")}
            </Typography>
            <TextField
              type="text"
              size="small"
              multiline
              rows={5}
              {...register("privateMessage")}
              placeholder={t("feedbackMessage")}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& .MuiOutlinedInput-root": {
                  color: "var(--text-color)",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              className="!border !w-full !h-[180px] !py-1 CraftworkGroteskRegular !border-[--text-color] rounded-[2px] my-2"
            />
          </Box>
        </Box>
        <Box className="border-t border-t-[--divider-color] fixed right-0 lg:right-[50%] left-0 z-20 bottom-0 bg-[--bg-color] px-8 h-[70px] items-center flex justify-end">
          <Button className="!bg-[--text-color] flex gap-1 !normal-case" type="submit">
            {loading ? (
              <CircularProgress size={24} className="!text-black" />
            ) : (
              <Typography className="text-[--bg-color] text-sm CraftworkGroteskHeavy">
                {t("send")}
              </Typography>
            )}
            <East className="text-[--bg-color]" />
          </Button>
        </Box>
      </form>
    </>
  );
};

export default LeaveFeedbackForms;
