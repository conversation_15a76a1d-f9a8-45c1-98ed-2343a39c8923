"use client";
import { But<PERSON> } from "@/component";
import { facebookLogin } from "@/store/slice/auth/login.auth.slice";
import { useDispatch } from "react-redux";
import FacebookSvg from "@/assets/svg/FacebookSvg.svg";
const FacebookLogin = () => {
  {
    /** code commneted out as from facebbok app is not yet approved */
  }
  const dispatch = useDispatch();
  const handleFacebookLogin = () => {
    dispatch(facebookLogin())
      .unwrap()
      .then(() => {
        //showSnackbar("Login Sucessfully", "success");
        //router.push("/dashboard");
      })
      .catch(() => {
        //showSnackbar("Login Failed", "error");
      });
  };
  return (
    <Button
      className="!bg-[--text-color] !w-[44px] !h-[44px] !rounded-full !p-0"
      sx={{
        minWidth: 0,
        "&.MuiButtonBase-root": {
          color: "white !important",
        },
      }}
      onClick={handleFacebookLogin}
    >
      <FacebookSvg className="!w-[20.7px] !h-[20.7px]" />
    </Button>
  );
};
export default FacebookLogin;
