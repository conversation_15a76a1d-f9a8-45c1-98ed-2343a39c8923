"use client";
import { But<PERSON> } from "@/component";
import GoogleSvg from "@/assets/svg/GoogleSvg.svg";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { googleLogin } from "@/store/slice/auth/login.auth.slice";
const GoogleLogin = () => {
  const { showSnackbar } = useSnackbar();
  const router = useRouter();
  const dispatch = useDispatch();
  const handleGoogleLogin = () => {
    dispatch(googleLogin())
      .unwrap()
      .then(() => {
        showSnackbar("Login Sucessfully", "success");
        router.push("/dashboard");
      })
      .catch(() => {
        //showSnackbar(error.message, "error");
      });
  };
  return (
    <Button
      className="!bg-[--text-color] !w-[44px] !h-[44px] !rounded-full !p-0"
      sx={{
        minWidth: 0,
        "&.MuiButtonBase-root": {
          color: "white !important",
        },
      }}
      onClick={handleGoogleLogin}
    >
      <GoogleSvg className="!w-[20.7px] !h-[20.7px]" />
    </Button>
  );
};
export default GoogleLogin;
