"use client";
import { useState, useCallback, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Box, Typography, CircularProgress } from "@mui/material";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { CheckBox, FormInput, Button } from "@/component";
import { useForm } from "react-hook-form";
import Link from "next/link";
import { yupResolver } from "@hookform/resolvers/yup";
import { useDispatch, useSelector } from "react-redux";
import { LoginValidation } from "@/validation/auth/login/login.auth.validation";
import { getCurrentUserEmail, login } from "@/store/slice/auth/login.auth.slice";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import {
  decode,
  encode,
  setLocalStorage,
  removeLocalStorage,
  getLocalStorage,
  setFormValues,
} from "@/utils";
import { useLocale } from "next-intl";
import { useTranslations } from "next-intl";
import { useAppContext } from "@/context/token-expired/token.expired.context";
import { resetPreviewData } from "@/store/slice/act/act.slice";
import { AUTH_CONSTANTS } from "@/validation/auth/constants";

const LoginForm = () => {
  const t = useTranslations("login");
  const lang = useLocale();
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { loading } = useSelector((state) => state.login);
  const resolver = yupResolver(LoginValidation);
  const { setIsTokenExpired } = useAppContext();

  const [rememberMe, setRememberMe] = useState(false);

  const {
    handleSubmit,
    getValues,
    setValue,
    formState: { errors },
    control,
  } = useForm({
    defaultValues: {
      email: "",
      password: "",
    },
    resolver,
    mode: "onChange",
  });

  const handleLogin = useCallback(
    (data) => {
      removeLocalStorage("previewData");
      removeLocalStorage("profileId");
      dispatch(resetPreviewData());
      const email = getValues("email");
      const password = getValues("password");
      if (rememberMe) {
        const encodedEmail = encode(email);
        const encodedPassword = encode(password);
        setLocalStorage("keep_logged_email", encodedEmail);
        setLocalStorage("keep_logged_password", encodedPassword);
      } else {
        removeLocalStorage("keep_logged_email");
        removeLocalStorage("keep_logged_password");
      }
      dispatch(login(data))
        .unwrap()
        .then((user) => {
          if (user.status === 200) {
            dispatch(getCurrentUserEmail());
            setLocalStorage("access_token", user.data.data.accessToken);
            showSnackbar(user.data.message, "success");
            user.data.data.promptForTwofaSetup && router.push(`/${lang}/2fa-authentication`);
            !user.data.data.promptForTwofaSetup &&
              router.push(`/${lang}/search?profileType=ACT_PROFILE`);
          } else if (user.status === 202 && user.data.data.twoFaEnabled) {
            //setLocalStorage("access_token", user.data.data.accessToken);
            //showSnackbar(user.data.message, "success");
            router.push(`/${lang}/otp`);
          } else if (user.status === 208) {
            setLocalStorage("access_token", null);
            showSnackbar(user.data, "error");
            router.push(`/${lang}/otp`);
          }
        })
        .catch((error) => {
          showSnackbar(error, "error");
        });
    },
    [dispatch, rememberMe, getValues],
  );

  useEffect(() => {
    setIsTokenExpired(false);
    const originalEmail = decode(getLocalStorage("keep_logged_email"));
    const originalPassword = decode(getLocalStorage("keep_logged_password"));
    setFormValues({
      setValue,
      values: {
        email: originalEmail || "",
        password: originalPassword || "",
      },
    });
    originalEmail && originalPassword && setRememberMe(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <form onSubmit={handleSubmit(handleLogin)}>
      <FormInput
        name="email"
        type="email"
        placeholder={t("LoginForm.Email")}
        control={control}
        errors={errors}
        size="small"
        inputProps={{
          maxLength: AUTH_CONSTANTS.EMAIL.MAX_LENGTH,
        }}
        sx={{
          "& input::placeholder": {
            color: "#EFEFEF",
            border: 0,
          },
          "& input": {
            color: "#EFEFEF",
            fontFamily: "var(--craftWorkRegular)",
          },
          "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          border: 0,
        }}
        className="w-full !border !py-1 !border-white rounded-[2px] !mb-3"
      />

      <FormInput
        name="password"
        type="password"
        placeholder={t("LoginForm.Password")}
        control={control}
        errors={errors}
        size="small"
        inputProps={{
          maxLength: AUTH_CONSTANTS.PASSWORD.MAX_LENGTH,
        }}
        sx={{
          "& input::placeholder": {
            color: "#EFEFEF",
            border: 0,
          },
          "& input": {
            color: "#EFEFEF",
            fontFamily: "var(--craftWorkRegular)",
          },
          "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          border: 0,
        }}
        className="w-full !border !py-1 !border-white rounded-[2px]"
      />
      <Link href={`/${lang}/password-recovery`}>
        <Typography className="underline CraftworkGroteskHeavy !pt-3 text-sm text-[--text-color] text-right">
          {t("LoginForm.ForgotPassword")}
        </Typography>
      </Link>
      {/** rember me */}
      <Box className="mt-3 !mb-6" onClick={() => setRememberMe(!rememberMe)} unselectable="on">
        <CheckBox
          className="mb-[3px] !mr-2 !w-[18px]"
          size="small"
          sx={{ color: "#EFEFEF" }}
          checked={rememberMe}
        />
        <label
          htmlFor="remember-me"
          className=" !text-[--text-color] CraftworkGroteskRegular cursor-pointer"
        >
          {t("LoginForm.rememberMe")}
        </label>
      </Box>
      <Typography className="!text-[--text-color] !text-center CraftworkGroteskMedium !text-sm !leading-[19.95px]">
        {t("LoginForm.youAgree")}
      </Typography>
      <Link href={`/${lang}/privacy-policy`}>
        <Typography className="!text-center !text-[--text-color] CraftworkGroteskHeavy !text-sm cursor-pointer">
          {t("LoginForm.PrivacyPolicy")}
        </Typography>
      </Link>
      <Button
        className="!bg-[--text-color] !flex !gap-x-4 !w-full !my-6 !py-3"
        type="submit"
        sx={{
          border: 0,
          "&.MuiButtonBase-root": {
            color: "white !important",
          },
        }}
      >
        {loading ? (
          <CircularProgress size={24} className="!text-black" />
        ) : (
          <>
            <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
              {t("LoginForm.login")}
            </Typography>
            <ArrowSouthEast />
          </>
        )}
      </Button>
    </form>
  );
};
export default LoginForm;
