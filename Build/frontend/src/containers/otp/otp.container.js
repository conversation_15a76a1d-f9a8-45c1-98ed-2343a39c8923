"use client";
import { useState } from "react";
import { Box, Typography, CircularProgress } from "@mui/material";
import { <PERSON><PERSON><PERSON>ield, <PERSON><PERSON> } from "@/component";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { useForm } from "react-hook-form";
import { getCurrentUserEmail, verifyOTP } from "@/store/slice/auth/login.auth.slice";
import { useSelector, useDispatch } from "react-redux";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { setLocalStorage } from "@/utils";
const OtpForm = () => {
  //const t = useTranslations("OTP");
  const lang = useLocale();
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { loading, data: userCredentials } = useSelector((state) => state.login);
  const [otp, setOtp] = useState("");
  const [error, setError] = useState(false);
  const { handleSubmit } = useForm({
    mode: "onSubmit",
  });

  const handleTwoFASubmit = () => {
    if (otp.length < 6) {
      setError(true);
    } else {
      setError(false);
      const data = {
        email: userCredentials.email,
        code: otp,
      };

      dispatch(verifyOTP(data))
        .unwrap()
        .then((user) => {
          dispatch(getCurrentUserEmail());
          setLocalStorage("access_token", user.data.accessToken);
          showSnackbar("Login successfully", "success");
          router.replace(`/${lang}/dashboard`);
        })
        .catch((error) => {
          showSnackbar(error, "error");
        });
    }
  };
  return (
    <form onSubmit={handleSubmit(handleTwoFASubmit)}>
      <Box className="!flex !flex-col !justify-center !items-center">
        <OTPField
          numInputs={6}
          inputStyle="lg:!w-[63px] lg:!h-[50px] md:!w-[74px] md:!h-[50px] !w-[9vw] !h-[5vh]  !text-[--text-color] !text-center !bg-transparent !border !border-[--text-color]"
          otp={otp}
          setOtp={setOtp}
          error={error}
        />
      </Box>
      <Button
        className="!bg-[--text-color] !flex !gap-x-4 !w-full !my-8 !py-3"
        type="submit"
        sx={{
          border: 0,
          "&.MuiButtonBase-root": {
            color: "white !important",
          },
        }}
      >
        {loading ? (
          <CircularProgress size={24} className="!text-black" />
        ) : (
          <>
            <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
              Verify OTP
            </Typography>
            <ArrowSouthEast />
          </>
        )}
      </Button>
    </form>
  );
};
export default OtpForm;
