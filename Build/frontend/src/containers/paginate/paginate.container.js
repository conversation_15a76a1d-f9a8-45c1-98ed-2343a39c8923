"use client";
import * as React from "react";
import usePaginate from "@/hooks/usePaginate"; // Adjust the path if needed

export default function Paginate({ totalRecords, perPageRecord }) {
  const { pageNo: currentPage, onPageChange } = usePaginate();

  const totalPages = Math.ceil(totalRecords / perPageRecord);
  const maxVisiblePages = 6; // The maximum number of pages to show in the pagination

  let startPage, endPage;

  if (totalPages <= maxVisiblePages) {
    // If the total pages are less than the max visible pages
    startPage = 1;
    endPage = totalPages;
  } else {
    if (currentPage <= 4) {
      // If the current page is near the start
      startPage = 1;
      endPage = maxVisiblePages - 1;
    } else if (currentPage + 3 >= totalPages) {
      // If the current page is near the end
      startPage = totalPages - (maxVisiblePages - 2);
      endPage = totalPages;
    } else {
      // If the current page is in the middle
      startPage = currentPage - 2;
      endPage = currentPage + 2;
    }
  }

  const items = [];

  // Add 'Prev' button
  items.push({
    type: "previous",
    page: currentPage - 1,
    disabled: currentPage === 1,
    selected: false,
  });

  // Add the first page
  if (startPage > 1) {
    items.push({
      type: "page",
      page: 1,
      disabled: false,
      selected: 1 === currentPage,
    });

    if (startPage > 2) {
      items.push({
        type: "ellipsis",
        page: null,
        disabled: true,
        selected: false,
      });
    }
  }

  // Add the middle pages
  for (let i = startPage; i <= endPage; i++) {
    items.push({
      type: "page",
      page: i,
      disabled: false,
      selected: i === currentPage,
    });
  }

  // Add the last page
  if (endPage < totalPages) {
    if (endPage < totalPages - 1) {
      items.push({
        type: "ellipsis",
        page: null,
        disabled: true,
        selected: false,
      });
    }

    items.push({
      type: "page",
      page: totalPages,
      disabled: false,
      selected: totalPages === currentPage,
    });
  }

  // Add 'Next' button
  items.push({
    type: "next",
    page: currentPage + 1,
    disabled: currentPage === totalPages,
    selected: false,
  });

  return (
    <nav className="flex mb-4 list-none !rounded-[5px] !cursor-pointer !h-fit table-pagination">
      {items.map(({ type, page, selected, disabled, ...item }, index) => {
        return (
          <li
            key={index}
            className="!py-2 md:!px-4 !px-2 text-sm"
            style={{
              backgroundColor: selected ? "var(--text-color)" : "",
              fontFamily: selected ? "var(--craftWorkBold)" : "var(--craftWorkRegular)",
              color: selected ? "var(--bg-color)" : "var(--text-color)",
              borderRadius: selected ? "6px" : "",
              borderRight: `${type === "next" ? "" : ""}`,
            }}
            onClick={disabled ? undefined : () => onPageChange(page)} // Pass page number to onPageChange
          >
            <button
              type="button"
              style={{
                fontFamily: selected ? "var(--craftWorkBold)" : "var(--craftWorkRegular)",
                border: "none",
                // backgroundColor: selected ? "var(--active-background)" : "transparent",
                color: selected ? "var(--active-text)" : "",
              }}
              {...item}
              disabled={disabled}
            >
              {type === "previous" || type === "next"
                ? type === "previous"
                  ? "Prev"
                  : "Next"
                : type === "ellipsis"
                  ? "..."
                  : page}
            </button>
          </li>
        );
      })}
    </nav>
  );
}
