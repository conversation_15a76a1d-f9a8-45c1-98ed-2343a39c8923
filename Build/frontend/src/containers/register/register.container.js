"use client";
import { Box, Typography, CircularProgress } from "@mui/material";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { signUp, validatePassword } from "@/store/slice/auth/signup.auth.slice";
import { FormInput, CheckBox } from "@/component";
import { yupResolver } from "@hookform/resolvers/yup";
import { signUpValidation } from "@/validation/auth/signup/signup.auth.validation";
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { useForm } from "react-hook-form";
import { Button } from "@/component";
import { useRouter } from "next/navigation";
import { setLocalStorage } from "@/utils";
import { useLocale, useTranslations } from "next-intl";
import { useAppContext } from "@/context/token-expired/token.expired.context";
import { AUTH_CONSTANTS } from "@/validation/auth/constants";
import Link from "next/link";

const RegisterForm = () => {
  const lang = useLocale();
  const t = useTranslations("signUp");
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const { loading } = useSelector((state) => state.signup);
  const dispatch = useDispatch();
  const { setIsTokenExpired } = useAppContext();
  const resolver = yupResolver(signUpValidation);
  setIsTokenExpired(false);
  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
  } = useForm({
    defaultValues: {
      email: "",
      password: "",
      repeatPassword: "",
    },
    resolver,
    mode: "onChange",
  });

  const handleRegisterForm = (data) => {
    const signupData = {
      email: data.email,
      password: data.password,
    };
    dispatch(validatePassword(signupData))
      .unwrap()
      .then((response) => {
        if (response && response.status === 200) {
          dispatch(signUp(signupData))
            .unwrap()
            .then((response) => {
              if (response.status === 200) {
                setLocalStorage("registerEmail", data.email);
                showSnackbar(response.data.message, "success");
                router.push(`/${lang}/email-verification`);
                reset();
              } else if (response.status === 208) {
                showSnackbar(response.data, "error");
              }
            })
            .catch((error) => {
              showSnackbar(error, "error");
            });
        }
      })
      .catch((error) => {
        showSnackbar(error, "error");
      });
  };
  return (
    <form onSubmit={handleSubmit(handleRegisterForm)}>
      <FormInput
        name="email"
        type="email"
        placeholder={t("SignupForm.Email")}
        control={control}
        errors={errors}
        size="small"
        inputProps={{
          maxLength: AUTH_CONSTANTS.EMAIL.MAX_LENGTH,
        }}
        sx={{
          "& input::placeholder": {
            color: "#EFEFEF",
            border: 0,
          },
          "& input": {
            color: "#EFEFEF",
            fontFamily: "var(--craftWorkRegular)",
          },
          "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          border: 0,
        }}
        className="w-full !border !py-1 !border-white rounded-[2px] !mb-3"
      />
      <FormInput
        name="password"
        type="password"
        placeholder={t("SignupForm.Password")}
        control={control}
        errors={errors}
        size="small"
        inputProps={{
          maxLength: AUTH_CONSTANTS.PASSWORD.MAX_LENGTH,
        }}
        sx={{
          "& input::placeholder": {
            color: "#EFEFEF",
            border: 0,
          },
          "& input": {
            color: "#EFEFEF",
            fontFamily: "var(--craftWorkRegular)",
          },
          "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          border: 0,
        }}
        className="w-full !border !py-1 !border-white rounded-[2px] !mb-3"
      />
      <FormInput
        name="repeatPassword"
        type="password"
        placeholder={t("SignupForm.RepeatPassword")}
        control={control}
        errors={errors}
        size="small"
        inputProps={{
          maxLength: AUTH_CONSTANTS.PASSWORD.MAX_LENGTH,
        }}
        sx={{
          "& input::placeholder": {
            color: "#EFEFEF",
            border: 0,
          },
          "& input": {
            color: "#EFEFEF",
            fontFamily: "var(--craftWorkRegular)",
          },
          "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          border: 0,
        }}
        className="w-full !py-1 !border !border-white rounded-[2px]"
      />
      <Box className="mt-3 lg:!mb-6 !mb-3">
        <CheckBox className="mb-[3px] !mr-2 !max-w-[18px]" size="small" sx={{ color: "#EFEFEF" }} />
        <label
          htmlFor="remember-me"
          className=" !text-[--text-color] !text-sm CraftworkGroteskRegular cursor-pointer"
        >
          {t("SignupForm.rememberMe")}
        </label>
      </Box>
      <Typography className="!text-[--text-color] !text-center CraftworkGroteskMedium !text-sm !leading-[19.95px]">
        {t("SignupForm.youAgree")}{" "}
      </Typography>
      <Link href={`/${lang}/privacy-policy`}>
        <Typography className="!text-center !text-[--text-color] CraftworkGroteskHeavy !text-sm cursor-pointer">
          {t("SignupForm.PrivacyPolicy")}
        </Typography>
      </Link>
      <Button
        className="!bg-[--text-color] !flex !gap-x-4 !w-full !my-6 !py-3"
        type="submit"
        sx={{
          border: 0,
          "&.MuiButtonBase-root": {
            color: "white !important",
          },
        }}
      >
        {loading ? (
          <CircularProgress size={24} className="!text-black" />
        ) : (
          <>
            <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
              {t("SignupForm.SignUp")}
            </Typography>
            <ArrowSouthEast />
          </>
        )}
      </Button>
    </form>
  );
};
export default RegisterForm;
