"use client";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { Typography, CircularProgress } from "@mui/material";
import { Button } from "@/component";
import { getLocalStorage } from "@/utils";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { resendEmailVerification } from "@/store/slice/auth/signup.auth.slice";
import { useSelector, useDispatch } from "react-redux";
import { stringifyParams } from "@/utils";
const ResendRegisterEmail = () => {
  const dispatch = useDispatch();
  const { loading } = useSelector((state) => state.signup);
  const { showSnackbar } = useSnackbar();
  const handleResendEmail = () => {
    const email = stringifyParams({ email: getLocalStorage("registerEmail") });
    dispatch(resendEmailVerification(email))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          showSnackbar(response.data.message, "success");
        } else if (response.status === 208) {
          showSnackbar(response.data.message, "error");
        }
      })
      .catch((error) => {
        showSnackbar(error, "error");
      });
  };
  return (
    <Button
      className="!bg-[--text-color] !flex !gap-x-4 !w-full !my-8 !py-3"
      onClick={handleResendEmail}
    >
      {loading ? (
        <CircularProgress size={24} className="!text-black" />
      ) : (
        <>
          <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
            Resend email
          </Typography>
          <ArrowSouthEast />
        </>
      )}
    </Button>
  );
};
export default ResendRegisterEmail;
