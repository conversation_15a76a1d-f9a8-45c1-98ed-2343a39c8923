"use client";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { CircularProgress, Typography } from "@mui/material";
import { FormInput, Button } from "@/component";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { resendEmailValidation } from "@/validation/auth/resend-email/resend-email.validation";
import { resendEmailVerification } from "@/store/slice/auth/signup.auth.slice";
import { useDispatch, useSelector } from "react-redux";
import { stringifyParams } from "@/utils";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { useTranslations } from "next-intl";

const ResendEmailForm = () => {
  const { t } = useTranslations("resendEmail");
  const { showSnackbar } = useSnackbar();
  const { loading } = useSelector((state) => state.signup);
  const resolver = yupResolver(resendEmailValidation);
  const dispatch = useDispatch();
  const {
    handleSubmit,
    formState: { errors },
    control,
  } = useForm({
    defaultValues: {
      email: "",
    },
    resolver,
    mode: "onSubmit",
  });

  const handleResendEmail = (data) => {
    const email = stringifyParams({ email: data.email });
    dispatch(resendEmailVerification(email))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          showSnackbar("Email sent successfully", "success");
        } else if (response.status === 208) {
          showSnackbar("Wait 15 minutes before re-sending an email", "error");
        }
      })
      .catch((error) => {
        showSnackbar(error, "error");
      });
  };
  return (
    <form onSubmit={handleSubmit(handleResendEmail)}>
      <FormInput
        name="email"
        type="email"
        placeholder={t("enterEmail")}
        control={control}
        errors={errors}
        size="small"
        sx={{
          "& input::placeholder": {
            color: "#EFEFEF",
            border: 0,
          },
          "& input": {
            color: "#EFEFEF",
          },
          "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          // "& input:-webkit-autofill": {
          //   "-webkit-text-fill-color": "transparent !important",
          //   "-webkit-box-shadow": "0 0 0 1000px #2E2E2E inset !important",
          //   transition: "background-color 5000s ease-in-out 0s",
          // },
          "& .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          border: 0,
        }}
        className="w-full !border !py-1 !border-white rounded-[2px] !mb-3"
      />
      <Button
        className="!bg-[--text-color] !flex !gap-x-4 !w-full !my-8 !py-3"
        type="submit"
        sx={{
          border: 0,
          "&.MuiButtonBase-root": {
            color: "white !important",
          },
        }}
      >
        {loading ? (
          <CircularProgress size={24} className="!text-black" />
        ) : (
          <>
            <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
              Verify Email
            </Typography>
            <ArrowSouthEast />
          </>
        )}
      </Button>
    </form>
  );
};
export default ResendEmailForm;
