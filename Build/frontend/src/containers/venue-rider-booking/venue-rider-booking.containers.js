import { CheckBox } from "@/component";
import { Box, TextField, Typography } from "@mui/material";
import React from "react";
import { Controller } from "react-hook-form";

const VenueRidersForm = ({ riderProfileType, control, errors }) => {
  const visualEffects = ["heat, pyrotechnics", "fog/smoke/haze", "confetti", "strobe"];

  return (
    <Box className="">
      {riderProfileType === "Venue" && (
        <>
          {/* Instructions Section */}
          <Box className="mb-6 p-4 bg-[--footer-bg] rounded-[4px] border border-[--divider-color]">
            <Typography className="text-lg text-red-600 CraftworkGroteskMedium mb-2">
              Instructions:
            </Typography>

            {riderProfileType === "Act" && (
              <>
                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular mb-2">
                  If you are the <span className="font-bold">ACT</span>, then:
                </Typography>
                <Box className="ml-4 mb-2">
                  <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular flex items-center">
                    <span className="mr-2">•</span> Please indicate if you have any specific Parking
                    requirements
                  </Typography>
                  <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular flex items-center">
                    <span className="mr-2">•</span> Also, please indicate which Visual effects you
                    would LIKE to use at this venue
                  </Typography>
                </Box>
              </>
            )}

            {riderProfileType === "Venue" && (
              <>
                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular mb-2">
                  If you are the <span className="font-bold">VENUE</span>, then:
                </Typography>
                <Box className="ml-4">
                  <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular flex items-center">
                    <span className="mr-2">•</span> Please indicate if you have any specific parking
                    restrictions or recommendations - particularly w.r.t. equipment loading
                    requirements
                  </Typography>
                  <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular flex items-center">
                    <span className="mr-2">•</span> Also, please clarify which Visual effects are
                    allowed at your venue
                  </Typography>
                </Box>
              </>
            )}
          </Box>

          <Typography className="text-lg text-[--text-color] CraftworkGroteskMedium">
            Parking
          </Typography>
          <Box className="w-full">
            <Controller
              name="parkingConditions"
              control={control}
              render={({ field }) => (
                <TextField
                  type="text"
                  size="small"
                  {...field}
                  InputLabelProps={{ style: { color: "#EFEFEF" } }}
                  sx={{
                    "& input::placeholder": {
                      color: "#EFEFEF",
                      border: 0,
                    },
                    "& input": {
                      color: "#EFEFEF",
                      fontFamily: "var(--craftWorkRegular)",
                    },
                    "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      borderWidth: 0,
                    },
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderWidth: 0,
                    },
                    border: 0,
                  }}
                  className="!border !w-full mt-4 !py-2 CraftworkGroteskRegular !border-white rounded-[2px]"
                  placeholder="What parking conditions?"
                />
              )}
            />
            {errors.parkingConditions && (
              <Typography as="span" className="text-sm !text-red-600">
                {errors.parkingConditions.message}
              </Typography>
            )}
          </Box>
          <Typography className="text-lg pt-12 text-[--text-color] CraftworkGroteskMedium">
            Visual effects allowed
          </Typography>
          <Box className="flex flex-wrap gap-4">
            {visualEffects.map((data) => (
              <Box key={data} className="inline-block">
                <Controller
                  control={control}
                  name="allowedVisualEffectsList"
                  render={({ field }) => {
                    const isChecked = field.value.includes(data);
                    return (
                      <Box className="flex items-center">
                        <CheckBox
                          className="!max-w-[24px]"
                          sx={{ color: "#EFEFEF", marginRight: "5px" }}
                          checked={isChecked}
                          onChange={(e) => {
                            const newValue = e.target.checked
                              ? [...field.value, data] // Add the selected option to the array
                              : field.value.filter((item) => item !== data); // Remove the deselected option from the array
                            field.onChange(newValue); // Update the form state
                          }}
                        />
                        <label
                          htmlFor="allowedVisualEffectsList"
                          className="!text-[--text-color] !text-sm CraftworkGroteskRegular cursor-pointer"
                        >
                          {data}
                        </label>
                      </Box>
                    );
                  }}
                />
              </Box>
            ))}
          </Box>
          {errors.allowedVisualEffectsList && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.allowedVisualEffectsList.message}
            </Typography>
          )}
        </>
      )}
      {/* {riderList.length > 0 && (
        <>
          <Typography className="text-lg text-[--text-color] CraftworkGroteskMedium mt-6">
            Attach{" "}
            {currentBookingStatus.profileType === "ACT_PROFILE" ||
            currentBookingStatus.profileType === "VIRTUAL_ACT_PROFILE"
              ? "Act"
              : "Venue"}{" "}
            rider
          </Typography>
          
          <Box className="w-full">
            <Controller
              name="riderUrl"
              control={control}
              render={({ field }) => (
                <RadioGroup
                  //value={field.value}
                  {...field}
                  className="w-full"
                  // onChange={(e) => {
                  //   field.onChange(e.target.value);
                  //   console.log(e.target.value);
                  // }}
                >
                  {riderList.map((data, id) => (
                    <FormControlLabel
                      key={id}
                      value={data?.riderDocument}
                      className="!w-[100%] mb-4"
                      sx={{
                        "& .MuiFormControlLabel-label": {
                          width: "100% !important",
                        },
                      }}
                      control={
                        <Radio
                          icon={<RadioButtonUnchecked className="!w-6 !h-6 !text-[--text-color]" />}
                          checkedIcon={<RadioButtonIcon className="!w-6 !h-6" />}
                          //value={data.riderDocument}
                        />
                      }
                      label={
                        <Box className="flex justify-between w-full p-4 bg-[--footer-bg] rounded-[4px] border border-[--divider-color]">
                          <Box className="flex gap-3 items-center">
                            <PDFIcon className="w-10 h-10" />
                            <Typography className="text-sm w-full text-[--text-color] CraftworkGroteskMedium">
                              {data?.riderDocument?.split("/").pop()}
                            </Typography>
                          </Box>
                          <Button
                            className="!bg-[--text-color] flex gap-2 !normal-case px-4"
                            onClick={() => {
                              const url = `/${currentBookingStatus?.profileId}/pdf-view?pdf=${data.riderDocument.split("/").pop()}`;
                              const newWindow = window.open(url, "_blank", "noopener,noreferrer");
                              if (newWindow) newWindow.opener = null;
                              // router.push(
                              //   `/${profileId}/pdf-view?pdf=${rider.riderDocument.split("/").pop()}`,
                              // )
                            }}
                          >
                            <Typography className="text-sm text-[--bg-color] CraftworkGroteskHeavy">
                              View
                            </Typography>
                            <ViewIcon className="w-4 h-4" />
                          </Button>
                        </Box>
                      }
                    />
                  ))}
                </RadioGroup>
              )}
            />
            {errors.riderUrl && (
              <Typography as="span" className="text-sm !text-red-600">
                {errors.riderUrl.message}
              </Typography>
            )}
          </Box>
        </>
      )} */}
      {riderProfileType === "Act" && (
        <>
          <Typography className="text-lg text-[--text-color] CraftworkGroteskMedium mt-6">
            Other rider details
          </Typography>
          <Controller
            name="riderConditions"
            control={control}
            render={({ field }) => (
              <TextField
                type="text"
                size="small"
                multiline
                rows={8}
                {...field}
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& .MuiOutlinedInput-root": {
                    color: "var(--text-color)",
                    fontFamily: "var(--craftWorkRegular)",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  border: 0,
                }}
                className="!border !w-full !h-[180px] !py-1 CraftworkGroteskRegular !border-[--text-color] rounded-[2px] !my-3"
                placeholder="Any illegal substances are prohibited?"
              />
            )}
          />
          {errors.riderConditions && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.riderConditions.message}
            </Typography>
          )}
        </>
      )}
    </Box>
  );
};

export default VenueRidersForm;
