"use client";

import React, { createContext, useContext, useRef, useState } from "react";
import SnackbarMessage from "@/common/snackbar-message";

const SnackbarContext = createContext({
  showSnackbar: () => {},
  hideSnackbar: () => {},
  snackbarOpen: false,
  snackbarSeverity: "",
  snackbarMessage: "",
});

export const SnackbarProvider = ({ children }) => {
  const snackbarRef = useRef();
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarSeverity, setSnackbarSeverity] = useState("info");
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarDuration, setSnackbarDuration] = useState(3000);

  const showSnackbar = (message, severity = "info", duration = 3000) => {
    setSnackbarOpen(true);
    setSnackbarSeverity(severity);
    setSnackbarMessage(message);
    setSnackbarDuration(duration);
  };

  const hideSnackbar = () => {
    setSnackbarOpen(false);
  };

  return (
    <SnackbarContext.Provider
      value={{
        showSnackbar,
        hideSnackbar,
        snackbarOpen,
        snackbarSeverity,
        snackbarMessage,
        snackbarDuration,
      }}
    >
      {children}
      {/* Include the SnackbarMessage component with the forwarded ref */}
      <SnackbarMessage ref={snackbarRef} />
    </SnackbarContext.Provider>
  );
};

export const useSnackbar = () => {
  return useContext(SnackbarContext);
};
