import { useCallback } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

const initialPage = 1;
const initialPerPage = 10;

const usePaginate = (paramKey = "pageNo", perPageKey = "perPage", paramSearchKey = "search") => {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  const pageNo = Number(searchParams.get(paramKey) || initialPage);
  const perPage = Number(searchParams.get(perPageKey) || initialPerPage);
  const search = searchParams.get(paramSearchKey) || "";

  const onPerPageChange = useCallback(
    (perPage) => {
      const params = new URLSearchParams(searchParams);
      params.set(paramKey, initialPage);
      params.set(perPageKey, perPage);
      router.push(`${pathname}?${params.toString()}`);
    },
    [router, searchParams, paramKey, pathname, perPageKey],
  );

  const onPageChange = useCallback(
    (newPage) => {
      const params = new URLSearchParams(searchParams);
      params.set(paramKey, newPage);
      router.push(`${pathname}?${params.toString()}`);
    },
    [router, searchParams, paramKey, pathname],
  );

  const onSearch = useCallback(
    // eslint-disable-next-line no-unused-vars
    (debouncedSearchQuery, callback) => {
      const params = new URLSearchParams(searchParams);
      params.set(paramKey, initialPage);
      if (debouncedSearchQuery) {
        params.set(paramSearchKey, debouncedSearchQuery);
      } else {
        params.delete(paramSearchKey);
      }
      router.push(`${pathname}?${params.toString()}`);
    },
    [router, searchParams, paramKey, pathname, paramSearchKey],
  );

  const generatePaginationItems = ({ totalPages }) => {
    const items = [];
    items.push({
      type: "previous",
      page: pageNo - 1,
      disabled: pageNo === 1,
    });
    for (let i = 1; i <= totalPages; i++) {
      items.push({
        type: "page",
        page: i,
        selected: i === pageNo,
      });
    }
    items.push({
      type: "next",
      page: pageNo + 1,
      disabled: pageNo === totalPages,
    });
    return items;
  };

  return {
    pageNo,
    perPage,
    search,
    onPageChange,
    onPerPageChange,
    onSearch,
    generatePaginationItems,
  };
};

export default usePaginate;
