"use client";
import html2canvas from "html2canvas";
import jsPD<PERSON> from "jspdf";
import { useCallback, useState } from "react";
const useCaptureImage = (options = {}) => {
  const [URLData, setURLData] = useState(""); // State to store the captured image URL
  const [error, setError] = useState(null); // State to store any errors
  const captureImage = async (element) => {
    if (!element) {
      setError("Element not found.");
      return;
    }
    try {
      // Lazy load html2canvas
      const html2canvas = await import(/* webpackPrefetch: true */ "html2canvas");
      // Capture the element as a canvas and convert to data URL
      const result = await html2canvas.default(element, options);
      const asURL = result.toDataURL("image/png");
      // create a link element to download the image
      const anchor = document.createElement("a");
      anchor.href = asURL;
      anchor.download = `${new Date().toISOString()}.png`;
      anchor.click();
      setURLData(asURL);
    } catch (reason) {
      setError(reason);
    }
  };

  // Helper function to hide elements with the no-print class
  const hideNoPrintElements = () => {
    const elements = document.querySelectorAll(".no-print");
    elements.forEach((element) => {
      element.style.display = "none";
    });
  };

  // Helper function to show elements with the no-print class
  const showNoPrintElements = () => {
    const elements = document.querySelectorAll(".no-print");
    elements.forEach((element) => {
      element.style.display = "";
    });
  };

  const downloadPDF = useCallback((element) => {
    if (!element) {
      return;
    }
    const divHeight = element.clientHeight;
    const divWidth = element.clientWidth;
    const ratio = divHeight / divWidth;
    // Hide elements with the no-print class
    hideNoPrintElements();

    // Use html2canvas to capture the element as an image
    html2canvas(element, {
      backgroundColor: "#181b1b",
      useCORS: true,
    }).then((canvas) => {
      const imgData = canvas.toDataURL("image/png"); // Convert canvas to image data

      // Show elements with the no-print class again after capture
      showNoPrintElements();

      const pdf = new jsPDF("p", "mm", "a4"); // Create a new jsPDF instance (A4 size)
      // const imgWidth = 210; // A4 width in mm
      // const pageHeight = 295; // A4 height in mm
      // const imgHeight = (canvas.height * imgWidth) / canvas.width;

      const imgWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const imgHeight = ratio * imgWidth;
      let heightLeft = imgHeight;
      let position = 0;

      // Set a background color for each page in the PDF
      const backgroundColor = "#181b1b"; // Replace with your desired background color
      pdf.setFillColor(backgroundColor);

      // Add the background color and image data to the first page
      pdf.rect(0, 0, imgWidth, pageHeight, "F"); // Fill entire page with background color
      pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // If content spans multiple pages, handle them here
      while (heightLeft > 0) {
        position = imgHeight - heightLeft; // Properly calculate the new position
        pdf.addPage();
        pdf.setFillColor(backgroundColor); // Set the background color for new pages
        pdf.rect(0, 0, imgWidth, pageHeight, "F"); // Fill entire page with background color
        pdf.addImage(imgData, "PNG", 0, -position, imgWidth, imgHeight); // Offset the image properly
        heightLeft -= pageHeight;
      }

      // Save the generated PDF with the background filled
      pdf.save(`${new Date().toISOString().replace(/:/g, "-")}_booking-details.pdf`);
    });
  }, []);

  return { URLData, captureImage, downloadPDF, error };
};
export default useCaptureImage;
