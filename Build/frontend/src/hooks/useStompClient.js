"use client";

import { useEffect, useRef, useState } from "react";
import SockJS from "sockjs-client";
import { Stomp } from "@stomp/stompjs";
import { getLocalStorage } from "@/utils";
import { wsURL } from "@/config";

const useStompClient = () => {
  const stompClient = useRef(null);
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    const socket = new SockJS(wsURL);
    stompClient.current = Stomp.over(socket);

    // Disable debug logging with an empty function instead of null
    stompClient.current.debug = () => {};

    const headers = {
      Authorization: "Bearer " + getLocalStorage("access_token"),
    };

    stompClient.current.connect(
      headers,
      () => {
        setConnected(true);
      },
      () => {},
    );

    return () => {
      if (stompClient.current) {
        stompClient.current.disconnect();
      }
    };
  }, []);

  return connected ? stompClient.current : null;
};

export default useStompClient;
