import { notFound } from "next/navigation";
import { getRequestConfig } from "next-intl/server";
import { locales } from "./navigation";
import { getLocalStorage, setLocalStorage } from "@/utils";

export default getRequestConfig(async ({ locale }) => {
  // validate incoming locale and localstorage locale

  if (locale !== getLocalStorage("lang")) {
    setLocalStorage("lang", locale);
  }
  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale)) notFound();
  return {
    messages: (await import(`../messages/${locale}.json`)).default,
  };
});
