import axios from "axios";
import { getLocalStorage, removeLocalStorage } from "@/utils";
import { baseURL } from "@/config";
import { isTokenExpired } from "@/utils";

// Create an Axios instance
const axiosInstance = axios.create({
  baseURL: baseURL,
});

// Axios request interceptor
axiosInstance.interceptors.request.use(function (config) {
  const token = getLocalStorage("access_token") || "";
  const url = window.location.pathname;
  const lang = url.split("/")[1];
  // do not add access token in public routes

  if (token && isTokenExpired(token)) {
    removeLocalStorage("access_token");
    window.location.href = `/${lang}/login`;
  }
  if (!config.url.includes("public") || token) {
    config.headers.Authorization = token ? `Bearer ${token}` : "";
  }
  // check current lang from url

  config.headers["Accept-Language"] = lang || "en"; // getLocalStorage("lang")
  return config;
});

// Axios response interceptor
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error?.response && error?.response?.data === "Expired JWT token") {
      removeLocalStorage("access_token");
      window.location.href = `/${lang}/login`;
      //showSnackbar(error.response.data.message, "error");
    } else if (error.response && error.response.status === 500) {
      if (error?.response?.data.includes("User not found")) {
        removeLocalStorage("access_token");
        window.location.href = `/${lang}/signup`;
      }
    }
    return Promise.reject(error);
  },
);

export default axiosInstance;
