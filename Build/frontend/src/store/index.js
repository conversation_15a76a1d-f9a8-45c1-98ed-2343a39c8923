import { configureStore, combineReducers } from "@reduxjs/toolkit";
import signUpReducer from "./slice/auth/signup.auth.slice";
import loginReducer from "./slice/auth/login.auth.slice";
import twoFaReducer from "./slice/auth/2fa.auth.slice";
import forgotPasswordReducer from "./slice/auth/forgot-password.auth.slice";
import changePasswordReducer from "./slice/auth/change-password.auth.slice";
import actReducer from "./slice/act/act.slice";
import commonReducer from "./slice/common/common.slice";
import commonSearchReducer from "./slice/common/search.slice";
import favouriteSliceReducer from "./slice/common/favourite.slice";
import instantMessageReducer from "./slice/common/instantMessage.slice";
import bookingReducer from "./slice/booking/booking.slice";
import eventReducer from "./slice/specialevent/event.slice";
import stripeReducer from "./slice/stripe/stripe.slice";
// combine all reducers
// const preloadedState = {
//   login: {
//     token: getLocalStorage("access_token"),
//     loading: true,
//   },
//   act: {
//     profileId: getLocalStorage("profileId"),
//     previewData: getLocalStorage("previewData"),
//   },
// };
const combineReducer = combineReducers({
  signup: signUpReducer,
  login: loginReducer,
  twoFa: twoFaReducer,
  forgotPassword: forgotPasswordReducer,
  changePassword: changePasswordReducer,
  act: actReducer,
  common: commonReducer,
  search: commonSearchReducer,
  favourite: favouriteSliceReducer,
  instantMessage: instantMessageReducer,
  booking: bookingReducer,
  events: eventReducer,
  stripe: stripeReducer,
});

const rootReducer = (state, action) => {
  if (action.type === "RESET") {
    // check for action type
    state = undefined;
  }
  return combineReducer(state, action);
};

const store = configureStore({
  reducer: rootReducer,
  //preloadedState,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export default store;
