import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axiosInstance from "@/lib/axios";
import { setLocalStorage, getLocalStorage } from "@/utils";

{
  /** languages */
}

export const actLanguages = createAsyncThunk("act/languages", async (_, thunkAPI) => {
  try {
    const response = await axiosInstance.get("private/act/supported-languages");
    if (response && response.status === 200) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

{
  /** roles */
}
export const actRoles = createAsyncThunk("act/roles", async (_, thunkAPI) => {
  try {
    const response = await axiosInstance.get("private/act/supported-act-roles");
    if (response && response.status === 200) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

{
  /** valid name */
}
export const actValidName = createAsyncThunk(
  "act/valid-name",
  async ({ profileId, name }, thunkAPI) => {
    try {
      const response = await axiosInstance.get(
        `private/act/profiles/${profileId}/valid-name?name=${name}`,
      );
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error?.response?.status) {
        errorMessage = error?.response?.data?.message ?? "Unexpected error occurred";
      } else {
        errorMessage = "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

{
  /** create profile */
}
export const createProfile = createAsyncThunk("act/create-profile", async (data, thunkAPI) => {
  try {
    const response = await axiosInstance.post("private/act/profiles", data);
    if (response && response.status === 200) {
      setLocalStorage("profileId", response.data.data.result);
      return response;
    } else {
      throw new Error("Error occurred creating the profile!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export const updateProfile = createAsyncThunk(
  "act/handle-profile",
  async ({ profileId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.put(`private/act/profiles/${profileId}`, data);
      if (response && response.status === 200) {
        //setLocalStorage("profileId", response.data.data.result);
        return response;
      } else {
        throw new Error("Error occurred updating the profile!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);
/** get profile */
export const getProfile = createAsyncThunk("act/get-profile", async (profileId, thunkAPI) => {
  try {
    const response = await axiosInstance.get(`private/act/profiles/${profileId}`);
    if (response && response.status === 200) {
      return response;
    } else {
      throw new Error("Error occurred updating the profile!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

{
  /** Create Location Location API */
}
export const createActLocation = createAsyncThunk(
  "act/location",
  async ({ data, profileId }, thunkAPI) => {
    try {
      const response = await axiosInstance.post(`private/act/profiles/${profileId}/location`, data);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const updateActLocation = createAsyncThunk(
  "act/update-location",
  async ({ data, profileId }, thunkAPI) => {
    try {
      const response = await axiosInstance.put(`private/act/profiles/${profileId}/location`, data);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const getActLocation = createAsyncThunk("act/get-location", async (profileId, thunkAPI) => {
  try {
    const response = await axiosInstance.get(`private/act/profiles/${profileId}/location`);
    if (response && response.status === 200) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

{
  /** location countries */
}

export const actSupportedCountries = createAsyncThunk("act/countries", async (_, thunkAPI) => {
  try {
    const response = await axiosInstance.get("private/act/supported-regions");
    if (response && response.status === 200) {
      return response.data;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

{
  /** act profile info */
}
export const createActProfileInfo = createAsyncThunk(
  "act/profile-info",
  async ({ profileId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.post(`private/act/profiles/${profileId}/info`, data);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

{
  /** publish act profile info */
}
export const publishActProfileInfo = createAsyncThunk(
  "publish/act/profile-info",
  async ({ profileId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.post(`private/act/profiles/${profileId}/status`, data);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);
export const updateActProfileInfo = createAsyncThunk(
  "act/update-profile-info",
  async ({ profileId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.post(`private/act/profiles/${profileId}/info`, data);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const gerActProfileInfo = createAsyncThunk(
  "act/get-profile-info",
  async (profileId, thunkAPI) => {
    try {
      const response = await axiosInstance.get(`private/act/profiles/${profileId}/info`);
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.statu) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

{
  /** act types */
}
export const gerActTypes = createAsyncThunk("act/actTypes", async (_, thunkAPI) => {
  try {
    const response = await axiosInstance.get("public/supported-entertainment-types");
    if (response && response.status === 200) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.statu) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

{
  /** music genre */
}

export const getMusicGenre = createAsyncThunk("act/music-genre", async (_, thunkAPI) => {
  try {
    const response = await axiosInstance.get("public/supported-music-genre");
    if (response && response.status === 200) {
      return response.data;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});
export const getMusicGenreSearch = createAsyncThunk(
  "act/music-genre-search",
  async (search, thunkAPI) => {
    try {
      const response = await axiosInstance.get(
        `private/act/music-genre/search?searchString=${search}`,
      );
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);
{
  /** create skills => act-types m music-genre */
}
export const createActSkills = createAsyncThunk(
  "act/act-skills",
  async ({ profileId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.post(`private/act/profiles/${profileId}/skills`, data);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const getActSkills = createAsyncThunk("act/get-act-skills", async (profileId, thunkAPI) => {
  try {
    const response = await axiosInstance.get(`private/act/profiles/${profileId}/skills`);
    if (response && response.status === 200) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    return thunkAPI.rejectWithValue(error.response);
  }
});
{
  /** act payment */
}
export const getActPayment = createAsyncThunk("act/payment", async (_, thunkAPI) => {
  try {
    const response = await axiosInstance.get("private/act/supported-options");
    if (response && response.status === 200) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});
{
  /** get act payment for the profile */
}
export const getProfileActPayment = createAsyncThunk(
  "act/profile-payment",
  async (profileId, thunkAPI) => {
    try {
      const response = await axiosInstance.get(`private/act/profiles/${profileId}/payments`);
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const createActPayment = createAsyncThunk(
  "act/createPayment",
  async ({ profileId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.post(`private/act/profiles/${profileId}/payments`, data);
      if (response && response.status == 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const NotForRentPayment = createAsyncThunk(
  "act/not-for-rent",
  async ({ profileId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.post(
        `private/act/profiles/${profileId}/not-for-rent?enable=${data}`,
      );
      if (response && response.status == 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const getActByProfileId = createAsyncThunk(
  "get/act/profiles/detailed",
  async (profileId, thunkAPI) => {
    try {
      const response = await axiosInstance.get(
        `private/act/profiles/detailed?profileId=${profileId}`,
      );
      if (response && response.status == 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const getRecentlyVisited = createAsyncThunk(
  "get/private/stats/visited",
  async (_, thunkAPI) => {
    try {
      const response = await axiosInstance.get("private/stats/visited");
      if (response && response.status == 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const getMayLiked = createAsyncThunk(
  "get/private/stats/similar",
  async (profileId, thunkAPI) => {
    try {
      const response = await axiosInstance.get(`private/stats/similar/${profileId}`);
      if (response && response.status == 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);
export const getFeedbackById = createAsyncThunk("get/feedback/id", async (profileId, thunkAPI) => {
  try {
    const response = await axiosInstance.get(`private/feedbacks/${profileId}`);
    if (response && response.status == 200) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export const updateActPayment = createAsyncThunk(
  "act/updatePayment",
  async ({ profileId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.post(`private/act/profiles/${profileId}/payments`, data);
      if (response && response.status == 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const createActMedia = createAsyncThunk(
  "act/media",
  async ({ file, profileId }, thunkAPI) => {
    try {
      const response = await axiosInstance.post(`private/act/profiles/${profileId}/media`, file, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const deleteActMedia = createAsyncThunk(
  "act/delete-media",
  async ({ fileName, profileId }, thunkAPI) => {
    try {
      const response = await axiosInstance.delete(
        `private/act/profiles/${profileId}/media/${fileName}`,
      );
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const deleteActByProfileId = createAsyncThunk(
  "act/delete-act",
  async ({ profileId }, thunkAPI) => {
    try {
      const response = await axiosInstance.delete(`private/act/profiles/${profileId}`);
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const updateActMedia = createAsyncThunk(
  "act/update-media",
  async ({ data, profileId }, thunkAPI) => {
    try {
      const response = await axiosInstance.put(`private/act/profiles/${profileId}/media`, data);
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);
export const getActMedia = createAsyncThunk("act/get-media", async ({ profileId }, thunkAPI) => {
  try {
    const response = await axiosInstance.get(`private/act/profiles/${profileId}/media`);
    if (response && response.status === 200) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

/** act profiles */
export const getProfiles = createAsyncThunk("act/get-profiles", async (params, thunkAPI) => {
  try {
    const response = await axiosInstance.get(`private/act/profiles/min?${params}`);
    if (response && response.status === 200) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

/** act profiles */
export const getProfilesCurrent = createAsyncThunk(
  "act/get-profiles-current",
  async (params, thunkAPI) => {
    try {
      const response = await axiosInstance.get(`private/act/profiles?${params}`);
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const getRiderPdf = createAsyncThunk("act/get-pdf", async (profileId, thunkAPI) => {
  try {
    const response = await axiosInstance.get(`private/act/profiles/${profileId}/rider`);
    if (response && response.status === 200) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

{
  /** Rider apis starts here */
}
export const uploadRiderPdf = createAsyncThunk(
  "act/act-pdf",
  async ({ file, profileId }, thunkAPI) => {
    try {
      const response = await axiosInstance.post(`private/act/profiles/${profileId}/rider`, file, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const viewPdf = createAsyncThunk(
  "act/view-rider-pdf",
  async ({ profileId, fileName }, thunkAPI) => {
    try {
      const response = await axiosInstance.get(
        `private/act/profiles/${profileId}/rider/view/${fileName}`,
        { responseType: "blob" },
      );
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const deleteRiderPdf = createAsyncThunk(
  "act/act-pdf",
  async ({ file, profileId }, thunkAPI) => {
    try {
      const response = await axiosInstance.delete(
        `private/act/profiles/${profileId}/rider/${file}`,
      );
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

{
  /** Distribution apis starts here */
}
export const getDistributionList = createAsyncThunk(
  "act/get-distribution-list",
  async ({ profileId, search }, thunkAPI) => {
    try {
      const response = await axiosInstance.get(
        `private/act/profiles/${profileId}/distribution-list?searchString=${search}`,
      );
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const createDistribution = createAsyncThunk(
  "act/distribution",
  async ({ profileId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.post(
        `private/act/profiles/${profileId}/distribution-list`,
        data,
      );
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const getSchedules = createAsyncThunk("act/get-schedules", async (profileId, thunkAPI) => {
  try {
    const response = await axiosInstance.get(`private/act/${profileId}/schedule`);
    if (response && response.status === 200) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export const createSchedules = createAsyncThunk(
  "act/create-schedules",
  async ({ profileId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.post(`private/act/${profileId}/schedule`, data);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const updateSchedules = createAsyncThunk(
  "act/update-schedules",
  async ({ profileId, data, scheduleId }, thunkAPI) => {
    try {
      const response = await axiosInstance.put(
        `private/act/${profileId}/schedule/${scheduleId}`,
        data,
      );
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const updateContractSchedules = createAsyncThunk(
  "act/update-schedules",
  async ({ contractId, profileId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.put(
        `private/contracts/${contractId}/schedule?profileId=${profileId}`,
        data,
      );
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const deleteContractSchedules = createAsyncThunk(
  "act/delete-schedules",
  async ({ contractId }, thunkAPI) => {
    try {
      const response = await axiosInstance.delete(`private/contracts/${contractId}/schedule`);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const deleteSchedule = createAsyncThunk(
  "act/update-schedules",
  async ({ profileId, contractId }, thunkAPI) => {
    try {
      const response = await axiosInstance.delete(
        `private/act/${profileId}/schedule/${contractId}`,
      );
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const getBlockedUsers = createAsyncThunk("users/blocked", async (_, thunkAPI) => {
  try {
    const response = await axiosInstance.get("private/users/current");
    if (response && response.status === 200) {
      return response.data;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export const unblockUser = createAsyncThunk("users/unblock", async (profileId, thunkAPI) => {
  try {
    const response = await axiosInstance.delete(`private/users/blocked?profileId=${profileId}`);
    if (response && response.status === 200) {
      return { profileId, response: response.data };
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export const blockUser = createAsyncThunk("users/block", async (profileId, thunkAPI) => {
  try {
    const response = await axiosInstance.put(`private/users/blocked?profileId=${profileId}`);
    if (response && response.status === 200) {
      return { profileId, response: response.data };
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

// Get calendar data with both schedules and special events
export const getCalendar = createAsyncThunk(
  "act/get-calendar",
  async ({ profileId, params }, thunkAPI) => {
    const { startDate, queryPeriod } = params;
    if (!startDate || !queryPeriod) {
      return thunkAPI.rejectWithValue("Invalid parameters");
    }
    try {
      const response = await axiosInstance.get(
        `private/act/${profileId}/calendar?start=${startDate}&period=${queryPeriod}`,
      );
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred fetching calendar data!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response?.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const actSlice = createSlice({
  name: "act",
  initialState: {
    loading: false,
    languages: null,
    roles: null,
    profileId: getLocalStorage("proifle_id"),
    currentProfile: {},
    previewData: getLocalStorage("previewData"),
    actTypes: null,
    musicGenres: null,
    actPayment: null,
    actRegions: null,
    profiles: null,
    riderList: [],
    scheduleData: [],
  },
  reducers: {
    setPreviewData: (state, action) => {
      const updatedPreviewData = { ...state.previewData, ...action.payload };
      setLocalStorage("previewData", updatedPreviewData);
      return { ...state, previewData: updatedPreviewData };
    },
    resetPreviewData: (state) => {
      setLocalStorage("previewData", null);
      return { ...state, previewData: null, profileId: null };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(actLanguages.pending, (state) => {
        state.loading = true;
      })
      .addCase(actLanguages.fulfilled, (state, action) => {
        state.loading = false;
        state.languages = action.payload.data.data.languages;
      })
      .addCase(actLanguages.rejected, (state) => {
        state.loading = false;
        state.languages = [];
      })
      .addCase(getSchedules.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSchedules.fulfilled, (state, action) => {
        state.loading = false;
        state.scheduleData = action.payload.data.data;
      })
      .addCase(getSchedules.rejected, (state) => {
        state.loading = false;
        state.languages = [];
      })
      .addCase(actRoles.pending, (state) => {
        state.loading = true;
      })
      .addCase(actRoles.fulfilled, (state, action) => {
        state.loading = false;
        state.roles = action.payload.data.data.roles;
      })
      .addCase(actRoles.rejected, (state) => {
        state.loading = false;
        state.roles = [];
      })
      .addCase(createProfile.fulfilled, (state, action) => {
        const profieId = action.payload.data.data.result;
        state.profileId = profieId;
      })
      .addCase(gerActTypes.pending, (state) => {
        state.loading = true;
      })
      .addCase(gerActTypes.fulfilled, (state, action) => {
        state.loading = false;
        state.actTypes = action.payload.data.data.entertainmentTypeList;
      })
      .addCase(gerActTypes.rejected, (state) => {
        state.loading = false;
        state.actTypes = [];
      })
      .addCase(getMusicGenre.pending, (state) => {
        state.loading = true;
      })
      .addCase(getMusicGenre.fulfilled, (state, action) => {
        state.loading = false;
        state.musicGenres = action.payload.data.genreList;
      })
      .addCase(getMusicGenre.rejected, (state) => {
        state.loading = false;
      })
      .addCase(getActPayment.pending, (state) => {
        state.loading = true;
      })
      .addCase(getActPayment.fulfilled, (state, action) => {
        state.loading = false;
        state.actPayment = action.payload.data.data;
      })
      .addCase(getActPayment.rejected, (state) => {
        state.loading = false;
      })
      .addCase(actSupportedCountries.pending, (state) => {
        state.loading = true;
      })
      .addCase(actSupportedCountries.fulfilled, (state, action) => {
        state.loading = false;
        state.actRegions = action.payload.data.Countries;
      })
      .addCase(actSupportedCountries.rejected, (state) => {
        state.loading = false;
      })
      .addCase(createActProfileInfo.pending, (state) => {
        state.loading = true;
      })
      .addCase(createActProfileInfo.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(createActProfileInfo.rejected, (state) => {
        state.loading = false;
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        const profieId = action.payload.data.data.result;
        state.profileId = profieId;
      })
      .addCase(updateActProfileInfo.pending, (state) => {
        state.loading = true;
      })
      .addCase(updateActProfileInfo.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(updateActProfileInfo.rejected, (state) => {
        state.loading = false;
      })
      .addCase(getMusicGenreSearch.pending, (state) => {
        state.loading = true;
      })
      .addCase(getMusicGenreSearch.fulfilled, (state, action) => {
        state.loading = false;
        state.musicGenres = action.payload.data.data;
      })
      .addCase(getMusicGenreSearch.rejected, (state) => {
        state.loading = false;
      })
      .addCase(getProfiles.pending, (state) => {
        state.loading = true;
      })
      .addCase(getProfiles.fulfilled, (state, action) => {
        state.loading = false;
        state.profiles = action.payload.data.data;
      })
      .addCase(getProfiles.rejected, (state) => {
        state.loading = false;
      })
      .addCase(getActByProfileId.pending, (state) => {
        state.loading = true;
      })
      .addCase(getActByProfileId.fulfilled, (state, action) => {
        state.loading = false;
        state.currentProfile = action.payload.data.data;
      })
      .addCase(getActByProfileId.rejected, (state) => {
        state.loading = false;
      })
      .addCase(getRiderPdf.pending, (state) => {
        state.loading = true;
      })
      .addCase(getRiderPdf.fulfilled, (state, action) => {
        state.loading = false;
        state.riderList = action.payload.data.data;
      })
      .addCase(getRiderPdf.rejected, (state) => {
        state.loading = false;
      });
  },
});
export const { setPreviewData, resetPreviewData } = actSlice.actions;
export default actSlice.reducer;
