import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axiosInstance from "@/lib/axios";

export const twoFaEmail = createAsyncThunk("2fa/TwoFaEmail", async (email, thunkAPI) => {
  try {
    const response = await axiosInstance.get(`public/resend-twofa?${email}`);
    if (response && (response.status === 200 || response.status === 208)) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export const twoFaPhone = createAsyncThunk("2fa/TwoFaPhone", async (phoneNumber, thunkAPI) => {
  try {
    const response = await axiosInstance.put(`private/users/phone-number?${phoneNumber}`);
    if (response && (response.status === 200 || response.status === 208)) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status === 401) {
      errorMessage = error.response.data.message;
    } else if (error && error.response.status === 403) {
      errorMessage = error.response.data.message;
    } else if (error && error.response.status === 417) {
      errorMessage = error.response.data.message;
    } else if (error && error.response.status === 500) {
      errorMessage = "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export const passwordLogin = createAsyncThunk("users/confirm-password", async (data, thunkAPI) => {
  try {
    const response = await axiosInstance.get(
      `private/users/confirm-password?password=${data}`,
      data,
    );
    if (
      response &&
      (response.status === 200 || response.status === 202 || response.status === 208)
    ) {
      const user = {
        ...response,
        ...response.data.data,
        email: data.email,
        password: data.password,
      };
      return user;
    } else {
      throw new Error("Verification Failed!");
    }
  } catch (error) {
    let errorMessage = null;

    if (error && error.response.status) {
      errorMessage = error.response.data.message
        ? error.response.data.message
        : error.response.data;
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

{
  /** verify otp for the phoneNumber */
}
export const verifyOTPPhoneNumer = createAsyncThunk(
  "auth/verifyOTPPhoneNumber",
  async (data, thunkAPI) => {
    try {
      const response = await axiosInstance.put(`private/users/phone-number/confirm?${data}`);
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("OTP verification Failed!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message;
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);
const twoFaSlice = createSlice({
  name: "TwoFa",
  initialState: {
    loading: false,
  },
  extraReducers: (builder) => {
    builder
      .addCase(twoFaEmail.pending, (state) => {
        state.loading = true;
      })
      .addCase(twoFaEmail.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(twoFaEmail.rejected, (state) => {
        state.loading = false;
      })
      .addCase(twoFaPhone.pending, (state) => {
        state.loading = true;
      })
      .addCase(twoFaPhone.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(twoFaPhone.rejected, (state) => {
        state.loading = false;
      })
      .addCase(verifyOTPPhoneNumer.pending, (state) => {
        state.loading = true;
      })
      .addCase(verifyOTPPhoneNumer.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(verifyOTPPhoneNumer.rejected, (state) => {
        state.loading = false;
      });
  },
});
export default twoFaSlice.reducer;
