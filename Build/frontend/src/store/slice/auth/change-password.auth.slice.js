import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axiosInstance from "@/lib/axios";

{
  /** change password */
}

export const changePassword = createAsyncThunk(
  "password/ChangePassword",
  async (data, thunkAPI) => {
    try {
      const response = await axiosInstance.put("private/change-password", data);
      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage || "Unexpected error Occured");
    }
  },
);

const changePasswordSlice = createSlice({
  name: "changePassword",
  initialState: {
    loading: false,
  },
  extraReducers: (builder) => {
    builder
      .addCase(changePassword.pending, (state) => {
        state.loading = true;
      })
      .addCase(changePassword.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(changePassword.rejected, (state) => {
        state.loading = false;
      });
  },
});
export default changePasswordSlice.reducer;
