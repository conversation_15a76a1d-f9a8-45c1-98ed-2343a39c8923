import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axiosInstance from "@/lib/axios";
import { setLocalStorage } from "@/utils";

{
  /** forgot password API */
}

export const forgotPassword = createAsyncThunk("forgot/forgotPassword", async (email, thunkAPI) => {
  try {
    const response = await axiosInstance.post(`/public/forgot-password?email=${email}`);
    if (response && (response.status === 200 || response.status === 208)) {
      setLocalStorage("forgotPasswordEmail", email);
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

{
  /** handle verify forgot otp */
}
export const verifyForgotOtp = createAsyncThunk(
  "forgot/verifyForgotOtp",
  async (data, thunkAPI) => {
    try {
      const response = await axiosInstance.get(`/public/forgot-password/verify-twofa?${data}`);
      if (response && (response.status === 200 || response.status === 208)) {
        return response;
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

{
  /** resetPassword */
}

export const resetPassword = createAsyncThunk("forgot/resetPassword", async (params, thunkAPI) => {
  try {
    const response = await axiosInstance.get(`/public/reset-password?${params}`);
    if (response && response.status === 200) {
      return response.data;
    } else {
      throw new Error("Reset Password Failed!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

const fotgotPasswordSlice = createSlice({
  name: "forgotPassword",
  initialState: {
    loading: false,
  },
  extraReducers: (builder) => {
    builder
      .addCase(forgotPassword.pending, (state) => {
        state.loading = true;
      })
      .addCase(forgotPassword.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(forgotPassword.rejected, (state) => {
        state.loading = false;
      })
      .addCase(resetPassword.pending, (state) => {
        state.loading = true;
      })
      .addCase(resetPassword.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(resetPassword.rejected, (state) => {
        state.loading = false;
      })
      .addCase(verifyForgotOtp.pending, (state) => {
        state.loading = true;
      })
      .addCase(verifyForgotOtp.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(verifyForgotOtp.rejected, (state) => {
        state.loading = false;
      });
  },
});
export default fotgotPasswordSlice.reducer;
