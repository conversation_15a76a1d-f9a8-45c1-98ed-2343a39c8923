import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axiosInstance from "@/lib/axios";
import { setLocalStorage, getLocalStorage, removeLocalStorage } from "@/utils";
import { GoogleAuth<PERSON><PERSON>ider, FacebookAuthProvider, signInWithPopup } from "firebase/auth";
import { auth } from "@/firebase";
import { oAuth2Config } from "@/config";

export const login = createAsyncThunk("auth/login", async (data, thunkAPI) => {
  try {
    const response = await axiosInstance.post("public/login", data);
    if (
      response &&
      (response.status === 200 || response.status === 202 || response.status === 208)
    ) {
      const user = {
        ...response,
        ...response.data.data,
        email: data.email,
        password: data.password,
      };
      return user;
    } else {
      throw new Error("Login Failed!");
    }
  } catch (error) {
    let errorMessage = null;

    if (error && error.response.status) {
      errorMessage = error.response.data.message
        ? error.response.data.message
        : error.response.data;
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

{
  /** google login  */
}
export const googleLogin = createAsyncThunk("auth/googleLogin", async (_, thunkAPI) => {
  window.location.href = `${oAuth2Config.baseURL}${oAuth2Config.google}`;
  return;
  try {
    const googleProvider = new GoogleAuthProvider();
    const response = await signInWithPopup(auth, googleProvider);
    if (response && response.user && response.user.accessToken) {
      setLocalStorage("access_token", response.user.accessToken);
      return response;
    } else {
      throw new Error("Google Login Failed!");
    }
  } catch (error) {
    // Handle error
    const errorMessage = error ? error.message : "unexpected error occurred";
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

// O-AUTH 2 LOGIN
export const loginWithOAuth2 = createAsyncThunk("auth/loginWithOAuth2", async (_, thunkAPI) => {
  try {
    const response = await axiosInstance.get("public/oauth2-login");
    if (response && (response.status === 200 || response.status === 202)) {
      return response.data;
    } else {
      throw new Error("Login Failed!");
    }
  } catch (error) {
    const errorMessage = error ? error.message : "unexpected error occurred";
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

{
  /** facebook login  */
}
export const facebookLogin = createAsyncThunk("auth/facebookLogin", async (_, thunkAPI) => {
  window.location.href = `${oAuth2Config.baseURL}${oAuth2Config.facebook}`;
  return;
  try {
    const facebookProvider = new FacebookAuthProvider();
    const response = await signInWithPopup(auth, facebookProvider);
    if (response && response.user && response.user.accessToken) {
      setLocalStorage("access_token", response.user.accessToken);
      return response;
    } else {
      throw new Error("facebook Login Failed!");
    }
  } catch (error) {
    const errorMessage = error ? error.message : "Unexpected error occurred";
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export const logout = createAsyncThunk("auth/logout", (_, thunkAPI) => {
  try {
    // Clear all user-related data from localStorage
    removeLocalStorage("access_token");
    removeLocalStorage("profileId");
    removeLocalStorage("currentUser");
    // Dispatch reset action to clear redux state
    thunkAPI.dispatch(reset());
    return true;
  } catch (error) {
    return thunkAPI.rejectWithValue("Unexpected error occurred");
  }
});

export const verifyOTP = createAsyncThunk("auth/verifyOTP", async (data, thunkAPI) => {
  try {
    const response = await axiosInstance.post("public/login-twofa", data);
    if (response && response.status === 200) {
      setLocalStorage("access_token", response.data.data.accessToken);
      return response.data;
    } else {
      throw new Error("Login Failed!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

{
  /** get current user email */
}

export const getCurrentUserEmail = createAsyncThunk(
  "auth/getCurrentUserEmail",
  async (_, thunkAPI) => {
    try {
      const response = await axiosInstance.get("private/users/current");
      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Failed to get user email");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const putCurrentUserEmail = createAsyncThunk(
  "auth/getCurrentUserEmail",
  async (data, thunkAPI) => {
    try {
      const response = await axiosInstance.put("private/users/current", data);
      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Failed to get user email");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

const loginSlice = createSlice({
  name: "login",
  initialState: {
    token: getLocalStorage("access_token"),
    data: null,
    loading: false,
    currentUser: getLocalStorage("currentUser"),
  },
  reducers: {
    reset: (state) => {
      state.token = null;
      state.data = null;
      state.currentUser = null;
      state.loading = false;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(login.pending, (state) => {
        state.loading = true;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.loading = false;
        state.token = action.payload.accessToken;
        state.data = action.payload;
      })
      .addCase(login.rejected, (state) => {
        state.loading = false;
      })
      .addCase(googleLogin.fulfilled, (state, action) => {
        state.token = action.payload.user.accessToken;
      })
      .addCase(facebookLogin.fulfilled, (state, action) => {
        state.token = action.payload.user.accessToken;
      })
      .addCase(logout.pending, (state) => {
        state.loading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        state.loading = false;
        state.token = null;
      })
      .addCase(logout.rejected, (state) => {
        state.loading = false;
      })
      .addCase(verifyOTP.pending, (state) => {
        state.loading = true;
      })
      .addCase(verifyOTP.fulfilled, (state, action) => {
        state.loading = false;
        state.token = action.payload.data.accessToken;
        state.data = action.payload;
      })
      .addCase(verifyOTP.rejected, (state) => {
        state.loading = false;
      })
      .addCase(getCurrentUserEmail.pending, (state) => {
        state.loading = true;
      })
      .addCase(getCurrentUserEmail.fulfilled, (state, action) => {
        state.loading = false;
        state.currentUser = action.payload.data;
        setLocalStorage("currentUser", action.payload.data);
      })
      .addCase(getCurrentUserEmail.rejected, (state) => {
        state.loading = false;
      })
      .addCase(loginWithOAuth2.pending, (state) => {
        state.loading = true;
      })
      .addCase(loginWithOAuth2.fulfilled, (state, action) => {
        state.loading = false;
        state.token = action.payload.data.accessToken;
        state.data = action.payload.data;
      })
      .addCase(loginWithOAuth2.rejected, (state) => {
        state.loading = false;
      });
  },
});
export default loginSlice.reducer;
export const { reset } = loginSlice.actions;
