import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axiosInstance from "@/lib/axios";

export const signUp = createAsyncThunk("auth/signup", async (data, thunkAPI) => {
  try {
    const response = await axiosInstance.post("public/register", data);
    if (
      response &&
      (response.status === 200 || response.status === 201 || response.status === 208)
    ) {
      return response;
    } else {
      throw new Error("Registeration failed!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});
export const validatePassword = createAsyncThunk(
  "auth/validatePassword",
  async (data, thunkAPI) => {
    try {
      const response = await axiosInstance.post("public/validate-password", {
        password: data.password,
      });
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Failed to validate the passsword");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);
{
  /** resend the email */
}
export const resendEmailVerification = createAsyncThunk(
  "auth/resendEmail",
  async (email, thunkAPI) => {
    try {
      const response = await axiosInstance.get(`/public/resend-register-email?${email}`);
      if (response && (response.status === 200 || response.status === 208)) {
        return response;
      } else {
        throw new Error("Failed to resend email");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);
const signUpSlice = createSlice({
  name: "signup",
  initialState: {
    data: null,
    loading: false,
  },
  extraReducers: (builder) => {
    builder
      .addCase(signUp.pending, (state) => {
        state.loading = true;
      })
      .addCase(signUp.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(signUp.rejected, (state) => {
        state.loading = false;
      })
      .addCase(validatePassword.pending, (state) => {
        state.loading = true;
      })
      .addCase(validatePassword.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(validatePassword.rejected, (state) => {
        state.loading = false;
      })
      .addCase(resendEmailVerification.pending, (state) => {
        state.loading = true;
      })
      .addCase(resendEmailVerification.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(resendEmailVerification.rejected, (state) => {
        state.loading = false;
      });
  },
});
export default signUpSlice.reducer;
