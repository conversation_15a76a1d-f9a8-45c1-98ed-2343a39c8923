import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axiosInstance from "@/lib/axios";
import { setLocalStorage, getLocalStorage } from "@/utils";

export const checkProfileOwner = createAsyncThunk(
  "get/profiles/owner",
  async (profileId, thunkAPI) => {
    try {
      const response = await axiosInstance.get(`private/act/profiles/owner/${profileId}`);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const previewContract = createAsyncThunk(
  "get/preview/contracts",
  async (contractId, thunkAPI) => {
    try {
      const response = await axiosInstance.get(`private/contracts/${contractId}/details`);
      if ((response && response.status === 200) || response.status === 208) {
        setLocalStorage("previewContract", response.data.data);
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const modifiedGoodsServices = createAsyncThunk(
  "get/preview/modified-goods-services",
  async (contractId, thunkAPI) => {
    try {
      const response = await axiosInstance.get(
        `private/contracts/${contractId}/modified-goods-services`,
      );
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const previewContractWithMessageId = createAsyncThunk(
  "get/preview/contracts",
  async ({ messageId }, thunkAPI) => {
    try {
      const response = await axiosInstance.get(`private/messages/received/${messageId}/contract`);
      if ((response && response.status === 200) || response.status === 208) {
        setLocalStorage("previewContract", response.data.data);
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const getFinePrint = createAsyncThunk(
  "get/contracts/fine-print",
  async (contractId, thunkAPI) => {
    try {
      const response = await axiosInstance.get("private/contracts/fine-print");
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const selectProvideInfo = createAsyncThunk(
  "get/contracts/act-info",
  async ({ contractId, profileId }, thunkAPI) => {
    try {
      const response = await axiosInstance.put(
        `private/contracts/${contractId}/act-info?profileId=${profileId}`,
      );
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const selectVenueInfo = createAsyncThunk(
  "get/contracts/venue-info",
  async ({ contractId, profileId }, thunkAPI) => {
    try {
      const response = await axiosInstance.put(
        `private/contracts/${contractId}/venue-info?profileId=${profileId}`,
      );
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const createContracts = createAsyncThunk("get/profiles/owner", async (data, thunkAPI) => {
  try {
    const response = await axiosInstance.post("private/contracts", data);
    if ((response && response.status === 200) || response.status === 208) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    return thunkAPI.rejectWithValue(error.response);
  }
});

export const actRiderChanges = createAsyncThunk(
  "put/contracts/actRiderChanges",
  async ({ contractId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.put(
        `private/contracts/${contractId}/act-rider-changes`,
        data,
      );
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const actRiderNotes = createAsyncThunk(
  "put/contracts/actRiderNotes",
  async ({ contractId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.put(
        `private/contracts/${contractId}/act-rider-notes`,
        data,
      );
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const venueRiderChanges = createAsyncThunk(
  "put/contracts/venueRiderChanges",
  async ({ contractId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.put(
        `private/contracts/${contractId}/venue-rider-changes`,
        data,
      );
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const venueRiderNotes = createAsyncThunk(
  "put/contracts/venueRiderNotes",
  async ({ contractId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.put(
        `private/contracts/${contractId}/venue-rider-notes`,
        data,
      );
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const gsMsgTemplates = createAsyncThunk(
  "get/contracts/gsMsgTemplates",
  async (_, thunkAPI) => {
    try {
      const response = await axiosInstance.get("private/contracts/gs-msg-templates");
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const addGsMsgTemplates = createAsyncThunk(
  "get/contracts/gsMsgTemplates",
  async (data, thunkAPI) => {
    try {
      const response = await axiosInstance.post("private/contracts/gs-msg-templates", data);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const putGsMsgTemplates = createAsyncThunk(
  "get/contracts/gsMsgTemplates",
  async (data, thunkAPI) => {
    try {
      const response = await axiosInstance.put("private/contracts/gs-msg-templates", data);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const deleteGsMsgTemplates = createAsyncThunk(
  "get/contracts/gsMsgTemplates",
  async (name, thunkAPI) => {
    try {
      const response = await axiosInstance.delete(`private/contracts/gs-msg-templates/${name}`);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);
export const goodsServices = createAsyncThunk(
  "put/contracts/goodsServices",
  async ({ contractId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.put(
        `private/contracts/${contractId}/goods-services`,
        data,
      );
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const negotiate = createAsyncThunk(
  "post/contracts/negotiate",
  async ({ contractId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.post(`private/contracts/${contractId}/negotiate`, data);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const myContracts = createAsyncThunk("get/contracts", async ({ page, size }, thunkAPI) => {
  try {
    const response = await axiosInstance.get(`private/contracts?page=${page}&size=${size}`);
    if ((response && response.status === 200) || response.status === 208) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    return thunkAPI.rejectWithValue(error.response);
  }
});

export const otherContracts = createAsyncThunk(
  "get/contracts",
  async ({ page, size }, thunkAPI) => {
    try {
      const response = await axiosInstance.get(
        `private/contracts-received?page=${page}&size=${size}`,
      );
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const sendContracts = createAsyncThunk("get/contracts", async (contractId, thunkAPI) => {
  try {
    const response = await axiosInstance.post(`private/contracts/${contractId}/send`);
    if ((response && response.status === 200) || response.status === 208) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    return thunkAPI.rejectWithValue(error.response);
  }
});

export const declineContracts = createAsyncThunk(
  "decline/contracts",
  async (contractId, thunkAPI) => {
    try {
      const response = await axiosInstance.post(`private/contracts/${contractId}/decline`);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const acceptContracts = createAsyncThunk(
  "accept/contracts",
  async (contractId, thunkAPI) => {
    try {
      const response = await axiosInstance.post(`private/contracts/${contractId}/accept`);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const cancelContracts = createAsyncThunk(
  "cancel/contracts",
  async (contractId, thunkAPI) => {
    try {
      const response = await axiosInstance.post(`private/contracts/${contractId}/cancel`);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const messageSeen = createAsyncThunk("put/messages-seen", async (messageId, thunkAPI) => {
  try {
    const response = await axiosInstance.put(`private/messages/seen?messageId=${messageId}`);
    if ((response && response.status === 200) || response.status === 208) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    return thunkAPI.rejectWithValue(error.response);
  }
});

export const recievedAck = createAsyncThunk("get/contracts", async (contractId, thunkAPI) => {
  try {
    const response = await axiosInstance.post(`private/contracts/${contractId}/receive-ack`);
    if ((response && response.status === 200) || response.status === 208) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    return thunkAPI.rejectWithValue(error.response);
  }
});

export const deleteNotification = createAsyncThunk(
  "delete/notification",
  async (messageId, thunkAPI) => {
    try {
      const response = await axiosInstance.delete(`private/messages/${messageId}/notification`);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const getCurrentEvent = createAsyncThunk("get/current/event", async (eventId, thunkAPI) => {
  try {
    const response = await axiosInstance.get(`private/events/${eventId}/default-event`);
    if ((response && response.status === 200) || response.status === 208) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    return thunkAPI.rejectWithValue(error.response);
  }
});

export const getMainInfo = createAsyncThunk("get/current/info", async (eventId, thunkAPI) => {
  try {
    const response = await axiosInstance.get(`private/events/${eventId}/main-info`);
    if ((response && response.status === 200) || response.status === 208) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    return thunkAPI.rejectWithValue(error.response);
  }
});

export const getEventImage = createAsyncThunk("get/event/image", async (eventId, thunkAPI) => {
  try {
    const response = await axiosInstance.get(`private/events/${eventId}/media-info`);
    if ((response && response.status === 200) || response.status === 208) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    return thunkAPI.rejectWithValue(error.response);
  }
});

export const createEventMedia = createAsyncThunk(
  "act/media",
  async ({ file, profileId }, thunkAPI) => {
    try {
      const response = await axiosInstance.post(`private/events/${profileId}/image`, file, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const deleteEventMedia = createAsyncThunk(
  "act/delete-media",
  async ({ imageName, profileId }, thunkAPI) => {
    try {
      const response = await axiosInstance.delete(`private/events/${profileId}/image/${imageName}`);
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const addMainInfo = createAsyncThunk(
  "put/event/main-info",
  async ({ eventId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.put(`private/events/${eventId}/main-info`, data);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

// contract APIS
export const getContractInfo = createAsyncThunk("get/contract/info", async (eventId, thunkAPI) => {
  try {
    const response = await axiosInstance.get(`private/events/${eventId}/contract-info`);
    if ((response && response.status === 200) || response.status === 208) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    return thunkAPI.rejectWithValue(error.response);
  }
});

export const getEventsById = createAsyncThunk("get/events/info", async (eventId, thunkAPI) => {
  try {
    const response = await axiosInstance.get(`private/events/${eventId}`);
    if ((response && response.status === 200) || response.status === 208) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    return thunkAPI.rejectWithValue(error.response);
  }
});

export const getContractList = createAsyncThunk("get/contract/list", async (eventId, thunkAPI) => {
  try {
    const response = await axiosInstance.get(
      `private/events/${eventId}/contract-list?page=0&size=10000`,
    );
    if ((response && response.status === 200) || response.status === 208) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    return thunkAPI.rejectWithValue(error.response);
  }
});

export const addContractInfo = createAsyncThunk(
  "add/contract/info",
  async ({ eventId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.put(`private/events/${eventId}/contract-info`, data);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const publishEvent = createAsyncThunk("add/contract/info", async (eventId, thunkAPI) => {
  try {
    const response = await axiosInstance.put(`private/events/${eventId}/status`);
    if ((response && response.status === 200) || response.status === 208) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    return thunkAPI.rejectWithValue(error.response);
  }
});

export const getActEventInfo = createAsyncThunk("get/event/act-info", async (eventId, thunkAPI) => {
  try {
    const response = await axiosInstance.get(`private/events/${eventId}/act-info`);
    if ((response && response.status === 200) || response.status === 208) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    return thunkAPI.rejectWithValue(error.response);
  }
});

export const getVenueEventInfo = createAsyncThunk(
  "get/event/venue-info",
  async (eventId, thunkAPI) => {
    try {
      const response = await axiosInstance.get(`private/events/${eventId}/venue-info`);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const eventList = createAsyncThunk(
  "get/events",
  async ({ status, page, size }, thunkAPI) => {
    try {
      const response = await axiosInstance.get(
        `private/events?status=${status}&page=${page}&size=${size}`,
      );
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const getUserCalender = createAsyncThunk(
  "get/calendar/user/by-period",
  async ({ startDate, period }, thunkAPI) => {
    try {
      const response = await axiosInstance.get(
        `private/calendar/user/by-period?start=${startDate}&period=${period}`,
      );
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const bookingSlice = createSlice({
  name: "booking",
  initialState: {
    loading: false,
    profileId: getLocalStorage("proifle_id"),
    currentBookingStatus: getLocalStorage("currentBookingStatus"),
    previewContract: getLocalStorage("previewContract"),
    eventInfo: getLocalStorage("eventInfo"),
  },
  reducers: {
    setBookingData: (state, action) => {
      const updatedCurrentBookingStatus = { ...state.currentBookingStatus, ...action.payload };
      setLocalStorage("currentBookingStatus", updatedCurrentBookingStatus);
      return { ...state, currentBookingStatus: updatedCurrentBookingStatus };
    },
    setPreviewContract: (state, action) => {
      const updatedPreviewContract = { ...state.previewContract, ...action.payload };
      setLocalStorage("previewContract", updatedPreviewContract);
      return { ...state, previewContract: updatedPreviewContract };
    },
    resetBookingData: (state) => {
      setLocalStorage("currentBookingStatus", null);
      setLocalStorage("previewContract", null);
      return { ...state, currentBookingStatus: null, previewContract: null };
    },
    setEventInfo: (state, action) => {
      const updatedEventInfo = { ...state.eventInfo, ...action.payload };
      setLocalStorage("eventInfo", updatedEventInfo);
      return { ...state, eventInfo: updatedEventInfo };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(previewContract.pending, (state) => {
        state.loading = true;
      })
      .addCase(previewContract.fulfilled, (state, action) => {
        state.loading = false;
        state.previewContract = action.payload.data.data;
      })
      .addCase(previewContract.rejected, (state) => {
        state.loading = false;
        state.previewContract = [];
      })
      .addCase(getCurrentEvent.pending, (state) => {
        state.loading = true;
      })
      .addCase(getCurrentEvent.fulfilled, (state, action) => {
        state.loading = false;
        state.eventInfo = action.payload.data.data;
      })
      .addCase(getCurrentEvent.rejected, (state) => {
        state.loading = false;
        state.eventInfo = [];
      });
  },
});
export const { setBookingData, resetBookingData, setPreviewContract, setEventInfo } =
  bookingSlice.actions;
export default bookingSlice.reducer;
