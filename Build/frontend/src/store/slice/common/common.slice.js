import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axiosInstance from "@/lib/axios";

export const getSupportedProfiles = createAsyncThunk(
  "users/supported-profiles",
  async (_, thunkAPI) => {
    try {
      const response = await axiosInstance.get("private/users/supported-profiles");
      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage || "Unexpected error Occured");
    }
  },
);

export const getSupportedLocales = createAsyncThunk(
  "users/supported-locales",
  async (_, thunkAPI) => {
    try {
      const response = await axiosInstance.get("private/users/supported-locales");
      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage || "Unexpected error Occured");
    }
  },
);

/** get favourite profile */
export const getFavProfile = createAsyncThunk(
  "act/get-fav-profile",
  async ({ profileType, pageParams }, thunkAPI) => {
    const page = pageParams?.page || 0;
    const size = pageParams?.size || 12;
    try {
      const response = await axiosInstance.get(
        `private/users/current/favourites?profileType=${profileType}&page=${page}&size=${size}`,
      );
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred updating the profile!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const giveActFeedback = createAsyncThunk(
  "add/feedback",
  async ({ feedbackId, data }, thunkAPI) => {
    try {
      const response = await axiosInstance.put(`/private/feedbacks/${feedbackId}`, data);
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage || "Unexpected error Occured");
    }
  },
);

export const listFeedback = createAsyncThunk("act/list-feedback", async (profileId, thunkAPI) => {
  try {
    const response = await axiosInstance.get(`private/feedbacks?profileId=${profileId}`);
    if (response && response.status === 200) {
      return response;
    } else {
      throw new Error("Error occurred");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export const verifyVirtualAct = createAsyncThunk(
  "public/verify-virtual-act",
  async (token, thunkAPI) => {
    try {
      const response = await axiosInstance.get(`public/verify-virtual-act?token=${token}`);
      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage || "Unexpected error Occured");
    }
  },
);

export const verifyVirtualVenue = createAsyncThunk(
  "public/verify-virtual-venue",
  async (token, thunkAPI) => {
    try {
      const response = await axiosInstance.get(`public/verify-virtual-venue?token=${token}`);
      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage || "Unexpected error Occured");
    }
  },
);
const commonSlice = createSlice({
  name: "common",
  initialState: {
    loading: false,
    supportedProfiles: [],
    supportedLocales: [],
    favouritesData: [],
  },
  extraReducers: (builder) => {
    builder
      .addCase(getSupportedProfiles.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSupportedProfiles.fulfilled, (state, action) => {
        state.loading = false;
        state.supportedProfiles = action.payload?.data || [];
      })
      .addCase(getSupportedProfiles.rejected, (state) => {
        state.loading = false;
      })
      .addCase(getSupportedLocales.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSupportedLocales.fulfilled, (state, action) => {
        state.loading = false;
        state.supportedLocales = action.payload?.data || [];
      })
      .addCase(getSupportedLocales.rejected, (state) => {
        state.loading = false;
      })
      .addCase(getFavProfile.pending, (state) => {
        state.loading = true;
      })
      .addCase(getFavProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.favouritesData = action.payload?.data?.data || [];
      })
      .addCase(getFavProfile.rejected, (state) => {
        state.loading = false;
      });
  },
});
export default commonSlice.reducer;
