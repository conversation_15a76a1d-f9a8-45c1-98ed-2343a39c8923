import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axiosInstance from "@/lib/axios";

export const addFavouriteByProfileID = createAsyncThunk(
  "add/current/favourites",
  async (data, thunkAPI) => {
    try {
      const response = await axiosInstance.post(
        `/private/users/current/favourites?profileId=${data.profileId}`,
      );
      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage || "Unexpected error Occured");
    }
  },
);

export const removeFavouriteByProfileID = createAsyncThunk(
  "remove/current/favourites",
  async (data, thunkAPI) => {
    try {
      const response = await axiosInstance.delete(
        `/private/users/current/favourites?profileId=${data.profileId}`,
      );
      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error?.response?.status) {
        errorMessage = error?.response?.data?.message ?? "Unexpected error occurred";
      } else {
        errorMessage = "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

const favouriteSlice = createSlice({
  name: "favourite",
  initialState: {
    loading: false,
  },
  extraReducers: (builder) => {
    builder
      .addCase(addFavouriteByProfileID.pending, (state) => {
        state.loading = true;
      })
      .addCase(addFavouriteByProfileID.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(addFavouriteByProfileID.rejected, (state) => {
        state.loading = false;
      })
      .addCase(removeFavouriteByProfileID.pending, (state) => {
        state.loading = true;
      })
      .addCase(removeFavouriteByProfileID.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(removeFavouriteByProfileID.rejected, (state) => {
        state.loading = false;
      });
  },
});
export default favouriteSlice.reducer;
