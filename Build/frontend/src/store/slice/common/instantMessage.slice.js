import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axiosInstance from "@/lib/axios";

export const notificationList = createAsyncThunk(
  "private/messages",
  async (profileId, thunkAPI) => {
    try {
      const response = await axiosInstance.get("private/messages/received");
      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage || "Unexpected error Occured");
    }
  },
);

export const deleteNotification = createAsyncThunk(
  "delete/notification",
  async (messageId, thunkAPI) => {
    try {
      const response = await axiosInstance.delete(`private/messages/${messageId}/notification`);
      if ((response && response.status === 200) || response.status === 208) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response);
    }
  },
);

export const addNotifications = createAsyncThunk(
  "post/private/messages",
  async (data, thunkAPI) => {
    try {
      const response = await axiosInstance.post("private/messages", data);
      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage || "Unexpected error Occured");
    }
  },
);

const instantMessage = createSlice({
  name: "instantMessage",
  initialState: {
    loading: false,
    instantMessage: [],
  },
  reducers: {
    setInstantMessage: (state, { payload }) => {
      state.instantMessage.unshift(payload);
    },
  },
  extraReducers: (builder) => {
    builder.addCase(notificationList.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(notificationList.fulfilled, (state, { payload }) => {
      state.loading = false;
      state.instantMessage = payload?.data ?? [];
    });
    builder.addCase(notificationList.rejected, (state) => {
      state.loading = false;
    });
  },
});
export default instantMessage.reducer;
export const { setInstantMessage } = instantMessage.actions;
