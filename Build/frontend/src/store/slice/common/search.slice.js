import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axiosInstance from "@/lib/axios";

export const getSearchData = createAsyncThunk(
  "public/search",
  async ({ filter, profileType, pageParams }, thunkAPI) => {
    const page = pageParams?.page || 0;
    const size = pageParams?.size || 12;

    const filterData = { ...filter };
    if (filterData?.searchType) {
      delete filterData.searchType;
    }
    try {
      // Determine the URL based on profileType
      const url =
        profileType === "EVENT_PROFILE"
          ? `public/event-search?page=${page}&size=${size}`
          : `public/search?profileType=${profileType}&page=${page}&size=${size}`;

      // Make the API call
      const response = await axiosInstance.post(url, filterData);

      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response?.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage || "Unexpected error occurred");
    }
  },
);

export const getPrivateSearchData = createAsyncThunk(
  "private/search",
  async ({ filter, profileType, pageParams }, thunkAPI) => {
    const page = pageParams?.page || 0;
    const size = pageParams?.size || 12;
    const filterData = { ...filter };
    if (filterData?.searchType) {
      delete filterData.searchType;
    }
    try {
      const url =
        profileType === "EVENT_PROFILE"
          ? `private/event-search?page=${page}&size=${size}`
          : `private/user-search?profileType=${profileType}&page=${page}&size=${size}`;

      const response = await axiosInstance.post(url, filterData);
      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage || "Unexpected error Occured");
    }
  },
);

export const getSearchSelectData = createAsyncThunk(
  "public/search",
  async ({ filter, profileType }, thunkAPI) => {
    try {
      const response = await axiosInstance.post(
        `private/search-select?profileType=${profileType}&size=100000`,
        filter,
      );
      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage || "Unexpected error Occured");
    }
  },
);

export const getSavedSearches = createAsyncThunk("act/search", async (_, thunkAPI) => {
  try {
    const response = await axiosInstance.get("private/act/search");
    if (response && response.status === 200) {
      return response.data;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    return [];
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage || "Unexpected error Occured");
  }
});

export const saveSavedSearches = createAsyncThunk("act/search", async (paload, thunkAPI) => {
  try {
    const response = await axiosInstance.post("private/act/search", paload);
    if (response && response.status === 200) {
      return response.data;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error.response.status) {
      errorMessage = error.response.data.message ?? "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage || "Unexpected error Occured");
  }
});

export const deleteSavedSearches = createAsyncThunk("act/search-delete", async (name, thunkAPI) => {
  try {
    // add axios delete request
    const response = await axiosInstance.delete(`private/act/search/${name}`);
    if (response && response.status === 200) {
      return response;
    } else {
      throw new Error("Error occurred!");
    }
  } catch (error) {
    let errorMessage = null;
    if (error && error?.response?.status) {
      errorMessage = error?.response?.data?.message ?? "Unexpected error occurred";
    } else {
      errorMessage = "Unexpected error occurred";
    }
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

export const getLocationSearchData = createAsyncThunk(
  "public/search-location",
  async (location, thunkAPI) => {
    try {
      const response = await axiosInstance.get(`public/locations-search?searchString=${location}`);
      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage || "Unexpected error Occured");
    }
  },
);

export const setDefaultLocation = createAsyncThunk(
  "private/search/default-location",
  async (params, thunkAPI) => {
    try {
      const response = await axiosInstance.post("private/search/default-location", params);
      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage || "Unexpected error Occured");
    }
  },
);

export const getDefaultLocation = createAsyncThunk(
  "get/search/default-location",
  async (_, thunkAPI) => {
    try {
      const response = await axiosInstance.get("private/search/default-location");
      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage || "Unexpected error Occured");
    }
  },
);

export const deleteDefaultLocation = createAsyncThunk(
  "delete/search/default-location",
  async (_, thunkAPI) => {
    try {
      const response = await axiosInstance.delete("private/search/default-location");
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

const searchSlice = createSlice({
  name: "search",
  initialState: {
    loading: false,
    savedSearches: [],
    searchData: [],
  },
  extraReducers: (builder) => {
    builder
      .addCase(getSavedSearches.pending, (state) => {
        state.loading = false;
      })
      .addCase(getSavedSearches.fulfilled, (state, action) => {
        state.loading = false;
        state.savedSearches = action.payload?.data || [];
      })
      .addCase(getSavedSearches.rejected, (state) => {
        state.loading = false;
      })
      .addCase(getSearchData.pending, (state) => {
        state.loading = false;
      })
      .addCase(getSearchData.fulfilled, (state, action) => {
        state.loading = false;
        state.searchData = action.payload?.data || [];
      })
      .addCase(getSearchData.rejected, (state) => {
        state.loading = false;
      })
      .addCase(getPrivateSearchData.pending, (state) => {
        state.loading = false;
      })
      .addCase(getPrivateSearchData.fulfilled, (state, action) => {
        state.loading = false;
        state.searchData = action.payload?.data || [];
      })
      .addCase(getPrivateSearchData.rejected, (state) => {
        state.loading = false;
      });
  },
});
export default searchSlice.reducer;
