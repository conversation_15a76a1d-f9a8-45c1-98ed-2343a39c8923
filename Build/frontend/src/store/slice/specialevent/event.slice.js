import axiosInstance from "@/lib/axios";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

export const addNewSpecialEventAction = createAsyncThunk(
  "events/addNewEvent",
  async (payload, thunkAPI) => {
    try {
      const response = await axiosInstance.post("private/special-events", payload, {
        params: {
          profileId: payload.ownerProfileId,
        },
      });

      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Error Adding Event");
      }
    } catch (error) {
      let errorMessage = null;

      if (error.response.status === 409) {
        return thunkAPI.rejectWithValue(
          "The selected time overlaps with another scheduled event. Please select another time.",
        );
      }
      if (error && error.response && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      throw thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const getAllSpecialEventsAction = createAsyncThunk(
  "events/getAllSpecialEvents",
  async ({ proId, page = 0, size = 6 }, thunkAPI) => {
    try {
      const response = await axiosInstance.get("private/special-events/all", {
        params: {
          profileId: proId,
          size,
          page,
        },
      });

      if (response && response.status === 200) {
        return response.data?.data;
      } else {
        throw new Error("Error Adding Event");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      throw thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const getSpecialEventByIdAction = createAsyncThunk(
  "events/getSpecialEventById",
  async (eventId, thunkAPI) => {
    try {
      const response = await axiosInstance.get(`private/special-events/${eventId}`);

      if (response && response.status === 200) {
        return response.data?.data;
      } else {
        throw new Error("Error Fetching Event");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      throw thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

//@not used yet
export const deleteSpecialEventByIdAction = createAsyncThunk(
  "events/deleteSpecialEventById",
  async (eventId, thunkAPI) => {
    try {
      const response = await axiosInstance.delete(`private/special-events/${eventId}`);

      if (response && response.status === 200) {
        return response.data?.data;
      } else {
        throw new Error("Error Fetching Event");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      throw thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const updateSpecialEventAction = createAsyncThunk(
  "events/updateSpecialEvent",
  async ({ eventId, data }, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.put(`/private/special-events/${eventId}`, data);
      return response.data;
    } catch (error) {
      if (error.response.status === 409) {
        return rejectWithValue(
          "The selected time overlaps with another scheduled event. Please select another time.",
        );
      }
      return rejectWithValue(error.response?.data?.message || "Failed to update event");
    }
  },
);

export const createEventMedia = createAsyncThunk(
  "act/media",
  async ({ file, profileId, specialEventId }, thunkAPI) => {
    try {
      const response = await axiosInstance.post(
        `private/special-events/${profileId}/image?specialEventId=${specialEventId}`,
        file,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        },
      );
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const deleteEventMedia = createAsyncThunk(
  "act/delete-media",
  async ({ fileName, profileId, specialEventId }, thunkAPI) => {
    try {
      const response = await axiosInstance.delete(
        `private/special-events/${profileId}/image/${fileName}?specialEventId=${specialEventId}`,
      );
      if (response && response.status === 200) {
        return response;
      } else {
        throw new Error("Error occurred!");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

const initialState = {
  data: null,
  loading: true,
  error: null,
};

const eventSlice = createSlice({
  name: "events",
  initialState,
  extraReducers: (builder) => {
    builder
      .addCase(addNewSpecialEventAction.pending, (state) => {
        state.loading = true;
      })
      .addCase(addNewSpecialEventAction.fulfilled, (state, action) => {
        state.data = action.payload;
        state.loading = false;
      })
      .addCase(addNewSpecialEventAction.rejected, (state, action) => {
        state.error = action.payload;
        state.loading = false;
      })
      .addCase(getAllSpecialEventsAction.pending, (state) => {
        state.loading = true;
      })
      .addCase(getAllSpecialEventsAction.fulfilled, (state, action) => {
        state.data = action.payload;
        state.loading = false;
      })
      .addCase(getAllSpecialEventsAction.rejected, (state, action) => {
        state.error = action.payload;
        state.loading = false;
      })
      .addCase(getSpecialEventByIdAction.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSpecialEventByIdAction.fulfilled, (state, action) => {
        state.data = action.payload;
        state.loading = false;
      })
      .addCase(getSpecialEventByIdAction.rejected, (state, action) => {
        state.error = action.payload;
        state.loading = false;
      });
  },
});

export default eventSlice.reducer;
