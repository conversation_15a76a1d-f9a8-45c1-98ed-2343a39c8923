import axiosInstance from "@/lib/axios";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

export const initializeStripePayment = createAsyncThunk(
  "stripe/initializePayment",
  async (payload, thunkAPI) => {
    try {
      const response = await axiosInstance.post("private/stripe/checkout", null, {
        params: {
          contractId: payload,
        },
      });
      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Error initializing payment");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      throw thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

export const verifyPaymentStatusAction = createAsyncThunk(
  "stripe/verifyPaymentStatus",
  async (payload, thunkAPI) => {
    try {
      const response = await axiosInstance.get(`private/stripe/status/${payload}`);
      if (response && response.status === 200) {
        return response.data;
      } else {
        throw new Error("Error verifying payment status");
      }
    } catch (error) {
      let errorMessage = null;
      if (error && error.response && error.response.status) {
        errorMessage = error.response.data.message ?? "Unexpected error occurred";
      }
      throw thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

const stripeSlice = createSlice({
  name: "stripe",
  initialState: {
    data: null,
    loading: true,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(initializeStripePayment.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(initializeStripePayment.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(initializeStripePayment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(verifyPaymentStatusAction.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(verifyPaymentStatusAction.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(verifyPaymentStatusAction.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default stripeSlice.reducer;
