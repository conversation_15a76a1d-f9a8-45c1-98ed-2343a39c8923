import React from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { Box } from "@mui/material";
import { KeyboardArrowLeft, KeyboardArrowRight } from "@mui/icons-material";
import ProfileSingleCard from "@/common/profile-card/profile-card-single.common";

const ActDetailSlider = ({ datas }) => {
  const detailSettings = {
    className: "slick-dots slick-thumb slick-thumb-act-detail",
    dots: false,
    infinite: false,
    slidesToShow: 4,
    slidesToScroll: 4,
    prevArrow: <KeyboardArrowLeft className="custom-prev-arrow" />,
    nextArrow: <KeyboardArrowRight />,
    responsive: [
      { breakpoint: 1324, settings: { slidesToShow: 3, slidesToScroll: 2 } },
      { breakpoint: 800, settings: { slidesToShow: 2, slidesToScroll: 2, initialSlide: 2 } },
      { breakpoint: 578, settings: { slidesToShow: 1, slidesToScroll: 1 } },
    ],
  };

  return (
    <>
      {datas?.length > 3 ? (
        <Slider {...detailSettings}>
          {datas?.map((data, index) => (
            <Box className="pr-4 slick-act-detail" key={index}>
              <ProfileSingleCard profilesData={data} type="select-profile" />
            </Box>
          ))}
        </Slider>
      ) : (
        <Box className="!flex gap-3 !flex-wrap">
          {datas?.map((data, index) => (
            <Box className="pr-4" key={index}>
              <ProfileSingleCard profilesData={data} type="select-profile" />
            </Box>
          ))}
        </Box>
      )}
    </>
  );
};

export default ActDetailSlider;
