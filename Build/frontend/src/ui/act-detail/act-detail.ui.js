"use client";
import { Box, Typography, useMediaQuery, useTheme } from "@mui/material";
import React, { useEffect, useState } from "react";
import { Button, CommonImage } from "@/component";
import ActReviewLocation from "@/common/(act)/act-review/act-review-location.info.common";
import Rating from "@/component/rating/rating.components";
import EditActFeedbackRate from "../edit-act-feedback-rate/edit-act-feedback-rate.ui";
//import { dropdownOptions } from "./act-detail.data.ui";
// import ProfileCard from '@/common/profile-card/profile-card.common';
import { useDispatch, useSelector } from "react-redux";
import {
  blockUser,
  //getMayLiked,
  getProfiles,
  getRecentlyVisited,
  unblockUser,
} from "@/store/slice/act/act.slice";
import { stringifyParams } from "@/utils";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import { useLocale, useTranslations } from "next-intl";
import FilledStarSvg from "@/assets/svg/FilledStarSvg.svg";
import { generateLocationString } from "@/utils";
import ActDetailSlider from "./act-detail.slider";
import { listFeedback } from "@/store/slice/common/common.slice";
import PublicSvg from "@/assets/svg/PublicSvg.svg";
//import WorldSvg from "@/assets/svg/WorldSvg.svg";
//import PhoneSvg from "@/assets/svg/PhoneSvg.svg";
import dynamic from "next/dynamic";
import BookingButtonComponent from "@/component/button/booking-button.component";
import Link from "next/link";
import Avatar from "@/assets/png/Avatar.png";
import { SouthEast, North, South } from "@mui/icons-material";
import { useRouter } from "next/navigation";
import ViewCalender from "@/component/calendar/view-calendar.component";
const MapSearch = dynamic(() => import("@/common/profile-card/map-search"), {
  ssr: false,
});

// import { favouritesData } from '@/common/favourites-card/favourites-card.data.common';
// import FavroutiesCard from '@/common/favourites-card/favourites-card.common';
// import Slider from 'react-slick';

const ActDetail = ({ data, profileId }) => {
  //const s = useTranslations("actPreview");
  const t = useTranslations("act");
  const p = useTranslations("actReview.actReviewLocation");
  // const { profiles: dashBoardProfiles } = useSelector(
  //     (state) => state.act
  // );
  const lang = useLocale();
  // const [selectedOption, setSelectedOption] = useState(dropdownOptions[0]);
  const [showAllReviews, setShowAllReviews] = useState(false);
  const [showAllBio, setShowAllBio] = useState(false);
  const [feedbackData, setFeedbackData] = useState([]);
  const { previewData } = useSelector((state) => state.act);
  // const handleSelectedOption = (option) => {
  //   setSelectedOption(option);
  // };
  const reviewsToShow = showAllReviews
    ? feedbackData?.receivedFeedbacks
    : feedbackData?.receivedFeedbacks?.slice(0, 6);
  // eslint-disable-next-line
  const handleShowMore = () => {
    setShowAllReviews(!showAllReviews);
  };
  // eslint-disable-next-line
  const handleShowBio = () => {
    setShowAllBio(!showAllBio);
  };
  const dispatch = useDispatch();
  const page = 0;
  const size = 100;

  const [recentlyVisitedData, setRecentlyVisitedData] = useState([]);
  //const [mayLikeData, setMayLikeData] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch Recently Visited Profiles
        const recentlyVisitedResponse = await dispatch(getRecentlyVisited()).unwrap();
        if (recentlyVisitedResponse?.data?.data?.length > 0) {
          setRecentlyVisitedData(recentlyVisitedResponse?.data?.data);
        }

        // Fetch May Like Profiles (only if profileId is available)
        if (profileId) {
          // const mayLikeResponse = await dispatch(getMayLiked(profileId)).unwrap();
          // if (mayLikeResponse?.data?.data?.length > 0) {
          //   setMayLikeData(mayLikeResponse?.data?.data);
          // }
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error("Error while fetching recently visited and may like profiles", error);
      }
    };

    fetchData();
  }, [dispatch, profileId]);

  useEffect(() => {
    const params = stringifyParams({
      page,
      size,
      profileType: "",
      searchString: "",
    });
    dispatch(getProfiles(params));
    dispatch(listFeedback(profileId))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setFeedbackData(response.data.data);
        }
      });
  }, []);

  //const displayedReviews = showAllReviews ? reviewData : reviewData.slice(0, 4);
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const [isBlocked, setIsBlocked] = useState(false);
  const router = useRouter();
  const handleBlockUser = async (userId) => {
    try {
      if (isBlocked) {
        // Call API to unblock user
        await dispatch(unblockUser(userId));
        setIsBlocked(false);
      } else {
        // Call API to block user
        await dispatch(blockUser(userId));
        setIsBlocked(true);
      }
      // Redirect to block list page
      router.push(`/${lang}/user/block-list`);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error("Error while blocking/unblocking user:", error);
    }
  };

  // const settings = {
  //     dots: true,
  //     infinite: false,
  //     speed: 1000,
  //     slidesToShow: 4,
  //     slidesToScroll: 2,
  //     autoplay: false,
  //     prevArrow: <KeyboardArrowLeft className='text-xl text-[--text-color]' />,
  //     nextArrow: <KeyboardArrowRight className='text-xl text-[--text-color]' />,
  //     initialSlide: 0,
  //     responsive: [
  //         {
  //             breakpoint: 1224,
  //             settings: {
  //                 slidesToShow: 3,
  //                 slidesToScroll: 2,
  //             },
  //         },
  //         {
  //             breakpoint: 800,
  //             settings: {
  //                 slidesToShow: 2,
  //                 slidesToScroll: 2,
  //                 initialSlide: 2,
  //             },
  //         },
  //         {
  //             breakpoint: 520,
  //             settings: {
  //                 slidesToShow: 1,
  //                 slidesToScroll: 1,
  //             },
  //         },
  //     ],
  // };

  const rateData = [
    {
      id: 0,
      text: t("entertainment"),
      rating: data?.profileRatingDto?.entertainmentValueRating,
    },
    {
      id: 1,
      text: t("professionalism"),
      rating: data?.profileRatingDto?.professionalismRating,
    },
    {
      id: 2,
      text: t("draw"),
      rating: data?.profileRatingDto?.drawRating,
    },
  ];

  return (
    <>
      <Box className="pr-5">
        <Box className="lg:flex gap-8 pb-5">
          <Box className="pt-6 lg:w-[71%] w-full">
            <Box className="lg:flex lg:justify-between pb-8">
              <Typography className="text-[--text-color] lg:text-4xl text-2xl CraftworkGroteskHeavy">
                {data.profileName}
              </Typography>
              <Box className="lg:hidden block">
                <Box className="!flex !flex-wrap !gap-3 !items-center !my-2">
                  <Rating value={parseFloat(feedbackData?.actRating?.overallRating)} readOnly />
                  <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                    {feedbackData?.actRating?.overallRating}
                  </Typography>
                  <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                    ({feedbackData?.actRating?.numberOfRatings} {p("reviews")})
                  </Typography>
                </Box>
                <Box className="!flex !items-center !gap-3 pb-2">
                  <LocationSvg className="!w-6 !h-6" />
                  <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                    {generateLocationString(data?.locationDto).length > 30
                      ? generateLocationString(data?.locationDto).substring(0, 40) + "..."
                      : generateLocationString(data?.locationDto)}
                  </Typography>
                </Box>
                {/* <Box className="!flex !items-center !gap-3">
                  <Globe className="!w-6 !h-6 !stroke-white" />
                  <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                    {p("playInternational")}:
                  </Typography>
                </Box>
                <Box className="!flex !items-center !mt-1 ml-7 !gap-3">
                  <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
                    {p("usa")} <span className="!underline">and 5 more</span>
                  </Typography>
                </Box> */}
              </Box>
              {/* <Box className="lg:flex hidden gap-2 items-center">
                <LikeIcon className="!text-xl" />
                <ShareIcon className="text-2xl" />
                <Button
                  sx={{
                    border: 0,
                    padding: 0,
                    "&.MuiButtonBase-root": {
                      color: "transparent !important",
                    },
                  }}
                  className="flex gap-1 !normal-case py-0 "
                >
                  <Typography className="text-sm text-[--text-color] underline CraftworkGroteskHeavy">
                    {t("follow")}
                  </Typography>
                  <AddCalender className="text-xl" />
                </Button>
              </Box> */}

              <Box className="flex gap-2 items-center">
                <Button
                  sx={{
                    border: 0,
                    padding: 0,
                    "&.MuiButtonBase-root": {
                      color: "transparent !important",
                    },
                  }}
                  className="flex gap-1 !normal-case py-0"
                  onClick={() => handleBlockUser(profileId)}
                >
                  <Typography className="text-sm text-[--text-color] underline CraftworkGroteskHeavy">
                    {isBlocked ? "" : "Block"}
                  </Typography>
                  {/* {isBlocked ? (
                      ""
                    ) : (
                      <Block className="!text-xl !text-[--text-color]" />
                    )} */}
                </Button>
              </Box>
            </Box>
            <Typography className="!text-[--text-color] !text-2xl CraftworkGroteskMedium !leading-[28.8px]">
              {data?.profileType === "VENUE_PROFILE"
                ? "About the Venue"
                : data?.profileType === "ACT_PROFILE"
                  ? "About the Act"
                  : data?.profileType === "VIRTUAL_ACT_PROFILE"
                    ? "About the Virtual Act"
                    : data?.profileType === "VIRTUAL_VENUE_PROFILE"
                      ? "About the Virtual Venue"
                      : ""}
            </Typography>
            <Box className="flex flex-wrap gap-4 py-2">
              {data?.skillsDto?.musicGenreList?.map((data) => (
                <Typography
                  key={data}
                  className="!text-[--text-color] !text-sm bg-[--divider-color] CraftworkGroteskRegular px-2 py-1 rounded-[4px]"
                >
                  {data?.name}
                </Typography>
              ))}
            </Box>
            {isSmallScreen ? (
              <Box className="flex gap-2">
                <Typography className="!text-sm text-[--text-color] break-words CraftworkGroteskRegular">
                  {showAllBio && data?.infoDto?.bio
                    ? data?.infoDto?.bio
                    : data?.infoDto?.bio.slice(0, 200)}
                </Typography>
              </Box>
            ) : (
              <Typography className="!text-sm text-[--text-color] break-words CraftworkGroteskRegular">
                {data?.infoDto?.bio}
              </Typography>
            )}
          </Box>
          {isSmallScreen ? (
            <ActReviewLocation data={data} type="actLocationSmallScreen" />
          ) : (
            <ActReviewLocation data={data} type="actLocation" />
          )}
        </Box>
        <ViewCalender
          initialView="dayGridMonth"
          height={300}
          seleselectable={false}
          profileId={profileId}
          editable={false}
        />

        {previewData?.profile?.option === t("Act") ? (
          <></>
        ) : (
          <Box className="pt-12">
            <Typography className="text-2xl text-[--text-color] CraftworkGroteskMedium">
              Where to find us
            </Typography>
            <Box className="flex gap-2 items-center pt-2">
              <LocationSvg className="text-2xl" />
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                {generateLocationString(data?.locationDto)}
              </Typography>
            </Box>
            {/* <Box className="flex gap-2 items-center pt-2">
              <WorldSvg className="text-2xl" />
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                danceagain.ca
              </Typography>
            </Box>
            <Box className="flex gap-2 items-center py-2">
              <PhoneSvg className="text-2xl" />
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                **************
              </Typography>
            </Box> */}
            {<MapSearch profiles={{ content: [data] }} />}
          </Box>
        )}

        <Box className="lg:flex md:flex justify-between items-end py-4">
          <Box className="lg:flex md:flex hidden  gap-x-1 items-end">
            <Typography className="!text-[--text-color] !text-2xl CraftworkGroteskMedium !leading-[28.8px] !pt-8">
              {t("reviews")}
            </Typography>
            {/* <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular !pt-2">
              {data?.profileRatingDto?.overallRating}
            </Typography> */}
            <Rating value={parseFloat(data?.profileRatingDto?.overallRating)} readOnly />
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular !pt-2">
              ({data?.profileRatingDto?.numberOfRatings} {p("reviews")})
            </Typography>
          </Box>
          <Box className="flex md:hidden lg:hidden justify-between gap-x-1 items-end">
            <Typography className="!text-[--text-color] !text-2xl CraftworkGroteskMedium !leading-[28.8px] !pt-8">
              {t("reviews")}
            </Typography>
            <Box className="flex gap-1 items-end">
              {/* <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular !pt-2">
              {data?.profileRatingDto?.overallRating}
              </Typography> */}
              <FilledStarSvg className="text-lg" />
              <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular !pt-2">
                ({data?.profileRatingDto?.numberOfRatings} {p("reviews")})
              </Typography>
            </Box>
          </Box>
          {/* <Dropdown
            options={dropdownOptions}
            selectedValue={selectedOption}
            onSelect={handleSelectedOption}
            title={t("sortBy")}
            className="text-[--text-color]"
            sx={{
              "& .MuiButton-root": {
                padding: 0,
              },
            }}
          /> */}
        </Box>
        <Box className="lg:flex gap-x-5">
          <EditActFeedbackRate rateData={rateData} />
        </Box>
        <Box className="grid lg:grid-cols-2 md:grid-cols-2 grid-cols-1 gap-4 py-4">
          {reviewsToShow?.map((feedback, index) => (
            <Box
              className="!border !border-[--divider-color] !bg-[--footer-bg] !rounded-[4px] !w-full !p-4"
              key={index}
            >
              <Box className="!flex !justify-between !items-center pb-2">
                <Box className="!flex !gap-x-2">
                  <CommonImage
                    src={feedback?.providerImageUrls?.[0] ?? Avatar}
                    alt="image"
                    width={50}
                    height={50}
                    className={"!rounded-[50%]"}
                  />
                  <Box>
                    <Typography className="!text-sm !text-[--text-color] CraftworkGroteskregular">
                      {feedback?.providerName}
                    </Typography>
                    {/* <Typography className="!text-sm !text-[--hide-color] CraftworkGroteskregular">
                  {t("booking")}
                </Typography> */}
                  </Box>
                </Box>
                <Box className="!flex !gap-2">
                  {/* <Typography className="!text-[--text-color] !text-sm CraftworkGroteskSemiBold !bg-[--inprogress-color] !text-center !py-1 !px-[6px] !rounded-[2px]">
                {t("new")}
              </Typography> */}
                  <Link href={`/${lang}/booking-details?contract-id=${feedback?.contractId}`}>
                    <Button
                      className=" flex gap-x-2"
                      sx={{
                        border: 0,
                        "&.MuiButtonBase-root": {
                          color: "white !important",
                        },
                      }}
                    >
                      <Typography className="!normal-case CraftworkGroteskHeavy !underline !text-sm !leading-[15.4px] !text-[--text-color]">
                        View contract
                      </Typography>
                      <SouthEast className="!text-xl" />
                    </Button>
                  </Link>
                </Box>
              </Box>
              <EditActFeedbackRate
                rateData={[
                  {
                    id: 0,
                    text: t("entertainment"),
                    rating: feedback?.entertainmentValue,
                  },
                  {
                    id: 1,
                    text: t("professionalism"),
                    rating: feedback?.professionalismValue,
                  },
                  {
                    id: 2,
                    text: t("draw"),
                    rating: feedback?.drawAsExpectedValue,
                  },
                ]}
              />
              <Box className="border border-[--divider-color] rounded-[4px] w-full p-4 mt-2">
                <Box className="flex gap-2 items-center">
                  <PublicSvg />
                  <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                    Public
                  </Typography>
                </Box>
                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular py-2">
                  {feedback?.publicMessage}
                </Typography>
                {/* <Box className="flex items-center !mt-2">
              <CheckBox className="!max-w-[24px]" sx={{ color: "#EFEFEF", marginRight: "5px" }} />
              <label className="cursor-pointer flex gap-x-2 items-center">
                <Typography className="text-[--text-color] pl-2 text-sm CraftworkGroteskRegular">
                  Add to my public account
                </Typography>
              </label>
            </Box> */}
              </Box>
            </Box>
          ))}
        </Box>
        {reviewsToShow && reviewsToShow.length > 6 && (
          <Box className="flex justify-center">
            <Button
              className="flex gap-1 items-center !normal-case"
              sx={{
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
              onClick={handleShowMore}
            >
              {showAllReviews ? (
                <>
                  <North className="text-xl text-[--text-color]" />
                  <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy underline">
                    {t("showLess")}
                  </Typography>
                  <North className="text-xl text-[--text-color]" />
                </>
              ) : (
                <>
                  <South className="text-xl text-[--text-color]" />
                  <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy underline">
                    {t("showMore")}
                  </Typography>
                  <South className="text-xl text-[--text-color]" />
                </>
              )}
            </Button>
          </Box>
        )}
        {/* <Typography className="text-[--text-color] text-2xl CraftworkGroteskMedium">
          {t("likeThis")}
        </Typography>
        <Box className="py-80 relative">
          <ActDetailSlider />
        </Box> */}
        {/* {mayLikeData?.length > 0 && (
          <>
            <Typography className="text-[--text-color] mt-12 text-2xl CraftworkGroteskMedium">
              {t("mayLike")}
            </Typography>
            <Box className="py-80 relative">
              <ActDetailSlider datas={mayLikeData} />
            </Box>
          </>
        )} */}

        {/* Show Recently Visited section only if there is data */}
        {recentlyVisitedData?.length > 0 && (
          <>
            <Typography className="text-[--text-color] mt-12 text-2xl CraftworkGroteskMedium">
              {t("recentlyVisited")} <span>({recentlyVisitedData.length})</span>
            </Typography>
            <Box className="py-80 relative">
              <ActDetailSlider datas={recentlyVisitedData} />
            </Box>
          </>
        )}
      </Box>
      <Box className="flex justify-between lg:hidden fixed z-20 bottom-0 left-0 right-0 border-t border-t-[--divider-color] bg-[--bg-color] px-5 py-3">
        <Box>
          <Typography className="!text-sm CraftworkGroteskRegular !text-[--hide-color]">
            {p("startedAt")}
          </Typography>
          <Typography className="!text-sm CraftworkGroteskHeavy !text-[--text-color]">
            {`$ ${data?.paymentsDto?.minimumPrice} / per ${data?.paymentsDto?.minPriceChargingType}`}
          </Typography>
        </Box>
        {!data?.ownProfile && (
          <BookingButtonComponent
            profileId={profileId}
            profileType={data?.profileDto?.profileType}
          />
        )}
      </Box>
    </>
  );
};

export default ActDetail;
