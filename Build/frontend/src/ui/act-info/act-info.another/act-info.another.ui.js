"use client";
import CountryCodeDropdown from "@/common/countrycode-dropdown/countrycode-dropdown.common";
import { data } from "@/common/countrycode-dropdown/countrycode-dropdown.data.common";
import { Box, InputLabel, TextField, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";

const ActInfoAnother = ({ register, errors, setValue }) => {
  const [selectedCountryCode, setSelectedCountryCode] = useState(data[0].code);
  useEffect(() => {
    setValue("authorizedRepresenterPhoneNumberCountryCode", selectedCountryCode);
  }, [selectedCountryCode]);

  const handleSelectCountryCode = (value) => {
    setSelectedCountryCode(value);
  };

  return (
    <>
      <Box className="!flex lg:!flex-row md:!flex-row !flex-col !gap-x-5 !mt-2">
        <Box className="!w-full">
          <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
            Name of the authorized representative
          </InputLabel>
          <TextField
            type="text"
            size="small"
            placeholder="Contact name"
            name="authorizedRepresenter"
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& input": {
                color: "#EFEFEF",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !mb-5"
            {...register("authorizedRepresenter")}
          />
          {errors && errors.authorizedRepresenter && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.authorizedRepresenter.message}
            </Typography>
          )}
        </Box>
      </Box>
      <Box className="!flex lg:!flex-row md:!flex-row !flex-col !gap-x-5">
        <Box className="!w-full">
          <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
            Authorized representer’s email
          </InputLabel>
          <TextField
            type="text"
            size="small"
            placeholder="<EMAIL>"
            name="authorizedRepresenterEmail"
            InputLabelProps={{ style: { color: "#EFEFEF" } }}
            sx={{
              "& input::placeholder": {
                color: "#EFEFEF",
                border: 0,
              },
              "& input": {
                color: "#EFEFEF",
                fontFamily: "var(--craftWorkRegular)",
              },
              "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderWidth: 0,
              },
              border: 0,
            }}
            className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !mb-5"
            {...register("authorizedRepresenterEmail")}
          />
          {errors && errors.authorizedRepresenterEmail && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.authorizedRepresenterEmail.message}
            </Typography>
          )}
        </Box>
        <Box className="!w-full">
          <InputLabel className="!text-[--text-color] !text-sm CraftworkGroteskGX !my-3">
            Alternative phone number
          </InputLabel>
          <Box className="flex !gap-4">
            <CountryCodeDropdown
              selectedValue={selectedCountryCode}
              onSelect={handleSelectCountryCode}
            />
            <TextField
              size="small"
              placeholder="0000000000"
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              autoComplete="off"
              type="tel"
              inputProps={{
                inputMode: "numeric",
                pattern: "[0-9]*",
                maxLength: 10,
              }}
              name="authorizedRepresenterPhoneNumber"
              className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] !mb-5"
              {...register("authorizedRepresenterPhoneNumber")}
            />
          </Box>
          {errors && errors.authorizedRepresenterPhoneNumber && (
            <Typography as="span" className="text-sm !text-red-600">
              {errors.authorizedRepresenterPhoneNumber.message}
            </Typography>
          )}
        </Box>
      </Box>
    </>
  );
};

export default ActInfoAnother;
