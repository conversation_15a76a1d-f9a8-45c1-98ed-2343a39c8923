import { CheckBox } from "@/component";
import { Box } from "@mui/material";
import { useTranslations } from "next-intl";
import React from "react";
import { Controller } from "react-hook-form";

const ActInfoIAm = ({ control, currentUserEmail }) => {
  const t = useTranslations("actInformation.ActInfoForm");
  return (
    <Box className="flex items-center !mt-2">
      <Controller
        name="useMyEmail"
        control={control}
        render={({ field }) => (
          <>
            <CheckBox
              className="!max-w-[24px]"
              checked={field.value}
              sx={{ color: "#EFEFEF", marginRight: "5px" }}
              onChange={(e) => {
                field.onChange(e.target.checked);
              }}
            />
            <label
              htmlFor=""
              className="!text-[--text-color] !text-sm CraftworkGroteskRegular cursor-pointer"
            >
              {t("ActInfoIAm.UseEmailAddress")}
              <span className="!text-sm !text-[--hide-color] CraftworkGroteskRegular !pl-3">
                {currentUserEmail}
              </span>
            </label>
          </>
        )}
      />
    </Box>
  );
};

export default ActInfoIAm;
