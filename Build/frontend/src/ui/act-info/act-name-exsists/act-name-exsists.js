import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Drawer,
  TextField,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import React from "react";
import { SouthEast } from "@mui/icons-material";
import { Button } from "@/component";
import { actValidName, setPreviewData, updateProfile } from "@/store/slice/act/act.slice";
import { useDispatch, useSelector } from "react-redux";

const ActNameExists = ({
  open,
  handleClose,
  profileId,
  profileData,
  redirectLocationButtonRoute,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const [nameError, setNameError] = React.useState(false);
  const [updatedActName, setUpdatedActName] = React.useState(profileData); // State to hold the entered value
  const { previewData } = useSelector((state) => state.act);
  const dispatch = useDispatch();

  const updateNameHandler = () => {
    dispatch(actValidName({ name: updatedActName?.profileName, profileId }))
      .unwrap()
      .then((response) => {
        if (response.data.data) {
          setNameError(false);
          dispatch(
            updateProfile({
              data: updatedActName,
              profileId,
            }),
          )
            .unwrap()
            .then(() => {
              // Create a new object with the updated profileName

              const updatedProfileData = {
                ...previewData,
                actInfo: {
                  ...previewData.actInfo, // Keep other fields in actInfo intact
                  profileName: updatedActName?.profileName, // Update the profileName field
                },
              };

              // Dispatch the updated data
              dispatch(setPreviewData(updatedProfileData));
              redirectLocationButtonRoute();
            })
            .catch(() => {
              setNameError(true);
            });
        } else {
          setNameError(true);
        }
      })
      .catch(() => {
        setNameError(true);
      });
  };

  const content = (
    <DialogContent className="!max-w-xl !bg-[--footer-bg] lg:border border-[--text-color] p-6">
      <Typography className="text-xl CraftworkGroteskHeavy text-[--text-color] pb-5">
        The name already exists in the same geographical area
      </Typography>
      <TextField
        type="text"
        size="small"
        placeholder="Enter name"
        value={updatedActName?.profileName} // Bind the input value to the state
        onChange={(e) =>
          setUpdatedActName({
            ...updatedActName,
            profileName: e.target.value,
          })
        } // Update the state when the input changes
        InputLabelProps={{ style: { color: "#EFEFEF" } }}
        sx={{
          "& input::placeholder": {
            color: "#EFEFEF",
            border: 0,
          },
          "& .MuiOutlinedInput-root": {
            color: "var(--text-color)",
            fontFamily: "var(--craftWorkRegular)",
          },
          "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          border: 0,
        }}
        className="!border !w-full !h-[60px] lg:mb-0 mb-[80px] !py-1 CraftworkGroteskRegular !border-[--text-color] rounded-[2px]"
      />

      {nameError && (
        <Typography as="span" className="text-sm !text-red-600">
          This name is already in use , please choose a different name for your profile.
        </Typography>
      )}

      <Box
        className={` ${
          isMobile
            ? "fixed flex justify-between bg-[--bg-color] items-center px-4 py-3 left-0 right-0 bottom-0 border-[1px] border-[--divider-color]"
            : "flex justify-end gap-[24px]"
        }`}
      >
        <Button
          className="!bg-[--text-color] !flex !gap-x-1 !px-4 !py-2 items-center !mt-5"
          sx={{
            border: 0,
            "&.MuiButtonBase-root": {
              color: "white !important",
            },
          }}
          onClick={updateNameHandler}
        >
          <Typography className="!normal-case CraftworkGroteskHeavy text-center !text-sm !leading-[15.4px] !text-[--bg-color]">
            Change Name
          </Typography>
          <SouthEast className="text-[--bg-color] text-xl" />
        </Button>
      </Box>
    </DialogContent>
  );

  return (
    <>
      {isMobile ? (
        <Drawer
          anchor="bottom"
          open={open}
          onClose={handleClose}
          sx={{
            "& .MuiPaper-root": {
              height: "98%",
              backgroundColor: "var(--bg-color)",
            },
          }}
        >
          {content}
        </Drawer>
      ) : (
        <Dialog
          open={open}
          maxWidth={false}
          sx={{ "& .MuiBackdrop-root": { backgroundColor: "#181B1BE5" } }}
        >
          {content}
        </Dialog>
      )}
    </>
  );
};

export default ActNameExists;
