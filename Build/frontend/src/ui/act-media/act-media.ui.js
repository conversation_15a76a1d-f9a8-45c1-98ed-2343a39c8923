"use client";
import { Box, InputAdornment, Input<PERSON><PERSON><PERSON>, TextField, Typography, IconButton } from "@mui/material";
import React, { useState, useEffect } from "react";
import PhotoIcon from "@/assets/svg/act-type.svg/PhotoIcon.svg";
import DragAndDropIcon from "@/assets/svg/act-type.svg/DragAndDrop.svg";
import VideoSvg from "@/assets/svg/act-type.svg/VideoSvg.svg";
import LinkSvg from "@/assets/svg/act-type.svg/LinkIcon.svg";
import { Button } from "@/component";
import { Add } from "@mui/icons-material";
import RemoveIcon from "@mui/icons-material/Remove";
import AudioSvg from "@/assets/svg/act-type.svg/AudioSvg.svg";
import DeleteSvg from "@/assets/svg/act-type.svg/DeleteSvg.svg";
import { useForm, Controller, useWatch, useFieldArray } from "react-hook-form";
import { ProfileFooter } from "@/common/profile";
import { useDropzone } from "react-dropzone";
import classNames from "classnames";
import { CommonImage } from "@/component";
import DeleteIcon from "@/assets/svg/DeleteIcon.svg";
import { useDispatch, useSelector } from "react-redux";
import {
  setPreviewData,
  createActMedia,
  getActMedia,
  deleteActMedia,
} from "@/store/slice/act/act.slice";
import { useSnackbar } from "@/context/snackbar/snackbar.provider";
import { useRouter } from "next/navigation";
import { actMediaValidation } from "@/validation/act/act.validation";
import { yupResolver } from "@hookform/resolvers/yup";
import NoteList from "@/ui/note-list/note-list.ui";
import { useLocale, useTranslations } from "next-intl";
import { ACT_CONSTANTS } from "@/validation/auth/constants";

const ActMediaForm = () => {
  const t = useTranslations("actMedia.actMediaForm");
  const p = useTranslations("profileFooter");
  const lang = useLocale();

  const imageNotes = [t("photoSize"), t("uploadingPhotos"), t("perfectSize")];

  const videoNotes = [t("quick"), t("overviewBand")];
  const resolver = yupResolver(actMediaValidation);
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const { previewData } = useSelector((state) => state.act);
  const profileId = previewData?.actInfo?.profileId;
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);
  const [fetch, setFetch] = useState(0);
  const dispatch = useDispatch();
  const {
    handleSubmit,
    register,
    setValue,
    control,
    watch,
    formState: { errors, defaultValues },
  } = useForm({
    resolver,
    defaultValues: {
      actPhotos: previewData?.actMedia?.actPhotos ?? [],
      videoLink: previewData?.actMedia?.videoLink ?? [],
      audioLink: previewData?.actMedia?.audioLink ?? [],
    },
    mode: "onChange",
  });

  {
    /** video links */
  }
  const {
    fields: videoFields,
    append: appendVideo,
    remove: removeVideo,
  } = useFieldArray({
    control,
    name: "videoLink",
  });

  {
    /** audio links */
  }
  const {
    fields: audioFields,
    append: appendAudio,
    remove: removeAudio,
  } = useFieldArray({
    control,
    name: "audioLink",
  });

  {
    /** photos upload */
  }
  const { getRootProps, getInputProps } = useDropzone({
    accept: { "image/*": [".jpeg", ".png", ".jpg"] },
    onDrop: (acceptedFiles) => {
      for (let i = 0; i < acceptedFiles.length; i++) {
        const formData = new FormData();
        const file = acceptedFiles[i];
        formData.append("file", file);
        handleUploadMedia(formData, profileId);
      }
    },
  });

  {
    /** delete image */
  }

  const isPreviewDataEmpty = Object.values(defaultValues).every((value) => {
    if (Array.isArray(value)) {
      return value.length === 0;
    } else if (typeof value === "object" && value !== null) {
      return Object.keys(value).length === 0;
    } else {
      return !value;
    }
  });

  const handleDeleteImage = (event, fileUrl) => {
    const fileName = fileUrl.split("/").pop();
    event.stopPropagation();
    event.preventDefault();
    dispatch(deleteActMedia({ fileName, profileId }))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setFetch((fetch) => fetch + 1);
          showSnackbar(response.data.message, "success");
        }
      })
      .catch((error) => {
        showSnackbar(error, "error");
      });
  };

  const handleUploadMedia = (formData, profileId) => {
    dispatch(createActMedia({ file: formData, profileId }))
      .unwrap()
      .then((response) => {
        if (response.status === 200) {
          setLoading(false);
          setFetch((fetch) => fetch + 1);
          showSnackbar(response.data.message, "success");
        }
      })
      .catch((error) => {
        setLoading(false);
        showSnackbar(error, "error");
      });
  };

  const habndleActMedia = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      previewData?.actInfo?.profileType === "ACT_PROFILE" && router.push(`/${lang}/payment`);
      previewData?.actInfo?.profileType === "VENUE_PROFILE" && router.push(`/${lang}/payment`);
      previewData?.actInfo?.profileType === "VIRTUAL_ACT_PROFILE" &&
        router.push(`/${lang}/${profileId}/virtual-profile-success`);
      previewData?.actInfo?.profileType === "VIRTUAL_VENUE_PROFILE" &&
        router.push(`/${lang}/${profileId}/virtual-profile-success`);
    }, 100);
  };

  const actData = useWatch({ control });
  useEffect(() => {
    if (actData.actPhotos.length > 0) {
      dispatch(setPreviewData({ actMedia: actData }));
    }
  }, [actData]);

  {
    /** get Act Media */
  }
  useEffect(() => {
    if (!isPreviewDataEmpty || fetch)
      dispatch(getActMedia({ profileId }))
        .unwrap()
        .then((response) => {
          if (response.status === 200) {
            const imageUrl = response.data.data.imageUrls;
            setValue("actPhotos", imageUrl);
          }
        })
        .catch((error) => {
          showSnackbar(error, "error");
        });
  }, [isPreviewDataEmpty, fetch]);

  return (
    <form onSubmit={handleSubmit(habndleActMedia)}>
      <Box className="!mt-12">
        <Box className="!flex !flex-col !gap-x-2">
          <Box className="flex gap-2">
            <PhotoIcon className="!text-2xl" />
            <Typography className="!text-[--text-color] !text-lg CraftworkGroteskMedium">
              {t("photos")}
            </Typography>
          </Box>

          {/** act Photos */}
          <Controller
            name="actPhotos"
            control={control}
            defaultValue={watch("actPhotos")}
            render={({}) => {
              const photos = watch("actPhotos");
              const isEmpty = photos.length === 0;
              return (
                <Box
                  {...getRootProps({
                    className: "cursor-pointer",
                  })}
                  className={classNames("!mt-3  !w-full  !rounded-[2px]", {
                    "!border-dashed lg:!w-[85%] !h-[220px] !flex !justify-center !items-center !border-[2px]":
                      isEmpty,
                  })}
                >
                  <Box>
                    <input {...getInputProps({})} />
                    {isEmpty ? (
                      <Box className="!h-full">
                        <Box className="!flex !gap-x-4">
                          <DragAndDropIcon className="!text-base" />
                          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy">
                            {t("drag&Drop")}
                          </Typography>
                        </Box>
                        <Typography className="!text-center !text-[--text-color] !text-sm !CraftworkGroteskRegular !py-3">
                          {t("duploaia")}
                        </Typography>
                      </Box>
                    ) : (
                      <Box className="!flex !flex-wrap !gap-3 relative">
                        {photos.map((file, index) => (
                          <Box key={index} className="relative">
                            <CommonImage
                              src={file}
                              alt={`Preview ${file.name || "media"}`}
                              className="!border !relative !w-[131px] !h-[131px] !rounded-[4px] !border-[--divider-color]"
                              onLoad={() => URL.revokeObjectURL(file)}
                              fill
                            />
                            {index === 0 && (
                              <Typography className="!text-[--text-color] !text-xs Poppins700 !absolute !left-0 !px-1 !py-2 !top-2 !bg-[--inprogress-color] !rounded-e-[8px]">
                                {t("cover")}
                              </Typography>
                            )}
                            <IconButton
                              className="!p-1 !rounded-full !bg-[--text-color] !absolute !right-2 !top-2"
                              onClick={(event) => {
                                handleDeleteImage(event, file);
                                event.stopPropagation();
                              }}
                            >
                              <DeleteIcon className="!text-[21px] " />
                            </IconButton>
                          </Box>
                        ))}
                        <Box className="!border-dashed !border-[2px] !rounded-[2px] !border-[--text-color] !w-[131px] !h-[131px]">
                          <Box className="!h-full !flex !p-2 !flex-col !items-center !justify-center">
                            <DragAndDropIcon className="!text-base" />
                            <Typography className="!text-[--text-color] !text-center !leading-[22.4px] !text-sm CraftworkGroteskHeavy">
                              {t("drag&Drop")}
                            </Typography>
                            <Typography className="!text-center !text-[--text-color] !text-sm !CraftworkGroteskRegular !pt-4">
                              {t("toUpload")}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                    )}
                  </Box>
                </Box>
              );
            }}
          />

          <NoteList notes={imageNotes} />
          {errors && errors.actPhotos && (
            <span className="!mt-1 text-sm !text-red-600">{errors.actPhotos.message}</span>
          )}
        </Box>

        {/** video links */}
        <Box className="!w-full">
          <InputLabel className="!flex !gap-2 !py-2">
            <VideoSvg className="!text-2xl" />
            <Typography className="!text-lg !text-[--text-color] CraftworkGroteskMedium">
              {t("videoLink")}
            </Typography>
          </InputLabel>
          <Box className="!flex">
            <TextField
              type="text"
              size="small"
              placeholder={t("vimeo")}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              InputProps={{
                startAdornment: (
                  <InputAdornment
                    position="start"
                    style={{
                      cursor: "pointer",
                    }}
                  >
                    <LinkSvg className="!text-2xl" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end" style={{ cursor: "pointer" }}>
                    {search && (
                      <DeleteSvg
                        className="!text-[--text-color] !text-lg"
                        onClick={() => setSearch("")}
                      />
                    )}
                  </InputAdornment>
                ),
              }}
              inputProps={{
                maxLength: ACT_CONSTANTS.ACT_MEDIA.VIDEO_LINK_MAX_LENGTH,
              }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px]"
              {...register(`videoLink.${0}.videoLink`)}
            />
            {errors?.videoLink?.[0]?.videoLink && (
              <Typography className="!text-red-600 !text-sm">
                {errors.videoLink[0].videoLink.message}
              </Typography>
            )}
            {videoFields.length > 1 && (
              <IconButton onClick={() => removeVideo(videoFields[0])}>
                <RemoveIcon className="!text-xl !text-[--text-color] mt-2" />
              </IconButton>
            )}
          </Box>

          {videoFields.slice(1).map((fields, index) => (
            <Box className="!flex" key={index}>
              <TextField
                key={fields.id}
                type="text"
                size="small"
                placeholder={t("vimeo")}
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment
                      position="start"
                      style={{
                        cursor: "pointer",
                      }}
                    >
                      <LinkSvg className="!text-2xl" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end" style={{ cursor: "pointer" }}>
                      {search && (
                        <DeleteSvg
                          className="!text-[--text-color] !text-lg"
                          onClick={() => setSearch("")}
                        />
                      )}
                    </InputAdornment>
                  ),
                }}
                inputProps={{
                  maxLength: ACT_CONSTANTS.ACT_MEDIA.VIDEO_LINK_MAX_LENGTH,
                }}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                    fontFamily: "var(--craftWorkRegular)",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  border: 0,
                }}
                className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] mt-3"
                {...register(`videoLink.${index + 1}.videoLink`)}
              />
              {errors?.videoLink?.[index + 1]?.videoLink && (
                <Typography className="!text-red-600 !text-sm">
                  {errors.videoLink[index + 1].videoLink.message}
                </Typography>
              )}
              <IconButton onClick={() => removeVideo(index + 1)}>
                <RemoveIcon className="!text-xl !text-[--text-color] mt-2" />
              </IconButton>
            </Box>
          ))}

          <Button
            className="!flex !gap-x-1 !normal-case"
            sx={{
              border: 0,
              "&.MuiButtonBase-root": {
                color: "transparent !important",
              },
            }}
            onClick={() => appendVideo({ videoLink: "" })}
          >
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy">
              {t("addLink")}
            </Typography>
            <Add className="!text-xl !text-[--text-color]" />
          </Button>
          <NoteList notes={videoNotes} />
        </Box>

        {/** audio Link */}

        <Box className="!w-full">
          <InputLabel className="!flex !gap-2 !py-2">
            <AudioSvg className="!text-2xl" />
            <Typography className="!text-lg !text-[--text-color] CraftworkGroteskMedium">
              {t("audioLink")}
            </Typography>
          </InputLabel>
          <Box className="!flex">
            <TextField
              type="text"
              size="small"
              placeholder={t("soundCloud")}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              InputProps={{
                startAdornment: (
                  <InputAdornment
                    position="start"
                    style={{
                      cursor: "pointer",
                    }}
                  >
                    <LinkSvg className="!text-2xl" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end" style={{ cursor: "pointer" }}>
                    {search && (
                      <DeleteSvg
                        className="!text-[--text-color] !text-base"
                        onClick={() => setSearch("")}
                      />
                    )}
                  </InputAdornment>
                ),
              }}
              inputProps={{
                maxLength: ACT_CONSTANTS.ACT_MEDIA.AUDIO_LINK_MAX_LENGTH,
              }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: 0,
              }}
              className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px]"
              {...register(`audioLink.${0}.audioLink`)}
            />
            {errors?.audioLink?.[0]?.audioLink && (
              <Typography className="!text-red-600 !text-sm">
                {errors.audioLink[0].audioLink.message}
              </Typography>
            )}
            {audioFields.length > 1 && (
              <IconButton onClick={() => removeAudio(audioFields[0])}>
                <RemoveIcon className="!text-xl !text-[--text-color] mt-2" />
              </IconButton>
            )}
          </Box>
          {audioFields.slice(1).map((fields, index) => (
            <Box className="!flex" key={index}>
              <TextField
                key={fields.id}
                type="text"
                size="small"
                placeholder={t("soundCloud")}
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment
                      position="start"
                      style={{
                        cursor: "pointer",
                      }}
                    >
                      <LinkSvg className="!text-2xl" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end" style={{ cursor: "pointer" }}>
                      {search && (
                        <DeleteSvg
                          className="!text-[--text-color] !text-base"
                          onClick={() => setSearch("")}
                        />
                      )}
                    </InputAdornment>
                  ),
                }}
                inputProps={{
                  maxLength: ACT_CONSTANTS.ACT_MEDIA.AUDIO_LINK_MAX_LENGTH,
                }}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                    fontFamily: "var(--craftWorkRegular)",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  border: 0,
                }}
                className="!border !w-full !py-1 CraftworkGroteskRegular !border-white rounded-[2px] mt-2"
                {...register(`audioLink.${index + 1}.audioLink`)}
              />
              {errors?.audioLink?.[index + 1]?.audioLink && (
                <Typography className="!text-red-600 !text-sm">
                  {errors.audioLink[index + 1].audioLink.message}
                </Typography>
              )}
              <IconButton onClick={() => removeAudio(index + 1)}>
                <RemoveIcon className="!text-xl !text-[--text-color] mt-2" />
              </IconButton>
            </Box>
          ))}
          <Button
            className="!flex !gap-x-1 !normal-case"
            sx={{
              border: 0,
              "&.MuiButtonBase-root": {
                color: "transparent !important",
              },
            }}
            onClick={() => appendAudio({ audioLink: "" })}
          >
            <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy">
              {t("addLink")}
            </Typography>
            <Add className="!text-xl !text-[--text-color]" />
          </Button>
        </Box>
      </Box>

      <ProfileFooter
        backurl={
          previewData?.profile?.option === "Venue"
            ? `/${lang}/info-person`
            : previewData?.selectedActType === "Live Music"
              ? `/${lang}/music-genre`
              : `/${lang}/entertainment-type`
        }
        loading={loading}
        buttonName={p("Next")}
      />
    </form>
  );
};

export default ActMediaForm;
