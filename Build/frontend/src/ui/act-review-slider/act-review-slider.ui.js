"use client";
import React, { useRef, useState } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { East, West } from "@mui/icons-material";
import MediaImage from "@/assets/png/MediaImage.png";

const ActReviewSlider = ({ images }) => {
  const sliderRef = useRef(null);
  const [currentSlide, setCurrentSlide] = useState(0);

  const settings = {
    dots: false,
    infinite: images && images.length > 1,

    // customPaging: (i) => {
    //     return (
    //       //  <a>
    //       //   <img src={images[i]} alt="images"/>
    //       //  </a>
    //     );
    // },
    afterChange: (current) => setCurrentSlide(current),
    prevArrow: <West />,
    nextArrow: <East />,
  };

  return (
    <div className="slider-container">
      <div className="custom-paging">
        <ul className="vertical-slider">
          {images?.length > 0 ? (
            images?.map((slide, index) => (
              <li
                key={index}
                className={index === currentSlide ? "active" : ""}
                onClick={() => sliderRef.current.slickGoTo(index)}
              >
                <img src={slide} className="" alt="logo" />
              </li>
            ))
          ) : (
            <li key={1} className="active pp" onClick={() => sliderRef.current.slickGoTo(index)}>
              <img src={MediaImage.src} className="logo" alt="logo" />
            </li>
          )}
        </ul>
      </div>
      <Slider ref={sliderRef} {...settings} className="slick-act-slides">
        {images?.length > 0 ? (
          images?.map((slide) => (
            <div key={slide.id} className="bg-[--divider-color]">
              <img src={slide} alt="images" className="object-contain" />
            </div>
          ))
        ) : (
          <div key={1} className="bg-[--divider-color]">
            <img src={MediaImage.src} alt="imagess" className="object-contain" />
          </div>
        )}
      </Slider>
    </div>
  );
};

export default ActReviewSlider;
