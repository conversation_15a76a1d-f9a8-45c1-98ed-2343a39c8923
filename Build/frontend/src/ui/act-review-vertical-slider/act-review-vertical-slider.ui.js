"use client";
import { CommonImage } from "@/component";
import { Box } from "@mui/material";
import React from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

const ActReviewVerticalSlider = ({ images }) => {
  const settings = {
    dots: false,
    infinite: true,
    slidesToShow: 4,
    slidesToScroll: 1,
    vertical: true,
    verticalSwiping: true,
    prevArrow: <></>,
    nextArrow: <></>,
    //beforeChange: function (currentSlide, nextSlide) {},
    //afterChange: function (currentSlide) {},
  };

  return (
    <Box position="relative object-cover">
      <Slider {...settings} className="slick-sliders">
        {images?.map((image, index) => (
          <Box key={index}>
            <CommonImage
              className="object-cover rounded-[4px] max-w-[131px] border border-[--divider-color]"
              src={image}
              alt={"review image"}
              fill
            />
          </Box>
        ))}
      </Slider>
      {/* <Box className="!absolute !text-center !bottom-3 left-0 !right-0">
        <ExpandMoreIcon className="!w-9 !h-9 !cursor-pointer !text-[--text-color]" />
      </Box> */}
    </Box>
  );
};

export default ActReviewVerticalSlider;
