import { Box, Typography } from "@mui/material";
import React from "react";
import AvatarImage from "@/assets/png/AvatarImage.png";
import VenueImage from "@/assets/png/Avatar.png";
//import {  CommonImage } from "@/component";
import Rating from "@/component/rating/rating.components";
import CalenderIcon from "@/assets/svg/CalenderIcon.svg";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import { formatTime, generateLocationString } from "@/utils";
import { CommonImage } from "@/component";

const BookingDetailsDescriptionUi = ({ previewContract }) => {
  const description = [
    {
      id: 0,
      name: "[Act_Name]",
      src: VenueImage,
      rating: 0,
    },
    {
      id: 1,
      name: "[Venue_Name]",
      src: AvatarImage,
      rating: 0,
    },
  ];
  if (previewContract?.bookingParty === "ACT") {
    description[0].name = previewContract?.actProfileName;
    description[0].src = previewContract?.actProfileImageUrls?.[0] ?? AvatarImage;
    description[0].rating = previewContract?.actRating;
    description[1].name = previewContract?.venueProfileName;
    description[1].src = previewContract?.venueProfileImageUrls?.[0] ?? VenueImage;
    description[1].rating = previewContract?.venueRating;
  }
  if (previewContract?.bookingParty === "VENUE") {
    description[0].name = previewContract?.venueProfileName;
    description[0].src = previewContract?.venueProfileImageUrls?.[0] ?? VenueImage;
    description[0].rating = previewContract?.venueRating;
    description[1].name = previewContract?.actProfileName;
    description[1].src = previewContract?.actProfileImageUrls?.[0] ?? AvatarImage;
    description[1].rating = previewContract?.actRating;
  }

  if (previewContract?.bookingParty === "USER") {
    description[0].name = `${previewContract?.purchaserInfo?.firstName} ${previewContract?.purchaserInfo?.lastName}`; //get from purchaser info
    description[0].src = "";
    description[0].rating = "NONE";
    description[1].name =
      previewContract?.otherParty === "ACT"
        ? previewContract?.actProfileName
        : previewContract?.venueProfileName;
    description[1].src =
      previewContract?.otherParty === "VENUE"
        ? previewContract?.venueProfileImageUrls?.[0] ?? VenueImage
        : previewContract?.actProfileImageUrls?.[0] ?? AvatarImage;
    description[1].rating =
      previewContract?.otherParty === "ACT"
        ? previewContract?.actRating
        : previewContract?.venueRating;
  }

  return (
    <Box className="border boder-[--text-color] rounded-[4px] p-4">
      {description.map((data) => (
        <Box
          key={data.id}
          className="bg-[--footer-bg] flex justify-between boder !border-[--divider-color] p-4 my-2 rounded-[4px]"
        >
          <Box className="flex gap-3 items-center">
            {data?.src && (
              <CommonImage
                src={data.src}
                alt="image"
                width={40}
                height={40}
                fallBackSrc={AvatarImage}
                className="w-10 h-10 boder boder-[--divider-color] rounded-full"
              />
            )}
            <Box>
              <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy">
                {data.name}
              </Typography>
              <Box className="flex gap-1 items-center">
                {/* <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                  248 reviews
                </Typography> */}
                {data?.rating !== "NONE" && (
                  <Rating
                    value={data.rating}
                    readOnly
                    sx={{
                      "& .MuiSvgIcon-root": {
                        width: 18,
                        height: 18,
                      },
                      "& .MuiRating-icon": {
                        color: "var(--text-color)",
                      },
                    }}
                  />
                )}
              </Box>
            </Box>
          </Box>
          {/* <Button
            sx={{
              border: 0,
              minWidth: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
            className="!border-[2px] !border-[--text-color] !normal-case !rounded-[4px]"
          >
           <EditIcon className="text-lg" /> 
          </Button> */}
        </Box>
      ))}
      <Box className="flex gap-2 items-start pt-3">
        <CalenderIcon className="text-2xl" />
        <Box>
          <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
            From {formatTime(previewContract?.scheduleTime?.startDate)} until{" "}
            {formatTime(previewContract?.scheduleTime?.endDate)}{" "}
          </Typography>
          {/* <Typography className="text-sm text-[--hide-color] CraftworkGroteskRegular">
            Repeat every Friday{" "}
          </Typography> */}
        </Box>
      </Box>
      <Box className="flex gap-2 items-start pt-3">
        <LocationSvg className="text-2xl" />
        <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
          {generateLocationString(previewContract?.purchaserInfo?.location)}
        </Typography>
      </Box>
      {/* <Box className="flex gap-2 items-start pl-1 pt-3">
        <ClockIcon className="text-xl" />
        <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
          Time 12 - 6 PM, EST
        </Typography>
      </Box> */}
      <Box className="flex justify-between pt-4 pl-2">
        <Box className="w-1/2">
          <Typography className="text-sm text-[--hide-color] CraftworkGroteskRegular">
            Rate
          </Typography>
          <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy">
            $ 3k / per event
          </Typography>
        </Box>
        <Box className="w-1/2">
          <Typography className="text-sm text-[--hide-color] CraftworkGroteskRegular">
            {previewContract?.goodsAndServices?.paymentType === "FLAT_RATE" && "Flat rate fee"}
            {previewContract?.goodsAndServices?.paymentType === "EXPOSURE_GIG" &&
              "Exposure rate fee"}
            {previewContract?.goodsAndServices?.paymentType === "DOOR_GIG" && "Door rate fee"}
          </Typography>
          <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
            {previewContract?.goodsAndServices?.paymentType === "FLAT_RATE" && (
              <>
                <span className="CraftworkGroteskHeavy">
                  {previewContract?.goodsAndServices?.flatRateCurrency}{" "}
                  {previewContract?.goodsAndServices?.flatRateAmount}
                </span>{" "}
                / {previewContract?.goodsAndServices?.flatRatePercentage} down
              </>
            )}
            {previewContract?.goodsAndServices?.paymentType === "EXPOSURE_GIG" && (
              <>
                <span className="CraftworkGroteskHeavy">
                  {previewContract?.goodsAndServices?.exposureGigCurrency}{" "}
                  {previewContract?.goodsAndServices?.exposureGigFee}
                </span>
              </>
            )}
            {previewContract?.goodsAndServices?.paymentType === "DOOR_GIG" && (
              <>
                <span className="CraftworkGroteskHeavy">
                  {"Entry fee: "} {previewContract?.goodsAndServices?.doorGigEntryFee}
                  <br />
                  {"Venue Capacity: "} {previewContract?.goodsAndServices?.venueCapacity}
                </span>{" "}
                <br />
                The door will be manned by the: {
                  previewContract?.goodsAndServices?.doorManagedBy
                }{" "}
                {previewContract?.goodsAndServices?.maximumPercentage}% of door with a guaranteed
                minimum of {previewContract?.goodsAndServices?.guaranteedMaximum}
              </>
            )}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default BookingDetailsDescriptionUi;
