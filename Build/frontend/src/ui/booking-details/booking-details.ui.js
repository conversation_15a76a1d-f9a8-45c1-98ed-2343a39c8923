import { <PERSON>, But<PERSON>, Rating, Typography } from "@mui/material";
import React from "react";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import PhoneSvg from "@/assets/svg/PhoneSvg.svg";
import UserSvg from "@/assets/svg/UserSvg.svg";
import VenueProfileSvg from "@/assets/svg/VenueProfile.svg";
import ActProfile from "@/assets/svg/ActProfile.svg";
import CalenderIcon from "@/assets/svg/CalenderIcon.svg";
import ClockIcon from "@/assets/svg/ClockIcon.svg";
import DistributionList from "@/assets/svg/act-type.svg/DistributionList.svg";
import Production from "@/assets/svg/Production.svg";
import RiderSvg from "@/assets/svg/act-type.svg/Rider.svg";
import BookingDetailsTitleUi from "../booking-details-title/booking-details-title.ui";
import Parking from "@/assets/svg/Parking.svg";
import AvatarImage from "@/assets/png/AvatarImage.png";
import dayjs from "dayjs";
import PurchaseCard from "./purchase-card";
import { generateLocationString } from "@/utils";
import { CommonImage } from "@/component";
import { useLocale } from "next-intl";
import SentIcon from "@/assets/svg/Sent.svg";
import classNames from "classnames";
import PDFIcon from "@/assets/svg/PDFIcon.svg";
import ViewIcon from "@/assets/svg/ViewIcon.svg";

const BookingDetailsUi = ({ previewContract, modifiedGoodsServicesData, contractType }) => {
  const lang = useLocale();
  const equipmentProviderDisplayMap = {
    PURCHASER: "By Purchaser",
    PERFORMER: "By Performer",
    NOT_APPLICABLE: "Not applicable",
  };

  return (
    <Box>
      <Box className="flex gap-6">
        <Typography className="text-lg text-[--text-color] CraftworkGroteskMedium">
          Booking request
        </Typography>
        <Box
          className={classNames("no-print flex gap-1 px-1 rounded-[2px] items-center", {
            "bg-[--inprogress-color]":
              previewContract?.contractState?.toUpperCase() === "NEGOTIATING",
            "bg-[--confirmed-color]": previewContract?.contractState?.toUpperCase() === "CONFIRMED",
            "bg-[--delete-color]": previewContract?.contractState?.toUpperCase() === "CANCELLED",
            "bg-[--done-color]": previewContract?.contractState?.toUpperCase() === "DONE",
            "bg-[--text-color]": !["NEGOTIATING", "CONFIRMED", "CANCELLED", "DONE"]?.includes(
              previewContract?.contractState?.toUpperCase(),
            ),
          })}
        >
          <SentIcon className="text-base text-white" />
          <Typography className="text-sm text-[--bg-color] CraftworkGroteskMedium">
            {previewContract?.contractState?.toUpperCase()}
          </Typography>
        </Box>

        {previewContract?.paymentStatus?.toUpperCase() === "PAYMENT_REQUIRED" ||
        previewContract?.paymentStatus?.toUpperCase() === "WAITING_OTHER_PARTY_PAYMENT" ||
        previewContract?.paymentStatus?.toUpperCase() === "FINALIZED" ? (
          <Box
            className={classNames("no-print flex gap-1 px-1 rounded-[2px] items-center", {
              "bg-[--inprogress-color]":
                previewContract?.paymentStatus?.toUpperCase() === "PAYMENT_REQUIRED" ||
                previewContract?.paymentStatus?.toUpperCase() === "WAITING_OTHER_PARTY_PAYMENT",
              "bg-[--confirmed-color]":
                previewContract?.paymentStatus?.toUpperCase() === "FINALIZED",
              "bg-[--text-color]": ![
                "PAYMENT_REQUIRED",
                "WAITING_OTHER_PARTY_PAYMENT",
                "FINALIZED",
              ]?.includes(previewContract?.paymentStatus?.toUpperCase()),
            })}
          >
            <SentIcon className="text-base text-white" />
            <Typography className="text-sm text-[--bg-color] CraftworkGroteskMedium">
              {previewContract?.paymentStatus?.toUpperCase() === "PAYMENT_REQUIRED" &&
                "Payment Required"}
              {previewContract?.paymentStatus?.toUpperCase() === "WAITING_OTHER_PARTY_PAYMENT" &&
                "Waiting Other Party Payment"}
              {previewContract?.paymentStatus?.toUpperCase() === "FINALIZED" && "Paid"}
            </Typography>
          </Box>
        ) : null}
      </Box>

      <Box className="flex gap-8 pt-8 md:pt-12 lg:pt-16">
        <Box>
          <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy">
            Purchaser
          </Typography>
          <Box className="flex gap-2 items-center pt-6">
            <UserSvg className="text-sm" />
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              {previewContract?.purchaserInfo?.firstName} {previewContract?.purchaserInfo?.lastName}
            </Typography>
          </Box>
          <Box className="flex gap-2 items-centerdj pt-1">
            <LocationSvg className="text-base" />
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              {generateLocationString(previewContract?.purchaserInfo?.location)}
            </Typography>
          </Box>
          <Box className="flex gap-2 items-center pt-1">
            <PhoneSvg className="text-base" />
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              {previewContract?.purchaserInfo?.phoneNumber}
            </Typography>
          </Box>
        </Box>
        {previewContract?.otherPartyInfo && (
          <Box>
            <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy">
              Act/Venue
            </Typography>
            <Box className="flex gap-2 items-center pt-6">
              <UserSvg className="text-sm" />
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                {previewContract?.otherPartyInfo?.firstName}{" "}
                {previewContract?.otherPartyInfo?.lastName}
              </Typography>
            </Box>
            <Box className="flex gap-2 items-center pt-1">
              <LocationSvg className="text-base" />
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                {generateLocationString(previewContract?.otherPartyInfo?.location)}
              </Typography>
            </Box>
            <Box className="flex gap-2 items-center pt-1">
              <PhoneSvg className="text-base" />
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                {previewContract?.otherPartyInfo?.phoneNumber}
              </Typography>
            </Box>
          </Box>
        )}
      </Box>
      {(previewContract?.bookingParty === "VENUE" || previewContract?.bookingParty === "ACT") && (
        <BookingDetailsTitleUi>
          {previewContract?.bookingParty === "ACT" && (
            <ActProfile className="lg:w-[37px] lg:h-[37px] md:w-[37px] md:h-[37px] w-[26px] h-[26px]" />
          )}
          {previewContract?.bookingParty === "VENUE" && (
            <VenueProfileSvg className="lg:w-[37px] lg:h-[37px] md:w-[37px] md:h-[37px] w-[26px] h-[26px]" />
          )}
          <Typography className="text-2xl text-[--text-color] CraftworkGroteskMedium">
            {previewContract?.bookingParty === "VENUE" ? "Venue" : "Act"}
          </Typography>
        </BookingDetailsTitleUi>
      )}
      {previewContract?.bookingParty === "VENUE" && (
        <PurchaseCard
          id={previewContract?.venueProfileId}
          image={previewContract?.venueProfileImageUrls[0]}
          name={previewContract?.venueProfileName}
          rating={previewContract?.venueRating}
          location={previewContract?.venueLocation}
        />
      )}
      {previewContract?.bookingParty === "ACT" && (
        <PurchaseCard
          id={previewContract?.actProfileId}
          image={previewContract?.actProfileImageUrls?.[0]}
          name={previewContract?.actProfileName}
          rating={previewContract?.actRating}
          location={previewContract?.actLocation}
        />
      )}
      <BookingDetailsTitleUi>
        {previewContract?.otherParty === "ACT" && (
          <ActProfile className="lg:w-[37px] lg:h-[37px] md:w-[37px] md:h-[37px] w-[26px] h-[26px]" />
        )}
        {previewContract?.otherParty === "VENUE" && (
          <VenueProfileSvg className="lg:w-[37px] lg:h-[37px] md:w-[37px] md:h-[37px] w-[26px] h-[26px]" />
        )}
        <Typography className="text-2xl text-[--text-color] CraftworkGroteskMedium">
          {previewContract?.otherParty === "ACT" ? "ACT" : "VENUE"}
        </Typography>
      </BookingDetailsTitleUi>
      <Box className="flex gap-3 flex-wrap items-center pt-3">
        <Box className="bg-[--footer-bg] border flex gap-3 flex-wrap border-[--divider-color] p-4 rounded-[2px]">
          <CommonImage
            //src={AvatarImage}
            src={
              previewContract?.otherParty === "ACT"
                ? previewContract?.actProfileImageUrls?.[0] || AvatarImage
                : previewContract?.venueProfileImageUrls?.[0] || AvatarImage
            }
            alt="avatar-image"
            width={40}
            height={40}
            layout="responsive"
            className="!w-[40px] !h-[40px] border border-[--divider-color] rounded-full"
          />
          <Box>
            <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy">
              {previewContract?.otherParty === "ACT"
                ? previewContract?.actProfileName
                : previewContract?.venueProfileName}
            </Typography>
            <Box className="flex gap-1 items-center">
              {/* <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                248 reviews
              </Typography> */}
              <Rating
                value={
                  previewContract?.otherParty === "ACT"
                    ? previewContract?.actRating
                    : previewContract?.venueRating
                }
                readOnly
                sx={{
                  "& .MuiSvgIcon-root": {
                    width: 18,
                    height: 18,
                  },
                  "& .MuiRating-icon": {
                    color: "var(--text-color)",
                  },
                }}
              />
            </Box>
          </Box>
          <Button
            sx={{
              border: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
            onClick={() => {
              const profileId =
                previewContract?.otherParty === "ACT"
                  ? previewContract?.actProfileId
                  : previewContract?.venueProfileId;

              if (profileId) {
                //router.push(`/${lang}/${profileId}/view`);
                window.open(`/${lang}/${profileId}/view`, "_blank", "noopener,noreferrer");
              }
            }}
            className="no-print !border-[2px] !border-[--text-color] !normal-case !rounded-[4px] !flex !gap-x-1 !py-2"
          >
            <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy">
              View profile
            </Typography>
            {/* <EditIcon className="text-sm" /> */}
          </Button>
        </Box>
        {previewContract?.goodsAndServices?.performersRole ? (
          <>
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              as a
            </Typography>
            <Box className="bg-[--footer-bg] border flex gap-3 border-[--divider-color] px-4 py-6 rounded-[2px]">
              <UserSvg className="text-base" />
              <Typography
                className={`text-sm  h-full ${modifiedGoodsServicesData?.performersRoleModified ? "text-red-500 CraftworkGroteskHeavy" : "text-[--text-color] CraftworkGroteskRegular"}`}
              >
                {previewContract?.goodsAndServices?.performersRole}
              </Typography>
            </Box>
          </>
        ) : (
          ""
        )}
      </Box>
      <Box className="flex gap-5 flex-wrap items-center py-4">
        <Box className="flex gap-2">
          <CalenderIcon className="w-[37px] h-[37px]" />
          <Box>
            <Typography className="text-sm text-[--hide-color] CraftworkGroteskRegular">
              Start date
            </Typography>
            <Typography
              className={`text-sm  ${modifiedGoodsServicesData?.startDateModified ? "text-red-500 CraftworkGroteskHeavy" : "text-[--text-color] CraftworkGroteskRegular"}`}
            >
              {previewContract?.scheduleTime?.startDate &&
                dayjs(previewContract?.scheduleTime?.startDate).format("MMM DD, YYYY")}
            </Typography>
          </Box>
        </Box>
        <Box className="flex gap-2">
          <CalenderIcon className="w-[37px] h-[37px]" />
          <Box>
            <Typography className="text-sm text-[--hide-color] CraftworkGroteskRegular">
              End date
            </Typography>
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              {previewContract?.scheduleTime?.endDate &&
                dayjs(previewContract?.scheduleTime?.endDate).format("MMM DD, YYYY")}
            </Typography>
          </Box>
        </Box>
        <Box className="flex gap-2">
          <ClockIcon className="w-[37px] h-[37px]" />
          <Box>
            <Typography className="text-sm text-[--hide-color] CraftworkGroteskRegular">
              Perform
            </Typography>
            <Typography
              className={`text-sm ${
                modifiedGoodsServicesData?.startDateModified
                  ? "text-red-500 CraftworkGroteskHeavy"
                  : "text-[--text-color] CraftworkGroteskRegular"
              }`}
            >
              {dayjs(previewContract?.goodsAndServices?.startDate).format("HH:mm a")}
            </Typography>
          </Box>
        </Box>
        {contractType !== "USERVENUE" && (
          <Box>
            <Typography className="text-sm text-[--hide-color] CraftworkGroteskRegular">
              Load
            </Typography>
            <Typography
              className={`text-sm ${
                modifiedGoodsServicesData?.loadingTimeModified
                  ? "text-red-500 CraftworkGroteskHeavy"
                  : "text-[--text-color] CraftworkGroteskRegular"
              }`}
            >
              {dayjs(previewContract?.goodsAndServices?.loadingTime).format("HH:mm a")}
            </Typography>
          </Box>
        )}
        <Box>
          <Typography className="text-sm text-[--hide-color] CraftworkGroteskRegular">
            Duration
          </Typography>
          <Typography
            className={`text-sm ${
              modifiedGoodsServicesData?.durationInHoursModified
                ? "text-red-500 CraftworkGroteskHeavy"
                : "text-[--text-color] CraftworkGroteskRegular"
            }`}
          >
            `{previewContract?.goodsAndServices?.durationInHours}h
          </Typography>
        </Box>
      </Box>
      <BookingDetailsTitleUi>
        <DistributionList className="lg:w-[37px] lg:h-[37px] md:w-[37px] md:h-[37px] w-[26px] h-[26px]" />
        <Typography className="text-2xl text-[--text-color] CraftworkGroteskMedium">
          Statement of goods and services
        </Typography>
      </BookingDetailsTitleUi>
      <Box className="mt-3 p-4 rounded-[2px]  bg-[--footer-bg] border border-[--divider-color]">
        <Typography
          className={`text-sm ${
            modifiedGoodsServicesData?.paymentTypeModified
              ? "text-red-500 CraftworkGroteskHeavy"
              : "text-[--hide-color] CraftworkGroteskRegular"
          }`}
        >
          {previewContract?.goodsAndServices?.paymentType === "FLAT_RATE" && "Flat rate fee"}
          {previewContract?.goodsAndServices?.paymentType === "EXPOSURE_GIG" && "Exposure rate fee"}
          {previewContract?.goodsAndServices?.paymentType === "DOOR_GIG" && "Door rate fee"}
        </Typography>
        <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
          {previewContract?.goodsAndServices?.paymentType === "FLAT_RATE" && (
            <>
              <div className="text-lg font-medium">Terms of Service:</div>
              <div className="mt-1">Flat rate fee as follows:</div>{" "}
              <div className="mt-1">
                Deposit:{" "}
                <span
                  className={
                    modifiedGoodsServicesData?.flatRatePercentageModified === true
                      ? "text-red-500 CraftworkGroteskHeavy"
                      : "CraftworkGroteskRegular"
                  }
                >
                  ${previewContract?.goodsAndServices?.flatRatePercentage}{" "}
                  {previewContract?.goodsAndServices?.flatRateCurrency}
                </span>{" "}
                <span className="text-sm">(please arrange deposit payment ASAP)</span>
              </div>{" "}
              <div className="mt-1">
                Balance:{" "}
                <span
                  className={
                    modifiedGoodsServicesData?.flatRateAmountModified === true ||
                    modifiedGoodsServicesData?.flatRateCurrencyModified === true
                      ? "text-red-500 CraftworkGroteskHeavy"
                      : "CraftworkGroteskRegular"
                  }
                >
                  $
                  {previewContract?.goodsAndServices?.flatRateAmount -
                    previewContract?.goodsAndServices?.flatRatePercentage}{" "}
                  {previewContract?.goodsAndServices?.flatRateCurrency}
                </span>{" "}
                <span className="text-sm">
                  (balance payment is due immediately following the performance)
                </span>
              </div>
            </>
          )}

          {previewContract?.goodsAndServices?.paymentType === "EXPOSURE_GIG" && (
            <>
              <span
                className={
                  modifiedGoodsServicesData?.exposureGigCurrencyModified === true
                    ? "text-red-500 CraftworkGroteskHeavy text-2xl"
                    : "CraftworkGroteskRegular text-2xl"
                }
              >
                {previewContract?.goodsAndServices?.exposureGigCurrency}{" "}
              </span>
              <span
                className={
                  modifiedGoodsServicesData?.exposureGigFeeModified === true
                    ? "text-red-500 CraftworkGroteskHeavy text-2xl"
                    : "CraftworkGroteskRegular text-2xl"
                }
              >
                {previewContract?.goodsAndServices?.exposureGigFee}
              </span>{" "}
            </>
          )}

          {previewContract?.goodsAndServices?.paymentType === "DOOR_GIG" && (
            <>
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular mb-4">
                Term of Service:
              </Typography>
              <Box className="flex flex-col gap-2">
                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                  Fee Based on the Door as follows:
                </Typography>
                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                  Door Entry Fee:{" "}
                  <span
                    className={
                      modifiedGoodsServicesData?.doorGigEntryFeeModified === true
                        ? "text-red-500 CraftworkGroteskHeavy"
                        : "CraftworkGroteskRegular"
                    }
                  >
                    ${previewContract?.goodsAndServices?.doorGigEntryFee}
                  </span>
                </Typography>

                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                  Venue Capacity:{" "}
                  <span
                    className={
                      modifiedGoodsServicesData?.venueCapacityModified === true
                        ? "text-red-500 CraftworkGroteskHeavy"
                        : "CraftworkGroteskRegular"
                    }
                  >
                    {previewContract?.goodsAndServices?.venueCapacity}
                  </span>
                </Typography>

                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                  Door to be manned by:{" "}
                  <span
                    className={`${
                      modifiedGoodsServicesData?.doorManagedByModified
                        ? "text-red-500 CraftworkGroteskHeavy"
                        : "CraftworkGroteskRegular"
                    }`}
                  >
                    The {previewContract?.goodsAndServices?.doorManagedBy}
                  </span>
                </Typography>

                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                  The {previewContract?.goodsAndServices?.doorManagedBy} will pay the{" "}
                  {previewContract?.goodsAndServices?.doorManagedBy === "ACT" ? "VENUE" : "ACT"}{" "}
                  <span
                    className={`${
                      modifiedGoodsServicesData?.maximumPercentageModified
                        ? "text-red-500 CraftworkGroteskHeavy"
                        : "CraftworkGroteskRegular"
                    }`}
                  >
                    {previewContract?.goodsAndServices?.maximumPercentage}%
                  </span>{" "}
                  of the Door with a guaranteed minimum of{" "}
                  <span
                    className={`${
                      modifiedGoodsServicesData?.guaranteedMaximumModified
                        ? "text-red-500 CraftworkGroteskHeavy"
                        : "CraftworkGroteskRegular"
                    }`}
                  >
                    ${previewContract?.goodsAndServices?.guaranteedMaximum}
                  </span>
                </Typography>
              </Box>
            </>
          )}
        </Typography>
      </Box>
      {/* <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy pt-5">
        Production Equipment{" "}
      </Typography>
      <Box className="flex gap-x-2 items-center py-2">
        <Production className="text-2xl" />
        <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
          Sound and lighting by Performer{" "}
        </Typography>
      </Box> */}
      <Box className="flex lg:justify-between gap-2 flex-wrap">
        {(previewContract?.goodsAndServices?.merchandiseSalesAllowed ||
          previewContract?.goodsAndServices?.performerMemberOfUnion) && (
          <Box>
            <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy pt-5">
              Special provisions{" "}
            </Typography>

            {previewContract?.goodsAndServices?.merchandiseSalesAllowed && (
              <Box className="flex gap-x-2 items-center py-1">
                <Production className="text-2xl" />
                <Typography
                  className={`text-sm  ${modifiedGoodsServicesData?.merchandiseSalesAllowedModified ? "text-red-500 CraftworkGroteskHeavy" : "text-[--text-color] CraftworkGroteskRegular"}`}
                >
                  CD/merchandise sales at venue are permitted
                </Typography>
              </Box>
            )}
            {previewContract?.goodsAndServices?.performerMemberOfUnion && (
              <Box className="flex gap-x-2 items-center py-1">
                <Production className="text-2xl" />
                <Typography
                  className={`text-sm  ${modifiedGoodsServicesData?.performerMemberOfUnionModified ? "text-red-500 CraftworkGroteskHeavy" : "text-[--text-color] CraftworkGroteskRegular"}`}
                >
                  Performer is/are members of appropriate union
                </Typography>
              </Box>
            )}
          </Box>
        )}
        {(previewContract?.goodsAndServices?.mealsProvidedByPurchaser ||
          previewContract?.goodsAndServices?.accommodationProvided) && (
          <Box>
            <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy pt-5">
              Meals, Accommodation{" "}
            </Typography>

            {previewContract?.goodsAndServices?.mealsProvidedByPurchaser && (
              <Box className="flex gap-x-2 items-center py-1">
                <Production className="text-2xl" />
                <Typography
                  className={`text-sm ${
                    modifiedGoodsServicesData?.mealsProvidedByPurchaserModified
                      ? "text-red-500 CraftworkGroteskHeavy"
                      : "text-[--text-color] CraftworkGroteskRegular"
                  }`}
                >
                  Accommodation provided to touring talent
                </Typography>
              </Box>
            )}
            {previewContract?.goodsAndServices?.accommodationProvided && (
              <Box className="flex gap-x-2 items-center py-1">
                <Production className="text-2xl" />

                <Typography
                  className={`text-sm ${
                    modifiedGoodsServicesData?.accommodationProvidedModified
                      ? "text-red-500 CraftworkGroteskHeavy"
                      : "text-[--text-color] CraftworkGroteskRegular"
                  }`}
                >
                  Meals provided by Purchaser
                </Typography>
              </Box>
            )}
          </Box>
        )}
      </Box>
      {previewContract?.goodsAndServices?.message && (
        <>
          <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy pt-8">
            Other Details
          </Typography>
          <Box className="mt-3 p-4 rounded-[2px]  bg-[--footer-bg] border border-[--divider-color]">
            {/* <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
          Hi Linda,
        </Typography> */}
            <Typography
              className={`text-sm  ${modifiedGoodsServicesData?.messageModified ? "text-red-500  CraftworkGroteskHeavy" : "text-[--text-color] CraftworkGroteskRegular"}`}
            >
              {previewContract?.goodsAndServices?.message}
            </Typography>
            {/* <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
          Matt
        </Typography> */}
          </Box>
        </>
      )}
      {previewContract?.bookingParty !== "USER" && (
        <BookingDetailsTitleUi>
          <RiderSvg className="lg:w-[37px] lg:h-[37px] md:w-[37px] md:h-[37px] w-[26px] h-[26px]" />
          <Typography className="text-2xl text-[--text-color] CraftworkGroteskMedium">
            Purchaser&#39;s rider
          </Typography>
        </BookingDetailsTitleUi>
      )}
      {previewContract?.bookingParty === "ACT" && previewContract?.actRiderNotes?.riderUrl && (
        <Box className="mt-3 p-4 rounded-[2px] flex justify-between bg-[--footer-bg] border border-[--divider-color]">
          <Box className="flex gap-x-3 items-center">
            <PDFIcon className="text-4xl" />
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              {previewContract?.actRiderNotes?.riderUrl &&
                previewContract?.actRiderNotes?.riderUrl.split("/").pop()}
            </Typography>
          </Box>

          <Button
            className="!bg-[--text-color] !py-1 lg:!flex lg:!gap-x-2"
            sx={{
              minWidth: 0,
              border: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
            onClick={() => {
              const url = `/${previewContract?.actProfileId}/pdf-view?pdf=${previewContract?.actRiderNotes?.riderUrl.split("/").pop()}`;
              const newWindow = window.open(url, "_blank", "noopener,noreferrer");
              if (newWindow) newWindow.opener = null;
              // router.push(
              //   `/${profileId}/pdf-view?pdf=${rider.riderDocument.split("/").pop()}`,
              // )
            }}
          >
            <Typography className="!normal-case hidden lg:inline CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
              View
            </Typography>
            <ViewIcon className="!text-lg" />
          </Button>
        </Box>
      )}

      {previewContract?.bookingParty === "VENUE" && previewContract?.venueRiderNotes?.riderUrl && (
        <Box className="mt-3 p-4 rounded-[2px] flex justify-between bg-[--footer-bg] border border-[--divider-color]">
          <Box className="flex gap-x-3 items-center">
            <PDFIcon className="text-4xl" />
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              {previewContract?.venueRiderNotes?.riderUrl &&
                previewContract?.venueRiderNotes?.riderUrl.split("/").pop()}
            </Typography>
          </Box>

          <Button
            className="!bg-[--text-color] !py-1 lg:!flex lg:!gap-x-2"
            sx={{
              minWidth: 0,
              border: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
            onClick={() => {
              const url = `/${previewContract?.venueProfileId}/pdf-view?pdf=${previewContract?.venueRiderNotes?.riderUrl.split("/").pop()}`;
              const newWindow = window.open(url, "_blank", "noopener,noreferrer");
              if (newWindow) newWindow.opener = null;
              // router.push(
              //   `/${profileId}/pdf-view?pdf=${rider.riderDocument.split("/").pop()}`,
              // )
            }}
          >
            <Typography className="!normal-case hidden lg:inline CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
              View
            </Typography>
            <ViewIcon className="!text-lg" />
          </Button>
        </Box>
      )}

      {previewContract?.goodsAndServices?.equipmentProvider && contractType !== "USERVENUE" && (
        <>
          <BookingDetailsTitleUi>
            <Typography className="text-2xl text-[--text-color] CraftworkGroteskMedium">
              Production Equipment
            </Typography>
          </BookingDetailsTitleUi>

          <Box className="flex gap-2 items-center pt-1">
            <Production />
            <Typography
              className={`text-sm ${
                modifiedGoodsServicesData?.equipmentProviderModified
                  ? "text-red-500 CraftworkGroteskHeavy"
                  : "text-[--text-color] CraftworkGroteskRegular"
              }`}
            >
              {equipmentProviderDisplayMap[previewContract?.goodsAndServices?.equipmentProvider] ||
                ""}
            </Typography>
          </Box>
        </>
      )}

      {previewContract?.venueRiderChanges?.parkingConditions && contractType !== "USERVENUE" && (
        <Box className="py-6">
          <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy">
            Parking
          </Typography>
          <Box className="flex gap-2 items-center pt-1">
            <Parking />
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              {previewContract?.venueRiderChanges?.parkingConditions}
            </Typography>
          </Box>
        </Box>
      )}
      {/* {previewContract?.bookingParty === "ACT" && (
        <Box className="py-6">
          <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy">
            Parking
          </Typography>
          <Box className="flex gap-2 items-center pt-1">
            <Parking />
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              {previewContract?.actRiderChanges?.parkingConditions}
            </Typography>
          </Box>
        </Box>
      )} */}
      {contractType !== "USERVENUE" &&
        previewContract?.venueRiderChanges?.allowedVisualEffectsList.length > 0 && (
          <Box className="py-6">
            <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy">
              Visual effects allowed
            </Typography>
            <Box className="flex lg:justify-between gap-2 flex-wrap items-center">
              {previewContract?.venueRiderChanges?.allowedVisualEffectsList.map((data) => (
                <Box key={data} className="flex gap-2 items-center pt-1">
                  <Production />
                  <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                    {data}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>
        )}
      {/* {previewContract?.bookingParty === "ACT" && (
        <Box className="py-6">
          <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy">
            Visual effects allowed
          </Typography>
          <Box className="flex lg:justify-between gap-2 flex-wrap items-center">
            {previewContract?.actRiderChanges?.allowedVisualEffectsList.map((data) => (
              <Box key={data} className="flex gap-2 items-center pt-1">
                <Production />
                <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                  {data}
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>
      )} */}
      {/* <Box className="py-6">
        <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy">
          Other rider details
        </Typography>
        <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
          Your details
        </Typography>
      </Box> */}

      {previewContract?.otherParty === "ACT" && previewContract?.actRiderNotes?.riderUrl && (
        <>
          <BookingDetailsTitleUi>
            <RiderSvg className="lg:w-[37px] lg:h-[37px] md:w-[37px] md:h-[37px] w-[26px] h-[26px]" />
            <Typography className="text-2xl text-[--text-color] CraftworkGroteskMedium">
              Performer&#39;s rider
            </Typography>
          </BookingDetailsTitleUi>
          <Box className="mt-3 p-4 rounded-[2px] flex justify-between bg-[--footer-bg] border border-[--divider-color]">
            <Box className="flex gap-x-3 items-center">
              <PDFIcon className="text-4xl" />
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                {previewContract?.actRiderNotes?.riderUrl &&
                  previewContract?.actRiderNotes?.riderUrl.split("/").pop()}
              </Typography>
            </Box>

            <Button
              className="!bg-[--text-color] !py-1 lg:!flex lg:!gap-x-2"
              sx={{
                minWidth: 0,
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
              onClick={() => {
                const url = `/${previewContract?.actProfileId}/pdf-view?pdf=${previewContract?.actRiderNotes?.riderUrl.split("/").pop()}`;
                const newWindow = window.open(url, "_blank", "noopener,noreferrer");
                if (newWindow) newWindow.opener = null;
                // router.push(
                //   `/${profileId}/pdf-view?pdf=${rider.riderDocument.split("/").pop()}`,
                // )
              }}
            >
              <Typography className="!normal-case hidden lg:inline CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                View
              </Typography>
              <ViewIcon className="!text-lg" />
            </Button>
          </Box>
        </>
      )}

      {previewContract?.otherParty === "VENUE" && previewContract?.venueRiderNotes?.riderUrl && (
        <>
          <BookingDetailsTitleUi>
            <RiderSvg className="lg:w-[37px] lg:h-[37px] md:w-[37px] md:h-[37px] w-[26px] h-[26px]" />
            <Typography className="text-2xl text-[--text-color] CraftworkGroteskMedium">
              Venue&#39;s rider
            </Typography>
          </BookingDetailsTitleUi>
          <Box className="mt-3 p-4 rounded-[2px] flex justify-between bg-[--footer-bg] border border-[--divider-color]">
            <Box className="flex gap-x-3 items-center">
              <PDFIcon className="text-4xl" />
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                {previewContract?.venueRiderNotes?.riderUrl &&
                  previewContract?.venueRiderNotes?.riderUrl.split("/").pop()}
              </Typography>
            </Box>

            <Button
              className="!bg-[--text-color] !py-1 lg:!flex lg:!gap-x-2"
              sx={{
                minWidth: 0,
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
              onClick={() => {
                const url = `/${previewContract?.venueProfileId}/pdf-view?pdf=${previewContract?.venueRiderNotes?.riderUrl.split("/").pop()}`;
                const newWindow = window.open(url, "_blank", "noopener,noreferrer");
                if (newWindow) newWindow.opener = null;
                // router.push(
                //   `/${profileId}/pdf-view?pdf=${rider.riderDocument.split("/").pop()}`,
                // )
              }}
            >
              <Typography className="!normal-case hidden lg:inline CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
                View
              </Typography>
              <ViewIcon className="!text-lg" />
            </Button>
          </Box>
        </>
      )}

      {previewContract?.otherParty === "ACT" &&
        previewContract?.actRiderNotes?.acceptedWithConditions && (
          <Box className="flex gap-2 items-center pt-1">
            <Production />
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              Accepted with the following exceptions
            </Typography>
          </Box>
        )}

      {previewContract?.otherParty === "ACT" &&
        previewContract?.actRiderNotes?.acceptedWithConditions &&
        previewContract?.actRiderNotes?.acceptanceConditions && (
          <Box className="mt-3 p-4 rounded-[2px] bg-[--footer-bg] border border-[--divider-color]">
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              {previewContract?.actRiderNotes?.acceptanceConditions}
            </Typography>
          </Box>
        )}

      {previewContract?.otherParty === "ACT" && previewContract?.actRiderNotes?.riderAccepted && (
        <Box className="flex gap-2 items-center pt-1">
          <Production />
          <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
            Accepted
          </Typography>
        </Box>
      )}

      {previewContract?.otherParty === "ACT" && previewContract?.actRiderNotes?.riderRejected && (
        <Box className="flex gap-2 items-center pt-1">
          <Production />
          <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
            Decline or subject to negotiation
          </Typography>
        </Box>
      )}

      {previewContract?.otherParty === "ACT" &&
        previewContract?.actRiderNotes?.acceptedWithConditions &&
        previewContract?.actRiderNotes?.riderRejectionReason && (
          <Box className="mt-3 p-4 rounded-[2px] bg-[--footer-bg] border border-[--divider-color]">
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              {previewContract?.actRiderNotes?.riderRejectionReason}
            </Typography>
          </Box>
        )}

      {previewContract?.otherParty === "VENUE" &&
        previewContract?.venueRiderNotes?.acceptedWithConditions && (
          <Box className="flex gap-2 items-center pt-1">
            <Production />
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              Accepted with the following exceptions
            </Typography>
          </Box>
        )}

      {previewContract?.otherParty === "VENUE" &&
        previewContract?.venueRiderNotes?.acceptedWithConditions &&
        previewContract?.venueRiderNotes?.acceptanceConditions && (
          <Box className="mt-3 p-4 rounded-[2px] bg-[--footer-bg] border border-[--divider-color]">
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              {previewContract?.venueRiderNotes?.acceptanceConditions}
            </Typography>
          </Box>
        )}

      {previewContract?.otherParty === "VENUE" &&
        previewContract?.venueRiderNotes?.riderAccepted && (
          <Box className="flex gap-2 items-center pt-1">
            <Production />
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              Accepted
            </Typography>
          </Box>
        )}

      {previewContract?.otherParty === "VENUE" &&
        previewContract?.venueRiderNotes?.riderRejected && (
          <Box className="flex gap-2 items-center pt-1">
            <Production />
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              Decline or subject to negotiation
            </Typography>
          </Box>
        )}

      {previewContract?.otherParty === "VENUE" &&
        previewContract?.venueRiderNotes?.acceptedWithConditions &&
        previewContract?.venueRiderNotes?.riderRejectionReason && (
          <Box className="mt-3 p-4 rounded-[2px] bg-[--footer-bg] border border-[--divider-color]">
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              {previewContract?.venueRiderNotes?.riderRejectionReason}
            </Typography>
          </Box>
        )}

      {/* <Box className="mt-3 p-4 rounded-[2px] bg-[--footer-bg] border border-[--divider-color]">
        <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
          My text with exceptions:
        </Typography>
        <List>
          {list.map((data) => (
            <ListItem
              sx={{ padding: 0 }}
              key={data}
              className="text-sm text-[--text-color] CraftworkGroteskRegular list-decimal"
            >
              {data}
            </ListItem>
          ))}
        </List>
        <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
          Thank you!
        </Typography>
      </Box> */}
      <BookingDetailsTitleUi>
        <RiderSvg className="lg:w-[37px] lg:h-[37px] md:w-[37px] md:h-[37px] w-[26px] h-[26px]" />
        <Typography className={"text-2xl text-[--text-color] CraftworkGroteskRegula"}>
          {previewContract?.finePrint?.title}
        </Typography>
      </BookingDetailsTitleUi>
      <Typography className={"text-sm pt-4 text-[--text-color] CraftworkGroteskRegular"}>
        {previewContract?.finePrint?.content}
        {previewContract?.finePrint?.footer}
      </Typography>
    </Box>
  );
};

export default BookingDetailsUi;
