import { CommonImage } from "@/component";
import { Button, Rating, Typography } from "@mui/material";
import { Box } from "@mui/system";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import { generateLocationString } from "@/utils";
import Link from "next/link";
import { useLocale } from "next-intl";
import AvatarImage from "@/assets/png/AvatarImage.png";

const PurchaseCard = ({ id, image, name, rating, location }) => {
  const lang = useLocale();
  const commonImage = image ?? AvatarImage;
  return (
    <Box className="mt-3 p-4 rounded-[2px] flex items-start lg:justify-between gap-2 flex-wrap bg-[--footer-bg] border border-[--divider-color]">
      <Box className="flex gap-3 flex-wrap">
        <CommonImage
          src={commonImage}
          alt="image"
          width={103}
          height={103}
          layout="responsive"
          className="!w-[103px] !h-[103px] border border-[--divider-color] rounded-full"
        />
        <Box>
          <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy">
            {name}
          </Typography>
          <Box className="flex gap-1 items-center">
            {/* <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                248 reviews
              </Typography> */}
            <Rating
              value={rating}
              readOnly
              sx={{
                "& .MuiSvgIcon-root": {
                  width: 18,
                  height: 18,
                },
                "& .MuiRating-icon": {
                  color: "var(--text-color)",
                },
              }}
            />
          </Box>
          <Box className="flex gap-2 pt-5">
            <LocationSvg className="text-lg" />
            <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
              {generateLocationString(location)}
            </Typography>
          </Box>
          {/* <Box className="flex gap-2 pt-1">
              <GlobeIcon className="text-lg" />
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                danceagain.ca
              </Typography>
            </Box> */}
          {/* <Box className="flex gap-2 items-center pt-1">
              <PhoneSvg className="text-base" />
              <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                **************
              </Typography>
            </Box> */}
        </Box>
      </Box>
      <Button
        sx={{
          border: 0,
          "&.MuiButtonBase-root": {
            color: "white !important",
          },
        }}
        className="no-print !border-[2px] !border-[--text-color] !normal-case !rounded-[4px] !flex !gap-x-1 !py-2"
      >
        <Link href={`/${lang}/${id}/view`} passHref target="_blank" rel="noopener noreferrer">
          <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy">
            View profile
          </Typography>
        </Link>
        {/* <EditIcon className="text-sm" /> */}
      </Button>
    </Box>
  );
};

export default PurchaseCard;
