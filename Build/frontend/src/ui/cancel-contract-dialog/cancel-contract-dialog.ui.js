import {
  Box,
  Dialog,
  DialogContent,
  Drawer,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import React from "react";
import AvatarImage from "@/assets/png/Avatar.png";
import VenueImage from "@/assets/png/Avatar.png";
import Image from "next/image";
import FilledStar from "@/assets/svg/FilledStar.svg";
import CalendarIcon from "@/assets/svg/CalenderIcon.svg";
import LocationSvg from "@/assets/svg/LocationSvg.svg";
import ClockIcon from "@/assets/svg/ClockIcon.svg";
import { Clear, SouthEast } from "@mui/icons-material";
import { Button } from "@/component";
import { formatTimeToAMPM, generateLocationString } from "@/utils";
import { useSelector } from "react-redux";
import { formatDate } from "@fullcalendar/core";

const CancelContractDialog = ({ open, handleClose, handleAcceptRejectContract, loading }) => {
  const { previewContract } = useSelector((state) => state.booking);

  const description = [
    {
      id: 0,
      name: "[Act_Name]",
      src: AvatarImage,
      rating: 0,
    },
    {
      id: 1,
      name: "[Venue_Name]",
      src: VenueImage,
      rating: 0,
    },
  ];
  if (previewContract?.bookingParty === "ACT") {
    description[0].name = previewContract?.actProfileName;
    description[0].src = previewContract?.actProfileImageUrls?.[0] ?? AvatarImage;
    description[0].rating = previewContract?.actRating;
    description[1].name = previewContract?.venueProfileName;
    description[1].src = previewContract?.venueProfileImageUrls?.[0] ?? VenueImage;
    description[1].rating = previewContract?.venueRating;
  }
  if (previewContract?.bookingParty === "VENUE") {
    description[0].name = previewContract?.venueProfileName;
    description[0].src = previewContract?.venueProfileImageUrls?.[0] ?? VenueImage;
    description[0].rating = previewContract?.venueRating;
    description[1].name = previewContract?.actProfileName;
    description[1].src = previewContract?.actProfileImageUrls?.[0] ?? AvatarImage;
    description[1].rating = previewContract?.actRating;
  }
  if (previewContract?.bookingParty === "USER") {
    description[0].name = `${previewContract?.purchaserInfo?.firstName} ${previewContract?.purchaserInfo?.lastName}`; //get from purchaser info
    description[0].src = "";
    description[0].rating = "NONE";
    description[1].name =
      previewContract?.otherParty === "ACT"
        ? previewContract?.actProfileName
        : previewContract?.venueProfileName;
    description[1].src =
      previewContract?.otherParty === "VENUE"
        ? previewContract?.venueProfileImageUrls?.[0] ?? VenueImage
        : previewContract?.actProfileImageUrls?.[0] ?? AvatarImage;
    description[1].rating =
      previewContract?.otherParty === "ACT"
        ? previewContract?.actRating
        : previewContract?.venueRating;
  }

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const content = (
    <DialogContent className="!max-w-xl !bg-[--footer-bg] lg:border border-[--text-color] p-6">
      <Typography className="text-2xl text-[--text-color] CraftworkGroteskHeavy">
        Are you sure you want to {open} booking contract?
      </Typography>
      {open === "cancel" && (
        <Typography className="text-sm py-2 text-[--text-color] CraftworkGroteskRegular">
          Please note your contract may have a non-refundable deposit and the fee for creating this
          contract is non-refundable.
        </Typography>
      )}
      <Box className="flex flex-col lg:flex-row md:flex-row gap-3 my-3 ">
        <Box className="p-[10px] flex gap-4 bg-[--divider-color] rounded-[10px] w-full">
          {description?.[0]?.src && (
            <Image
              src={description[0].src}
              alt="avatar-image"
              width={40}
              height={40}
              className="w-[40px] h-[40px] rounded-full border border-[--divider-color]"
            />
          )}
          <Box>
            <Typography className="text-sm text-[--text-color] font-craftWorkHeavy">
              {description[0].name}
            </Typography>
            {description?.[0]?.rating !== "NONE" && (
              <Box className="flex gap-2">
                <Typography className="text-sm text-[--text-color] font-craftWorkRegular">
                  {description[0].rating}
                </Typography>
                <FilledStar className="text-base" />
                {/* <Typography className="text-sm text-[--hide-color] font-craftWorkRegular">
                  248 reviews
                </Typography> */}
              </Box>
            )}
          </Box>
        </Box>
        <Box className="p-[10px] flex gap-4 bg-[--divider-color] rounded-[10px] w-full">
          <Image
            src={description[1].src}
            alt="avatar-image"
            width={40}
            height={40}
            fallBackSrc={AvatarImage}
            className="w-[40px] h-[40px] rounded-full border border-[--divider-color]"
          />
          <Box>
            <Typography className="text-sm text-[--text-color] font-craftWorkHeavy">
              {description[1].name}
            </Typography>
            <Box className="flex gap-2">
              <Typography className="text-sm text-[--text-color] font-craftWorkRegular">
                {description[1].rating}
              </Typography>
              <FilledStar className="text-base" />
              {/* <Typography className="text-sm text-[--hide-color] font-craftWorkRegular">
                248 reviews
              </Typography> */}
            </Box>
          </Box>
        </Box>
      </Box>
      <Box className="p-[10px] flex gap-4 bg-[--divider-color] rounded-[10px] w-full">
        <CalendarIcon className="text-2xl" />
        <Box>
          <Typography className="text-sm text-[--text-color] font-craftWorkRegular">
            From {formatDate(previewContract?.scheduleTime?.startDate)} until{" "}
            {formatDate(previewContract?.scheduleTime?.endDate)}{" "}
          </Typography>
          {/* <Typography className="text-sm text-[--hide-color] font-craftWorkRegular">
            Repeat every Friday
          </Typography> */}
        </Box>
      </Box>
      <Box className="p-[10px] flex gap-4 bg-[--divider-color] rounded-[10px] w-full my-3">
        <LocationSvg className="text-2xl" />
        <Typography className="text-sm text-[--text-color] font-craftWorkRegular">
          {generateLocationString(previewContract?.purchaserInfo?.location)}
        </Typography>
      </Box>
      <Box className="flex gap-4 flex-col lg:flex-row md:flex-row items-center mb-3">
        <Box className="p-[10px] flex gap-4 items-center h-[58px] bg-[--divider-color] rounded-[10px] w-full">
          <ClockIcon className="text-2xl" />
          <Typography className="text-sm text-[--text-color] font-craftWorkRegular">
            {formatTimeToAMPM(previewContract?.scheduleTime?.startDate)} -{" "}
            {formatTimeToAMPM(previewContract?.scheduleTime?.endDate)}{" "}
          </Typography>
        </Box>
        <Box className="p-[10px] bg-[--divider-color] rounded-[10px] w-full">
          <Typography className="text-sm text-[--hide-color] font-craftWorkRegular">
            {previewContract?.goodsAndServices?.paymentType === "FLAT_RATE" && "Flat rate fee"}
            {previewContract?.goodsAndServices?.paymentType === "EXPOSURE_GIG" &&
              "Exposure rate fee"}
            {previewContract?.goodsAndServices?.paymentType === "DOOR_GIG" && "Door rate fee"}
          </Typography>
          <Typography className="text-sm text-[--text-color] font-craftWorkHeavy">
            {previewContract?.goodsAndServices?.paymentType === "FLAT_RATE" && (
              <>
                <span className="CraftworkGroteskHeavy">
                  {previewContract?.goodsAndServices?.flatRateCurrency}{" "}
                  {previewContract?.goodsAndServices?.flatRateAmount}
                </span>{" "}
                / {previewContract?.goodsAndServices?.flatRatePercentage} down
              </>
            )}
            {previewContract?.goodsAndServices?.paymentType === "EXPOSURE_GIG" && (
              <>
                <span className="CraftworkGroteskHeavy">
                  {previewContract?.goodsAndServices?.exposureGigCurrency}{" "}
                  {previewContract?.goodsAndServices?.exposureGigFee}
                </span>
              </>
            )}
            {previewContract?.goodsAndServices?.paymentType === "DOOR_GIG" && (
              <>
                <span className="CraftworkGroteskHeavy">
                  {"Entry fee: "} {previewContract?.goodsAndServices?.doorGigEntryFee}
                  <br />
                  {"Venue Capacity: "} {previewContract?.goodsAndServices?.venueCapacity}
                </span>{" "}
                <br />
                The door will be manned by the: {
                  previewContract?.goodsAndServices?.doorManagedBy
                }{" "}
                {previewContract?.goodsAndServices?.maximumPercentage}% of door with a guaranteed
                minimum of {previewContract?.goodsAndServices?.guaranteedMaximum}
              </>
            )}
          </Typography>
        </Box>
      </Box>
      {/* {open === "cancel" && <>
      <Typography className="text-sm text-[--text-color] font-craftWorkRegular py-3">
        What is the reason?
      </Typography>
      <TextField
        type="text"
        size="small"
        multiline
        rows={5}
        placeholder="Specify the reason"
        InputLabelProps={{ style: { color: "#EFEFEF" } }}
        sx={{
          "& input::placeholder": {
            color: "#EFEFEF",
            border: 0,
          },
          "& .MuiOutlinedInput-root": {
            color: "var(--text-color)",
            fontFamily: "var(--craftWorkRegular)",
          },
          "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderWidth: 0,
          },
          border: 0,
        }}
        className="!border !w-full !h-[120px] lg:mb-0 mb-[80px] !py-1 CraftworkGroteskRegular !border-[--text-color] rounded-[2px]"
      />
      </>
      } */}
      <Box
        className={` ${isMobile ? "fixed flex justify-between bg-[--bg-color] items-center px-4 py-3 left-0 right-0 bottom-0 border-[1px] border-[--divider-color]" : "flex justify-end gap-[24px]"}`}
      >
        <Button
          className=" !flex !gap-x-2 items-center !mt-5"
          onClick={handleClose}
          sx={{
            border: 0,
            "&.MuiButtonBase-root": {
              color: "white !important",
            },
          }}
        >
          <Typography className="!normal-case CraftworkGroteskHeavy text-center !underline !text-sm !leading-[15.4px] !text-[--text-color]">
            Keep it
          </Typography>
          <Clear className="text-[--text-color] text-xl" />
        </Button>

        <Button
          className="!bg-[--text-color] !flex !gap-x-1 !px-4 !py-2 items-center !mt-5"
          sx={{
            border: 0,
            "&.MuiButtonBase-root": {
              color: "white !important",
            },
          }}
          disabled={loading}
          onClick={handleAcceptRejectContract}
        >
          <Typography className="!normal-case CraftworkGroteskHeavy text-center !text-sm !leading-[15.4px] !text-[--bg-color]">
            {open === "accept"
              ? "Accept contract"
              : open === "cancel"
                ? "Cancel contract"
                : open === "decline"
                  ? "Decline contract"
                  : ""}
          </Typography>
          <SouthEast className="text-[--bg-color] text-xl" />
        </Button>
      </Box>
    </DialogContent>
  );

  return (
    <>
      {isMobile ? (
        <Drawer
          anchor="bottom"
          open={open}
          onClose={handleClose}
          sx={{
            "& .MuiPaper-root": {
              height: "98%",
              backgroundColor: "var(--bg-color)",
            },
          }}
        >
          {content}
        </Drawer>
      ) : (
        <Dialog
          open={open}
          maxWidth={false}
          sx={{ "& .MuiBackdrop-root": { backgroundColor: "#181B1BE5" } }}
        >
          {content}
        </Dialog>
      )}
    </>
  );
};

export default CancelContractDialog;
