import { Dialog, DialogContent, Typography } from "@mui/material";
import React from "react";
import DeleteSuccess from "@/assets/svg/act-type.svg/DeleteSuccess.svg";
import { Button } from "@/component";
import { SouthEast } from "@mui/icons-material";
import { useDispatch } from "react-redux";
import { getProfiles } from "@/store/slice/act/act.slice";

const ConfirmDeleteDialogSuccess = ({ open, handleClose, deleteSuccessMessage, params }) => {
  const dispatch = useDispatch();
  return (
    <Dialog open={open} maxWidth={false}>
      <DialogContent className="max-w-lg p-8 border border-[--divider-color] !bg-[--bg-color]">
        <DeleteSuccess className="w-12 h-12" />
        <Typography className="text-2xl text-[--text-color] CraftworkGroteskHeavy my-5">
          {deleteSuccessMessage}
        </Typography>
        <Button
          className="!normal-case flex gap-x-2 !bg-[--text-color] w-full"
          onClick={() => {
            handleClose("closePopup");
            dispatch(getProfiles(params));
          }}
        >
          <Typography className="text-sm text-[--bg-color] CraftworkGroteskHeavy">
            Back to my accounts
          </Typography>
          <SouthEast className="text-[--bg-color] " />
        </Button>
      </DialogContent>
    </Dialog>
  );
};

export default ConfirmDeleteDialogSuccess;
