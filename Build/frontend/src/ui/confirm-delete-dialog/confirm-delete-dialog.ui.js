import { Button } from "@/component";
import { Clear, SouthEast } from "@mui/icons-material";
import { Box, Dialog, DialogContent, Typography, TextField } from "@mui/material";
import React, { useState } from "react";

const ConfirmDeleteDialog = ({ open, handleClose, passwordConfirmHandler }) => {
  const [value, setValue] = useState("");
  return (
    <Dialog
      open={open}
      maxWidth={false}
      // sx={{ "& .MuiBackdrop-root": { backgroundColor: "#181B1BE5" } }}
    >
      <DialogContent className="p-8 border border-[--divider-color] !bg-[--bg-color]">
        <Typography className="text-2xl text-[--text-color] CraftworkGroteskHeavy">
          Confirm deletion
        </Typography>
        <TextField
          name="password"
          type="password"
          placeholder="Password"
          autoComplete="new-password"
          value={value}
          onChange={(e) => {
            setValue(e.target.value);
          }}
          size="small"
          sx={{
            "& input::placeholder": {
              color: "#EFEFEF",
              border: 0,
            },
            "& input": {
              color: "#EFEFEF",
              fontFamily: "var(--craftWorkRegular)",
            },
            "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            "& .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            border: 0,
          }}
          className="w-full lg:w-[435px] !border !py-1 my-4 !border-white rounded-[2px]"
        />
        <Box className="flex gap-6">
          <Button
            type="button"
            sx={{ border: 0 }}
            className="flex items-center !normal-case"
            onClick={() => handleClose("closePopup")}
          >
            <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy underline">
              Cancel
            </Typography>
            <Clear className="text-lg text-[--text-color]" />
          </Button>
          <Button
            type="button"
            sx={{
              border: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
            className="flex gap-2 items-center w-full !normal-case !bg-[--text-color]"
            onClick={() => passwordConfirmHandler(value)}
          >
            <Typography className="text-sm text-[--bg-color] CraftworkGroteskHeavy">
              Confirm
            </Typography>
            <SouthEast className="text-[--bg-color] text-2xl" />
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default ConfirmDeleteDialog;
