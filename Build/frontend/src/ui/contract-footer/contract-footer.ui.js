"use client";
import { <PERSON><PERSON> } from "@/component";
import { Clear, SouthEast } from "@mui/icons-material";
import { Box, Typography, useMediaQuery, useTheme } from "@mui/material";
import React, { useState } from "react";
//import Download from "@/assets/svg/Download.svg";
import { useDispatch, useSelector } from "react-redux";
import {
  acceptContracts,
  cancelContracts,
  declineContracts,
  resetBookingData,
  sendContracts,
} from "@/store/slice/booking/booking.slice";
import CancelContractDialog from "../cancel-contract-dialog/cancel-contract-dialog.ui";
import { useRouter, useSearchParams } from "next/navigation";
import { useLocale } from "next-intl";
import { previewContract as previewContractDispatch } from "@/store/slice/booking/booking.slice";
import { initializeStripePayment } from "@/store/slice/stripe/stripe.slice";
import { showSnackbar } from "@/utils/snackbar.utils";

const ContractFooter = ({ loading, setLoading }) => {
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  //const stompClient = useStompClient();
  const { previewContract } = useSelector((state) => state.booking);
  const dispatch = useDispatch();
  const [openPopupCancel, setOpenPopupCancel] = useState(false);
  const router = useRouter();
  const handleClosePopup = () => {
    setOpenPopupCancel(false);
  };
  const lang = useLocale();
  const searchParams = useSearchParams();

  const contractId = searchParams.get("contract-id") || previewContract?.contractId;

  // const initializePayment = () => {
  //   dispatch(initializeStripePayment(contractId))
  //     .unwrap()
  //     .then((response) => {
  //       localStorage.setItem("sessionId", response.data?.sessionId);
  //       if (response.data?.sessionId && response.data?.sessionUrl) {
  //         // router.replace(response.data?.sessionUrl);
  //         // -------------------------------
  //         window.open(response.data?.sessionUrl, "_self", "noopener,noreferrer");
  //       }
  //     })
  //     .catch((error) => {
  //       showSnackbar("error", error.message, "error");
  //     });
  // };

  // const handleOpenPopup = () => {
  //   setOpenPopup(true);
  // };
  // const handleSendMessage = () => {
  //   if (stompClient && stompClient.connected) {
  //     const msg = {
  //       sender: previewContract?.originatingUser,
  //       receiver: "<EMAIL>",
  //       messageType: "BOOKING_REQUEST",
  //       contractId: previewContract?.contractId,
  //       //timestamp: new Date().toISOString(),
  //       isSeen: false,
  //     };

  //     const headers = {
  //       Authorization: "Bearer " + getLocalStorage("access_token"),
  //     };

  //      stompClient.send("/api/v1/private/message", headers, JSON.stringify(msg));
  //   }
  // };

  const initializePayment = () => {
    dispatch(initializeStripePayment(contractId))
      .unwrap()
      .then((response) => {
        // Store session ID for payment tracking and post-payment navigation
        localStorage.setItem("sessionId", response.data?.sessionId);

        if (response.data?.sessionId && response.data?.sessionUrl) {
          window.open(response.data?.sessionUrl, "_self", "noopener,noreferrer");
        }
      })
      .catch((error) => {
        showSnackbar("error", error.message, "error");
      });
  };

  const handleSendContract = () => {
    setLoading(true);
    dispatch(sendContracts(previewContract?.contractId));
    dispatch(resetBookingData());
    router.push(`/${lang}/contracts/contracts-by-me`);
  };

  const handleNegotiateContract = () => {
    setLoading(true);
    router.push(`/${lang}/negotiate?contract-id=${previewContract?.contractId}`);
  };
  const handleAcceptRejectContract = () => {
    setLoading(true);
    if (openPopupCancel === "accept") {
      dispatch(acceptContracts(previewContract?.contractId))
        .unwrap()
        .then(() => {
          dispatch(previewContractDispatch(previewContract?.contractId))
            .unwrap()
            .then(() => {
              setLoading(false);
              handleClosePopup();
            });
        })
        .catch(() => {
          setLoading(false);
        });
    }

    if (openPopupCancel === "decline") {
      dispatch(declineContracts(previewContract?.contractId))
        .unwrap()
        .then(() => {
          dispatch(previewContractDispatch(previewContract?.contractId))
            .unwrap()
            .then(() => {
              setLoading(false);
              handleClosePopup();
            });
        })
        .catch(() => {
          setLoading(false);
        });
    }

    if (openPopupCancel === "cancel") {
      dispatch(cancelContracts(previewContract?.contractId))
        .unwrap()
        .then(() => {
          dispatch(previewContractDispatch(previewContract?.contractId))
            .unwrap()
            .then(() => {
              setLoading(false);
              handleClosePopup();
            });
        })
        .catch(() => {
          setLoading(false);
        });
    }
  };

  return (
    <Box className=" !flex !justify-between !fixed !bottom-0 !h-[79px] right-0 !bg-[--bg-color] !left-0 !border-t !border-t-[--divider-color] !z-20 !py-5 lg:!px-10 md:!px-6 !px-4">
      <Button className="!normal-case lg:flex md:flex gap-2 hidden">
        {/* <Typography className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline">
          Cancel request
        </Typography> */}
        {/* <Clear className="text-[--text-color] text-xl" /> */}
      </Button>
      <Box className="flex gap-3">
        <Button className="!normal-case lg:flex md:flex hidden">
          {/* <Typography
            className="text-sm text-[--text-color] CraftworkGroteskHeavy !underline"
            onClick={handleCapture}
          >
            Download
          </Typography>
          <Download className="text-xl" /> */}
        </Button>
        {/* <Button className="!normal-case flex !bg-[--text-color]">
          <Typography className="text-sm text-[--bg-color] CraftworkGroteskHeavy">
            {isSmallScreen ? "Distribute" : " Distribute contract"}
          </Typography>
          <SouthEast className="text-xl text-[--bg-color]" />
        </Button> */}
        {previewContract?.contractState === "CREATED" && (
          <Button className="!normal-case flex !bg-[--text-color]">
            <Typography
              className="text-sm text-[--bg-color] CraftworkGroteskHeavy"
              onClick={handleSendContract}
              // onClick={handleOpenPopup}
            >
              {/* {isSmallScreen ? "Create Promo" : "Create Promo materials"} */}
              {isSmallScreen ? "Send" : " Send contract"}
            </Typography>
            <SouthEast className="text-xl text-[--bg-color]" />
          </Button>
        )}

        {previewContract?.possibleActions?.includes("DECLINE") && (
          <>
            <Button className="!normal-case lg:flex md:flex gap-2 hidden">
              <Typography
                className="!text-[--text-color] !text-sm CraftworkGroteskHeavy !underline"
                onClick={() => setOpenPopupCancel("decline")}
              >
                Decline
              </Typography>
              <Clear className="text-[--text-color] text-xl" />
            </Button>
          </>
        )}
        {previewContract?.possibleActions?.includes("NEGOTIATE") && (
          <>
            <Button className="!normal-case flex !bg-[--text-color]">
              <Typography
                className="text-sm text-[--bg-color] CraftworkGroteskHeavy"
                onClick={handleNegotiateContract}
                // onClick={handleOpenPopup}
              >
                Negotiate
              </Typography>
              <SouthEast className="text-xl text-[--bg-color]" />
            </Button>
          </>
        )}
        {previewContract?.possibleActions?.includes("ACCEPT") && (
          <>
            <Button className="!normal-case flex !bg-[--text-color]">
              <Typography
                className="text-sm text-[--bg-color] CraftworkGroteskHeavy"
                onClick={() => setOpenPopupCancel("accept")}
                // onClick={handleOpenPopup}
              >
                Accept
              </Typography>
              <SouthEast className="text-xl text-[--bg-color]" />
            </Button>
          </>
        )}
        {previewContract?.possibleActions?.includes("CANCEL") && (
          <>
            <Button className="!normal-case flex !bg-[--text-color]">
              <Typography
                className="text-sm text-[--bg-color] CraftworkGroteskHeavy"
                onClick={() => setOpenPopupCancel("cancel")}
                // onClick={handleOpenPopup}
              >
                Cancel
              </Typography>
              <SouthEast className="text-xl text-[--bg-color]" />
            </Button>
          </>
        )}

        {previewContract?.paymentStatus?.toUpperCase() === "PAYMENT_REQUIRED" && (
          <Button
            className="!normal-case flex !bg-[--text-color]"
            onClick={() => initializePayment()}
          >
            <Typography className="text-sm text-[--bg-color] CraftworkGroteskHeavy">
              Proceed to Payment
            </Typography>
            <SouthEast className="text-xl text-[--bg-color]" />
          </Button>
        )}
      </Box>
      <CancelContractDialog
        open={openPopupCancel}
        handleClose={handleClosePopup}
        handleAcceptRejectContract={handleAcceptRejectContract}
        loading={loading}
      />
    </Box>
  );
};

export default ContractFooter;
