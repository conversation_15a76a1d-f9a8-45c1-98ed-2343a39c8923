"use client";
import { I<PERSON><PERSON><PERSON><PERSON>, InputAdornment, TextField, Typography } from "@mui/material";
import { Box } from "@mui/system";
import React, { useState } from "react";
import CalenderIcon from "@/assets/svg/CalenderIcon.svg";
import { Dropdown } from "@/component";
import { Clear } from "@mui/icons-material";
import OutlinedSearch from "@/assets/svg/OutlinedSearch.svg";
import { useTranslations } from "next-intl";
import Filter from "@/assets/svg/Filter.svg";

const ContractsFilter = ({ eventRequest }) => {
  const t = useTranslations("contractsFilter");
  const Display = ["Display", "Display", "Display"];
  const Profile = ["Profile", "Profile", "Profile"];
  const Status = ["Status", "Status", "Status"];
  const SortBy = ["Sort by", "Sort by", "Sort by"];
  const [selectedProfile, setSelectedProfile] = useState(Profile[0]);
  const [selectedSortBy, setSelectedSortBy] = useState(SortBy[0]);
  const [selectedDisplay, setSelectedDisplay] = useState(Display[0]);
  const [selectedStatus, setSelectedStatus] = useState(Status[0]);
  const [search, setSearch] = useState("");
  const handleSelectProfile = (value) => {
    setSelectedProfile(value);
  };

  const handleSelectDisplay = (value) => {
    setSelectedDisplay(value);
  };

  const handleSelectStatus = (value) => {
    setSelectedStatus(value);
  };

  const handleSelectSortBy = (value) => {
    setSelectedSortBy(value);
  };

  return (
    <Box className="!flex !justify-between !items-center">
      <Typography className="!text-[--text-color] !hidden lg:!inline CraftworkGroteskRegular !text-sm">
        {eventRequest} {t("totalRequest")}
      </Typography>
      <Box className="!flex !gap-x-2 !items-center">
        <Box className="lg:!flex !gap-x-3 !items-center !hidden">
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
            {t("date")}
          </Typography>
          <CalenderIcon className="!text-2xl" />
        </Box>
        <Dropdown
          options={Display}
          onSelect={handleSelectDisplay}
          selectedValue={selectedDisplay}
          title="Display"
          className="!text-[--text-color] !hidden lg:!inline"
        />
        <Dropdown
          options={Profile}
          onSelect={handleSelectProfile}
          selectedValue={selectedProfile}
          title="Profile"
          className="!text-[--text-color] !hidden lg:!inline"
        />
        <Dropdown
          options={Status}
          onSelect={handleSelectStatus}
          selectedValue={selectedStatus}
          title="Status"
          className="!text-[--text-color] !hidden lg:!inline"
        />
        <Dropdown
          options={SortBy}
          onSelect={handleSelectSortBy}
          selectedValue={selectedSortBy}
          title="Sort by"
          className="!text-[--text-color] !hidden lg:!inline"
        />
        <TextField
          size="small"
          placeholder={t("searchByKeyword")}
          className="Sora500 !text-[--text-color] !border !border-[--text-color] !rounded-[4px]"
          InputProps={{
            endAdornment: (
              <InputAdornment position="end" style={{ cursor: "pointer" }}>
                {search && (
                  <Clear
                    className="!text-[--text-color] !text-base"
                    onClick={() => setSearch("")}
                  />
                )}
              </InputAdornment>
            ),
            startAdornment: (
              <InputAdornment position="start" style={{ cursor: "pointer" }}>
                <OutlinedSearch className="!text-2xl" />
              </InputAdornment>
            ),
          }}
          InputLabelProps={{ style: { color: "#EFEFEF" } }}
          sx={{
            "& input::placeholder": {
              color: "#EFEFEF",
              border: 0,
            },
            "& input": {
              color: "#EFEFEF",
              fontFamily: "var(--craftWorkRegular)",
            },
            "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            "& .MuiOutlinedInput-notchedOutline": {
              borderWidth: 0,
            },
            border: 0,
          }}
          value={search}
          onChange={(event) => setSearch(event.target.value)}
        />
        <IconButton className="lg:hidden">
          <Filter className="!text-2xl" />
        </IconButton>
      </Box>
    </Box>
  );
};

export default ContractsFilter;
