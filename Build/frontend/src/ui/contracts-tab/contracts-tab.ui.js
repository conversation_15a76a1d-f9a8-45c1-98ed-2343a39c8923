"use client";
import React from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Box from "@mui/material/Box";
import Link from "next/link";
import classNames from "classnames";

function ContractTabs() {
  const [value, setValue] = React.useState(0);

  React.useEffect(() => {
    const activeTab = data.findIndex((tab) => tab.path === window.location.pathname);
    if (activeTab !== -1) {
      setValue(activeTab);
    }
  }, []);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const data = [
    { id: 0, label: "Generated by me", path: "/contracts/contracts-by-me" },
    { id: 1, label: "Generated by others", path: "/contracts/contracts-by-others" },
  ];

  return (
    <Box
      sx={{
        width: "100%",
        display: "flex",
      }}
    >
      <Tabs
        value={value}
        onChange={handleChange}
        aria-label=""
        selectionFollowsFocus
        sx={{
          "& .MuiTabs-indicator": {
            background: "var(--text-color)",
          },
          "& .MuiButtonBase-root": {
            padding: 0,
          },
        }}
      >
        {data.map((tab) => (
          <Link key={tab.id} href={tab.path} passHref>
            <Tab
              key={tab.id}
              label={tab.label}
              iconPosition="start"
              className={classNames(
                "!normal-case",
                "!text-sm",
                value === tab.id
                  ? "!text-[--text-color] CraftworkGroteskHeavy"
                  : "!text-[--text-color] CraftworkGroteskRegular !mx-5",
              )}
            />
          </Link>
        ))}
      </Tabs>
    </Box>
  );
}

export default ContractTabs;
