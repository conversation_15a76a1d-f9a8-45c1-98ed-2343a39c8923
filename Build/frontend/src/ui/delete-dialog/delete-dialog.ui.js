import { <PERSON><PERSON>, <PERSON>B<PERSON> } from "@/component";
import { Clear } from "@mui/icons-material";
import { Box, Dialog, DialogContent, Typography } from "@mui/material";
import React from "react";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import classNames from "classnames";

const DeleteDialog = ({ open, handleClose, deleteProfileHamdler, title = "", message = "" }) => {
  const [checked, setChecked] = React.useState(false);
  return (
    <Dialog
      open={open}
      maxWidth={false}
      // sx={{ "& .MuiBackdrop-root": { backgroundColor: "#181B1BE5" } }}
    >
      <DialogContent className="max-w-md border border-[--divider-color] !bg-[--bg-color]">
        <Typography className="text-2xl text-[--text-color] CraftworkGroteskHeavy">
          {title}
        </Typography>
        <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular !py-2">
          {message}
        </Typography>
        <Box className="flex items-center">
          <CheckBox
            className="!max-w-[24px]"
            sx={{ color: "#EFEFEF", marginRight: "5px" }}
            checked={checked}
            onChange={(e) => setChecked(e.target.checked)}
          />
          <label
            htmlFor="agree"
            className="!text-[--text-color] !text-sm CraftworkGroteskRegular cursor-pointer"
          >
            I agree
          </label>
        </Box>
        <Box className="flex gap-6">
          <Button
            sx={{ border: 0 }}
            className="flex items-center !normal-case"
            onClick={handleClose}
          >
            <Typography className="text-sm text-[--text-color] CraftworkGroteskHeavy underline">
              Cancel
            </Typography>
            <Clear className="text-lg text-[--text-color]" />
          </Button>
          <Button
            sx={{ border: 0 }}
            disabled={checked ? false : true}
            className={classNames("flex gap-2 items-center w-full !normal-case", {
              "!bg-[--disabled-color]": !checked,
              "!bg-[--delete-color]": checked,
            })}
            onClick={() => {
              deleteProfileHamdler();
              setChecked(false);
            }}
          >
            <Typography className="text-sm text-[--bg-color] CraftworkGroteskHeavy underline">
              Delete
            </Typography>
            <ArrowSouthEast />
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteDialog;
