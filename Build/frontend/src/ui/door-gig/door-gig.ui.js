"use client";
import { Dropdown } from "@/component";
import { Box, InputAdornment, TextField, Typography } from "@mui/material";
import React from "react";
import { Controller } from "react-hook-form";
import { CONTRACT_DETAILS } from "@/validation/auth/constants";

const DoorGigUi = ({ control, selectedOption, watch, errors }) => {
  const events = ["VENUE", "ACT"];
  // const cad = ["CAD", "CAD"];
  const isFlatRateSelected = selectedOption === "Door Gig" || selectedOption === "DOOR_GIG";
  const borderColor = isFlatRateSelected ? "var(--text-color)" : "var(--hide-color)";
  return (
    <Box className="ml-6">
      <Box className=" lg:w-[75%] w-full flex gap-x-4 items-start pt-2">
        <Typography className="text-sm  CraftworkGroteskRegular" sx={{ color: borderColor }}>
          Entry Fee
        </Typography>
        <Box className=" lg:w-[33%] w-[43%]">
          <Controller
            name="doorGigEntryFee"
            control={control}
            render={({ field }) => (
              <TextField
                type="number"
                size="small"
                {...field}
                inputProps={{
                  min: 0,
                  onChange: (e) => {
                    const maxLength = Math.pow(10, CONTRACT_DETAILS.FEE.MAX_LENGTH) - 1;
                    if (e.target.value > maxLength) {
                      e.target.value = e.target.value.slice(0, String(maxLength).length);
                    }
                  },
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Typography
                        className="CraftworkGroteskRegular text-sm "
                        sx={{ color: borderColor }}
                      >
                        $
                      </Typography>
                    </InputAdornment>
                  ),
                }}
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                    fontFamily: "var(--craftWorkRegular)",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  border: "1px solid",
                  borderColor: borderColor,
                }}
                disabled={!isFlatRateSelected}
                className=" !w-full !py-1 CraftworkGroteskRegular rounded-[2px]"
                placeholder="Amount"
              />
            )}
          />
          {errors?.doorGigEntryFee && (
            <Typography as="span" className="text-[12px] !text-red-600">
              {errors?.doorGigEntryFee?.message}
            </Typography>
          )}
        </Box>
        <Typography className="text-sm  CraftworkGroteskRegular" sx={{ color: borderColor }}>
          per person
        </Typography>
      </Box>
      <Box className=" lg:w-[75%] w-full flex flex-wrap gap-x-3 items-start pt-2">
        <Typography className="text-sm  CraftworkGroteskRegular" sx={{ color: borderColor }}>
          Venue Capacity
        </Typography>
        <Box className=" lg:w-[33%] w-[43%]">
          <Controller
            name="venueCapacity"
            control={control}
            render={({ field }) => (
              <TextField
                type="number"
                size="small"
                inputProps={{
                  min: 0,
                  onChange: (e) => {
                    const maxLength = Math.pow(10, CONTRACT_DETAILS.FEE.MAX_LENGTH) - 1;
                    if (e.target.value > maxLength) {
                      e.target.value = e.target.value.slice(0, String(maxLength).length);
                    }
                  },
                }}
                {...field}
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                disabled={!isFlatRateSelected}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                    fontFamily: "var(--craftWorkRegular)",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  border: "1px solid",
                  borderColor: borderColor,
                }}
                className="!w-full !py-1 CraftworkGroteskRegular rounded-[2px]"
                placeholder="Amount"
              />
            )}
          />
          {errors?.venueCapacity && (
            <Typography as="span" className="text-[12px] !text-red-600">
              {errors?.venueCapacity?.message}
            </Typography>
          )}
        </Box>
        {/* <Typography className="text-sm  CraftworkGroteskRegular" sx={{ color: borderColor }}>
          people
        </Typography> */}
      </Box>
      <Box className="lg:w-[65%] w-full flex gap-3 lg:!flex-nowrap md:!flex-nowrap !flex-wrap my-4 items-start">
        <Typography
          className="text-sm lg:w-full CraftworkGroteskRegular"
          sx={{ color: borderColor }}
        >
          Door Fee will be collected by the:
        </Typography>
        <Box className=" lg:w-[70%] md:w-[50%] w-full ">
          <Box sx={{ borderColor: borderColor }} className="!border !rounded-[2px] !py-1">
            <Controller
              name="doorManagedBy"
              control={control}
              render={({ field }) => (
                <Dropdown
                  onSelect={field.onChange}
                  options={events || []}
                  selectedValue={field.value}
                  title="Select"
                  disabled={!isFlatRateSelected}
                  sx={{ color: borderColor }}
                  className="!w-full"
                />
              )}
            />
          </Box>
          {errors?.doorManagedBy && (
            <Typography as="span" className="text-[12px] !text-red-600">
              {errors?.doorManagedBy?.message}
            </Typography>
          )}
        </Box>
      </Box>
      <Box className=" w-full flex gap-3 !flex-wrap my-4 items-start">
        <Typography className="text-sm  CraftworkGroteskRegular" sx={{ color: borderColor }}>
          The
        </Typography>
        <Box
          sx={{ borderColor: borderColor }}
          className="!border lg:w-[28%] md:w-[36%] w-[50%] !rounded-[2px] !py-3 text-white pl-2"
        >
          <Typography style={{ color: borderColor }}>
            {watch("doorManagedBy") && watch("doorManagedBy") === "VENUE" ? "VENUE" : "ACT"}
          </Typography>
          {/* <Controller
            name="venue"
            control={control}
            render={({ field }) => (
              <Dropdown
                onSelect={field.onChange}
                options={events || []}
                disabled={!isFlatRateSelected}
                selectedValue={field.value}
                title="Venue"
                sx={{ color: borderColor }}
                className="!w-full"
              />
            )}
          /> */}
        </Box>
        <Typography className="text-sm  CraftworkGroteskRegular" sx={{ color: borderColor }}>
          will pay the
        </Typography>
        <Box
          sx={{ borderColor: borderColor }}
          className="!border lg:w-[28%] md:w-[36%] w-[50%] !rounded-[2px] !py-3 text-white pl-2"
        >
          <Typography style={{ color: borderColor }}>
            {watch("doorManagedBy") && watch("doorManagedBy") === "VENUE" ? "ACT" : "VENUE"}
          </Typography>
          {/* <Controller
            name="venue"
            control={control}
            render={({ field }) => (
              <Dropdown
                onSelect={field.onChange}
                disabled={!isFlatRateSelected}
                options={events || []}
                selectedValue={field.value}
                title="Venue"
                sx={{ color: borderColor }}
                className="!w-full"
              />
            )}
          /> */}
        </Box>
        {/* <Typography className="text-sm  CraftworkGroteskRegular" sx={{ color: borderColor }}>
          a maximum of
        </Typography> */}
        <Box className=" lg:w-[28%] md:w-[43%] w-full">
          <Controller
            name="maximumPercentage"
            control={control}
            render={({ field }) => (
              <TextField
                type="number"
                size="small"
                inputProps={{
                  min: 0,
                  onChange: (e) => {
                    const maxLength = Math.pow(10, CONTRACT_DETAILS.FEE.MAX_LENGTH) - 1;
                    if (e.target.value > maxLength) {
                      e.target.value = e.target.value.slice(0, String(maxLength).length);
                    }
                  },
                }}
                {...field}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <Typography
                        className="CraftworkGroteskRegular text-sm "
                        sx={{ color: borderColor }}
                      >
                        %
                      </Typography>
                    </InputAdornment>
                  ),
                }}
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                disabled={!isFlatRateSelected}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                    fontFamily: "var(--craftWorkRegular)",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  border: "1px solid",
                  borderColor: borderColor,
                }}
                className="!w-full !py-1 CraftworkGroteskRegular rounded-[2px]"
                placeholder="Amount"
              />
            )}
          />
          {errors?.maximumPercentage && (
            <Typography as="span" className="text-[12px] !text-red-600">
              {errors?.maximumPercentage?.message}
            </Typography>
          )}
        </Box>
        <Typography className="text-sm  CraftworkGroteskRegular" sx={{ color: borderColor }}>
          of door with a guaranteed minimum of
        </Typography>
        <Box className=" lg:w-[28%] md:w-[43%] w-full">
          <Controller
            name="guaranteedMaximum"
            control={control}
            render={({ field }) => (
              <TextField
                type="number"
                size="small"
                {...field}
                inputProps={{
                  min: 0,
                  onChange: (e) => {
                    const maxLength = Math.pow(10, CONTRACT_DETAILS.FEE.MAX_LENGTH) - 1;
                    if (e.target.value > maxLength) {
                      e.target.value = e.target.value.slice(0, String(maxLength).length);
                    }
                  },
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Typography
                        className=" CraftworkGroteskRegular text-sm "
                        sx={{ color: borderColor }}
                      >
                        $
                      </Typography>
                    </InputAdornment>
                  ),
                }}
                disabled={!isFlatRateSelected}
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                    fontFamily: "var(--craftWorkRegular)",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  border: "1px solid",
                  borderColor: borderColor,
                }}
                className=" !w-full !py-1 CraftworkGroteskRegular rounded-[2px] "
                placeholder="Amount"
              />
            )}
          />
          {errors?.guaranteedMaximum && (
            <Typography as="span" className="text-[12px] !text-red-600">
              {errors?.guaranteedMaximum?.message}
            </Typography>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default DoorGigUi;
