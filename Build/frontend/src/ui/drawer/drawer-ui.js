"use client";
import { <PERSON><PERSON>, <PERSON>Box, Dropdown } from "@/component";
import { Clear, North, RadioButtonUnchecked, South, SouthEast } from "@mui/icons-material";
import {
  <PERSON>,
  Divider,
  Drawer,
  FormControlLabel,
  IconButton,
  Radio,
  RadioGroup,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import ReactDatePicker from "react-datepicker";
import { Controller, useForm } from "react-hook-form";
import RadioButtonIcon from "@/assets/svg/RadioButton.svg";
import DocumentSvg from "@/assets/svg/Document.svg";
import { useSelector } from "react-redux";

const DrawerUi = ({
  open,
  setOpenSaveSearch,
  handleClose,
  type,
  setSelectedProfile,
  selectedProfile,
}) => {
  const label = ["Today", "This week", "This month", "Choose dates"];
  const genreData = ["Solo", "Rock", "Pop", "Solo", "Rock", "Jazz", "Solo"];
  const { control } = useForm();
  const [showAllReviews, setShowAllReviews] = useState(false);
  const displayedReviews = showAllReviews ? genreData : genreData.slice(0, 4);
  const [dateRange, setDateRange] = useState([null, null]);
  const [startDate, endDate] = dateRange;
  const handleShowMore = () => {
    setShowAllReviews(!showAllReviews);
  };

  const { supportedProfiles } = useSelector((state) => state.common);

  const profiles = supportedProfiles;

  const handleSelectProfile = (value) => {
    if (value === "Select Profile") {
      value = "";
    }
    if (value === "Venue") {
      value = "VENUE_PROFILE";
    }
    if (value === "Act") {
      value = "ACT_PROFILE";
    }
    setSelectedProfile(value);
    handleClose();
  };
  return (
    <Drawer
      open={open}
      anchor="bottom"
      sx={{
        "& .MuiPaper-root": {
          height: "95%",
          backgroundColor: "var(--bg-color)",
        },
      }}
    >
      {type === "Profile" && (
        <>
          <Box className="px-4 pt-5">
            <Box className="flex justify-between">
              <Typography className="text-[--text-color] CraftworkGroteskHeavy text-2xl">
                Filter
              </Typography>
            </Box>
            <Box className="border border-[--text-color] rounded-[4px] mt-6">
              <Dropdown
                options={["Select Profile", ...profiles]}
                onSelect={handleSelectProfile}
                selectedValue={
                  selectedProfile === "ACT_PROFILE"
                    ? "Act"
                    : selectedProfile === "VENUE_PROFILE"
                      ? "Venue"
                      : selectedProfile
                }
                title="Select Profile"
                className="!text-[--text-color] w-full"
              />
            </Box>
          </Box>
          <Box
            className="flex justify-between bg-[--bg-color] px-4 py-5 border-t border-t-[--divider-color] bottom-0
        left-0 right-0 fixed"
          >
            <Button className="flex gap-1 items-center !normal-case" onClick={handleClose}>
              <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy underline">
                Cancel
              </Typography>
              <Clear className="text-[--text-color] text-xl" />
            </Button>
            <Button
              sx={{
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
              onClick={handleClose}
              className="flex gap-1 items-center !bg-[--text-color] rounded-[4px] !normal-case"
            >
              <Typography className="text-[--bg-color] text-sm CraftworkGroteskHeavy ">
                Ok
              </Typography>
              <SouthEast className="text-[--bg-color]" />
            </Button>
          </Box>
        </>
      )}
      {type === "Search" && (
        <>
          <Box className="px-4 pt-5">
            <Box className="flex justify-between">
              <Typography className="text-[--text-color] CraftworkGroteskHeavy text-2xl">
                Sort by
              </Typography>
              <Button className="flex gap-1 !normal-case" onClick={() => setOpenSaveSearch()}>
                <DocumentSvg className="text-2xl" />
                <Typography className="text-[--text-color] underline text-sm CraftworkGroteskHeavy">
                  Save search
                </Typography>
              </Button>
            </Box>
            <Controller
              name="suitableForAdultsOrChildren"
              control={control}
              render={({ field }) => (
                <RadioGroup value={field.value} onChange={(e) => field.onChange(e.target.value)}>
                  {label.map((data, id) => (
                    <FormControlLabel
                      key={id}
                      value={data}
                      control={
                        <Radio
                          icon={<RadioButtonUnchecked className="!w-6 !h-6 !text-[--text-color]" />}
                          checkedIcon={<RadioButtonIcon className="!w-6 !h-6" />}
                        />
                      }
                      label={
                        <Typography
                          className="!text-[--text-color] !normal-case Poppins400 !text-sm"
                          htmlFor={`radio-${id}`}
                        >
                          {data}
                        </Typography>
                      }
                    />
                  ))}
                </RadioGroup>
              )}
            />
          </Box>
          <Box className="px-4 pt-3 pb-24">
            <Box className="flex justify-between">
              <Typography className="text-[--text-color] text-lg CraftworkGroteskMedium">
                Date
              </Typography>
              <IconButton>
                <Clear className="text-[--text-color] text-lg" />
              </IconButton>
            </Box>
            <Controller
              name="suitableForAdultsOrChildren"
              control={control}
              render={({ field }) => (
                <RadioGroup value={field.value} onChange={(e) => field.onChange(e.target.value)}>
                  {label.map((data, id) => (
                    <FormControlLabel
                      key={id}
                      value={data}
                      control={
                        <Radio
                          icon={<RadioButtonUnchecked className="!w-6 !h-6 !text-[--text-color]" />}
                          checkedIcon={<RadioButtonIcon className="!w-6 !h-6" />}
                        />
                      }
                      label={
                        <Typography
                          className="!text-[--text-color] !normal-case Poppins400 !text-sm"
                          htmlFor={`radio-${id}`}
                        >
                          {data}
                        </Typography>
                      }
                    />
                  ))}
                </RadioGroup>
              )}
            />
            <ReactDatePicker
              selectsRange={true}
              startDate={startDate}
              endDate={endDate}
              // showIcon
              placeholderText="mm/dd/yy - mm/dd/yy"
              onChange={(update) => {
                setDateRange(update);
              }}
              className="react-date-picker-class"
            />
            <Divider
              sx={{ borderTop: "thin solid var(--divider-color)" }}
              className="w-full mt-4"
            />
            <Box className="flex justify-between mt-2">
              <Typography className="text-[--text-color] text-lg CraftworkGroteskMedium">
                Genre (type)
              </Typography>
              <IconButton>
                <Clear className="text-[--text-color] text-lg" />
              </IconButton>
            </Box>
            {displayedReviews.map((data, index) => (
              <Controller
                key={index}
                name="stayWithinCountry"
                control={control}
                render={({ field }) => (
                  <Box key={index} className="flex items-center !mb-2">
                    <CheckBox
                      className="!max-w-[24px]"
                      sx={{ color: "#EFEFEF", marginRight: "5px" }}
                      checked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    />
                    <label
                      htmlFor={`location_${index}`} // Make sure each label has a unique htmlFor
                      className="!text-[--text-color] !text-sm Poppins400 cursor-pointer"
                    >
                      {data}
                    </label>
                  </Box>
                )}
              />
            ))}
            <Box className="flex justify-center">
              <Button
                className="flex gap-1 items-center !normal-case"
                sx={{
                  border: 0,
                  "&.MuiButtonBase-root": {
                    color: "white !important",
                  },
                }}
                onClick={handleShowMore}
              >
                {showAllReviews ? (
                  <>
                    <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy underline">
                      Show less
                    </Typography>
                    <North className="text-xl text-[--text-color]" />
                  </>
                ) : (
                  <>
                    <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy underline">
                      Show more
                    </Typography>
                    <South className="text-xl text-[--text-color]" />
                  </>
                )}
              </Button>
            </Box>
          </Box>
          <Box
            className="flex justify-between bg-[--bg-color] px-4 py-5 border-t border-t-[--divider-color] bottom-0
        left-0 right-0 fixed"
          >
            <Button className="flex gap-1 items-center !normal-case" onClick={handleClose}>
              <Typography className="text-[--text-color] text-sm CraftworkGroteskHeavy underline">
                Cancel
              </Typography>
              <Clear className="text-[--text-color] text-xl" />
            </Button>
            <Button
              sx={{
                border: 0,
                "&.MuiButtonBase-root": {
                  color: "white !important",
                },
              }}
              className="flex gap-1 items-center !bg-[--text-color] rounded-[4px] !normal-case"
            >
              <Typography className="text-[--bg-color] text-sm CraftworkGroteskHeavy ">
                Save
              </Typography>
              <SouthEast className="text-[--bg-color]" />
            </Button>
          </Box>
        </>
      )}
    </Drawer>
  );
};

export default DrawerUi;
