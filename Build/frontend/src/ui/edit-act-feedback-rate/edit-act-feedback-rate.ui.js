import { Box, Typography } from "@mui/material";
import React from "react";
// import FilledStarSvg from "@/assets/svg/FilledStarSvg.svg";
import Rating from "@/component/rating/rating.components";

const EditActFeedbackRate = ({ rateData }) => {
  return (
    <>
      <Box className="!border !p-2 !mb-2 !border-[--divider-color] !rounded-[4px] !w-full !flex !justify-between !items-center">
        <Typography className="!text-sm !text-[--text-color] CraftworkGrotestRegular">
          Entertainment Value
        </Typography>
        <Box className="!flex !gap-x-2">
          <Rating value={rateData?.[0]?.rating} readOnly />
          <Typography className="!text-sm !text-[--text-color] CraftworkGrotestRegular">
            {rateData?.rating}
          </Typography>
        </Box>
      </Box>

      <Box className="!border !p-2 !mb-2 !border-[--divider-color] !rounded-[4px] !w-full !flex !justify-between !items-center">
        <Typography className="!text-sm !text-[--text-color] CraftworkGrotestRegular">
          Professionalism
        </Typography>
        <Box className="!flex !gap-x-2">
          <Rating value={rateData?.[1]?.rating} readOnly />
          <Typography className="!text-sm !text-[--text-color] CraftworkGrotestRegular">
            {rateData?.professionalismRating}
          </Typography>
        </Box>
      </Box>

      <Box className="!border !p-2 !mb-2 !border-[--divider-color] !rounded-[4px] !w-full !flex !justify-between !items-center">
        <Typography className="!text-sm !text-[--text-color] CraftworkGrotestRegular">
          Draw
        </Typography>
        <Box className="!flex !gap-x-2">
          <Rating value={rateData?.[2]?.rating} readOnly />
          <Typography className="!text-sm !text-[--text-color] CraftworkGrotestRegular">
            {rateData?.drawRating}
          </Typography>
        </Box>
      </Box>
    </>
  );
};

export default EditActFeedbackRate;
