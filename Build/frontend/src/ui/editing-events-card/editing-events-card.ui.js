import { Box, Divider, Typography } from "@mui/material";
import EastIcon from "@mui/icons-material/East";
import React from "react";
import Link from "next/link";
import InprogressTag from "@/common/(tags)/tags-inprogress/tags-inprogress.common";

import { useLocale } from "next-intl";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

const EditingEventsCard = ({ editEventData }) => {
  const lang = useLocale();

  const formatDateTime = (dateString) => {
    const timeZone = "America/Toronto"; // Set the timezone
    const date = dayjs(dateString).tz(timeZone);

    return `${date.format("ddd")} | ${date.format("hA")}, EST`;
  };
  return (
    <>
      {editEventData?.content?.length > 0 &&
        editEventData?.content?.map(
          (event) =>
            event && (
              <Box
                key={event.eventId}
                className="lg:!flex p-4 border border-[--divider-color] bg-[--footer-bg] lg:!mr-10 !mr-4 mb-3 rounded-[4px]"
              >
                <Box className="flex gap-x-4 lg:!w-1/2">
                  <Box className="px-2">
                    <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                      {event.scheduleTime.startDate &&
                        dayjs(event.scheduleTime.startDate).tz("America/Toronto").format("MMM")}
                    </Typography>
                    <Typography className="text-2xl text-[--text-color] CraftworkGroteskMedium">
                      {event.scheduleTime.startDate &&
                        dayjs(event.scheduleTime.startDate).tz("America/Toronto").date()}
                    </Typography>
                  </Box>
                  <Divider
                    orientation="vertical"
                    sx={{ borderRight: "1px solid rgba(76, 78, 79, 0.5)" }}
                    className="h-14"
                  />
                  <Box className="px-2">
                    <Typography className="text-2xl text-[--text-color] CraftworkGroteskMedium">
                      {event.eventName}
                    </Typography>
                    <Typography className="text-sm text-[--text-color] CraftworkGroteskRegular">
                      {/* {event.scheduleTime.startDate && dayjs(event.scheduleTime.startDate).tz("America/Toronto").format("ddd")} */}
                      {formatDateTime(event.scheduleTime.startDate)}
                    </Typography>
                  </Box>
                </Box>
                <Box className="!w-full lg:!flex md:!flex md:!items-center md:!justify-between lg:items-center !mt-1 lg:justify-between">
                  <InprogressTag />
                  <Box className="p-4 !hidden lg:!inline">
                    {/* <Typography className="mb-2 text-sm text-[--text-color] CraftworkGroteskRegular">
                {event.taskDone} done
              </Typography> */}
                    {/* <LinearProgress
                variant="determinate"
                value={+event.value}
                className="!min-w-[161px]"
                sx={{
                  borderRadius: "20px",
                  backgroundColor: "var(--divider-color)",
                  height: "2px",
                  "& .MuiLinearProgress-barColorPrimary": {
                    backgroundColor: "var(--inprogress-color)",
                  },
                }}
              /> */}
                  </Box>
                  <Box className="!flex !justify-between !items-center !mt-2">
                    {/* <Box className="p-4 !flex !items-center lg:!hidden !gap-2"> */}
                    {/* <Typography className=" text-sm text-[--text-color] CraftworkGroteskRegular">
                  {event.taskDone}
                </Typography> */}
                    {/* <LinearProgress
                  variant="determinate"
                  value={+event.value}
                  className="!min-w-[61px]"
                  sx={{
                    borderRadius: "20px",
                    backgroundColor: "var(--divider-color)",
                    height: "2px",
                    "& .MuiLinearProgress-barColorPrimary": {
                      backgroundColor: "var(--inprogress-color)",
                    },
                  }}
                /> */}
                    {/* </Box> */}
                    {event?.editable && (
                      <Link href={`/${lang}/event/${event.eventId}/main-info?redirect=dashboard`}>
                        <Box className="flex gap-2">
                          <Typography className="underline text-[--text-color] CraftworkGroteskHeavy text-sm">
                            Continue editing
                          </Typography>
                          <EastIcon className="text-[--text-color]" />
                        </Box>
                      </Link>
                    )}
                  </Box>
                </Box>
              </Box>
            ),
        )}

      {/* <Box className="flex justify-center lg:mb-0 mb-20">
        {editEventData?.totalPages > 1 && (
          <Paginate totalRecords={editEventData?.totalElements} perPageRecord={editEventData.size} />
        )}
      </Box>  */}
    </>
  );
};

export default EditingEventsCard;
