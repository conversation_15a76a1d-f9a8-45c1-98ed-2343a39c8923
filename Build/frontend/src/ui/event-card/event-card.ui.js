import { Box, Typography, IconButton, useMediaQuery, useTheme } from "@mui/material";
import React, { useRef } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import CalendarIcon from "@/assets/svg/CalenderIcon.svg";
//import LocationSvg from "@/assets/svg/LocationSvg.svg";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { Button, CommonImage } from "@/component";
import TicketSvg from "@/assets/svg/Ticket.svg";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { generateLocationString } from "@/utils";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import Avatar from "@/assets/png/Avatar.png";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
dayjs.extend(utc);
dayjs.extend(timezone);

const EventCard = ({ eventData }) => {
  const sliderRef = useRef(null);
  const router = useRouter();
  const lang = useLocale();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  // Custom arrow components
  const NextArrow = (props) => {
    const { onClick } = props;
    return (
      <IconButton
        onClick={onClick}
        className="!absolute !right-[-15px] sm:!right-[-25px] md:!right-[-40px] !top-1/2 !transform !-translate-y-1/2 !z-10 !bg-[--footer-bg] hover:!bg-[--divider-color]"
        sx={{
          display: eventData?.length <= 1 ? "none" : "flex",
        }}
      >
        <ArrowForwardIosIcon
          className="!text-[--text-color]"
          fontSize={isMobile ? "small" : "medium"}
        />
      </IconButton>
    );
  };

  const PrevArrow = (props) => {
    const { onClick } = props;
    return (
      <IconButton
        onClick={onClick}
        className="!absolute  !left-[-15px] flex justify-center items-center sm:!left-[-25px] md:!left-[-40px] !top-1/2 !transform !-translate-y-1/2 !z-10 !bg-[--footer-bg] hover:!bg-[--divider-color]"
        sx={{
          display: eventData?.length <= 1 ? "none" : "flex",
        }}
      >
        <ArrowBackIosNewIcon
          className="!text-[--text-color]  "
          fontSize={isMobile ? "small" : "14px"}
        />
      </IconButton>
    );
  };

  const formatDateTime = (dateString) => {
    const timeZone = "America/Toronto";
    const date = dayjs(dateString).tz(timeZone);
    return `${date.format("MMM D, YYYY")} | ${date.format("hA")}, EST`;
  };

  const settings = {
    dots: false,
    infinite: false,
    speed: 1000,
    slidesToShow: Math.min(4, eventData?.length || 1),
    slidesToScroll: 2,
    autoplay: false,
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />,
    initialSlide: 0,
    responsive: [
      {
        breakpoint: 1224,
        settings: {
          slidesToShow: Math.min(3, eventData?.length || 1),
          slidesToScroll: 2,
        },
      },
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: Math.min(2, eventData?.length || 1),
          slidesToScroll: 2,
        },
      },
      {
        breakpoint: 900,
        settings: {
          slidesToShow: Math.min(2, eventData?.length || 1),
          slidesToScroll: 2,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          arrows: !isMobile,
        },
      },
    ],
  };

  const renderCard = (data) => (
    <Box
      key={data.eventId}
      className="!border !border-[--divider-color] !mr-4 !p-4 !bg-[--footer-bg] !mt-4"
      sx={{ width: "100%", height: "100%" }}
    >
      <Typography className="!text-[--text-color] !text-2xl CraftworkGroteskMedium">
        {data.eventName}
      </Typography>
      <Box className="!flex !gap-3 !pt-3">
        <CalendarIcon />
        <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
          {formatDateTime(data.scheduleTime.startDate)}
        </Typography>
      </Box>
      <Box className="!flex !gap-3 !pt-3">
        {/* <LocationSvg className="!w-5 !h-5" /> */}
        <CommonImage
          src={data?.venueImageUrls?.[0] ?? Avatar}
          alt="avatar"
          className="size-6 rounded-full"
          width={10}
          height={10}
        />
        <Box>
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular">
            {data.venueName}
          </Typography>
          <Typography className="!text-[--hide-color] !text-sm CraftworkGroteskRegular">
            {generateLocationString(data?.venueLocation).length > 30
              ? `${generateLocationString(data?.venueLocation).substring(0, 30)}...`
              : generateLocationString(data?.venueLocation)}
          </Typography>
        </Box>
      </Box>
      {data?.actProfileInfoList?.[0] && (
        <Box className="!flex !gap-5 !pt-3">
          <CommonImage
            src={data?.actProfileInfoList[0]?.actImageUrls?.[0] ?? Avatar}
            alt="avatar"
            className="size-6 rounded-full"
            width={10}
            height={10}
          />
          <Typography className="text-[--text-color] text-sm font-craftWorkRegular">
            {data?.actProfileInfoList[0]?.actName}
          </Typography>
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskRegular" />
        </Box>
      )}

      <Box className="!flex !gap-2 !my-6">
        {data.isTicket === true && (
          <Button
            className="!border-[2px] !border-[--text-color] !rounded-[4px]"
            sx={{
              minWidth: 0,
              border: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
          >
            <TicketSvg className="!w-6 !h-6" />
          </Button>
        )}
        <Button
          className="!bg-[--text-color] !w-full !gap-x-4 !py-3"
          sx={{
            minWidth: 0,
            "&.MuiButtonBase-root": {
              color: "white !important",
            },
          }}
          onClick={() => {
            router.push(`/${lang}/event/${data.eventId}/${data.editable ? "main-info" : "view"}`);
          }}
        >
          <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
            View Event
          </Typography>
          <ArrowSouthEast alt="arrow" />
        </Button>
      </Box>
    </Box>
  );

  return (
    <Box className="object-cover" sx={{ position: "relative" }}>
      {eventData?.length >= 4 ? (
        <Box sx={{ position: "relative", px: { xs: "15px", sm: "25px", md: "40px" } }}>
          <Slider ref={sliderRef} {...settings} className="dashboard-slider">
            {eventData?.map((data) => (
              <Box key={data.eventId} sx={{ px: 1 }}>
                {renderCard(data)}
              </Box>
            ))}
          </Slider>
        </Box>
      ) : (
        <Box className="!flex gap-3 !flex-wrap">
          {eventData?.map((data) => (
            <Box key={data.eventId}>{renderCard(data)}</Box>
          ))}
        </Box>
      )}
    </Box>
  );
};

export default EventCard;
