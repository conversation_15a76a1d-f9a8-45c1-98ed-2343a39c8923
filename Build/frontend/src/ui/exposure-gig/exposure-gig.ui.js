import { Dropdown } from "@/component";
import { Box, TextField, Typography } from "@mui/material";
import React from "react";
import { Controller } from "react-hook-form";
import { CONTRACT_DETAILS } from "@/validation/auth/constants";

const ExposureGigUi = ({ control, selectedOption, currencies, errors }) => {
  // const cad = ["CAN", "CAN"];
  const isFlatRateSelected = selectedOption === "Exposure Gig" || selectedOption === "EXPOSURE_GIG";
  const borderColor = isFlatRateSelected ? "var(--text-color)" : "var(--hide-color)";

  return (
    <Box className="ml-6">
      <Typography
        sx={{ color: borderColor }}
        className="text-sm w-full CraftworkGroteskRegular py-1"
      >
        Honorarium applies
      </Typography>
      <Box className="lg:w-[50%] md:w-[50%] w-full flex gap-x-3 my-2 items-start">
        <Box className="w-full">
          <Controller
            name="exposureGigFee"
            control={control}
            render={({ field }) => (
              <TextField
                type="text"
                size="small"
                {...field}
                inputProps={{
                  maxLength: CONTRACT_DETAILS.FEE.MAX_LENGTH,
                }}
                InputLabelProps={{ style: { color: "#EFEFEF" } }}
                disabled={!isFlatRateSelected}
                sx={{
                  "& input::placeholder": {
                    color: "#EFEFEF",
                    border: 0,
                  },
                  "& input": {
                    color: "#EFEFEF",
                    fontFamily: "var(--craftWorkRegular)",
                  },
                  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderWidth: 0,
                  },
                  border: "1px solid",
                  borderColor: borderColor,
                }}
                className="!w-full !py-1 CraftworkGroteskRegular rounded-[2px]"
                defaultValue="0"
              />
            )}
          />
          {errors?.exposureGigFee && (
            <Typography as="span" className="text-[12px] !text-red-600">
              {errors?.exposureGigFee?.message}
            </Typography>
          )}
        </Box>
        <Box className="w-full">
          <Box
            sx={{
              borderColor: borderColor,
            }}
            className="py-1 rounded-[2px] border"
          >
            <Controller
              name="exposureGigCurrency"
              control={control}
              render={({ field }) => (
                <Dropdown
                  onSelect={field.onChange}
                  options={currencies || []}
                  selectedValue={field.value}
                  disabled={!isFlatRateSelected}
                  title="CAD"
                  sx={{ color: borderColor }}
                  className=" !w-full"
                />
              )}
            />
          </Box>
          {errors?.exposureGigCurrency && (
            <Typography as="span" className="text-[12px] !text-red-600">
              {errors?.exposureGigCurrency?.message}
            </Typography>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default ExposureGigUi;
