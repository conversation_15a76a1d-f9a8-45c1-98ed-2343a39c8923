import { Dropdown } from "@/component";
import { Box, TextField, Typography } from "@mui/material";
import React from "react";
import { Controller } from "react-hook-form";
import { CONTRACT_DETAILS } from "@/validation/auth/constants";

const FlatRateUi = ({ control, selectedOption, className, currencies, errors }) => {
  // const roles = ["CAD", "CAD", "CAD"];
  // const percentages = ["10", "20", "30"];
  const isFlatRateSelected = selectedOption === "Flat rate" || selectedOption === "FLAT_RATE";
  const borderColor = isFlatRateSelected ? "var(--text-color)" : "var(--hide-color)";

  return (
    <Box
      className={`${className} w-full ml-6 flex gap-3 lg:!flex-nowrap md:!flex-nowrap !flex-wrap my-2 items-start`}
    >
      <Box className="lg:w-full md:w-full w-[43%]">
        <Controller
          name="flatRateAmount"
          control={control}
          render={({ field }) => (
            <TextField
              type="text"
              size="small"
              {...field}
              inputProps={{
                maxLength: CONTRACT_DETAILS.FEE.MAX_LENGTH,
              }}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: "1px solid",
                borderColor: borderColor,
              }}
              className="!w-full !py-1 CraftworkGroteskRegular rounded-[2px]"
              defaultValue=""
              disabled={!isFlatRateSelected}
            />
          )}
        />
        {errors?.flatRateAmount && (
          <Typography as="span" className="text-[12px] text-red-600">
            {errors?.flatRateAmount?.message}
          </Typography>
        )}
      </Box>
      <Box className=" lg:w-full md:w-full w-[43%]">
        <Box
          sx={{
            borderColor: borderColor,
          }}
          className="border !rounded-[2px] !py-1"
        >
          <Controller
            name="flatRateCurrency"
            control={control}
            render={({ field }) => (
              <Dropdown
                onSelect={field.onChange}
                options={currencies || []}
                selectedValue={field.value}
                disabled={!isFlatRateSelected}
                title="Select currency"
                sx={{ color: borderColor }}
                className=" !w-full"
              />
            )}
          />
        </Box>
        {errors?.flatRateCurrency && (
          <Typography as="span" className="text-[12px] text-red-600">
            {errors?.flatRateCurrency?.message}
          </Typography>
        )}
      </Box>
      <Box className="lg:w-full md:w-full w-[43%]">
        <Controller
          name="flatRatePercentage"
          control={control}
          render={({ field }) => (
            <TextField
              type="text"
              size="small"
              {...field}
              InputLabelProps={{ style: { color: "#EFEFEF" } }}
              inputProps={{
                maxLength: CONTRACT_DETAILS.FEE.MAX_LENGTH,
              }}
              sx={{
                "& input::placeholder": {
                  color: "#EFEFEF",
                  border: 0,
                },
                "& input": {
                  color: "#EFEFEF",
                  fontFamily: "var(--craftWorkRegular)",
                },
                "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 0,
                },
                border: "1px solid",
                borderColor: borderColor,
              }}
              className="!w-full !py-1 CraftworkGroteskRegular rounded-[2px]"
              defaultValue=""
              disabled={!isFlatRateSelected}
            />
          )}
        />
        {errors?.flatRatePercentage && (
          <Typography as="span" className="text-[12px] text-red-600">
            {errors?.flatRatePercentage?.message}
          </Typography>
        )}
      </Box>

      <Typography
        sx={{ color: borderColor }}
        className={"text-sm lg:w-full md:w-full w-[43%] CraftworkGroteskRegular"}
      >
        Of deposit required. Balance payment is due on the date of performance.
      </Typography>
    </Box>
  );
};

export default FlatRateUi;
