"use client";
import * as React from "react";
import Button from "@mui/material/Button";
import MenuIcon from "@mui/icons-material/Menu";
import { Box, Divider, Drawer, IconButton, List, ListItem, Typography } from "@mui/material";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import Link from "next/link";
import { Dropdown } from "@/component";
import { useSelector } from "react-redux";
import OutlinedUser from "@/assets/svg/OutlinedUser.svg";
import { useRouter } from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
import { locales } from "../../../navigation";
import LogoComponent from "@/common/logo-component/logo-component.common";

// import { Clear } from "@mui/icons-material";

export default function HomeHeaderSidebar() {
  const t = useTranslations("Header");
  const router = useRouter();
  const lang = useLocale();
  const options = [
    { id: 0, text: t("aboutUs"), path: "/" },
    { id: 1, text: t("benefits"), path: "/" },
    { id: 2, text: t("pricing"), path: "/" },
    { id: 3, text: t("contactUs"), path: "/" },
  ];
  const cityOptions = ["Ottawa", "Victoria", "Toronto", "	Edmonton"];
  const languageOptions = locales;

  const [open, setOpen] = React.useState(false);
  const [selectedCity, setSelectedCity] = React.useState(cityOptions[0]);

  const [selectedLanguage, setSelectedLanguage] = React.useState(useLocale());
  const [startTransition] = React.useTransition();
  const { token } = useSelector((state) => state.login);
  const handleSelectCity = (value) => {
    setSelectedCity(value);
  };
  const handleSelectLanguage = (value) => {
    startTransition(() => {
      router.replace(`/${value}`);
    });
    setLocalStorage("lang", value);
    setSelectedLanguage(value);
  };

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <Box className="!h-[72px] !border-b !border-b-[--divider-color] bg-[--bg-color] !flex !items-center !px-8 !justify-between">
      <IconButton onClick={handleClickOpen}>
        <MenuIcon className="text-[--text-color] " />
      </IconButton>
      {/* <Dialog
        open={open}
        onClose={handleClose}
        PaperProps={{
          style: {
            position: "absolute",
            top: "20px",
            left: "20px",
          },
        }}
      > */}
      {/* <DialogContent> */}
      <Drawer
        open={open}
        anchor="top"
        onClose={handleClose}
        sx={{
          "& .MuiDrawer-paper": {
            marginTop: "72px",
            width: "100%",
          },
        }}
      >
        <List className="bg-[--bg-color]">
          {options.map((option) => (
            <React.Fragment key={option.id}>
              <Link href={option.path}>
                <ListItem className="CraftworkGroteskMedium text-[--text-color] !text-base">
                  {option.text}
                </ListItem>
              </Link>
              <Divider
                sx={{ borderBottom: "thin solid var(--divider-color)" }}
                className="my-2 w-full"
              />
            </React.Fragment>
          ))}
        </List>
        <Box className="!flex !items-center bg-[--bg-color] !gap-2">
          <Box className="!flex">
            <Dropdown
              options={cityOptions}
              onSelect={handleSelectCity}
              selectedValue={selectedCity}
              title="Select Location"
              className="!text-[--text-color]"
            />
          </Box>
          <Box className="!flex !items-center !gap2">
            <Dropdown
              options={languageOptions}
              onSelect={handleSelectLanguage}
              selectedValue={selectedLanguage}
              title="Select Language"
              className="!text-[--text-color]"
            />
          </Box>
        </Box>
      </Drawer>
      {/* </DialogContent>
      </Dialog> */}
      <Link href={token ? `/${lang}/search?profileType=ACT_PROFILE&distance=0` : `/${lang}`}>
        {/* <Logo className="!w-[70px] !h-6" /> */}
        <LogoComponent />
      </Link>

      {!token || token === null ? (
        <Link href={`/${lang}/login`}>
          <Button className="!bg-[--text-color] !flex !gap-x-2 !px-2 !py-3" sx={{ px: 0 }}>
            <Typography className="!normal-case !hidden lg:!inline md:!inline CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
              Register / Log in
            </Typography>
            <ArrowSouthEast alt="arrow" />
          </Button>
        </Link>
      ) : (
        <Link href={`/${lang}/dashboard`}>
          {" "}
          <OutlinedUser className="!w-6 !h-6 !cursor-pointer" />{" "}
        </Link>
      )}
    </Box>
  );
}
