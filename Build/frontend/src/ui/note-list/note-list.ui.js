import { useTranslations } from "next-intl";

const { List, Typography, ListItem } = require("@mui/material");

const NoteList = ({ notes }) => {
  const t = useTranslations("noteList");
  return (
    <List>
      <Typography className="!text-sm !text-[--text-color] CraftworkGroteskRegular">
        {t("pleaseNote")}:
      </Typography>
      {notes.map((note, index) => (
        <ListItem key={index} className="!pl-2" style={{ listStyleType: "disc", padding: 0 }}>
          <Typography className="!text-sm !text-[--text-color] CraftworkGroteskRegular">
            {`\u2022 ${note}`}
          </Typography>
        </ListItem>
      ))}
    </List>
  );
};

export default NoteList;
