"use client";
import { Box, Dialog, DialogActions, DialogContent, IconButton, Typography } from "@mui/material";
import React from "react";
import ExpireTokenEye from "@/assets/svg/ExpireTokenEye.svg";
import { Clear, SouthEast } from "@mui/icons-material";
import { Button } from "@/component";
import ArrowSouthEast from "@/assets/svg/ArrowSouthEast.svg";
import { useAppContext } from "@/context/token-expired/token.expired.context";
import Link from "next/link";
import { useLocale } from "next-intl";

const TokenExpirePopup = () => {
  const lang = useLocale();
  const { isTokenExpired, closeTokenExpiredPopup } = useAppContext();

  const handleClose = () => {
    closeTokenExpiredPopup();
  };
  return (
    <Dialog
      open={isTokenExpired}
      onClose={handleClose}
      maxWidth={false}
      sx={{ "& .MuiBackdrop-root": { backgroundColor: "#181B1BE5" } }}
    >
      <DialogContent className="!flex !gap-x-14 !max-w-md !bg-[--footer-bg] !border-b-transparent !border-[1px] !px-10 !pt-10 !border-[--text-color]">
        <Box>
          <Box className="!flex !py-2 !justify-between !items-center">
            <ExpireTokenEye className="!w-[59px] !h-[43px]" />
            <IconButton onClick={handleClose}>
              <Clear className="!text-[--text-color] !text-xl" />
            </IconButton>
          </Box>
          <Typography className="!text-[--text-color] lg:!text-2xl md:!text-2xl !text-lg !py-2 CraftworkGroteskMedium ">
            Hey, it looks like you&apos;re not logged in
          </Typography>
          <Typography className="!text-[--text-color] !text-sm CraftworkGroteskGX">
            We&apos;d love to help you, but we can&apos;t save the result if you&apos;re not logged
            in. Sign in or create an account so you don&apos;t lose it.
          </Typography>
        </Box>
      </DialogContent>
      <DialogActions
        sx={{
          justifyContent: "space-between",
        }}
        className="!bg-[--footer-bg] !flex !gap-x-14 !border-t-transparent !px-10 !pb-10 !border-[1px] !border-[--text-color]"
      >
        <Link href={`/${lang}/signup`}>
          <Button
            className="!bg-[--text-color] !flex lg:!gap-x-4 md:!gap-x-4 !w-full !py-3"
            type="submit"
            sx={{
              border: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
          >
            <Typography className="!normal-case CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--bg-color]">
              Register
            </Typography>

            <ArrowSouthEast className="!!hidden lg:!inline md:!inline" />
          </Button>
        </Link>
        <Link href={`/${lang}/login`}>
          <Button
            className=" !flex !gap-x-2 !mr-4 !py-3"
            type="submit"
            sx={{
              border: 0,
              padding: 0,
              minWidth: 0,
              "&.MuiButtonBase-root": {
                color: "white !important",
              },
            }}
          >
            <Typography className="!normal-case !underline CraftworkGroteskHeavy !text-sm !leading-[15.4px] !text-[--text-color]">
              Login
            </Typography>

            <SouthEast className=" !text-[--text-color]" />
          </Button>
        </Link>
      </DialogActions>
    </Dialog>
  );
};

export default TokenExpirePopup;
