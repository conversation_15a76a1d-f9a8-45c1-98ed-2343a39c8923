import queryString from "query-string";
import axiosInstance from "@/lib/axios";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import listPlugin from "@fullcalendar/list";
import rrulePlugin from "@fullcalendar/rrule";
import { jwtDecode } from "jwt-decode";
import dayjs from "dayjs";
const TIMEZONE = "America/Toronto";
// function to check token is valid or not
export const validateToken = (token) => {
  return token !== null && token.length > 0;
};

export const getLocalStorage = (key) => {
  try {
    return JSON.parse(localStorage.getItem(key));
  } catch (e) {
    return null || undefined;
  }
};

export const setLocalStorage = (key, data) => {
  if (typeof window !== "undefined") {
    const dataWithoutCircularRefs = removeCircularReferences(data);
    localStorage.setItem(key, JSON.stringify(dataWithoutCircularRefs));
  }
};

function removeCircularReferences(obj) {
  const seen = new WeakSet();
  return JSON.parse(
    JSON.stringify(obj, (key, value) => {
      if (typeof value === "object" && value !== null) {
        if (seen.has(value)) {
          return; // Ignore circular references
        }
        seen.add(value);
      }
      return value;
    }),
  );
}

export const removeLocalStorage = (key) => {
  localStorage.removeItem(key);
};

export const setFormValues = ({ values, setValue }) => {
  return Object.entries(values).forEach(([key, value]) => {
    setValue(key, value);
  });
};

export const encode = (data) => {
  if (data && typeof data === "string") {
    return btoa(data);
  }
};

export const decode = (encodedString) => {
  if (encodedString) {
    return atob(encodedString);
  }
};

export const stringifyParams = (parms) => {
  return queryString.stringify(parms);
};

export const parseParams = (params) => {
  return queryString.parse(params);
};

// clear local storage
export const clearLocalStorage = () => {
  if (typeof window !== "undefined") {
    localStorage.clear();
  }
};

export const formatLocation = (location) => {
  return [location?.city, location?.state, location?.country].filter(Boolean).join(", ");
};

export const extractProfileId = (string) => {
  return string.split(":")[1];
};

{
  /** transform text i.e popVariety => pop variety */
}
export const transformText = (text) => {
  return text.replace(/([a-z])([A-Z])/g, "$1 $2");
};

{
  /** genrate the address string */
}
export const generateLocationString = (location) => {
  if (!location) return "Location";
  const { country, state, city, zipCode, streetAddress } = location ?? {};
  const locationComponents = [country, state, city, streetAddress, zipCode];
  const filteredLocation = locationComponents.filter((component) => !!component);
  return filteredLocation.join(", ");
};

/** act-media-image */
export const getImageFromUrlAsBlob = async (url) => {
  try {
    const response = await axiosInstance.get(url, { responseType: "blob" });
    return response.data;
  } catch (error) {
    return null;
  }
};

export const getImageFromUrl = (url) => {
  return getImageFromUrlAsBlob(url).then((blob) => {
    if (blob) {
      const imageUrl = URL.createObjectURL(blob);
      return imageUrl;
    } else {
      return null;
    }
  });
};

// belong to calendar.component.js

export const PLUGINS = [interactionPlugin, dayGridPlugin, timeGridPlugin, listPlugin, rrulePlugin];

export const BUTTON_TEXT = {
  today: "Today",
  month: "Month",
  week: "Week",
  day: "Day",
  listMonth: "List Month",
  listYear: "List Year",
  listWeek: "List Week",
  listDay: "List Day",
  nextYear: "Next Year",
  prevYear: "Prev Year",
};

// export const HEADER_TOOLBAR = {
//   // left: "prev,next today",
//   left: "prevBtn,nextBtn customToday",
//   center: "title",
//   // right: "timeGridDay,timeGridWeek,dayGridMonth",
//   right: "customMonth,customWeek,customDay",
// };

export const HEADER_TOOLBAR = null;

export const INITIAL_VIEW = "timeGridWeek";

export const VIEWS = {
  dayGrid: {
    // Options apply only to dayGrid view
    titleFormat: { year: "numeric", month: "long", day: "numeric" },
  },
  timeGrid: {
    // Options apply only to timeGrid view
    titleFormat: { year: "numeric", month: "long", day: "numeric" },
  },
  list: {
    // Options apply only to list view
    titleFormat: { year: "numeric", month: "long", day: "numeric" },
  },
  day: {
    // Options apply only to day view
    titleFormat: { year: "numeric", month: "short", day: "numeric" },
  },
  week: {
    // Options apply only to week view
    titleFormat: { year: "numeric", month: "short", day: "numeric" },
  },
  month: {
    // Options apply only to month view
    titleFormat: { year: "numeric", month: "short" },
  },
  year: {
    // Options apply only to year view
    titleFormat: { year: "numeric" },
  },
};

export const defaultIntervalInMinutes = 60;

export const COLORS_SWATCH = [
  { name: "Peacock", code: "#039BE5" },
  { name: "Tomato", code: "#D50000" },
  { name: "Flamingo", code: "#E67C73" },
  { name: "Tangerine", code: "#F4511E" },
  { name: "Banana", code: "#F6BF26" },
  { name: "Sage", code: "#33B679" },
  { name: "Basil", code: "#0B8043" },
  { name: "Blueberry", code: "#3F51B5" },
  { name: "Lavender", code: "#7986CB" },
  { name: "Grape", code: "#8E24AA" },
  { name: "Graphite", code: "#616161" },
];

export const isTokenExpired = (token) => {
  if (!token) return true;

  try {
    const decoded = jwtDecode(token);
    const currentTime = Date.now() / 1000; // Current time in seconds

    return decoded.exp < currentTime; // Check if the token is expired
  } catch (error) {
    return true; // If the token is invalid, consider it expired
  }
};

export const redirectButtonRoute = (
  router,
  currentRoute,
  type,
  isMobile,
  profileType,
  profileStatus,
) => {
  const route = currentRoute.split("/");
  const lang = route[1];
  const id = route[2];
  const currentRouteType = route[3];
  //ACT_PROFILE VENUE_PROFILE VIRTUAL_ACT_PROFILE VIRTUAL_VENUE_PROFILE

  if (!isMobile && type === "save" && profileStatus !== "STATUS_CREATED") {
    router.push(`/${lang}/profiles`);
  } else if (currentRouteType === "edit-information") {
    router.push(`/${lang}/${id}/edit-info-person`);
  } else if (currentRouteType === "edit-info-person") {
    if (type === "save") {
      if (
        profileType === "VIRTUAL_VENUE_PROFILE" ||
        profileType === "VENUE_PROFILE" ||
        profileType === "VIRTUAL_ACT_PROFILE"
      ) {
        router.push(`/${lang}/${id}/edit-media`);
      } else {
        router.push(`/${lang}/${id}/edit-entertainment-type`);
      }
    } else {
      router.push(`/${lang}/${id}/edit-information`);
    }
  } else if (currentRouteType === "edit-media") {
    if (type === "back") {
      if (
        profileType === "VIRTUAL_VENUE_PROFILE" ||
        profileType === "VENUE_PROFILE" ||
        profileType === "VIRTUAL_ACT_PROFILE"
      ) {
        router.push(`/${lang}/${id}/edit-info-person`);
      } else {
        router.push(`/${lang}/${id}/edit-entertainment-type`);
      }
    }
  } else if (currentRouteType === "edit-entertainment-type") {
    if (type === "back") {
      router.push(`/${lang}/${id}/edit-info-person`);
    }
  } else if (currentRouteType === "edit-payment") {
    if (type === "back") {
      router.push(`/${lang}/${id}/edit-location`);
    }
  } else {
    router.back();
  }
};

const checkDays = (day) => {
  const daysMap = {
    MONDAY: "su",
    TUESDAY: "mo",
    WEDNESDAY: "tu",
    THURSDAY: "we",
    FRIDAY: "th",
    SATURDAY: "fr",
    SUNDAY: "sa",
  };
  const selectedDay = daysMap[day];
  return selectedDay;
};

function calculateByWeekDay(date = new Date()) {
  const dayOfWeek = dayjs(date).day(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday

  switch (dayOfWeek) {
    case 0:
      return ["su"]; // Sunday
    case 1:
      return ["mo"]; // Monday
    case 2:
      return ["tu"]; // Tuesday
    case 3:
      return ["we"]; // Wednesday
    case 4:
      return ["th"]; // Thursday
    case 5:
      return ["fr"]; // Friday
    case 6:
      return ["sa"]; // Saturday
    default:
      throw new Error("Invalid day of the week");
  }
}

function calculateWeekPosition(date) {
  const targetDate = new Date(date);
  const dayOfMonth = targetDate.getDate(); // Get the day of the month
  const dayOfWeek = targetDate.getDay(); // Get the day of the week (0 for Sunday, 6 for Saturday)

  // Determine the week position in the month
  const weekPosition = Math.ceil((dayOfMonth + (targetDate.getDay() === 0 ? 6 : dayOfWeek)) / 7);

  return weekPosition;
}

export const filterEvent = (
  item,
  type = "",
  name = "",
  images = [],
  profileType = "",
  status = "",
) => {
  const colors = {
    profile: {
      backgroundColor: "var(--profile-bg)",
      borderColor: "var(--profile-border)",
      textColor: "var(--profile-text)",
    },
    favorite: {
      backgroundColor: "var(--favorite-bg)",
      borderColor: "var(--favorite-border)",
      textColor: "var(--favorite-text)",
    },
    contract: {
      backgroundColor: "var(--contract-bg)",
      borderColor: "var(--contract-border)",
      textColor: "var(--contract-text)",
    },
  };

  const eventData = {
    id: item.scheduleId || Math.random().toString(36).substring(2, 15),
    title: name,
    backgroundColor: colors[type]?.backgroundColor || "var(--default-bg)",
    borderColor: colors[type]?.borderColor || "var(--default-border)",
    textColor: colors[type]?.textColor || "var(--default-text)",
    extendedProps: {
      id: item.scheduleId || Math.random().toString(36).substring(2, 15),
      editable: false,
      imageUrl: images?.[0] ?? "",
      type,
      profileType,
      status,
    },
  };

  // Handle recurring events
  // if (item.recurring) {
  //   const startDate = new Date(item.startDate);
  //   const endDate = new Date(item.endDate);
  //   const durationMs = endDate - startDate;
  //   const durationHours = Math.floor(durationMs / (1000 * 60 * 60));
  //   const durationMinutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

  //   return {
  //     ...eventData,
  //     startTime: dayjs(item.startDate).format("HH:mm"), // Add start time
  //     endTime: dayjs(item.endDate).format("HH:mm"), // Add end time
  //     duration: `${String(durationHours).padStart(2, "0")}:${String(durationMinutes).padStart(2, "0")}`,
  //     rrule: {
  //       freq: item.recurrence?.recurrenceType === "MONTHLY" ? "monthly" : "weekly",
  //       interval: item.recurrence?.interval || 1,
  //       dtstart: dayjs(item.startDate).tz(TIMEZONE).format(),
  //       until: item.recurrence?.endDate
  //         ? dayjs(item.recurrence.endDate).tz(TIMEZONE).format()
  //         : null,
  //       byweekday:
  //         item.recurrence?.recurrenceType === "MONTHLY"
  //           ? calculateByWeekDay(item.startDate)
  //           : item.recurrence?.daysOfWeek?.map((day) => checkDays(day)) || [],
  //       ...(item.recurrence?.recurrenceType === "MONTHLY" && {
  //         bysetpos: calculateWeekPosition(item.startDate),
  //       }),
  //     },
  //   };
  // }

  // Handle non-recurring events
  return {
    ...eventData,
    recurring: false,
    start: dayjs(item.startDate).tz(TIMEZONE).format(),
    end: dayjs(item.endDate).tz(TIMEZONE).format(),
  };
};

export const filterEventData = (item, edit = false) => {
  let eventData = {
    id: item.scheduleId || "Event",
    startDate: dayjs(item.startDate).format(),
    endDate: dayjs(item.endDate).format(),
    recurring: item.recurring,
  };

  if (item.recurring) {
    const startDate = new Date(item.startDate);
    const endDate = new Date(item.endDate);
    const durationMs = endDate - startDate;
    const durationHours = Math.floor(durationMs / (1000 * 60 * 60));
    const durationMinutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

    const formattedHours = String(durationHours).padStart(2, "0");
    const formattedMinutes = String(durationMinutes).padStart(2, "0");

    eventData = {
      ...eventData,
      title: item.scheduleType,
      recurrenceType: item.recurrence?.recurrenceType,
      daysOfWeek: item.recurrence?.daysOfWeek,
      interval: item.recurrence?.interval,
      backgroundColor: "var(--footer-bg)",
      borderColor: "var(--footer-bg)",
      textColor: "var(--calendar-text-color)",
      extendedProps: {
        id: item.scheduleId || "Event",
        editable: edit,
      },
      duration: `${formattedHours}:${formattedMinutes}`,
      rrule: {
        freq: "weekly",
        interval: item.recurrence?.recurrenceType === "WEEKLY" ? "1" : "2",
        byweekday:
          item.recurrence?.recurrenceType === "MONTHLY"
            ? calculateByWeekDay(item.startDate)
            : item.recurrence?.daysOfWeek.map((day) => checkDays(day)),
        dtstart: dayjs(item.startDate).format(),
      },
    };
  } else {
    eventData = {
      ...eventData,
      start: dayjs(item.startDate).format(),
      end: dayjs(item.endDate).format(),
      backgroundColor: "var(--footer-bg)",
      borderColor: "var(--footer-bg)",
      textColor: "var(--calendar-text-color)",
      extendedProps: {
        id: item.scheduleId,
        editable: edit,
      },
      title: item.scheduleType,
    };
  }
  if (item.recurring && item.recurrence?.recurrenceType === "MONTHLY") {
    eventData.rrule.freq = "monthly";
    delete eventData.rrule.interval;
    eventData.rrule.bysetpos = calculateWeekPosition(item.startDate);
  }
  return eventData;
};
export const filterToURL = (router, lang, filter) => {
  const params = new URLSearchParams();

  if (filter.searchName) {
    params.append("searchName", filter.searchName);
  }

  if (filter.searchType) {
    params.append(
      "profileType",
      filter.searchType === "SEARCH_TYPE_VENUE"
        ? "VENUE_PROFILE"
        : filter.searchType === "SEARCH_TYPE_EVENT"
          ? "EVENT_PROFILE"
          : "ACT_PROFILE",
    );
  }

  if (filter.searchStrings && filter.searchStrings.length > 0) {
    params.append("searchStrings", filter.searchStrings.join(","));
  }

  if (filter.searchFilter) {
    if (filter.searchFilter.searchLocation) {
      const { cityName, stateName, countryName } = filter.searchFilter.searchLocation;
      if (cityName) params.append("city", cityName);
      if (stateName) params.append("state", stateName);
      if (countryName) params.append("country", countryName);
    }
    if (filter.searchFilter?.searchLocation?.distance) {
      params.append("distance", filter.searchFilter?.searchLocation?.distance);
    }
    if (filter.searchFilter.searchDate) {
      const { searchDateType, startDate, endDate } = filter.searchFilter.searchDate;
      if (searchDateType) params.append("searchDateType", searchDateType);
      if (startDate) params.append("startDate", startDate);
      if (endDate) params.append("endDate", endDate);
    }

    if (
      filter.searchFilter.entertainmentTypesList &&
      filter.searchFilter.entertainmentTypesList.length > 0
    ) {
      filter.searchFilter.entertainmentTypesList.forEach((item) => {
        params.append("entertainmentType", item.name);
      });
    }

    if (filter.searchFilter.actRating && filter.searchFilter.actRating.overallRating) {
      params.append("overallRating", filter.searchFilter.actRating.overallRating);
    }

    if (filter.searchFilter.musicGenreList) {
      if (Array.isArray(filter.searchFilter.musicGenreList)) {
        filter.searchFilter.musicGenreList.forEach((item) => {
          params.append("musicGenre", item.name);
        });
      } else {
        params.append("musicGenre", filter.searchFilter.musicGenreList.name);
      }
    }
  }

  // redirect to the search page with the filter
  const url = `/${lang}/search?${params.toString()}`;
  router.push(url);
};

export const formatTimeToAMPM = (dateString) => {
  // Create a Date object from the ISO date string
  const date = new Date(dateString);

  // Check if the Date object is valid
  if (isNaN(date.getTime())) {
    return "Invalid date"; // Handle invalid date case
  }

  // Get the browser's timezone
  const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone || "UTC";

  // Format the time in AM/PM format with the browser's timezone
  return new Intl.DateTimeFormat("en-US", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
    timeZone: timeZone,
  }).format(date);
};

export const formatTime = (dateString) => {
  // Create a Date object from the ISO date string
  const date = new Date(dateString);

  // Check if the Date object is valid
  if (isNaN(date.getTime())) {
    return "Invalid date"; // Handle invalid date case
  }

  // Get the browser's timezone
  const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone || "UTC";

  // Format the time in AM/PM format with the browser's timezone
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
    timeZone: timeZone,
  }).format(date);
};

export const formatDate = (dateString) => {
  // Create a Date object from the ISO date string
  const date = new Date(dateString);

  // Check if the Date object is valid
  if (isNaN(date.getTime())) {
    return "Invalid date"; // Handle invalid date case
  }

  // Get the browser's timezone
  const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone || "UTC";

  // Format the time in AM/PM format with the browser's timezone
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    timeZone: timeZone,
  }).format(date);
};
