export const appendQueryParams = (queryParamsObj, router, searchParams, redirect = "") => {
  const newParams = new URLSearchParams(searchParams);

  // Append each key-value pair from queryParamsObj
  Object.entries(queryParamsObj).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      // If the value is an array, append each item
      value.forEach((item) => newParams.append(key, item));
    } else {
      // Otherwise, set the single value
      newParams.set(key, value);
    }
  });

  // Push the new URL with updated query parameters
  if (redirect) {
    router.push(`/${redirect}/?${newParams.toString()}`);
  } else {
    router.push(`?${newParams.toString()}`);
  }
};

export const removeQueryParams = (paramsToRemove, router, searchParams) => {
  const newParams = new URLSearchParams(searchParams);

  // Remove each specified query parameter
  paramsToRemove.forEach((param) => {
    newParams.delete(param);
  });

  // Push the new URL with updated query parameters
  router.push(`?${newParams.toString()}`);
};

export const updateQueryParams = (key, values, router, searchParams) => {
  const newParams = new URLSearchParams();

  // Keep existing parameters except for the key to be updated
  for (const [paramKey, paramValue] of searchParams.entries()) {
    if (paramKey !== key) {
      newParams.append(paramKey, paramValue);
    }
  }

  // Update the key with new values or remove specified values
  if (Array.isArray(values)) {
    // Remove specified values if they exist
    const currentValues = searchParams.getAll(key);
    values.forEach((value) => {
      if (currentValues.includes(value)) {
        currentValues.splice(currentValues.indexOf(value), 1);
      }
    });
    // Append remaining values
    currentValues.forEach((value) => newParams.append(key, value));
  } else if (values !== null) {
    // If values is not an array and not null, set the single value
    newParams.set(key, values);
  }

  // Push the new URL with updated query parameters
  router.push(`?${newParams.toString()}`);
};
