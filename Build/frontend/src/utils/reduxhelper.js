import store from "@/store";

export const redirectBookingButtonRoute = (router, lang) => {
  const {
    booking: { currentBookingStatus },
  } = store.getState();

  // const disaptch = store.dispatch;
  // disaptch(setBookingData({ contractId: contactId, contactType, previousRoute }));
  /* 
      {
      contactId: 0,
      contactType: "string", // bookExistingVenue, bookPrivateVenue, UserWithoutActVenue, venueBookAct, actBookVenue
      nextRoute: "string",    // next route
      previousRoute: "string", // previous route
      }
  
      create-booking, add-booking, select-venue, select-date-and-time,  user-data
      */
  if (currentBookingStatus) {
    const { contactType, previousRoute, otherProfileId } = currentBookingStatus;

    if (previousRoute === "add-booking") {
      router.push(`/${lang}/select-date-and-time`);
    }
    if (previousRoute === "select-date-and-time") {
      router.push(`/${lang}/user-data`);
    }
    // if (previousRoute === "user-data") {
    //   router.push(`/${lang}/select-profile`);
    //   //router.push(`/${lang}/riders`);
    // }
    if (previousRoute === "select-profile") {
      router.push(`/${lang}/contract-details  `);
      // if (contactType === "userBookAct" || contactType === "actBookVenue") {
      //   router.push(`/${lang}/rider-notes`);
      // } else {
      //   router.push(`/${lang}/riders`);
      // }
    }

    if (previousRoute === "contract-details") {
      if (contactType === "venueBookAct" || contactType === "userBookVenue") {
        router.replace(`/${lang}/riders`);
      }
      if (contactType === "actBookVenue" || contactType === "userBookAct") {
        router.replace(`/${lang}/rider-notes`);
      }
    }

    if (previousRoute === "riders") {
      router.push(`/${lang}/venue-rider-notes`);
      // if (contactType === "actBookVenue") {
      //   router.push(`/${lang}/venue-rider-notes`);
      // }
      // if (contactType === "venueBookAct") {
      //   router.push(`/${lang}/venue-rider-notes`);
      // }
    }

    if (previousRoute === "venue-rider-notes") {
      if (
        contactType === "actBookVenue" ||
        contactType === "userBookVenue" ||
        contactType === "userBookAct"
      ) {
        router.push(`/${lang}/booking-details`);
      }
      if (contactType === "venueBookAct") {
        router.push(`/${lang}/rider-notes`);
      }
    }

    if (
      !otherProfileId &&
      previousRoute === "rider-notes" &&
      (contactType === "venueBookAct" || contactType === "actBookVenue")
    ) {
      router.push(`/${lang}/select-profile`);
    }

    if (
      !otherProfileId &&
      previousRoute === "rider-notes" &&
      (contactType === "userBookVenue" || contactType === "userBookAct")
    ) {
      //router.push(`/${lang}/standard-contract`);
      router.push(`/${lang}/select-profile`);
    }

    if (previousRoute === "rider-notes") {
      //router.push(`/${lang}/standard-contract`);
      if (
        contactType === "userBookAct" ||
        contactType === "userBookVenue" ||
        contactType === "venueBookAct"
      ) {
        router.push(`/${lang}/booking-details`);
      } else {
        router.push(`/${lang}/riders`);
      }
    }

    if (
      previousRoute === "standard-contract" &&
      (contactType === "venueBookAct" ||
        contactType === "userBookVenue" ||
        contactType === "actBookVenue" ||
        contactType === "userBookAct")
    ) {
      router.push(`/${lang}/booking-details`);
    }
    if (
      previousRoute === "booking-details" &&
      (contactType === "venueBookAct" ||
        contactType === "userBookVenue" ||
        contactType === "actBookVenue" ||
        contactType === "userBookAct")
    ) {
      router.push(`/${lang}/booking-success`);
    }
    if (previousRoute === "booking-success") {
      router.push(`/${lang}/contracts/contracts-by-me`);
    }
  }

  // const route = currentRoute.split("/");
  // const lang = route[1];
  // const id = route[2];
  // const currentRouteType = route[3];
  //ACT_PROFILE VENUE_PROFILE VIRTUAL_ACT_PROFILE VIRTUAL_VENUE_PROFILE
  // if (contactType === "bookExistingVenue" && previousRoute === "select-venue") {
  //   router.push(`/${lang}/profiles/${id}/select-date-and-time/${contactId}`);

  // } else {
  //   router.back();
  // }
};
