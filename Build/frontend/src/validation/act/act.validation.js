import dayjs from "dayjs";
import * as yup from "yup";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";

import { ACT_CONSTANTS, CONTRACT_DETAILS, USER_CONSTANTS } from "../auth/constants";

dayjs.extend(isSameOrAfter);

{
  /** act information */
}
export const actInformationValidation = ({ previewData }) => {
  let isVenueValue = previewData?.profile?.option || "";
  if (
    previewData?.profileType === "VIRTUAL_ACT_PROFILE" ||
    previewData?.profileType === "ACT_PROFILE"
  ) {
    isVenueValue = "Act";
  }
  if (
    previewData?.profileType === "VENUE_PROFILE" ||
    previewData?.profileType === "VIRTUAL_VENUE_PROFILE"
  ) {
    isVenueValue = "Venue";
  }

  return yup.object().shape({
    profileName: yup
      .string()
      .required("Profile Name is required")
      .max(
        ACT_CONSTANTS.PROFILE_NAME.MAX_LENGTH,
        `Profile Name must not exceed ${ACT_CONSTANTS.PROFILE_NAME.MAX_LENGTH} characters`,
      ),
    profileRole: yup.string().test("is-required", "Role is required", function (value) {
      return isVenueValue === "Venue" ? true : !!value;
    }),
    preferredLanguage: yup.string().required("Please select one"),
    performanceLanguages: yup.array().test("is-required", "Please select one", function (value) {
      return this.parent.authorizedRepresendter === "Another person" ? true : !!value;
    }),
    communicationLanguages: yup.array().min(1, "Please select one").required("Please select one"),
    authorizedRepresendter: yup.string().optional(),
    authorizedRepresenter: yup
      .string()
      .nullable()
      .test("is-required", "Name of the authorized representative is required", function (value) {
        return this.parent.authorizedRepresendter === "Another person" ? !!value : true;
      }),
    authorizedRepresenterEmail: yup
      .string()
      .nullable()
      .email("Please enter a valid email address")
      .test("is-required", "Authorized representer’s email is required", function (value) {
        return this.parent.authorizedRepresendter === "Another person" ? !!value : true;
      }),
    authorizedRepresenterPhoneNumber: yup.string().nullable(),
    profileEmail: yup
      .string()
      .email("Please enter a valid email address")
      .max(
        ACT_CONSTANTS.PROFILE_EMAIL.MAX_LENGTH,
        `Email must not exceed ${ACT_CONSTANTS.PROFILE_EMAIL.MAX_LENGTH} characters`,
      )
      .test("is-required", "Profile Email Address is Required", function (value) {
        const useMyEmailAddress = this.parent.useMyEmail;
        const authorizedRepresendter = this.parent.authorizedRepresendter;
        if (authorizedRepresendter !== "Another person" && !useMyEmailAddress) {
          return !!value;
        } else {
          return true;
        }
      }),
    useMyEmail: yup.boolean(),
  });
};

{
  /** act location */
}
export const actLocationValidation = yup.object().shape({
  country: yup.string().required("Country is required"),
  state: yup.string().required("State is required"),
  city: yup
    .string()
    .required("City is required")
    .max(
      ACT_CONSTANTS.LOCATION.CITY_MAX_LENGTH,
      `City must not exceed ${ACT_CONSTANTS.LOCATION.CITY_MAX_LENGTH} characters`,
    ),
  streetAddress: yup
    .string()
    .required("Street Address is required")
    .max(
      ACT_CONSTANTS.LOCATION.STREET_ADDRESS_MAX_LENGTH,
      `Street Address must not exceed ${ACT_CONSTANTS.LOCATION.STREET_ADDRESS_MAX_LENGTH} characters`,
    ),
  zipCode: yup
    .string()
    .required("Zip Code is required")
    .max(
      ACT_CONSTANTS.LOCATION.ZIP_CODE_MAX_LENGTH,
      `Zip Code must not exceed ${ACT_CONSTANTS.LOCATION.ZIP_CODE_MAX_LENGTH} characters`,
    ),
});

{
  /** act feedback */
}
export const actFeedbackValidation = yup.object().shape({
  publicMessage: yup.string().optional(),
});

{
  /** act distribution list */
}
export const actDistributionValidation = yup.object().shape({
  useActProfile: yup.string().required("ActProfile is required"),
  receiverEmail: yup.string().email().required("Email is required"),
  distributionType: yup.string().required("Distribution Type is required"),
});

{
  /** schedule */
}
export const scheduleValidation = yup
  .object()
  .shape({
    profileType: yup.string().optional().nullable(),
    scheduleName: yup.string().optional().nullable(),
    scheduleDescription: yup.string().optional().nullable(),
    scheduleType: yup.string().optional().nullable(),
    timeZone: yup.string().optional().nullable(),
    startDate: yup.date().optional().nullable(),
    endDate: yup.date().optional().nullable(),
    recurring: yup.boolean().optional().nullable(),
    recurrence: yup
      .object()
      .shape({
        recurrenceType: yup.string().optional().nullable(),
        interval: yup.number().optional().nullable(),
        count: yup.number().optional().nullable(),
        endDate: yup.string().optional().nullable(),
        daysOfWeek: yup.array().of(yup.string()).optional().nullable(),
        daysOfMonth: yup.number().optional().nullable(),
        monthsOfYear: yup.array().of(yup.string()).optional().nullable(),
        recurrenceEndType: yup
          .object()
          .shape({
            never: yup.boolean().optional().nullable(),
            endDate: yup.date().optional().nullable(),
            occurrences: yup.number().optional().nullable(),
          })
          .optional()
          .nullable(),
      })
      .optional()
      .nullable(),
  })
  .test(
    "loading-time-earlier",
    "Loading time must be earlier than perform time",
    function (values) {
      const { startDate, endDate } = values;
      const load = dayjs(startDate);
      const start = dayjs(endDate);
      if (load && start && load >= start) {
        return this.createError({
          message: "Start must be earlier than End time",
          path: "endDate",
        });
      }
      return true;
    },
  );

{
  /** act info person */
}
export const actInfoPersonValidation = yup.object().shape({
  bio: yup
    .string()
    .required("Bio is required")
    .max(
      ACT_CONSTANTS.ACT_INFO.BIO_MAX_LENGTH,
      `Bio must not exceed ${ACT_CONSTANTS.ACT_INFO.BIO_MAX_LENGTH} characters`,
    ),
  spotifyLink: yup
    .string()
    .max(
      ACT_CONSTANTS.ACT_INFO.SPOTIFY_LINK_MAX_LENGTH,
      `Spotify link must not exceed ${ACT_CONSTANTS.ACT_INFO.SPOTIFY_LINK_MAX_LENGTH} characters`,
    ),
  soundCloudLink: yup
    .string()
    .max(
      ACT_CONSTANTS.ACT_INFO.SOUNDCLOUD_LINK_MAX_LENGTH,
      `SoundCloud link must not exceed ${ACT_CONSTANTS.ACT_INFO.SOUNDCLOUD_LINK_MAX_LENGTH} characters`,
    ),
  instagramLink: yup
    .string()
    .max(
      ACT_CONSTANTS.ACT_INFO.INSTAGRAM_LINK_MAX_LENGTH,
      `Instagram link must not exceed ${ACT_CONSTANTS.ACT_INFO.INSTAGRAM_LINK_MAX_LENGTH} characters`,
    ),
  youtubeLink: yup
    .string()
    .max(
      ACT_CONSTANTS.ACT_INFO.YOUTUBE_LINK_MAX_LENGTH,
      `YouTube link must not exceed ${ACT_CONSTANTS.ACT_INFO.YOUTUBE_LINK_MAX_LENGTH} characters`,
    ),
  facebookLink: yup
    .string()
    .max(
      ACT_CONSTANTS.ACT_INFO.FACEBOOK_LINK_MAX_LENGTH,
      `Facebook link must not exceed ${ACT_CONSTANTS.ACT_INFO.FACEBOOK_LINK_MAX_LENGTH} characters`,
    ),
});

{
  /** act payment validation */
}
export const actPaymentValidation = ({ previewData }) => {
  const isVenue =
    previewData?.actInfo?.profileType === "VENUE_PROFILE" ||
    previewData?.actInfo?.profileType === "VIRTUAL_VENUE_PROFILE";

  return yup.object().shape({
    forRentOrNot: yup.string(),

    typicalPrice: yup
      .string()
      .nullable()
      .test("is-required", "Typical Price is required", function (value) {
        return !isVenue || this.parent.forRentOrNot !== "Not for rent" ? !!value : true;
      })
      .max(
        ACT_CONSTANTS.PAYMENT.TYPICAL_PRICE_MAX_LENGTH,
        `Typical Price must not exceed ${ACT_CONSTANTS.PAYMENT.TYPICAL_PRICE_MAX_LENGTH} characters`,
      ),

    charityPrice: yup
      .string()
      .nullable()
      .test("is-required", "Charity Price is required", function (value) {
        return !isVenue || this.parent.forRentOrNot !== "Not for rent" ? !!value : true;
      })
      .max(
        ACT_CONSTANTS.PAYMENT.MINIMAL_PRICE_MAX_LENGTH,
        `Minimal Price must not exceed ${ACT_CONSTANTS.PAYMENT.MINIMAL_PRICE_MAX_LENGTH} characters`,
      )
      .test(
        "is-lower-than-typical",
        "Charity Price must be less than Typical Price",
        function (charityPrice) {
          const { typicalPrice } = this.parent;
          if (!charityPrice || !typicalPrice) return true;
          return parseFloat(charityPrice) < parseFloat(typicalPrice);
        },
      ),

    standardPriceCurrency: yup
      .string()
      .nullable()
      .test("is-required", "Currency is required", function (value) {
        return !isVenue || this.parent.forRentOrNot !== "Not for rent" ? !!value : true;
      }),

    standardPricePer: yup
      .string()
      .nullable()
      .test("is-required", "Standard Price Per is required", function (value) {
        return !isVenue || this.parent.forRentOrNot !== "Not for rent" ? !!value : true;
      }),

    minimalPriceCurrency: yup.string().nullable(),

    minimalPricePer: yup
      .string()
      .nullable()
      .test("is-required", "Minimal Price Per is required", function (value) {
        return !isVenue || this.parent.forRentOrNot !== "Not for rent" ? !!value : true;
      }),

    paymentMethod: yup
      .array()
      .nullable()
      .test("is-required", "Please select at least one payment method", function (value) {
        return !isVenue || this.parent.forRentOrNot !== "Not for rent"
          ? value && value.length > 0
          : true;
      }),
  });
};

{
  /** act media validation */
}
export const actMediaValidation = yup.object().shape({
  actPhotos: yup.array(),
  videoLink: yup.array().of(
    yup.object().shape({
      videoLink: yup
        .string()
        .max(
          ACT_CONSTANTS.ACT_MEDIA.VIDEO_LINK_MAX_LENGTH,
          `Video link must not exceed ${ACT_CONSTANTS.ACT_MEDIA.VIDEO_LINK_MAX_LENGTH} characters`,
        ),
    }),
  ),
  audioLink: yup.array().of(
    yup.object().shape({
      audioLink: yup
        .string()
        .max(
          ACT_CONSTANTS.ACT_MEDIA.AUDIO_LINK_MAX_LENGTH,
          `Audio link must not exceed ${ACT_CONSTANTS.ACT_MEDIA.AUDIO_LINK_MAX_LENGTH} characters`,
        ),
    }),
  ),
});

export const userPersonalValidation = yup.object().shape({
  firstName: yup
    .string()
    .required("First Name is required")
    .max(
      USER_CONSTANTS.FIRST_NAME.MAX_LENGTH,
      `First Name must not exceed ${USER_CONSTANTS.FIRST_NAME.MAX_LENGTH} characters`,
    ),
  lastName: yup
    .string()
    .required("Last Name is required")
    .max(
      USER_CONSTANTS.LAST_NAME.MAX_LENGTH,
      `Last Name must not exceed ${USER_CONSTANTS.LAST_NAME.MAX_LENGTH} characters`,
    ),
  email: yup
    .string()
    .email("Please enter a valid email address")
    .max(
      USER_CONSTANTS.EMAIL.MAX_LENGTH,
      `Email must not exceed ${USER_CONSTANTS.EMAIL.MAX_LENGTH} characters`,
    ),
  location: yup.object().shape({
    city: yup
      .string()
      .max(
        USER_CONSTANTS.LOCATION.CITY_MAX_LENGTH,
        `City must not exceed ${USER_CONSTANTS.LOCATION.CITY_MAX_LENGTH} characters`,
      ),
    streetAddress: yup
      .string()
      .max(
        USER_CONSTANTS.LOCATION.STREET_ADDRESS_MAX_LENGTH,
        `Street Address must not exceed ${USER_CONSTANTS.LOCATION.STREET_ADDRESS_MAX_LENGTH} characters`,
      ),
    zipCode: yup
      .string()
      .max(
        USER_CONSTANTS.LOCATION.ZIP_CODE_MAX_LENGTH,
        `Zip Code must not exceed ${USER_CONSTANTS.LOCATION.ZIP_CODE_MAX_LENGTH} characters`,
      ),
  }),
});

export const validateBookingRiders = (type) => {
  return yup.object().shape({
    ...(type === "Act"
      ? {}
      : {
          //parkingConditions: yup.string().required("Parking Conditions are required"),
          allowedVisualEffectsList: yup.array().of(yup.string()),
        }),
  });
};

export const validateRiderNotes = yup.object().shape({
  riderAccepted: yup.boolean(),
  riderRejected: yup.boolean(),
  acceptedWithConditions: yup.boolean(),
  riderRejectionReason: yup.string().optional(),
  acceptanceConditions: yup
    .string()
    .test("is-required", "AcceptanceConditions is required", function (value) {
      return this.parent.acceptedWithConditions === true ? !!value : true;
    }),
});

export const contractValidation = (previewContract, contractType) =>
  yup
    .object()
    .shape({
      performersRole:
        contractType !== "USERVENUE"
          ? yup
              .string()
              .required("Performer's Role is required")
              .max(
                CONTRACT_DETAILS.PERFORMER_ROLE.MAX_LENGTH,
                `Performer's Role must not exceed ${CONTRACT_DETAILS.PERFORMER_ROLE.MAX_LENGTH} characters`,
              )
          : yup.string().nullable(),
      startDate: yup.string().optional(),
      loadingTime:
        contractType !== "USERVENUE"
          ? yup.string().required("Load in time is required")
          : yup.string().nullable(),
      durationInHours: yup.number().nullable().min(0, "Duration must be a non-negative value"),
      paymentType: yup.string().required("Payment type is required"),
      flatRateAmount: yup
        .number()
        .nullable()
        .min(0, "Flat Rate Amount must be a non-negative value")
        .test(
          "max-digits",
          `Amount cannot exceed ${CONTRACT_DETAILS.FEE.MAX_LENGTH} digits`,
          (val) => !val || val.toString().length <= CONTRACT_DETAILS.FEE.MAX_LENGTH,
        ),
      flatRatePercentage: yup.number().nullable().min(0, "Deposit must be a non-negative value"),
      venueCapacity:
        contractType === "DOOR_GIG"
          ? yup.number().required("Venue Capacity is required")
          : yup.number().nullable(),
      doorManagedBy:
        contractType === "DOOR_GIG"
          ? yup.string().required("Door Managed By is required")
          : yup.string().nullable(),
      maximumPercentage:
        contractType === "DOOR_GIG"
          ? yup.number().required("Maximum Percentage is required")
          : yup.number().nullable(),
      guaranteedMaximum: yup
        .number()
        .nullable()
        .min(0, "Guaranteed Maximum must be a non-negative value")
        .test(
          "max-digits",
          `Amount cannot exceed ${CONTRACT_DETAILS.FEE.MAX_LENGTH} digits`,
          (val) => !val || val.toString().length <= CONTRACT_DETAILS.FEE.MAX_LENGTH,
        ),
      doorGigEntryFee: yup
        .number()
        .nullable()
        .min(0, "Door Gig Entry Fee must be a non-negative value")
        .test(
          "max-digits",
          `Amount cannot exceed ${CONTRACT_DETAILS.FEE.MAX_LENGTH} digits`,
          (val) => !val || val.toString().length <= CONTRACT_DETAILS.FEE.MAX_LENGTH,
        ),
      exposureGigFee: yup
        .number()
        .nullable()
        .min(0, "Exposure Gig Fee must be a non-negative value")
        .test(
          "max-digits",
          `Amount cannot exceed ${CONTRACT_DETAILS.FEE.MAX_LENGTH} digits`,
          (val) => !val || val.toString().length <= CONTRACT_DETAILS.FEE.MAX_LENGTH,
        ),
      equipmentProvider:
        contractType !== "USERVENUE" ? yup.string().nullable() : yup.string().optional(),
      mealsProvidedByPurchaser:
        contractType !== "USERVENUE" ? yup.boolean().optional() : yup.boolean().nullable(),
      accommodationProvided:
        contractType !== "USERVENUE" ? yup.boolean().optional() : yup.boolean().nullable(),
      merchandiseSalesAllowed:
        contractType !== "USERVENUE" ? yup.boolean().optional() : yup.boolean().nullable(),
      performerMemberOfUnion:
        contractType !== "USERVENUE" ? yup.boolean().optional() : yup.boolean().nullable(),
      message: yup
        .string()
        .nullable()
        .max(
          CONTRACT_DETAILS.MESSAGE.MAX_LENGTH,
          `Message must not exceed ${CONTRACT_DETAILS.MESSAGE.MAX_LENGTH} characters`,
        ),
    })
    .test("validate-payment-type", function (values) {
      const errors = [];
      const {
        paymentType,
        flatRateAmount,
        flatRateCurrency,
        flatRatePercentage,
        exposureGigFee,
        exposureGigCurrency,
        doorGigEntryFee,
        venueCapacity,
        doorManagedBy,
        maximumPercentage,
        guaranteedMaximum,
      } = values;

      // FLAT_RATE validation
      if (paymentType === "FLAT_RATE") {
        if (!flatRateCurrency) {
          errors.push(
            this.createError({
              path: "flatRateCurrency",
              message: "Currency is required for FLAT_RATE",
            }),
          );
        }
        if (!flatRateAmount || flatRateAmount === 0) {
          errors.push(
            this.createError({ path: "flatRateAmount", message: "Flat Rate Amount is required" }),
          );
        }

        if (flatRatePercentage === undefined || flatRatePercentage === null) {
          errors.push(
            this.createError({ path: "flatRatePercentage", message: "Deposit amount required" }),
          );
        }
        if (flatRatePercentage > flatRateAmount) {
          errors.push(
            this.createError({
              path: "flatRatePercentage",
              message: "Deposit must be less than Flat Rate Amount",
            }),
          );
        }
      }

      // EXPOSURE_GIG validation
      if (paymentType === "EXPOSURE_GIG") {
        if (!exposureGigFee || exposureGigFee <= 0) {
          errors.push(
            this.createError({
              path: "exposureGigFee",
              message: "Exposure Gig Fee is required and must be greater than zero",
            }),
          );
        }
        if (!exposureGigCurrency) {
          errors.push(
            this.createError({
              path: "exposureGigCurrency",
              message: "Currency is required for EXPOSURE_GIG",
            }),
          );
        }
      }

      // DOOR_GIG validation
      if (paymentType === "DOOR_GIG") {
        if (!doorGigEntryFee || doorGigEntryFee <= 0) {
          errors.push(
            this.createError({
              path: "doorGigEntryFee",
              message: "Door Gig Entry Fee is required and must be greater than zero",
            }),
          );
        }
        if (!venueCapacity || venueCapacity <= 0) {
          errors.push(
            this.createError({
              path: "venueCapacity",
              message: "Venue Capacity is required and must be greater than zero",
            }),
          );
        }
        if (!doorManagedBy) {
          errors.push(
            this.createError({ path: "doorManagedBy", message: "Door Managed By is required" }),
          );
        }
        if (!maximumPercentage || maximumPercentage <= 0) {
          errors.push(
            this.createError({
              path: "maximumPercentage",
              message: "Maximum Percentage is required and must be greater than zero",
            }),
          );
        }
        if (!guaranteedMaximum || guaranteedMaximum <= 0) {
          errors.push(
            this.createError({
              path: "guaranteedMaximum",
              message: "Guaranteed Maximum is required and must be greater than zero",
            }),
          );
        }
      }

      if (errors.length > 0) {
        throw new yup.ValidationError(errors);
      }
      return true;
    })
    .test(
      "loading-time-earlier",
      "Loading time must be earlier than perform time",
      function (values) {
        const { loadingTime, startDate } = values;
        if (!loadingTime || !startDate) return true;
        const load = dayjs(loadingTime).format("HH:mm"); // Ensure correct time parsing
        const start = dayjs(startDate).format("HH:mm"); // Ensure correct time parsing
        if (dayjs(load, "HH:mm").isSameOrAfter(dayjs(start, "HH:mm"))) {
          return this.createError({
            message:
              contractType === "USERVENUE"
                ? "Loading time must be earlier than event time"
                : "Loading time must be earlier than perform time",
            path: "loadingTime",
          });
        }
        return true;
      },
    )
    .test("start-date-range", "Start date must be within the allowed range", function (value) {
      if (!value.startDate) return true;
      const start = dayjs(value.startDate);
      const allowedStart = dayjs(previewContract?.scheduleTime?.startDate);
      const allowedEnd = dayjs(previewContract?.scheduleTime?.endDate);

      if (start.isBefore(allowedStart) || start.isAfter(allowedEnd)) {
        return this.createError({
          message: "Start date must be within the allowed range",
          path: "startDate",
        });
      }
      return true;
    });
