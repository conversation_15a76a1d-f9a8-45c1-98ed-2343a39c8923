import * as yup from "yup";

export const TwoFAEmailValidation = yup.object().shape({
  email: yup
    .string()
    .email("Invalid Email")
    .max(50, "Email must be at most 50 characters")
    .required("Email is Required!"),
});

export const twoFaPhoneValidation = yup.object().shape({
  phoneNumber: yup
    .string()
    .required("Phone Number is Required!")
    .matches(/^\(\d{3}\) \d{3}-\d{4}$/, "Enter a valid phone number")
    .max(14, "Enter a valid phone number"),
});
