import * as yup from "yup";

export const changePasswordValidation = yup.object().shape({
  oldPassword: yup
    .string()
    .required("Old Password is Required!")
    .min(6, "Password must be at least 6 characters")
    .max(20, "Password must be at most 20 characters"),
  newPassword: yup
    .string()
    .required("New Password is Required!")
    .min(6, "New Password must be at least 6 characters")
    .max(20, "New Password must be at most 20 characters")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
    ),
  repeatPassword: yup
    .string()
    .required("Repeat Password is Required!")
    .oneOf([yup.ref("newPassword")], "Passwords must match"),
});
