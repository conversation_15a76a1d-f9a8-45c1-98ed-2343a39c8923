export const AUTH_CONSTANTS = {
  EMAIL: {
    MAX_LENGTH: 50,
  },
  PASSWORD: {
    MIN_LENGTH: 6,
    MAX_LENGTH: 20,
  },
};

export const ACT_CONSTANTS = {
  PROFILE_NAME: {
    MAX_LENGTH: 50,
  },
  PROFILE_EMAIL: {
    MAX_LENGTH: 50,
  },
  LOCATION: {
    CITY_MAX_LENGTH: 50,
    STREET_ADDRESS_MAX_LENGTH: 50,
    ZIP_CODE_MAX_LENGTH: 7,
  },
  ACT_INFO: {
    BIO_MAX_LENGTH: 300,
    SPOTIFY_LINK_MAX_LENGTH: 50,
    SOUNDCLOUD_LINK_MAX_LENGTH: 50,
    INSTAGRAM_LINK_MAX_LENGTH: 50,
    YOUTUBE_LINK_MAX_LENGTH: 50,
    FACEBOOK_LINK_MAX_LENGTH: 50,
  },
  ACT_MEDIA: {
    VIDEO_LINK_MAX_LENGTH: 50,
    AUDIO_LINK_MAX_LENGTH: 50,
  },
  PAYMENT: {
    TYPICAL_PRICE_MAX_LENGTH: 10,
    MINIMAL_PRICE_MAX_LENGTH: 10,
  },
  HOME: {
    SEARCH_MAX_LENGTH: 100,
    LOCATION_MAX_LENGTH: 50,
    DISTANCE_MAX_LENGTH: 10,
  },
};

export const USER_CONSTANTS = {
  EMAIL: {
    MAX_LENGTH: 50,
  },
  FIRST_NAME: {
    MAX_LENGTH: 50,
  },
  LAST_NAME: {
    MAX_LENGTH: 50,
  },
  LOCATION: {
    CITY_MAX_LENGTH: 50,
    STREET_ADDRESS_MAX_LENGTH: 50,
    ZIP_CODE_MAX_LENGTH: 7,
  },
};

export const CONTRACT_DETAILS = {
  PERFORMER_ROLE: {
    MAX_LENGTH: 50,
  },
  FEE: {
    MAX_LENGTH: 10,
  },
  MESSAGE: {
    MAX_LENGTH: 300,
  },
};

export const EVENT_CONSTANTS = {
  EVENT_NAME: {
    MAX_LENGTH: 50,
  },
  ABOUT_EVENT: {
    MAX_LENGTH: 300,
  },
  SOCIAL_LINKS: {
    MAX_LENGTH: 50,
  },
};
