import * as yup from "yup";
import { AUTH_CONSTANTS } from "../constants";

export const LoginValidation = yup.object().shape({
  email: yup
    .string()
    .email("Invalid Email")
    .max(
      AUTH_CONSTANTS.EMAIL.MAX_LENGTH,
      `Email must be at most ${AUTH_CONSTANTS.EMAIL.MAX_LENGTH} characters`,
    )
    .required("Em<PERSON> is Required!"),
  password: yup
    .string()
    .min(
      AUTH_CONSTANTS.PASSWORD.MIN_LENGTH,
      `Password must be at least ${AUTH_CONSTANTS.PASSWORD.MIN_LENGTH} characters`,
    )
    .max(
      AUTH_CONSTANTS.PASSWORD.MAX_LENGTH,
      `Password must be at most ${AUTH_CONSTANTS.PASSWORD.MAX_LENGTH} characters`,
    )
    .required("Password is Required!"),
});
