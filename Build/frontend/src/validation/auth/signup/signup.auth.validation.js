import * as yup from "yup";
import { AUTH_CONSTANTS } from "../constants";

export const signUpValidation = yup.object().shape({
  email: yup
    .string()
    .trim()
    .email("Invalid Email")
    .max(
      AUTH_CONSTANTS.EMAIL.MAX_LENGTH,
      `Email must be at most ${AUTH_CONSTANTS.EMAIL.MAX_LENGTH} characters`,
    )
    .required("Email is Required!"),
  password: yup
    .string()
    .required("Password is Required!")
    .min(
      AUTH_CONSTANTS.PASSWORD.MIN_LENGTH,
      `Password must be at least ${AUTH_CONSTANTS.PASSWORD.MIN_LENGTH} characters`,
    )
    .max(
      AUTH_CONSTANTS.PASSWORD.MAX_LENGTH,
      `Password must be at most ${AUTH_CONSTANTS.PASSWORD.MAX_LENGTH} characters`,
    )
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
    ),
  repeatPassword: yup
    .string()
    .required("Repeat Password is Required!")
    .oneOf([yup.ref("password")], "Passwords must match"),
});
