/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,ts,jsx,tsx,mdx}"],
  theme: {
    extend: {
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic": "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      fontFamily: {
        craftWorkRegular: ["CraftworkGroteskRegular"],
        craftWorkGX: ["CraftworkGroteskGX"],
        craftWorkMedium: ["CraftworkGroteskMedium"],
        craftWorkSemiBold: ["CraftworkGroteskSemiBold"],
        craftWorkBold: ["CraftworkGroteskBold"],
        craftWorkHeavy: ["CraftworkGroteskHeavy"],
      },
    },
  },
  plugins: [],
};
