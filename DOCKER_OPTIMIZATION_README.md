# StageMinder Docker 优化配置指南

## 概览

这个优化配置将 Neo4j 数据库提取为独立服务，让 `StageMinder` 和 `AdminConsole` 两个项目可以共享同一个 Neo4j 实例。这种架构具有以下优势：

✅ **资源效率**: 只需要一个 Neo4j 实例  
✅ **数据一致性**: 两个项目共享同一个数据库  
✅ **独立部署**: 各个项目可以独立启动/停止  
✅ **简化维护**: 统一的数据库管理  
✅ **开发友好**: 支持开发和生产环境  

## 架构说明

```
┌─────────────────────────────────────────────────────────────┐
│                    StageMinder System                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                 │
│  │  StageMinder    │    │  AdminConsole   │                 │
│  │  ┌─────────────┐│    │  ┌─────────────┐│                 │
│  │  │  Frontend   ││    │  │  Frontend   ││                 │
│  │  │  :3000      ││    │  │  :3001      ││                 │
│  │  └─────────────┘│    │  └─────────────┘│                 │
│  │  ┌─────────────┐│    │  ┌─────────────┐│                 │
│  │  │  Backend    ││    │  │  Backend    ││                 │
│  │  │  :8080      ││    │  │  :8081      ││                 │
│  │  └─────────────┘│    │  └─────────────┘│                 │
│  │  ┌─────────────┐│    │                 │                 │
│  │  │   Nginx     ││    │                 │                 │
│  │  │   :80       ││    │                 │                 │
│  │  └─────────────┘│    │                 │                 │
│  └─────────────────┘    └─────────────────┘                 │
│           │                       │                         │
│           └───────────┬───────────┘                         │
│                       │                                     │
│              ┌─────────────────┐                            │
│              │     Neo4j       │                            │
│              │  :7474, :7687   │                            │
│              │   (Shared DB)   │                            │
│              └─────────────────┘                            │
└─────────────────────────────────────────────────────────────┘
```

## 文件结构

```
StageMinderRepo-main/
├── docker-compose.neo4j.yml          # 独立的 Neo4j 服务
├── docker-compose.main.yml           # 完整系统管理
├── docker-control.sh                 # Linux/Mac 控制脚本
├── docker-control.bat                # Windows 控制脚本
├── DOCKER_OPTIMIZATION_README.md     # 本文档
│
├── StageMinder/
│   ├── docker-compose-optimized.yml       # 生产环境配置
│   └── docker-compose-local-optimized.yml # 开发环境配置
│
└── adminconsole/
    └── docker-compose-optimized.yml       # Admin Console 配置
```

## 快速开始

### 方法一：使用控制脚本（推荐）

**Windows 用户：**
```powershell
# 启动完整系统
.\docker-control.bat start

# 启动开发环境
.\docker-control.bat start dev

# 查看状态
.\docker-control.bat status

# 停止所有服务
.\docker-control.bat stop
```

**Linux/Mac 用户：**
```bash
# 添加执行权限
chmod +x docker-control.sh

# 启动完整系统
./docker-control.sh start

# 启动开发环境
./docker-control.sh start dev

# 查看状态
./docker-control.sh status

# 停止所有服务
./docker-control.sh stop
```

### 方法二：手动启动

1. **启动共享 Neo4j 数据库：**
```bash
docker-compose -f docker-compose.neo4j.yml up -d
```

2. **启动 StageMinder 应用：**
```bash
cd StageMinder
docker-compose -f docker-compose-optimized.yml up -d
cd ..
```

3. **启动 AdminConsole：**
```bash
cd adminconsole
docker-compose -f docker-compose-optimized.yml up -d
cd ..
```

## 详细使用说明

### 控制脚本命令

| 命令 | 说明 |
|------|------|
| `start` | 启动完整系统（生产模式） |
| `start neo4j` | 仅启动 Neo4j 数据库 |
| `start stageminder` | 仅启动 StageMinder 应用 |
| `start admin` | 仅启动 Admin Console |
| `start dev` | 启动开发环境（热重载） |
| `stop` | 停止所有服务 |
| `restart` | 重启所有服务 |
| `status` | 显示服务状态 |
| `logs [service]` | 显示日志 |
| `cleanup` | 清理所有数据（危险操作！） |

### 服务访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| Neo4j Browser | http://localhost:7474 | 数据库管理界面 |
| StageMinder 应用 | http://localhost | 主应用 |
| StageMinder API | http://localhost:8080 | 后端 API |
| Admin Console UI | http://localhost:3001 | 管理界面 |
| Admin Console API | http://localhost:8081 | 管理 API |

**Neo4j 登录信息：**
- 用户名: `neo4j`
- 密码: `stageminder2024`

## 环境配置

### 生产环境

使用 `docker-compose.main.yml` 或 `start` 命令：
- 完整的服务栈
- 优化的资源配置
- 自动重启策略
- 健康检查

### 开发环境

使用 `start dev` 命令或手动启动开发配置：
- 代码热重载
- 开发工具支持
- 源码挂载
- LiveReload 功能

## 数据持久化

所有数据存储在命名卷中：
- `stageminder_neo4j_data`: 数据库文件
- `stageminder_neo4j_logs`: 日志文件
- `stageminder_neo4j_conf`: 配置文件
- `stageminder_maven_cache`: Maven 缓存

## 网络配置

使用统一的 Docker 网络：
- 网络名: `stageminder-shared-network`
- 类型: bridge
- 所有服务都连接到此网络

## 常见问题

### 1. Neo4j 启动失败

**问题**: Neo4j 容器启动后立即退出
**解决方案**:
```bash
# 查看日志
docker logs stageminder-neo4j-shared

# 检查内存配置
docker stats

# 如果内存不足，调整 docker-compose.neo4j.yml 中的内存设置
```

### 2. 服务无法连接到 Neo4j

**问题**: 应用报告无法连接到数据库
**解决方案**:
```bash
# 1. 确保 Neo4j 完全启动
docker-control.sh status

# 2. 检查网络连接
docker network ls | grep stageminder

# 3. 测试连接
docker exec stageminder-neo4j-shared cypher-shell -u neo4j -p stageminder2024 "RETURN 1"
```

### 3. 端口冲突

**问题**: 端口已被占用
**解决方案**:
```bash
# 查看端口占用
netstat -tulpn | grep :7474
netstat -tulpn | grep :8080

# 修改 docker-compose 文件中的端口映射
```

### 4. 权限问题（Linux/Mac）

**问题**: 权限被拒绝
**解决方案**:
```bash
# 给脚本添加执行权限
chmod +x docker-control.sh

# 如果是数据卷权限问题
sudo chown -R $(id -u):$(id -g) /var/lib/docker/volumes/stageminder_neo4j_data
```

## 迁移指南

### 从旧配置迁移

如果你之前使用的是包含 Neo4j 的配置，请按以下步骤迁移：

1. **备份数据（重要！）**
```bash
# 导出现有数据
docker exec <old-neo4j-container> neo4j-admin dump --database=neo4j --to=/tmp/backup.dump
docker cp <old-neo4j-container>:/tmp/backup.dump ./neo4j-backup.dump
```

2. **停止旧服务**
```bash
docker-compose down
```

3. **启动新的 Neo4j 服务**
```bash
docker-compose -f docker-compose.neo4j.yml up -d
```

4. **恢复数据**
```bash
# 复制备份文件到新容器
docker cp ./neo4j-backup.dump stageminder-neo4j-shared:/tmp/backup.dump

# 停止 Neo4j 进行恢复
docker-compose -f docker-compose.neo4j.yml stop
docker exec stageminder-neo4j-shared neo4j-admin load --from=/tmp/backup.dump --database=neo4j --force
docker-compose -f docker-compose.neo4j.yml start
```

5. **启动应用服务**
```bash
./docker-control.sh start
```

### 回滚到旧配置

如果需要回到之前的配置：

1. 停止新服务：`./docker-control.sh stop`
2. 使用旧的 docker-compose 文件
3. 如果需要，恢复数据备份

## 监控和维护

### 查看资源使用情况
```bash
# 查看容器资源使用
docker stats

# 查看磁盘使用
docker system df

# 查看卷使用情况
docker volume ls
```

### 日志管理
```bash
# 查看特定服务日志
./docker-control.sh logs neo4j
./docker-control.sh logs backend

# 清理日志（如果需要）
docker system prune -f
```

### 定期维护
```bash
# 清理未使用的镜像和容器
docker system prune -f

# 更新镜像
docker-compose -f docker-compose.neo4j.yml pull
docker-compose -f docker-compose.main.yml pull
```

## 性能优化

### Neo4j 性能调优

在 `docker-compose.neo4j.yml` 中调整以下参数：

```yaml
environment:
  # 根据服务器内存调整
  - NEO4J_dbms_memory_heap_initial__size=1G
  - NEO4J_dbms_memory_heap_max__size=4G
  - NEO4J_dbms_memory_pagecache_size=2G
  
  # 查询性能
  - NEO4J_dbms_logs_query_enabled=INFO
  - NEO4J_dbms_logs_query_threshold=1s
```

### 容器资源限制

在必要时添加资源限制：

```yaml
deploy:
  resources:
    limits:
      memory: 4G
      cpus: '2'
    reservations:
      memory: 2G
      cpus: '1'
```

## 安全考虑

1. **更改默认密码**: 在生产环境中修改 Neo4j 密码
2. **网络隔离**: 使用自定义网络隔离服务
3. **访问控制**: 配置防火墙规则限制端口访问
4. **TLS 加密**: 在生产环境中启用 HTTPS

## 支持

如果遇到问题，请：

1. 查看日志：`./docker-control.sh logs`
2. 检查状态：`./docker-control.sh status`
3. 参考本文档的常见问题部分
4. 查看 Docker 官方文档

---

**注意**: 这个优化配置已经经过测试，但在生产环境使用前，请确保在测试环境中验证所有功能正常工作。
