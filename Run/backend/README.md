# StageMinder Run Backend folder
It's a placeholder and when deployStageMinder.sh script ran, it will copy the backend artifacts and configuration in this folder. Please refere Backend under Build for the details.

The logs folder will have hdb-server.log rollover every day and StageServer.log when it runs everytime.

Following folders copied from Build are listed below.

1. config - application.properties
2. images - icons
3. json-files - json files that get loaded into DB
4. pdf-files - pdf templates for rider, etc.

