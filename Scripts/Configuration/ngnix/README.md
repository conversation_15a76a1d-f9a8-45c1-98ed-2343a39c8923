# Nginx Configuration for StageMinder

This document explains the Nginx configuration for the `StageMinder.com` application, which serves as a reverse proxy to route traffic to a frontend (running on port 3000) and a Spring Boot backend (running on port 8080). The configuration includes HTTPS redirection, WebSocket support, OAuth2 routes, and optimized image handling.

## Overview

The provided Nginx configuration file, named `default_SSL`, is located in the `/Configuration/nginx` folder. It is designed to:
- Redirect HTTP traffic to HTTPS.
- Proxy requests to a frontend application (port 3000).
- Proxy API, WebSocket, OAuth2, and actuator endpoints to a Spring Boot backend (port 8080).
- Support secure communication with SSL/TLS certificates.
- Handle specific OAuth2 routes for Google and Facebook authentication.
- Optimize image delivery for Next.js applications.

A script, `setNginxSSL.sh`, is provided to automate the placement of the `default_SSL` configuration file into the appropriate Nginx directory. The configuration is typically placed in `/etc/nginx/sites-available/StageMinder` and symlinked to `/etc/nginx/sites-enabled/` for activation.

## Prerequisites

1. **Nginx Installed**: Ensure Nginx is installed on your server (e.g., `sudo apt install nginx` on Debian/Ubuntu).
2. **SSL Certificates**: Obtain and place your SSL certificate (`StageMinder.crt`) and private key (`StageMinder.key`) in `/etc/nginx/ssl/`.
3. **Backend Services**:
   - A frontend application (e.g., Next.js) running on `localhost:3000`.
   - A Spring Boot backend running on `localhost:8080`.
4. **Domain Setup**: Replace `StageMinder.com` with your actual domain or server IP.

## Configuration Breakdown

### 1. HTTP to HTTPS Redirection
```nginx
server {
    listen 80;
    server_name StageMinder.com;
    return 301 https://$host$request_uri;
}
```
### 2. Main HTTPS Server Block
```server {
    listen 443 ssl;
    server_name StageMinder.com localhost;
    ssl_certificate /etc/nginx/ssl/StageMinder.crt;
    ssl_certificate_key /etc/nginx/ssl/StageMinder.key;
    client_max_body_size 64M;
}
```
### 3. Frontend Proxy (Root Path)
```location / {
    proxy_pass http://localhost:3000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```
### 4. Image Optimization for Next.js
```location /_next/image {
    proxy_pass http://localhost:3000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_buffer_size 128k;
    proxy_buffers 4 256k;
    proxy_busy_buffers_size 256k;
}
```
### 5. API Proxy
```location /api/ {
    proxy_pass http://localhost:8080/api/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```
### 6. WebSocket Proxy
```location /ws/ {
    proxy_pass http://localhost:8080/ws/;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "Upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```
### 7. OAuth2 Routes
```location /oauth2/ {
    proxy_pass http://localhost:8080/oauth2/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
location = /login/oauth2/code/google {
    proxy_pass http://localhost:8080/login/oauth2/code/google;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
location = /login/oauth2/code/facebook {
    proxy_pass http://localhost:8080/login/oauth2/code/facebook;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```
## Use script to set the config.
The setNginxSSL.sh script automates the deployment of the default_SSL configuration file. Below is a breakdown of its assumed functionality (based on typical Nginx setup scripts):


