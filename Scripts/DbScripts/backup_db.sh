#!/bin/bash

# Neo4j Backup Script for Community Edition
# Run this script as a user with appropriate permissions (e.g., neo4j user or with sudo)

# Configuration variables
NEO4J_HOME="/var/lib/neo4j"  # Neo4j installation directory
BACKUP_DIR="/home/<USER>/StageMinder/Scripts/DbScripts"  # Directory to store backups
DATABASE_NAME="neo4j"
BACKUP_FILENAME="neo4j_backup_$(date +%Y%m%d_%H%M%S).tar.gz"  # Timestamped backup file
LOG_FILE="/var/log/neo4j_backup.log"  # Log file for backup operations
NEO4J_USER="neo4j"  # User running the Neo4j service

# Ensure backup directory exists
mkdir -p "$BACKUP_DIR"
if [ $? -ne 0 ]; then
    echo "$(date): Failed to create backup directory $BACKUP_DIR" >> "$LOG_FILE"
    exit 1
fi

# Function to log messages
log_message() {
    echo "$(date): $1" >> "$LOG_FILE"
}

# Check if script is run with sufficient permissions
if [ "$(id -u)" != "0" ] && [ "$(whoami)" != "$NEO4J_USER" ]; then
    log_message "Error: Script must be run as root or $NEO4J_USER user."
    echo "Error: Script must be run as root or $NEO4J_USER user."
    exit 1
fi

# Stop the Neo4j service
log_message "Stopping Neo4j service..."
if ! systemctl stop neo4j; then
    log_message "Error: Failed to stop Neo4j service."
    echo "Error: Failed to stop Neo4j service."
    exit 1
fi

# Verify Neo4j is stopped
# With this:
if systemctl is-active --quiet neo4j; then
    log_message "Error: Neo4j service is still running."
    echo "Error: Neo4j service is still running."
    exit 1
fi

# Create backup
log_message "Creating backup of $DATABASE_NAME..."
cd "$NEO4J_HOME/data/databases" || {
    log_message "Error: Could not access database directory $NEO4J_HOME/data/databases."
    echo "Error: Could not access database directory."
    exit 1
}

tar -czf "$BACKUP_DIR/$BACKUP_FILENAME" "$DATABASE_NAME" 2>>"$LOG_FILE"
if [ $? -eq 0 ]; then
    log_message "Backup created successfully: $BACKUP_DIR/$BACKUP_FILENAME"
    echo "Backup created successfully: $BACKUP_DIR/$BACKUP_FILENAME"
else
    log_message "Error: Backup creation failed."
    echo "Error: Backup creation failed."
    exit 1
fi

# Set correct permissions for the backup file
chown "$NEO4J_USER:$NEO4J_USER" "$BACKUP_DIR/$BACKUP_FILENAME"
chmod 600 "$BACKUP_DIR/$BACKUP_FILENAME"
log_message "Set permissions for backup file."

# Start the Neo4j service
log_message "Starting Neo4j service..."
if ! systemctl start neo4j; then
    log_message "Error: Failed to start Neo4j service."
    echo "Error: Failed to start Neo4j service."
    exit 1
fi

# Verify Neo4j is running
if systemctl is-active neo4j | grep -q "active"; then
    log_message "Neo4j service started successfully."
    echo "Neo4j service started successfully."
else
    log_message "Error: Neo4j service failed to start."
    echo "Error: Neo4j service failed to start."
    exit 1
fi

log_message "Backup process completed."
echo "Backup process completed."