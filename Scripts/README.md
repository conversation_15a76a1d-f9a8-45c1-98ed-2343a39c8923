# State Minder Scripts

## Overview

**State Minder Scripts** is a collection of scripts designed to manage and configure the **StageMinder** system. This repository includes scripts for database operations, service control, configuration management, building, deploying, and running the StageMinder server to ensure smooth operation and maintenance of the application.

## Folder Structure

The repository is organized into the following directories:

- **`DbScripts/`**: Contains database-related scripts, such as database backup and maintenance scripts.
- **`ServiceCtrl/`**: Includes scripts for controlling services, such as starting and stopping services in the StageMinder system.
- **`Configuration/`**: Stores all configuration files used by the StageMinder system.
  - **`applied/`**: Stores copies of configurations applied during the last run of StageMinder. This directory is updated by scripts like `runStageMinder.sh` and `freshStageMinder.sh` when the server starts, providing a convenient location to review applied configurations.
  - **`neo4j/`**: Contains configuration files specific to the Neo4j database.
  - **`ngnix/`**: Holds Nginx server configuration files.
    - **`SSL/`**: Contains SSL certificates and related configuration scripts for Nginx.
  - **`server/`**: Includes frontend and backend configuration files for the StageMinder system.
  - **`other/`**: Stores alternative or inactive configurations that are not currently in use.
- **`Build/`**: Contains source code for the backend and frontend, used for building the StageMinder application.
- **`Run/`**: Stores deployed artifacts (e.g., compiled backend and frontend files) used to run the StageMinder server.

## Usage

1. **Database Scripts** (`DbScripts/`):
   - Use these scripts for database backup, restoration, or other database-related tasks.
   - Example: Run `bash backup_db.sh` to create a backup of the StageMinder database.

2. **Service Control Scripts** (`ServiceCtrl/`):
   - Use these scripts to start, stop, or manage StageMinder services.
   - Example: Run `bash start_service.sh` to start a specific service.

3. **Building and Deploying the StageMinder Server**:
   - Use the `freshStageMinder.sh` script to perform a fresh setup and deployment of the StageMinder server. This script:
     1. Sets the necessary configurations (from `Configuration/`).
     2. Builds the backend and frontend from sources located in the `Build/` directory.
     3. Deploys the resulting artifacts to the `Run/` directory.
     4. Executes the `runStageMinder.sh` script to start the server.
   - **Important**: Before running `freshStageMinder.sh`, ensure no frontend processes are currently running to avoid conflicts during deployment.
   - Example: Run `bash freshStageMinder.sh` to build, deploy, and start the StageMinder server.

4. **Running the StageMinder Server**:
   - Use the `runStageMinder.sh` script to start the StageMinder server (backend first, then frontend). This script also copies the applied configurations to the `Configuration/applied/` directory for reference.
   - **Important**: Ensure no frontend processes are running before executing this script to avoid conflicts.
   - Example: Run `bash runStageMinder.sh` to start the StageMinder server.

5. **Configuration Management** (`Configuration/`):
   - Modify configuration files in the respective subdirectories (`neo4j/`, `ngnix/`, `server/`) as needed.
   - The `applied/` folder is automatically updated by `runStageMinder.sh` and `freshStageMinder.sh` when the server starts, reflecting the configurations used in the last run.
   - SSL certificates and configurations are managed in the `SSL/` subdirectory under `ngnix/`.
   - Inactive or alternative configurations can be stored in the `other/` folder.

**Note**: Before running any `.sh` script (including `freshStageMinder.sh` or `runStageMinder.sh`), ensure it has executable permissions. Use the following command to make a script executable:
```bash
chmod +x script_name.sh
