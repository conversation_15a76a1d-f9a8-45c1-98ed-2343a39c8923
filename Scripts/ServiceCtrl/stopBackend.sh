#!/bin/bash

# This script stops all the services that StageMinder uses

APP_NAME="StageServer"
BACKEND_DIR="/home/<USER>/StageMinder/Run/backend"

# Stop Stage Server
cd "$BACKEND_DIR"
# Look for the latest matching StageServer-*.jar in the current directory
JAR_NAME=$(ls -t ./${APP_NAME}-*.jar 2>/dev/null | head -n 1)

if [ -z "$JAR_NAME" ]; then
    echo "No JAR file found in current directory matching ${APP_NAME}-*.jar"
    exit 1
fi

echo "🔍 Found JAR: $JAR_NAME"

# Check if debug mode is requested
DEBUG_MODE=false
if [[ "$1" == "--debug" ]]; then
    DEBUG_MODE=true
fi

# Kill existing app process if running
server_pid=$(ps -ef | grep "$APP_NAME" | grep -v grep | awk '{print $2}')
if [ -n "$server_pid" ]; then
    echo "$APP_NAME is already running (PID $server_pid). Killing it..."
    kill -9 "$server_pid"
    sleep 2
else
    echo "$APP_NAME is not currently running."
fi

echo "StageMinder backend stopped successfully"