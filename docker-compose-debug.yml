version: '3.8'

services:
  admin-console-debug:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: stageminder-admin-console-debug
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      # Enable remote debugging
      - DEBUG_OPTS=-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005
      # Force Neo4j connection settings
      - SPRING_NEO4J_URI=bolt://stageminder-neo4j:7687
      - NEO4J_URI=bolt://stageminder-neo4j:7687
      - SPRING_NEO4J_AUTHENTICATION_USERNAME=neo4j
      - NEO4J_USERNAME=neo4j
      - SPRING_NEO4J_AUTHENTICATION_PASSWORD=stageminder2024
      - NEO4J_PASSWORD=stageminder2024
      # Connect to StageM<PERSON> backend if needed
      - STAGEMINDER_BACKEND_URL=http://stageminder-backend:8080
      # Disable system metrics to avoid ProcessorMetrics issues
      - MANAGEMENT_METRICS_BINDERS_SYSTEM_ENABLED=false
      # Override any hardcoded localhost settings
      - SPRING_DATA_NEO4J_URI=bolt://stageminder-neo4j:7687
    ports:
      - "8081:8081"
      - "5005:5005"  # Debug port
    networks:
      - stageminder-shared-network
    volumes:
      # Optional: Mount source code for hot reload (if using Spring DevTools)
      - ./src:/app/src:ro
    restart: unless-stopped

networks:
  stageminder-shared-network:
    external: true
