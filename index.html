<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StageMinder Admin Console</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css" rel="stylesheet">
    <link rel="icon" href="data:,">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar Navigation -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="logo.webp" alt="StageMinder Logo" class="logo-img" />
                    <span class="logo-text">StageMinder</span>
                </div>
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <ul class="nav-list">
                        <li class="nav-item active">
                            <a href="#dashboard" class="nav-link" onclick="showSection('dashboard')">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#users" class="nav-link" onclick="showSection('users')">
                                <i class="fas fa-users"></i>
                                <span>User Management</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#acts-venues" class="nav-link" onclick="showSection('acts-venues')">
                                <i class="fas fa-music"></i>
                                <span>Acts & Venues</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#analytics" class="nav-link" onclick="showSection('analytics')">
                                <i class="fas fa-chart-line"></i>
                                <span>Analytics</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#reports" class="nav-link" onclick="showSection('reports')">
                                <i class="fas fa-file-alt"></i>
                                <span>Reports</span>
                            </a>
                        </li>
                    </ul>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#settings" class="nav-link" onclick="showSection('settings')">
                                <i class="fas fa-cog"></i>
                                <span>Settings</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#logs" class="nav-link" onclick="showSection('logs')">
                                <i class="fas fa-clipboard-list"></i>
                                <span>System Logs</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <div class="sidebar-footer">
                <div class="connection-status">
                    <span id="connectionStatus" class="status disconnected">
                        <i class="fas fa-circle"></i>
                        <span>Disconnected</span>
                    </span>
                </div>
            </div>
        </aside>

        <!-- Main Content Area -->
        <div class="main-wrapper">
            <!-- Top Navigation -->
            <header class="top-nav">
                <div class="breadcrumb">
                    <span class="breadcrumb-item">
                        <i class="fas fa-home"></i>
                        Admin Console
                    </span>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item" id="currentSection">Dashboard</span>
                </div>
                
                <div class="top-nav-actions">
                    <button class="action-btn" onclick="refreshData()" title="Refresh Data">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="action-btn" onclick="showNotifications()" title="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <button id="mockToggleBtn" class="action-btn" onclick="toggleMockMode()" title="Toggle Mock Data Mode">
                        <i class="fas fa-flask"></i>
                    </button>
                    <div class="user-menu">
                        <button class="user-avatar" onclick="toggleUserMenu()">
                            <i class="fas fa-user-circle"></i>
                            <span>Admin User</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="user-dropdown" id="userDropdown">
                            <a href="#profile" class="dropdown-item">
                                <i class="fas fa-user"></i>
                                Profile
                            </a>
                            <a href="#logout" class="dropdown-item">
                                <i class="fas fa-sign-out-alt"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Dashboard Section -->
                <div id="dashboard-section" class="content-section active">
                    <div class="page-header">
                        <h1 class="page-title">Dashboard Overview</h1>
                        <p class="page-description">System status and key metrics at a glance</p>
                    </div>
                    
                    <!-- Statistics Cards -->
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalUsersCount">0</div>
                                <div class="stat-label">Total Users</div>
                                <div class="stat-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    12% from last month
                                </div>
                            </div>
                        </div>
                        
                        <div class="stat-card success">
                            <div class="stat-icon">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="activeUsersCount">0</div>
                                <div class="stat-label">Active Users</div>
                                <div class="stat-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    8% from last month
                                </div>
                            </div>
                        </div>
                        
                        <div class="stat-card warning">
                            <div class="stat-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="secureUsersCount">0</div>
                                <div class="stat-label">2FA Enabled</div>
                                <div class="stat-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    15% from last month
                                </div>
                            </div>
                        </div>
                        
                        <div class="stat-card info">
                            <div class="stat-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="systemHealthScore">98%</div>
                                <div class="stat-label">System Health</div>
                                <div class="stat-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    Excellent
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="dashboard-section">
                        <h2 class="section-title">Quick Actions</h2>
                        <div class="quick-actions">
                            <button class="action-card" onclick="queryAllUsers()">
                                <i class="fas fa-users"></i>
                                <span>View All Users</span>
                                <div class="action-description">Access complete user database</div>
                            </button>
                            <button class="action-card" onclick="queryUserStats()">
                                <i class="fas fa-chart-bar"></i>
                                <span>Generate Report</span>
                                <div class="action-description">Create comprehensive statistics</div>
                            </button>
                            <button class="action-card" onclick="checkSystemHealth()">
                                <i class="fas fa-heartbeat"></i>
                                <span>System Check</span>
                                <div class="action-description">Run diagnostic tests</div>
                            </button>
                            <button class="action-card" onclick="showSection('settings')">
                                <i class="fas fa-cog"></i>
                                <span>System Settings</span>
                                <div class="action-description">Configure system parameters</div>
                            </button>
                        </div>
                    </div>

                    <!-- Dashboard Widgets -->
                    <div class="dashboard-widgets-grid">
                        <div class="widget-card">
                            <div class="widget-title"><i class="fas fa-chart-line"></i> Daily Active Users (30d)</div>
                            <canvas id="dauChart" height="120"></canvas>
                        </div>
                        <div class="widget-card">
                            <div class="widget-title"><i class="fas fa-share-nodes"></i> Sign-in Providers</div>
                            <canvas id="providersChartDashboard" height="120"></canvas>
                        </div>
                        <div class="widget-card">
                            <div class="widget-title"><i class="fas fa-star"></i> Top Artists</div>
                            <div class="results-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Followers</th>
                                            <th>Posts</th>
                                            <th>Last Login</th>
                                        </tr>
                                    </thead>
                                    <tbody id="topArtistsBody"></tbody>
                                </table>
                            </div>
                        </div>
                        <div class="widget-card">
                            <div class="widget-title"><i class="fas fa-shield-alt"></i> Security Overview</div>
                            <div class="progress-group">
                                <div class="progress-row">
                                    <div class="progress-label">2FA Adoption</div>
                                    <div class="progress"><div id="prog2fa" class="progress-bar" style="width: 0%"></div></div>
                                    <div id="prog2faPct" class="progress-pct">0%</div>
                                </div>
                                <div class="progress-row">
                                    <div class="progress-label">Active Users</div>
                                    <div class="progress"><div id="progActive" class="progress-bar success" style="width: 0%"></div></div>
                                    <div id="progActivePct" class="progress-pct">0%</div>
                                </div>
                                <div class="progress-row">
                                    <div class="progress-label">Social Login Ratio</div>
                                    <div class="progress"><div id="progSocial" class="progress-bar info" style="width: 0%"></div></div>
                                    <div id="progSocialPct" class="progress-pct">0%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Management Section -->
                <div id="users-section" class="content-section">
                    <div class="page-header">
                        <h1 class="page-title">User Management</h1>
                        <p class="page-description">Manage users, permissions, and access controls</p>
                    </div>
                    
                    <!-- Search and Filters -->
                    <div class="toolbar">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="Search users..." id="userSearch">
                        </div>
                        <div class="toolbar-actions">
                            <button class="btn secondary" onclick="exportUsers()">
                                <i class="fas fa-download"></i>
                                Export
                            </button>
                            <button class="btn primary" onclick="addUser()">
                                <i class="fas fa-plus"></i>
                                Add User
                            </button>
                        </div>
                    </div>
                    
                    <!-- Query Filters -->
                    <div class="filter-panel">
                        <h3 class="filter-title">Query Filters</h3>
                        <div class="filter-buttons">
                            <button class="filter-btn active" onclick="queryAllUsers()" data-filter="all">
                                <i class="fas fa-users"></i>
                                All Users
                            </button>
                            <button class="filter-btn" onclick="queryEnabledUsers()" data-filter="enabled">
                                <i class="fas fa-user-check"></i>
                                Active Only
                            </button>
                            <button class="filter-btn" onclick="queryArtistUsers()" data-filter="artists">
                                <i class="fas fa-palette"></i>
                                Artists
                            </button>
                            <button class="filter-btn" onclick="queryUsersWithLocation()" data-filter="location">
                                <i class="fas fa-map-marker-alt"></i>
                                With Location
                            </button>
                        </div>
                    </div>
                    
                    <!-- Results Section -->
                    <div class="results-section">
                        <div class="results-header">
                            <h3>Query Results</h3>
                            <div class="results-info">
                                <span id="resultsCount">No results</span>
                                <span id="queryTime"></span>
                            </div>
                        </div>
                        
                        <div class="loading" id="loading" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>Loading data...</span>
                        </div>
                        
                        <div class="error-message" id="errorMessage" style="display: none;">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span id="errorText"></span>
                        </div>
                        
                        <div class="results-container">
                            <div id="resultsTable" class="results-table">
                                <div class="no-results">
                                    <i class="fas fa-users"></i>
                                    <p>Select a filter above to view user data</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Acts & Venues Section -->
                <div id="acts-venues-section" class="content-section">
                    <div class="page-header">
                        <h1 class="page-title">Acts & Venues Management</h1>
                        <p class="page-description">Manage acts and venues data</p>
                    </div>
                    
                    <!-- Statistics Cards -->
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-icon">
                                <i class="fas fa-music"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalActsCount">0</div>
                                <div class="stat-label">Total Acts</div>
                                <div class="stat-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    Including virtual acts
                                </div>
                            </div>
                        </div>
                        
                        <div class="stat-card success">
                            <div class="stat-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalVenuesCount">0</div>
                                <div class="stat-label">Total Venues</div>
                                <div class="stat-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    Including virtual venues
                                </div>
                            </div>
                        </div>
                        
                        <div class="stat-card warning">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="publishedActsCount">0</div>
                                <div class="stat-label">Published Acts</div>
                                <div class="stat-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    Active profiles
                                </div>
                            </div>
                        </div>
                        
                        <div class="stat-card info">
                            <div class="stat-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalProfilesCount">0</div>
                                <div class="stat-label">Total Profiles</div>
                                <div class="stat-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    All profile types
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="dashboard-section">
                        <h2 class="section-title">Quick Actions</h2>
                        <div class="quick-actions">
                            <button class="action-card" onclick="insertSampleActs()">
                                <i class="fas fa-plus"></i>
                                <span>Insert Sample Acts</span>
                                <div class="action-description">Add 10 sample act profiles to database</div>
                            </button>
                            <button class="action-card" onclick="refreshActsVenuesStats()">
                                <i class="fas fa-sync-alt"></i>
                                <span>Refresh Statistics</span>
                                <div class="action-description">Update acts and venues counts</div>
                            </button>
                            <button class="action-card" onclick="viewAllActs()">
                                <i class="fas fa-list"></i>
                                <span>View All Acts</span>
                                <div class="action-description">Display all act profiles</div>
                            </button>
                            <button class="action-card" onclick="viewAllVenues()">
                                <i class="fas fa-building"></i>
                                <span>View All Venues</span>
                                <div class="action-description">Display all venue profiles</div>
                            </button>
                        </div>
                    </div>

                    <!-- Filter Panel -->
                    <div class="filter-panel">
                        <h3 class="filter-title">View Filters</h3>
                        <div class="filter-buttons">
                            <button class="filter-btn active" onclick="viewActsVenuesStats()" data-filter="stats">
                                <i class="fas fa-chart-bar"></i>
                                Statistics Overview
                            </button>
                            <button class="filter-btn" onclick="viewAllActs()" data-filter="acts">
                                <i class="fas fa-music"></i>
                                All Acts
                            </button>
                            <button class="filter-btn" onclick="viewAllVenues()" data-filter="venues">
                                <i class="fas fa-building"></i>
                                All Venues
                            </button>
                        </div>
                    </div>
                    
                    <!-- Results Section -->
                    <div class="results-section">
                        <div class="results-header">
                            <h3 id="actsVenuesResultsTitle">Statistics Overview</h3>
                            <div class="results-info">
                                <span id="actsVenuesResultsCount">No results</span>
                                <span id="actsVenuesQueryTime"></span>
                            </div>
                        </div>
                        
                        <div class="loading" id="actsVenuesLoading" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>Loading data...</span>
                        </div>
                        
                        <div class="error-message" id="actsVenuesErrorMessage" style="display: none;">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span id="actsVenuesErrorText"></span>
                        </div>
                        
                        <div class="results-container">
                            <div id="actsVenuesResultsTable" class="results-table">
                                <div class="no-results">
                                    <i class="fas fa-music"></i>
                                    <p>Select a filter above to view acts and venues data</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other sections (placeholders) -->
                <div id="analytics-section" class="content-section">
                    <div class="page-header">
                        <h1 class="page-title">Analytics</h1>
                        <p class="page-description">Detailed analytics and performance metrics</p>
                    </div>
                    <div class="charts-grid">
                        <div class="chart-card">
                            <div class="chart-title"><i class="fas fa-file-signature"></i> Contracts Created (Last 30 days)</div>
                            <canvas id="usersGrowthChart" height="140"></canvas>
                        </div>
                        <div class="chart-card">
                            <div class="chart-title"><i class="fas fa-calendar-check"></i> Events Scheduled (Next 30 days)</div>
                            <canvas id="providerBreakdownChart" height="140"></canvas>
                        </div>
                        <div class="chart-card">
                            <div class="chart-title"><i class="fas fa-earth-americas"></i> Users by Country (Top 10)</div>
                            <canvas id="topCountriesChart" height="140"></canvas>
                        </div>
                         <div class="chart-card">
                             <div class="chart-title"><i class="fas fa-chart-pie"></i> Event Status Distribution</div>
                             <canvas id="eventStatusChart" height="140"></canvas>
                         </div>
                         <div class="chart-card">
                             <div class="chart-title"><i class="fas fa-people-group"></i> Act vs Venue Profiles</div>
                             <canvas id="actVenueChart" height="140"></canvas>
                         </div>
                         <div class="chart-card">
                             <div class="chart-title"><i class="fas fa-circle-nodes"></i> Profile Status (Published/Created/Deleted)</div>
                             <canvas id="profileStatusChart" height="140"></canvas>
                         </div>
                    </div>
                </div>

                <div id="reports-section" class="content-section">
                    <div class="page-header">
                        <h1 class="page-title">Reports</h1>
                        <p class="page-description">Generate and download system reports</p>
                    </div>
                    <div class="dashboard-section">
                        <div class="toolbar">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" placeholder="Search reports..." id="reportSearch">
                            </div>
                            <div class="toolbar-actions">
                                <button class="btn primary" onclick="generateReport()">
                                    <i class="fas fa-gears"></i> Generate Report
                                </button>
                            </div>
                        </div>
                        <div class="results-section">
                            <div class="results-header">
                                <h3><i class="fas fa-list"></i> Available Reports</h3>
                                <div class="results-info">
                                    <span id="reportsCount">0 items</span>
                                </div>
                            </div>
                            <div id="reportsList" class="reports-list"></div>
                        </div>
                    </div>
                </div>

                <div id="settings-section" class="content-section">
                    <div class="page-header">
                        <h1 class="page-title">System Settings</h1>
                        <p class="page-description">Configure system parameters and preferences</p>
                    </div>
                    <div class="dashboard-section">
                        <h2 class="section-title">Mock & Data Controls</h2>
                        <div class="settings-grid">
                            <div class="setting-item">
                                <div class="setting-meta">
                                    <div class="setting-title">Mock Mode</div>
                                    <div class="setting-desc">Use rich fake data and simulate APIs</div>
                                </div>
                                <button class="btn secondary" onclick="toggleMockMode()"><i class="fas fa-flask"></i> Toggle</button>
                            </div>
                            <div class="setting-item">
                                <div class="setting-meta">
                                    <div class="setting-title">Regenerate Mock Users</div>
                                    <div class="setting-desc">Recreate dataset with random size and values</div>
                                </div>
                                <button class="btn primary" onclick="regenerateMockData()"><i class="fas fa-arrows-rotate"></i> Regenerate</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="logs-section" class="content-section">
                    <div class="page-header">
                        <h1 class="page-title">System Logs</h1>
                        <p class="page-description">View and analyze system activity logs</p>
                    </div>
                    <div class="dashboard-section">
                        <div class="toolbar">
                            <div class="toolbar-actions">
                                <button class="btn secondary" onclick="startMockLogs()"><i class="fas fa-play"></i> Start</button>
                                <button class="btn secondary" onclick="stopMockLogs()"><i class="fas fa-stop"></i> Stop</button>
                                <button class="btn secondary" onclick="clearLogs()"><i class="fas fa-broom"></i> Clear</button>
                            </div>
                        </div>
                        <div id="logsContainer" class="logs-container"></div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Chart.js library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <!-- Admin Console Script -->
    <script src="script.js"></script>
</body>
</html>