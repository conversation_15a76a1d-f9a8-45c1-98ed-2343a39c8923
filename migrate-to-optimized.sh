#!/bin/bash

# StageMinder Docker Configuration Migration Script
# This script helps migrate from the old Docker setup to the optimized shared Neo4j setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker Desktop first."
        exit 1
    fi
}

# Function to create backup directory
create_backup_dir() {
    BACKUP_DIR="./migration_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    echo "$BACKUP_DIR"
}

# Function to backup Neo4j data
backup_neo4j_data() {
    local backup_dir=$1
    print_info "Backing up Neo4j data..."
    
    # Find running Neo4j container
    NEO4J_CONTAINER=$(docker ps --format "table {{.Names}}" | grep -E "(neo4j|stageminder.*neo4j)" | head -1)
    
    if [ -z "$NEO4J_CONTAINER" ]; then
        print_warning "No running Neo4j container found. Checking for existing data volumes..."
        
        # Check for existing volumes
        if docker volume ls | grep -q "neo4j_data\|stageminder.*neo4j"; then
            print_info "Found existing Neo4j volumes. They will be preserved."
            docker volume ls | grep "neo4j" > "$backup_dir/volumes_list.txt"
            print_success "Volume list saved to $backup_dir/volumes_list.txt"
        else
            print_info "No existing Neo4j data found."
        fi
        return 0
    fi
    
    print_info "Found Neo4j container: $NEO4J_CONTAINER"
    
    # Create database dump
    print_info "Creating database dump..."
    docker exec "$NEO4J_CONTAINER" neo4j-admin database dump neo4j --to-path=/tmp/ 2>/dev/null || {
        print_warning "Database dump failed. Trying alternative method..."
        docker exec "$NEO4J_CONTAINER" cypher-shell -u neo4j -p stageminder2024 "CALL apoc.export.cypher.all('/tmp/backup.cypher', {});" 2>/dev/null || {
            print_warning "Cypher export also failed. Manual backup may be needed."
        }
    }
    
    # Copy dump file
    docker cp "$NEO4J_CONTAINER:/tmp/neo4j.dump" "$backup_dir/" 2>/dev/null || {
        docker cp "$NEO4J_CONTAINER:/tmp/backup.cypher" "$backup_dir/" 2>/dev/null || {
            print_warning "Could not copy backup files. Data may still be safe in volumes."
        }
    }
    
    # Backup volume information
    docker volume ls | grep "neo4j" > "$backup_dir/volumes_list.txt"
    
    print_success "Backup completed in $backup_dir"
}

# Function to stop old services
stop_old_services() {
    print_info "Stopping existing services..."
    
    # Stop StageMinder services
    if [ -d "StageMinder" ]; then
        cd StageMinder
        if [ -f "docker-compose.yml" ]; then
            docker-compose -f docker-compose.yml down 2>/dev/null || true
        fi
        if [ -f "docker-compose-local.yml" ]; then
            docker-compose -f docker-compose-local.yml down 2>/dev/null || true
        fi
        cd ..
    fi
    
    # Stop AdminConsole services
    if [ -d "adminconsole" ]; then
        cd adminconsole
        if [ -f "docker-compose.yml" ]; then
            docker-compose -f docker-compose.yml down 2>/dev/null || true
        fi
        if [ -f "docker-compose-admin-only.yml" ]; then
            docker-compose -f docker-compose-admin-only.yml down 2>/dev/null || true
        fi
        cd ..
    fi
    
    print_success "Old services stopped"
}

# Function to migrate volumes
migrate_volumes() {
    print_info "Migrating Neo4j volumes..."
    
    # Check if old volumes exist and rename them
    if docker volume ls -q | grep -q "stageminder_neo4j_data"; then
        print_info "Renaming existing stageminder_neo4j_data volume..."
        # Docker doesn't support volume rename, so we'll use the existing volume
        print_success "Existing volume will be reused"
    elif docker volume ls -q | grep -E ".*neo4j.*data"; then
        OLD_VOLUME=$(docker volume ls -q | grep -E ".*neo4j.*data" | head -1)
        print_info "Found existing Neo4j data volume: $OLD_VOLUME"
        print_info "Creating new volume structure..."
        
        # Create temporary container to copy data
        docker run --rm -v "$OLD_VOLUME":/old -v stageminder_neo4j_data:/new alpine sh -c "cp -a /old/. /new/" 2>/dev/null || {
            print_warning "Volume migration failed. You may need to manually restore data."
        }
    fi
}

# Function to setup new configuration
setup_new_config() {
    print_info "Setting up optimized Docker configuration..."
    
    # Make control scripts executable
    if [ -f "docker-control.sh" ]; then
        chmod +x docker-control.sh
        print_success "Made docker-control.sh executable"
    fi
    
    # Create shared network if it doesn't exist
    if ! docker network ls | grep -q "stageminder-shared-network"; then
        docker network create stageminder-shared-network 2>/dev/null || true
        print_success "Created shared network"
    fi
}

# Function to start optimized services
start_optimized_services() {
    print_info "Starting optimized services..."
    
    # Start Neo4j first
    print_info "Starting shared Neo4j service..."
    docker-compose -f docker-compose.neo4j.yml up -d
    
    # Wait for Neo4j to be ready
    print_info "Waiting for Neo4j to be ready..."
    for i in {1..30}; do
        if docker exec stageminder-neo4j-shared cypher-shell -u neo4j -p stageminder2024 "RETURN 1" > /dev/null 2>&1; then
            print_success "Neo4j is ready!"
            break
        fi
        print_info "Waiting for Neo4j... ($i/30)"
        sleep 2
    done
    
    # Ask user what to start
    echo ""
    print_info "What would you like to start?"
    echo "1) Complete system (StageMinder + AdminConsole)"
    echo "2) Only StageMinder"
    echo "3) Only AdminConsole"
    echo "4) Development environment"
    echo "5) Nothing (just Neo4j)"
    
    read -p "Enter your choice (1-5): " choice
    
    case $choice in
        1)
            ./docker-control.sh start
            ;;
        2)
            ./docker-control.sh start stageminder
            ;;
        3)
            ./docker-control.sh start admin
            ;;
        4)
            ./docker-control.sh start dev
            ;;
        5)
            print_info "Only Neo4j started. Use ./docker-control.sh to start other services."
            ;;
        *)
            print_warning "Invalid choice. Only Neo4j started."
            ;;
    esac
}

# Function to restore data if needed
restore_data() {
    local backup_dir=$1
    
    if [ -f "$backup_dir/neo4j.dump" ] || [ -f "$backup_dir/backup.cypher" ]; then
        echo ""
        print_warning "Backup files found. Do you want to restore the data? (y/N)"
        read -p "This will overwrite any existing data: " restore_choice
        
        if [[ "$restore_choice" =~ ^[Yy]$ ]]; then
            print_info "Restoring data..."
            
            # Stop Neo4j for restoration
            docker-compose -f docker-compose.neo4j.yml stop neo4j
            
            if [ -f "$backup_dir/neo4j.dump" ]; then
                # Restore from dump
                docker cp "$backup_dir/neo4j.dump" stageminder-neo4j-shared:/tmp/
                docker run --rm --volumes-from stageminder-neo4j-shared neo4j:5.15-community \
                    neo4j-admin database load neo4j --from-path=/tmp/neo4j.dump --overwrite-destination=true
            elif [ -f "$backup_dir/backup.cypher" ]; then
                # Restore from cypher file
                docker-compose -f docker-compose.neo4j.yml start neo4j
                sleep 10
                docker cp "$backup_dir/backup.cypher" stageminder-neo4j-shared:/tmp/
                docker exec stageminder-neo4j-shared cypher-shell -u neo4j -p stageminder2024 -f /tmp/backup.cypher
            fi
            
            # Restart Neo4j
            docker-compose -f docker-compose.neo4j.yml start neo4j
            
            print_success "Data restoration completed"
        else
            print_info "Data restoration skipped"
        fi
    fi
}

# Function to verify migration
verify_migration() {
    print_info "Verifying migration..."
    
    # Check if Neo4j is running
    if docker ps | grep -q "stageminder-neo4j-shared"; then
        print_success "✅ Neo4j is running"
    else
        print_error "❌ Neo4j is not running"
        return 1
    fi
    
    # Test Neo4j connection
    if docker exec stageminder-neo4j-shared cypher-shell -u neo4j -p stageminder2024 "RETURN 1" > /dev/null 2>&1; then
        print_success "✅ Neo4j is accessible"
    else
        print_error "❌ Cannot connect to Neo4j"
        return 1
    fi
    
    # Check network
    if docker network ls | grep -q "stageminder-shared-network"; then
        print_success "✅ Shared network exists"
    else
        print_error "❌ Shared network missing"
        return 1
    fi
    
    print_success "Migration verification completed successfully!"
    
    echo ""
    print_info "Access URLs:"
    echo "  • Neo4j Browser: http://localhost:7474 (neo4j/stageminder2024)"
    echo "  • StageMinder: http://localhost"
    echo "  • Admin Console: http://localhost:3001"
    echo ""
    print_info "Use './docker-control.sh status' to check service status"
    print_info "Use './docker-control.sh --help' to see all available commands"
}

# Main migration process
main() {
    echo "StageMinder Docker Configuration Migration"
    echo "=========================================="
    echo ""
    
    check_docker
    
    print_warning "This script will migrate your Docker configuration to use a shared Neo4j instance."
    print_warning "It's recommended to backup your data before proceeding."
    echo ""
    
    read -p "Do you want to continue? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        print_info "Migration cancelled."
        exit 0
    fi
    
    # Create backup
    BACKUP_DIR=$(create_backup_dir)
    print_info "Backup directory: $BACKUP_DIR"
    
    # Backup existing data
    backup_neo4j_data "$BACKUP_DIR"
    
    # Stop old services
    stop_old_services
    
    # Migrate volumes
    migrate_volumes
    
    # Setup new configuration
    setup_new_config
    
    # Start optimized services
    start_optimized_services
    
    # Restore data if needed
    restore_data "$BACKUP_DIR"
    
    # Verify migration
    if verify_migration; then
        print_success "Migration completed successfully!"
        print_info "Backup files are stored in: $BACKUP_DIR"
        print_info "You can delete the backup directory if everything works correctly."
    else
        print_error "Migration verification failed. Please check the logs."
        print_info "Backup files are in: $BACKUP_DIR"
    fi
}

# Run main function
main "$@"
