<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Act Count</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Act Count Test</h1>
    <div id="results"></div>
    
    <h2>Test Elements</h2>
    <p>Total Acts: <span id="totalActsCount">Loading...</span></p>
    
    <script>
        function addResult(message, isSuccess = true) {
            const div = document.createElement('div');
            div.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            div.textContent = message;
            document.getElementById('results').appendChild(div);
        }
        
        function testActCount() {
            // Test 1: Check if element exists
            const element = document.getElementById('totalActsCount');
            if (element) {
                addResult('✓ totalActsCount element found');
            } else {
                addResult('✗ totalActsCount element not found', false);
                return;
            }
            
            // Test 2: Set the count
            const realActCount = 41;
            element.textContent = realActCount;
            addResult(`✓ Set act count to ${realActCount}`);
            
            // Test 3: Verify the count
            if (element.textContent === '41') {
                addResult('✓ Act count correctly displayed as 41');
            } else {
                addResult(`✗ Act count shows "${element.textContent}" instead of "41"`, false);
            }
            
            // Test 4: Test API call simulation
            setTimeout(() => {
                try {
                    fetch('http://localhost:3001/script.js')
                        .then(response => response.text())
                        .then(text => {
                            if (text.includes('realActCount = 41')) {
                                addResult('✓ Script.js contains realActCount = 41');
                            } else {
                                addResult('✗ Script.js does not contain realActCount = 41', false);
                            }
                        })
                        .catch(error => {
                            addResult('✗ Failed to fetch script.js: ' + error.message, false);
                        });
                } catch (error) {
                    addResult('✗ Error testing script.js: ' + error.message, false);
                }
            }, 1000);
        }
        
        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', testActCount);
    </script>
</body>
</html>
